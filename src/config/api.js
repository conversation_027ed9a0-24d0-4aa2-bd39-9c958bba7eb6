/**
 * 🔧 API Configuration
 * API配置文件 - 集中管理所有API相关配置
 */

// 获取当前环境
const isDevelopment = !window.location.origin.includes('github.io') && 
                     !window.location.origin.includes('netlify.app') &&
                     !window.location.origin.includes('vercel.app');

// 从环境变量获取API基础URL，提供合理的默认值
const getApiBaseUrl = () => {
    // 优先使用环境变量
    if (import.meta.env.VITE_API_BASE_URL) {
        return import.meta.env.VITE_API_BASE_URL;
    }
    
    // 如果没有环境变量，根据环境自动决定
    return isDevelopment ? 'http://localhost:3001' : 'http://************:3001';
};

// API基础URL配置
export const API_CONFIG = {
    // 后端API地址
    BASE_URL: getApiBaseUrl(),
    
    // QCEW API端点
    QCEW_ENDPOINTS: {
        HEALTH: '/api/qcew/health',
        SEARCH_AREAS: '/api/qcew/search-areas',
        DATA_BY_CITY: '/api/qcew/data-by-city',
        CALCULATE_PAYROLL: '/api/qcew/calculate-payroll-by-city',
        TEST: '/api/qcew/test'
    },
    
    // LAUS API端点
    LAUS_ENDPOINTS: {
        HEALTH: '/api/us-labor/laus/health',
        CALCULATE_AVAILABLE_LABOR: '/api/us-labor/laus/calculate-available-labor'
    },
    
    // 请求配置
    REQUEST_CONFIG: {
        TIMEOUT: 30000, // 30秒超时
        HEADERS: {
            'Content-Type': 'application/json'
        }
    }
};

// 构建完整的API URL
export const buildApiUrl = (endpoint) => {
    return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// 环境检测
export const ENV_INFO = {
    isDevelopment,
    currentOrigin: window.location.origin,
    timestamp: new Date().toISOString()
};

console.log('🔧 API配置加载:', {
    baseUrl: API_CONFIG.BASE_URL,
    environment: isDevelopment ? 'development' : 'production',
    origin: window.location.origin
}); 
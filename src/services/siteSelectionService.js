/**
 * 🏭 Site Selection Service
 * 站点选择功能的前端服务
 * 
 * 提供以下功能：
 * - 站点勘探分析 (Site Prospecting)
 * - 财务承保分析 (Financial Underwriting)
 * - 压力测试 (Stress Testing)
 */

import { API_CONFIG } from '../config/api.js';
const API_BASE_URL = `${API_CONFIG.BASE_URL}/api/site-selection`;

/**
 * 通用API请求函数
 * @param {string} endpoint - API端点
 * @param {object} data - 请求数据
 * @param {string} method - HTTP方法
 * @returns {Promise} - API响应
 */
async function apiRequest(endpoint, data = null, method = 'GET') {
    try {
        const config = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        if (data && method !== 'GET') {
            config.body = JSON.stringify(data);
        }

        const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error(`API请求失败 (${endpoint}):`, error);
        throw error;
    }
}

/**
 * 站点勘探分析
 * @param {object} searchParams - 搜索参数
 * @returns {Promise} - 勘探结果
 */
export async function runSiteProspecting(searchParams) {
    const {
        searchArea,
        parcelFilters = {},
        userConstraints = {},
        maxCandidates = 10
    } = searchParams;

    // 验证必需参数
    if (!searchArea || !searchArea.type || !searchArea.value) {
        throw new Error('搜索区域参数不完整');
    }

    const requestData = {
        search_area: searchArea,
        parcel_filters: parcelFilters,
        user_constraints: userConstraints,
        max_candidates: maxCandidates
    };

    return await apiRequest('/prospecting', requestData, 'POST');
}

/**
 * 财务承保分析
 * @param {object} analysisParams - 分析参数
 * @returns {Promise} - 财务分析结果
 */
export async function runFinancialAnalysis(analysisParams) {
    const { parcel, financialParams = {} } = analysisParams;

    // 验证必需参数
    if (!parcel || !parcel.parcel_id) {
        throw new Error('地块数据不完整');
    }

    const requestData = {
        parcel,
        financial_params: financialParams
    };

    return await apiRequest('/financial', requestData, 'POST');
}

/**
 * 压力测试分析
 * @param {object} stressParams - 压力测试参数
 * @returns {Promise} - 压力测试结果
 */
export async function runStressTesting(stressParams) {
    const { baseInputs, stressParams: testParams = {} } = stressParams;

    // 验证必需参数
    if (!baseInputs) {
        throw new Error('基础输入数据不完整');
    }

    const requestData = {
        base_inputs: baseInputs,
        stress_params: testParams
    };

    return await apiRequest('/stress', requestData, 'POST');
}

/**
 * 测试Python环境
 * @returns {Promise} - 测试结果
 */
export async function testPythonEnvironment() {
    return await apiRequest('/test');
}

/**
 * 健康检查
 * @returns {Promise} - 健康状态
 */
export async function healthCheck() {
    return await apiRequest('/health');
}

/**
 * 预设搜索参数
 */
export const PRESET_SEARCH_PARAMS = {
    // 达拉斯工业园区搜索
    dallas: {
        searchArea: {
            type: "city",
            value: "Dallas, TX"
        },
        parcelFilters: {
            min_area_sqft: 50000,
            max_area_sqft: 200000,
            zoning: "industrial",
            max_distance_to_port_miles: 100
        },
        userConstraints: {
            workers: 100,
            annual_kwh: 500000,
            port_hub: "Houston, TX"
        },
        maxCandidates: 10
    },

    // 洛杉矶工业园区搜索
    losAngeles: {
        searchArea: {
            type: "city",
            value: "Los Angeles, CA"
        },
        parcelFilters: {
            min_area_sqft: 75000,
            max_area_sqft: 300000,
            zoning: "industrial",
            max_distance_to_port_miles: 50
        },
        userConstraints: {
            workers: 150,
            annual_kwh: 750000,
            port_hub: "Los Angeles, CA"
        },
        maxCandidates: 15
    },

    // 芝加哥工业园区搜索
    chicago: {
        searchArea: {
            type: "city",
            value: "Chicago, IL"
        },
        parcelFilters: {
            min_area_sqft: 40000,
            max_area_sqft: 150000,
            zoning: "industrial",
            max_distance_to_port_miles: 200
        },
        userConstraints: {
            workers: 80,
            annual_kwh: 400000,
            port_hub: "Chicago, IL"
        },
        maxCandidates: 12
    }
};

/**
 * 预设财务参数
 */
export const PRESET_FINANCIAL_PARAMS = {
    // 保守型投资
    conservative: {
        building_cost_per_sqft: 100.0,
        equipment_cost: 2000000,
        down_payment_rate: 0.30,
        mortgage_rate: 0.060,
        starting_rent_per_sqft: 25.0,
        occupancy_rate: 0.90,
        escalation_rate: 0.020
    },

    // 标准型投资
    standard: {
        building_cost_per_sqft: 120.0,
        equipment_cost: 2500000,
        down_payment_rate: 0.25,
        mortgage_rate: 0.055,
        starting_rent_per_sqft: 30.0,
        occupancy_rate: 0.95,
        escalation_rate: 0.025
    },

    // 激进型投资
    aggressive: {
        building_cost_per_sqft: 140.0,
        equipment_cost: 3000000,
        down_payment_rate: 0.20,
        mortgage_rate: 0.050,
        starting_rent_per_sqft: 35.0,
        occupancy_rate: 0.98,
        escalation_rate: 0.030
    }
};

/**
 * 预设压力测试参数
 */
export const PRESET_STRESS_PARAMS = {
    // 轻度压力测试
    mild: {
        rate_shock: { delta_rate: 0.005 },
        rent_downturn: { down_pct: 0.05 },
        occupancy_drop: { drop_pct: 0.05 }
    },

    // 中度压力测试
    moderate: {
        rate_shock: { delta_rate: 0.01 },
        rent_downturn: { down_pct: 0.10 },
        occupancy_drop: { drop_pct: 0.10 }
    },

    // 严重压力测试
    severe: {
        rate_shock: { delta_rate: 0.02 },
        rent_downturn: { down_pct: 0.20 },
        occupancy_drop: { drop_pct: 0.20 }
    }
};

/**
 * 格式化财务数据显示
 * @param {number} value - 数值
 * @param {string} type - 类型 (currency, percentage, number)
 * @returns {string} - 格式化后的字符串
 */
export function formatFinancialValue(value, type = 'currency') {
    if (value === null || value === undefined) return 'N/A';

    switch (type) {
        case 'currency':
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(value);

        case 'percentage':
            return new Intl.NumberFormat('en-US', {
                style: 'percent',
                minimumFractionDigits: 1,
                maximumFractionDigits: 2
            }).format(value);

        case 'number':
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2
            }).format(value);

        default:
            return value.toString();
    }
}

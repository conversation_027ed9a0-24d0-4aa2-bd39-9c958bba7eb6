/**
 * 📈 LAUS (Local Area Unemployment Statistics) Service
 * 前端服务 - 获取县级劳动力数据并计算可招聘余量
 */

import { API_CONFIG, buildApiUrl } from '../config/api';

/**
 * 常量定义
 */
export const LAUS_CONSTANTS = {
    FIELD_MAPPING: {
        LAUS_LABOR_FORCE: 'labor_force',
        LAUS_EMPLOYED: 'employment',
        LAUS_UNEMPLOYED: 'unemployed', // 派生
        AVAILABLE_LABOR: 'available_labor'
    }
};

/**
 * 通用API请求
 */
async function apiRequest(endpoint, data = null, method = 'GET') {
    try {
        const config = {
            method,
            headers: {
                'Content-Type': 'application/json'
            },
        };
        if (data && method !== 'GET') {
            config.body = JSON.stringify(data);
        }

        const url = buildApiUrl(endpoint);
        const response = await fetch(url, config);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error(`LAUS API 请求失败 (${endpoint}):`, error);
        throw error;
    }
}

/**
 * 计算可招聘余量 (AVAILABLE_LABOR)
 * @param {object} params - { city, state, areaCode, year }
 */
export async function calculateAvailableLabor(params) {
    const { city, state, areaCode, year } = params;

    if (!areaCode && !city) {
        throw new Error('请至少提供 areaCode 或 city');
    }

    const requestBody = {
        area_code: areaCode,
        city,
        state,
        year
    };

    const result = await apiRequest(API_CONFIG.LAUS_ENDPOINTS.CALCULATE_AVAILABLE_LABOR, requestBody, 'POST');
    return result;
}

/**
 * 健康检查
 */
export async function lausHealthCheck() {
    return await apiRequest(API_CONFIG.LAUS_ENDPOINTS.HEALTH);
} 
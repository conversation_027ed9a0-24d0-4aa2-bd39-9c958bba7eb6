/**
 * 🏭 QCEW (Quarterly Census of Employment and Wages) Service
 * 季度就业与工资普查服务 - 支持城市名称查询和地区代码转换
 * 
 * 提供以下功能：
 * - 城市名称搜索和地区代码转换
 * - 基于城市的QCEW数据获取
 * - 基于城市的年度薪酬计算
 * - API可行性测试
 */

import { API_CONFIG, buildApiUrl } from '../config/api';

/**
 * QCEW常量定义 - 方便未来调用和维护
 */
export const QCEW_CONSTANTS = {
    // 自定义变量名到API字段的映射
    FIELD_MAPPING: {
        QCEW_AVG_WEEKLY_WAGE: 'avg_weekly_wage',  // 平均周薪
        QCEW_EMPLOYMENT: 'avg_employment',        // 平均就业人数
        QCEW_TOTAL_WAGES: 'total_wages',         // 总工资
        QCEW_ESTABLISHMENTS: 'qtrly_estabs'      // 季度企业数
    },
    
    // 计算常量
    WEEKS_PER_YEAR: 52,
    
    // 系列ID构建参数
    SERIES_COMPONENTS: {
        SURVEY: 'EW',        // QCEW调查
        SEASONALITY: 'U',    // 未季节性调整
        INDUSTRY: '10',      // 所有行业
        OWNERSHIP: '5',      // 私营部门
        DATA_TYPE: '6'       // 平均周薪
    },
    
    // 推荐的主要城市
    RECOMMENDED_CITIES: [
        { name: 'Los Angeles', state: 'California' },
        { name: 'New York', state: 'New York' },
        { name: 'Chicago', state: 'Illinois' },
        { name: 'Houston', state: 'Texas' },
        { name: 'Phoenix', state: 'Arizona' },
        { name: 'Philadelphia', state: 'Pennsylvania' },
        { name: 'San Antonio', state: 'Texas' },
        { name: 'San Diego', state: 'California' },
        { name: 'Dallas', state: 'Texas' },
        { name: 'San Jose', state: 'California' }
    ]
};

/**
 * 通用API请求函数
 * @param {string} endpoint - API端点
 * @param {object} data - 请求数据
 * @param {string} method - HTTP方法
 * @returns {Promise} - API响应
 */
async function apiRequest(endpoint, data = null, method = 'GET') {
    try {
        const config = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        if (data && method !== 'GET') {
            config.body = JSON.stringify(data);
        }

        const url = buildApiUrl(`/api/qcew${endpoint}`);
        console.log(`🔍 QCEW API请求: ${method} ${url}`, data);

        const response = await fetch(url, config);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log(`✅ QCEW API响应:`, result);
        
        return result;
    } catch (error) {
        console.error(`❌ QCEW API请求失败 (${endpoint}):`, error);
        throw error;
    }
}

/**
 * 🔍 搜索城市对应的地区代码
 * @param {string} cityName - 城市名称
 * @param {string} state - 州名（可选）
 * @returns {Promise} - 地区搜索结果
 */
export async function searchCityAreas(cityName, state = null) {
    if (!cityName || cityName.trim() === '') {
        throw new Error('请提供城市名称');
    }

    const params = new URLSearchParams();
    params.append('city', cityName.trim());
    if (state && state.trim()) {
        params.append('state', state.trim());
    }

    return await apiRequest(`/search-areas?${params.toString()}`);
}

/**
 * 📊 基于城市名称获取QCEW数据
 * @param {object} params - 查询参数
 * @param {string} params.city - 城市名称 (如: "Los Angeles")
 * @param {string} params.state - 州名 (可选，如: "California")
 * @param {string} params.year - 年份 (可选，默认: "2024")
 * @returns {Promise} - QCEW数据响应
 */
export async function getQCEWDataByCity(params) {
    const { city, state, year = '2024' } = params;

    if (!city) {
        throw new Error('城市名称是必需的');
    }

    const requestData = {
        city: city.trim(),
        state: state ? state.trim() : null,
        year
    };

    return await apiRequest('/data-by-city', requestData, 'POST');
}

/**
 * 🧮 基于城市名称计算年度薪酬
 * @param {object} params - 计算参数
 * @param {string} params.city - 城市名称
 * @param {string} params.state - 州名 (可选)
 * @param {number} params.plannedFTE - 计划全职员工数
 * @param {string} params.year - 年份 (可选，默认: "2024")
 * @returns {Promise} - 薪酬计算结果
 */
export async function calculatePayrollByCity(params) {
    const { city, state, plannedFTE, year = '2024' } = params;

    // 验证必需参数
    if (!city) {
        throw new Error('城市名称是必需的');
    }

    if (!plannedFTE || typeof plannedFTE !== 'number' || plannedFTE <= 0) {
        throw new Error('计划员工数必须是正数');
    }

    const requestData = {
        city: city.trim(),
        state: state ? state.trim() : null,
        planned_fte: plannedFTE,
        year
    };

    return await apiRequest('/calculate-payroll-by-city', requestData, 'POST');
}

/**
 * 💰 本地薪酬计算函数（用于快速计算）
 * @param {object} params - 计算参数
 * @param {number} params.avgWeeklyWage - 平均周薪
 * @param {number} params.plannedFTE - 计划全职员工数
 * @param {number} params.weeksPerYear - 每年周数 (可选，默认52)
 * @returns {object} - 计算结果
 */
export function calculateAnnualPayroll(params) {
    const {
        avgWeeklyWage,
        plannedFTE,
        weeksPerYear = QCEW_CONSTANTS.WEEKS_PER_YEAR
    } = params;

    // 验证参数
    if (typeof avgWeeklyWage !== 'number' || avgWeeklyWage <= 0) {
        throw new Error('平均周薪必须是正数');
    }

    if (typeof plannedFTE !== 'number' || plannedFTE <= 0) {
        throw new Error('计划全职员工数必须是正数');
    }

    // 执行计算：Annual_Payroll = QCEW_AVG_WEEKLY_WAGE × WEEKS_PER_YEAR × PLANNED_FTE
    const annualPayroll = avgWeeklyWage * weeksPerYear * plannedFTE;
    const monthlyPayroll = annualPayroll / 12;
    const perEmployeeAnnual = avgWeeklyWage * weeksPerYear;

    return {
        success: true,
        calculation: {
            formula: 'Annual_Payroll = QCEW_AVG_WEEKLY_WAGE × WEEKS_PER_YEAR × PLANNED_FTE',
            inputs: {
                QCEW_AVG_WEEKLY_WAGE: avgWeeklyWage,
                WEEKS_PER_YEAR: weeksPerYear,
                PLANNED_FTE: plannedFTE
            },
            result: {
                annual_payroll: annualPayroll,
                monthly_payroll: monthlyPayroll,
                per_employee_annual: perEmployeeAnnual,
                formatted_annual: formatCurrency(annualPayroll),
                formatted_monthly: formatCurrency(monthlyPayroll),
                formatted_per_employee: formatCurrency(perEmployeeAnnual)
            }
        },
        metadata: {
            calculation_time: new Date().toISOString()
        }
    };
}

/**
 * 🎯 智能城市薪酬计算 - 一站式服务
 * 自动处理城市搜索、数据获取和薪酬计算
 * @param {object} params - 计算参数
 * @param {string} params.city - 城市名称
 * @param {string} params.state - 州名 (可选)
 * @param {number} params.plannedFTE - 计划全职员工数
 * @param {string} params.year - 年份 (可选)
 * @returns {Promise} - 完整的分析结果
 */
export async function getSmartCityPayrollAnalysis(params) {
    try {
        console.log('🎯 开始智能城市薪酬分析:', params);

        // 第一步：搜索城市地区代码
        const searchResult = await searchCityAreas(params.city, params.state);
        
        if (!searchResult.success || searchResult.matches.length === 0) {
            throw new Error(`未找到匹配的地区: ${params.city}`);
        }

        const bestMatch = searchResult.matches[0];
        console.log(`✅ 选择最佳匹配地区: ${bestMatch.areaTitle} (${bestMatch.areaCode})`);

        // 第二步：计算薪酬
        const payrollResult = await calculatePayrollByCity(params);

        if (!payrollResult.success) {
            throw new Error('薪酬计算失败');
        }

        // 第三步：整合结果
        return {
            success: true,
            city_analysis: {
                query: {
                    city: params.city,
                    state: params.state,
                    planned_fte: params.plannedFTE,
                    year: params.year || '2024'
                },
                selected_area: {
                    area_code: bestMatch.areaCode,
                    area_title: bestMatch.areaTitle,
                    area_type: bestMatch.type,
                    match_score: bestMatch.score
                },
                wage_data: {
                    avg_weekly_wage: payrollResult.calculation.components.avg_weekly_wage,
                    data_source: payrollResult.calculation.metadata.data_source,
                    series_id: payrollResult.calculation.metadata.series_id
                },
                payroll_calculation: payrollResult.calculation,
                alternatives: searchResult.matches.slice(1, 5)
            },
            metadata: {
                analysis_time: new Date().toISOString(),
                api_version: '2.0.0'
            }
        };

    } catch (error) {
        console.error('❌ 智能城市薪酬分析失败:', error);
        return {
            success: false,
            error: error.message,
            details: '智能分析过程中发生错误',
            recommendations: QCEW_CONSTANTS.RECOMMENDED_CITIES
        };
    }
}

/**
 * 🧪 测试QCEW API的完整功能
 * @param {object} testParams - 测试参数
 * @returns {Promise} - 测试结果
 */
export async function testQCEWAPI(testParams = {}) {
    const defaultParams = {
        city: 'Los Angeles',
        state: 'California',
        fte: 100,
        year: '2024'
    };

    const params = { ...defaultParams, ...testParams };

    try {
        console.log('🧪 开始QCEW API完整功能测试...', params);

        const testResult = await apiRequest(
            `/test?city=${params.city}&state=${params.state}&fte=${params.fte}&year=${params.year}`
        );

        return testResult;

    } catch (error) {
        console.error('❌ QCEW API测试失败:', error);
        return {
            success: false,
            error: error.message,
            message: 'API测试失败，请检查网络连接和服务状态'
        };
    }
}

/**
 * 🏥 检查QCEW服务健康状态
 * @returns {Promise} - 健康检查结果
 */
export async function checkQCEWServiceHealth() {
    try {
        const healthResult = await apiRequest('/health');
        return healthResult;
    } catch (error) {
        console.error('❌ QCEW服务健康检查失败:', error);
        return {
            success: false,
            error: error.message,
            status: '服务不可用'
        };
    }
}

/**
 * 📋 获取推荐的城市列表
 * @returns {Array} - 推荐城市列表
 */
export function getRecommendedCities() {
    return QCEW_CONSTANTS.RECOMMENDED_CITIES;
}

/**
 * 💲 格式化货币显示
 * @param {number} amount - 金额
 * @returns {string} - 格式化后的货币字符串
 */
function formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '$0.00';
    }

    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

/**
 * 🔢 格式化数字显示
 * @param {number} number - 数字
 * @returns {string} - 格式化后的数字字符串
 */
export function formatNumber(number) {
    if (typeof number !== 'number' || isNaN(number)) {
        return '0';
    }

    return new Intl.NumberFormat('en-US').format(number);
}

/**
 * ⏱️ 格式化时间显示
 * @param {string|Date} timestamp - 时间戳
 * @returns {string} - 格式化后的时间字符串
 */
export function formatTimestamp(timestamp) {
    try {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return '时间未知';
    }
} 
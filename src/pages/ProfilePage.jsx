import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI } from '../services/api';
import Silk from '../components/3D/Silk';
import '../styles/ProfilePage.css';

// 图标组件
const UserIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
);

const EmailIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
    <polyline points="22,6 12,13 2,6" />
  </svg>
);

const CalendarIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
    <line x1="16" y1="2" x2="16" y2="6"/>
    <line x1="8" y1="2" x2="8" y2="6"/>
    <line x1="3" y1="10" x2="21" y2="10"/>
  </svg>
);

const ClockIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <circle cx="12" cy="12" r="10"/>
    <polyline points="12,6 12,12 16,14"/>
  </svg>
);

const StarIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
  </svg>
);

const EditIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
  </svg>
);

const SaveIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
  </svg>
);

const CancelIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
  </svg>
);

const ArrowLeftIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19 12H5M12 19l-7-7 7-7" />
  </svg>
);

const LanguageIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z" />
  </svg>
);

const ProfilePage = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  
  // 编辑表单状态
  const [editForm, setEditForm] = useState({
    username: '',
    preferred_language: 'en'
  });

  // 多语言文本
  const texts = useMemo(() => ({
    en: {
      title: 'User Profile',
      subtitle: 'Manage your account settings',
      personalInfo: 'Personal Information',
      accountInfo: 'Account Information',
      edit: 'Edit Profile',
      save: 'Save Changes',
      cancel: 'Cancel',
      back: 'Back',
      userId: 'User ID',
      email: 'Email Address',
      username: 'Username',
      preferredLanguage: 'Preferred Language',
      createdAt: 'Member Since',
      lastLoginAt: 'Last Login',
      subscriptionStatus: 'Subscription Status',
      english: 'English',
      chinese: 'Chinese',
      free: 'Free',
      premium: 'Premium',
      basic: 'Basic',
      loading: 'Loading profile...',
      saving: 'Saving changes...',
      saveSuccess: 'Profile updated successfully!',
      saveError: 'Failed to update profile',
      loadError: 'Failed to load profile'
    },
    zh: {
      title: '用户资料',
      subtitle: '管理您的账户设置',
      personalInfo: '个人信息',
      accountInfo: '账户信息',
      edit: '编辑资料',
      save: '保存更改',
      cancel: '取消',
      back: '返回',
      userId: '用户ID',
      email: '邮箱地址',
      username: '用户名',
      preferredLanguage: '首选语言',
      createdAt: '注册时间',
      lastLoginAt: '最后登录',
      subscriptionStatus: '订阅状态',
      english: 'English',
      chinese: '中文',
      free: '免费版',
      premium: '高级版',
      basic: '基础版',
      loading: '加载中...',
      saving: '保存中...',
      saveSuccess: '资料更新成功！',
      saveError: '资料更新失败',
      loadError: '加载资料失败'
    }
  }), []);

  const t = texts[language];

  // 加载用户信息
  const loadUserProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      setError('');
      
      const response = await authAPI.getCurrentUser();
      if (response.success) {
        setUser(response.data.user);
        setEditForm({
          username: response.data.user.username || '',
          preferred_language: response.data.user.preferred_language || 'en'
        });
      } else {
        throw new Error(response.message || t.loadError);
      }
    } catch (err) {
      console.error('加载用户资料失败:', err);
      setError(err.message || t.loadError);
    } finally {
      setIsLoading(false);
    }
  }, [t.loadError]);

  // 页面加载时获取用户信息
  useEffect(() => {
    loadUserProfile();
  }, [loadUserProfile]);

  // 处理编辑模式切换
  const handleEditToggle = () => {
    if (isEditing) {
      // 取消编辑，重置表单
      setEditForm({
        username: user?.username || '',
        preferred_language: user?.preferred_language || 'en'
      });
      setError('');
      setSuccess('');
    }
    setIsEditing(!isEditing);
  };

  // 处理表单输入
  const handleInputChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 保存更改
  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError('');
      setSuccess('');

      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 10000);
      });

      const response = await Promise.race([
        authAPI.updateProfile(editForm),
        timeoutPromise
      ]);
      
      if (response.success) {
        setUser(response.data.user);
        setIsEditing(false);
        setSuccess(t.saveSuccess);
        
        // 如果语言偏好变更，更新本地存储
        if (editForm.preferred_language !== language) {
          localStorage.setItem('preferredLanguage', editForm.preferred_language);
          setLanguage(editForm.preferred_language);
        }
        
        // 3秒后清除成功消息
        setTimeout(() => setSuccess(''), 3000);
      } else {
        throw new Error(response.message || t.saveError);
      }
    } catch (err) {
      console.error('更新资料失败:', err);
      setError(err.message || t.saveError);
      
      // 如果出错，重置编辑状态
      setEditForm({
        username: user?.username || '',
        preferred_language: user?.preferred_language || 'en'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // 格式化日期时间
  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleString(language === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取订阅状态显示文本
  const getSubscriptionStatusText = (status) => {
    switch (status) {
      case 'free': return t.free;
      case 'premium': return t.premium;
      case 'basic': return t.basic;
      default: return status || '-';
    }
  };

  // 处理返回
  const handleBack = () => {
    navigate(-1);
  };

  // 强制重置状态
  const handleReset = () => {
    setIsSaving(false);
    setIsEditing(false);
    setError('');
    setSuccess('');
    loadUserProfile();
  };

  if (isLoading) {
    return (
      <div className="profile-page">
        <div className="profile-container">
          <div className="loading-section">
            <div className="loading-spinner"></div>
            <p className="loading-text">{t.loading}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modern-profile-page">
      {/* Silk 3D背景 */}
      <div className="profile-background">
        <Silk
          speed={2}
          scale={1.2}
          color="#1a1a2e"
          noiseIntensity={0.4}
          rotation={0.1}
        />
        <div className="silk-overlay"></div>
      </div>

      {/* 顶部导航栏 */}
      <div className="top-navigation">
        <button className="nav-back-btn" onClick={handleBack}>
          <ArrowLeftIcon />
          <span>{t.back}</span>
        </button>
        
        <div className="nav-title">
          <h1>{t.title}</h1>
          <span className="nav-subtitle">{t.subtitle}</span>
        </div>
        
        <div className="nav-actions">
          {(isSaving || error) && (
            <button className="nav-reset-btn" onClick={handleReset}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M3 12a9 9 0 013-7"/>
                <path d="m3 5 2.5-2.5L8 5"/>
                <path d="m21 12a9 9 0 01-3 7"/>
                <path d="m21 19-2.5 2.5L16 19"/>
              </svg>
              {t.cancel}
            </button>
          )}
        </div>
      </div>

      {/* 全局消息提示 */}
      {(error || success) && (
        <div className={`global-notification ${error ? 'error' : 'success'}`}>
          <div className="notification-content">
            {error && (
              <>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"/>
                  <line x1="15" y1="9" x2="9" y2="15"/>
                  <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                <span>{error}</span>
              </>
            )}
            {success && (
              <>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 12l2 2 4-4"/>
                  <circle cx="12" cy="12" r="10"/>
                </svg>
                <span>{success}</span>
              </>
            )}
          </div>
        </div>
      )}

      {/* 主网格布局 */}
      <div className="profile-grid">
        {/* 左侧：用户概览 */}
        <div className="profile-overview">
          <div className="user-avatar-section">
            <div className="avatar-container">
              <div className="avatar-ring">
                <div className="avatar-content">
                  <UserIcon />
                </div>
              </div>
              <div className="avatar-status">
                <div className="status-indicator active"></div>
              </div>
            </div>
            
            <div className="user-basic-info">
              <h2 className="user-display-name">{user?.username || 'User'}</h2>
              <p className="user-email">{user?.email}</p>
              <div className="user-meta">
                <span className="user-id">#{user?.id}</span>
                <span className="user-status">
                  <span className={`status-badge ${user?.active_subscription_status || 'free'}`}>
                    {getSubscriptionStatusText(user?.active_subscription_status)}
                  </span>
                </span>
              </div>
            </div>
          </div>

          {/* 快速统计 */}
          <div className="quick-stats">
            <div className="stat-item">
              <div className="stat-icon">
                <CalendarIcon />
              </div>
              <div className="stat-content">
                <span className="stat-label">{t.createdAt}</span>
                <span className="stat-value">{formatDate(user?.created_at)}</span>
              </div>
            </div>
            
            <div className="stat-item">
              <div className="stat-icon">
                <ClockIcon />
              </div>
              <div className="stat-content">
                <span className="stat-label">{t.lastLoginAt}</span>
                <span className="stat-value">{formatDateTime(user?.last_login_at)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 中间：可编辑信息 */}
        <div className="editable-section">
          <div className="section-header">
            <h3>{t.personalInfo}</h3>
            <button 
              className={`edit-toggle ${isEditing ? 'editing' : ''}`}
              onClick={handleEditToggle}
              disabled={isSaving}
            >
              {isEditing ? (
                <>
                  <CancelIcon />
                  <span>{t.cancel}</span>
                </>
              ) : (
                <>
                  <EditIcon />
                  <span>{t.edit}</span>
                </>
              )}
            </button>
          </div>

          <div className="form-grid">
            {/* 用户名字段 */}
            <div className="form-field">
              <label className="field-label">
                <UserIcon />
                <span>{t.username}</span>
              </label>
              {isEditing ? (
                <div className="input-container">
                  <input
                    type="text"
                    className="modern-input"
                    value={editForm.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    placeholder={t.username}
                  />
                  <div className="input-underline"></div>
                </div>
              ) : (
                <div className="field-display">
                  {user?.username || '-'}
                </div>
              )}
            </div>

            {/* 语言偏好字段 */}
            <div className="form-field">
              <label className="field-label">
                <LanguageIcon />
                <span>{t.preferredLanguage}</span>
              </label>
              {isEditing ? (
                <div className="select-container">
                  <select
                    className="modern-select"
                    value={editForm.preferred_language}
                    onChange={(e) => handleInputChange('preferred_language', e.target.value)}
                  >
                    <option value="en">{t.english}</option>
                    <option value="zh">{t.chinese}</option>
                  </select>
                  <div className="select-arrow">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </div>
              ) : (
                <div className="field-display">
                  <span className="language-indicator">
                    {user?.preferred_language === 'zh' ? '🇨🇳' : '🇺🇸'}
                  </span>
                  {user?.preferred_language === 'zh' ? t.chinese : t.english}
                </div>
              )}
            </div>

            {/* 邮箱字段（只读） */}
            <div className="form-field readonly">
              <label className="field-label">
                <EmailIcon />
                <span>{t.email}</span>
              </label>
              <div className="field-display readonly">
                <span>{user?.email || '-'}</span>
                <div className="readonly-indicator">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                    <circle cx="12" cy="16" r="1"/>
                    <path d="m7 11 0-4a5 5 0 0 1 10 0v4"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* 保存按钮 */}
          {isEditing && (
            <div className="save-actions">
              <button 
                className="save-btn"
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <div className="spinner"></div>
                    <span>{t.saving}</span>
                  </>
                ) : (
                  <>
                    <SaveIcon />
                    <span>{t.save}</span>
                  </>
                )}
              </button>
            </div>
          )}
        </div>

        {/* 右侧：账户状态和设置 */}
        <div className="account-panel">
          <div className="panel-section">
            <h3 className="panel-title">{t.accountInfo}</h3>
            
            <div className="info-cards">
              <div className="info-card">
                <div className="card-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="m22 21-3-3"/>
                  </svg>
                </div>
                <div className="card-content">
                  <span className="card-label">User ID</span>
                  <span className="card-value">#{user?.id}</span>
                </div>
              </div>

              <div className="info-card">
                <div className="card-icon subscription">
                  <StarIcon />
                </div>
                <div className="card-content">
                  <span className="card-label">{t.subscriptionStatus}</span>
                  <span className={`card-badge ${user?.active_subscription_status || 'free'}`}>
                    {getSubscriptionStatusText(user?.active_subscription_status)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 活动指标 */}
          <div className="panel-section">
            <h3 className="panel-title">Activity</h3>
            
            <div className="activity-timeline">
              <div className="timeline-item">
                <div className="timeline-dot"></div>
                <div className="timeline-content">
                  <span className="timeline-label">Last Login</span>
                  <span className="timeline-time">{formatDateTime(user?.last_login_at)}</span>
                </div>
              </div>
              
              <div className="timeline-item">
                <div className="timeline-dot"></div>
                <div className="timeline-content">
                  <span className="timeline-label">Member Since</span>
                  <span className="timeline-time">{formatDate(user?.created_at)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage; 
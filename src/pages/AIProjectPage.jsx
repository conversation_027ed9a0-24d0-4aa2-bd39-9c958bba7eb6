import { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { createPortal } from 'react-dom';
import '../styles/AIProjectPage.css';
import { PRESET_SEARCH_PARAMS } from '../services/siteSelectionService';

const AIProjectPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // Current project data loaded from API
  const [project, setProject] = useState(null);
  
  // User's preferred language (stored in localStorage)
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  
  // Form input fields for structured data entry
  const [inputText, setInputText] = useState({
    locations: '',
    transportation: '',
    budget: '',
    industries: '',
    size: ''
  });
  
  // Loading state during AI analysis
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  // Results from AI analysis
  const [analysisResult, setAnalysisResult] = useState(null);
  
  // Error message if analysis fails
  const [error, setError] = useState(null);
  
  // Controls language dropdown visibility
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  
  // Controls results modal visibility
  const [showResults, setShowResults] = useState(false);
  
  // Toggles between form input and email analysis tabs
  const [showForm, setShowForm] = useState(true);
  
  // Raw text input for email analysis mode
  const [singleInput, setSingleInput] = useState('');

  // Reference for language dropdown to handle click outside
  const dropdownRef = useRef(null);
  
  // AbortController for canceling ongoing analysis
  const abortControllerRef = useRef(null);

  // Available languages with flags and names
  const supportedLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' }
  ];

  // Translation strings for both English and Chinese
  const translations = {
    en: {
      backToProjects: 'Back to Projects',
      projectNotFound: 'Project not found',
      inputPlaceholder: 'Enter information about the industrial park you want to analyze...\n\nFor example:\n- Location details (city, state, country)\n- Transportation access (highways, ports, airports)\n- Available budget and infrastructure\n- Target industries or business requirements\n- Size and zoning information',
      analyzeButton: 'Analyze with AI',
      analyzing: 'Analyzing...',
      analysisResults: 'Analysis Results',
      analysisSummaryTitle: 'Analysis Summary',
      extractedInfo: 'Extracted Information',
      cities: 'Cities',
      countries: 'Countries',
      highways: 'Highways',
      industries: 'Industries',
      states_provinces: 'States/Provinces',
      facility_type: 'Facility Type',
      specific_requirements: 'Specific Requirements',
      airports: 'Airports',
      ports: 'Ports',
      property_info: 'Property Info',
      other: 'Other Keywords',
      noResults: 'No analysis results yet',
      error: 'Error',
      tryAgain: 'Try Again',
      clearResults: 'Clear Results',
      selectLanguage: 'Select Language',
      focusResults: 'Focus on Results',
      showBoth: 'Show Both Panels',
      editResults: 'Click to edit',
      addItem: 'Add Item',
      deleteItem: 'Delete',
      saveChanges: 'Save Changes',
      exportResults: 'Export Results',
      siteSelection: 'Site Selection',
      siteSelectionTitle: 'Industrial Site Selection',
      siteSelectionSubtitle: 'Find optimal industrial locations based on your requirements',
      runProspecting: 'Run Site Prospecting',
      prospectingResults: 'Prospecting Results',
      financialAnalysis: 'Financial Analysis',
      loading: 'Loading...',
      noSiteResults: 'No site selection results yet',
      parcelId: 'Parcel ID',
      totalCost: 'Total Cost',
      area: 'Area',
      location: 'Location',
      costBreakdown: 'Cost Breakdown',
      laborCost: 'Labor Cost',
      utilityCost: 'Utility Cost',
      transportCost: 'Transportation Cost',
      landCost: 'Land Cost',
      selectParcel: 'Select for Analysis',
      viewToggle: 'View',
      listView: 'List View',
      mapView: 'Map View',
      showOnMap: 'Show on Map',
      selectedParcel: 'Selected Parcel',
      irr: 'IRR',
      npv: 'NPV',
      cashOnCash: 'Cash-on-Cash',
      performanceRating: 'Performance Rating',
      locations: 'Locations',
      locationsPlaceholder: 'Enter location details (city, state, country)...',
      transportation: 'Transportation Access',
      transportationPlaceholder: 'Enter transportation details (highways, ports, airports)...',
      budget: 'Budget',
      budgetPlaceholder: 'Enter your budget or financial constraints (e.g., $500,000 total, $50,000-$200,000 range)...',
      industries: 'Target Industries',
      industriesPlaceholder: 'Enter target industries or business requirements...',
      size: 'Size & Zoning',
      sizePlaceholder: 'Enter size and zoning information...',
      analysisInput: 'Analysis Input',
      analysisInputDesc: 'Describe your industrial requirements'
    },
    zh: {
      backToProjects: '返回项目列表',
      projectNotFound: '项目未找到',
      inputPlaceholder: '请输入您想要分析的工业园区信息...\n\n例如：\n- 位置详情（城市、州/省、国家）\n- 交通便利性（高速公路、港口、机场）\n- 可用设施和基础设施\n- 目标行业或业务需求\n- 规模和分区信息',
      analyzeButton: 'AI分析',
      analyzing: '分析中...',
      analysisResults: '分析结果',
      analysisSummaryTitle: '分析摘要',
      extractedInfo: '提取的信息',
      cities: '城市',
      countries: '国家',
      highways: '高速公路',
      industries: '行业',
      states_provinces: '州/省',
      facility_type: '设施类型',
      specific_requirements: '具体要求',
      airports: '机场',
      ports: '港口',
      property_info: '属性信息',
      other: '其他关键词',
      noResults: '暂无分析结果',
      error: '错误',
      tryAgain: '重试',
      clearResults: '清除结果',
      selectLanguage: '选择语言',
      focusResults: '专注结果',
      showBoth: '显示双面板',
      editResults: '点击编辑',
      addItem: '添加项目',
      deleteItem: '删除',
      saveChanges: '保存更改',
      exportResults: '导出结果',
      siteSelection: '站点选择',
      siteSelectionTitle: '工业站点选择',
      siteSelectionSubtitle: '根据您的需求找到最佳工业位置',
      runProspecting: '运行站点勘探',
      prospectingResults: '勘探结果',
      financialAnalysis: '财务分析',
      loading: '加载中...',
      noSiteResults: '暂无站点选择结果',
      parcelId: '地块ID',
      totalCost: '总成本',
      area: '面积',
      location: '位置',
      costBreakdown: '成本明细',
      laborCost: '劳动力成本',
      utilityCost: '公用事业成本',
      transportCost: '运输成本',
      landCost: '土地成本',
      selectParcel: '选择进行分析',
      viewToggle: '视图',
      listView: '列表视图',
      mapView: '地图视图',
      showOnMap: '在地图上显示',
      selectedParcel: '已选择地块',
      irr: '内部收益率',
      npv: '净现值',
      cashOnCash: '现金回报率',
      performanceRating: '性能评级',
      locations: '位置',
      locationsPlaceholder: '输入位置详情（城市、州/省、国家）...',
      transportation: '交通便利性',
      transportationPlaceholder: '输入交通详情（高速公路、港口、机场）...',
      budget: '预算',
      budgetPlaceholder: '输入预算或财务约束...',
      industries: '目标行业',
      industriesPlaceholder: '输入目标行业或业务需求...',
      size: '规模和分区',
      sizePlaceholder: '输入规模和分区信息...',
      analysisInput: '分析输入',
      analysisInputDesc: '描述您的工业需求'
    }
  };

  // Current translation object based on selected language
  const t = translations[language] || translations.en;

  // Helper function to get current language object with flag and name
  const getCurrentLanguage = () => {
    return supportedLanguages.find(lang => lang.code === language) || supportedLanguages[0];
  };

  // Add/remove CSS classes for page styling
  useEffect(() => {
    document.documentElement.classList.add('ai-project-active');
    document.body.classList.add('ai-project-active');
    return () => {
      document.documentElement.classList.remove('ai-project-active');
      document.body.classList.remove('ai-project-active');
    };
  }, []);

  // Cleanup effect to abort ongoing analysis when component unmounts
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Load project data from API when component mounts
  useEffect(() => {
    const loadProject = async () => {
      if (!id) return;
      try {
        const { authAPI, projectAPI } = await import('../services/api');
        if (!authAPI.isAuthenticated()) {
          navigate('/login');
          return;
        }
        const response = await projectAPI.getProject(id);
        if (response.success) {
          const formattedProject = {
            id: response.data.project.id.toString(),
            name: response.data.project.project_name,
            description: response.data.project.description || '',
            createdAt: response.data.project.created_at,
            updatedAt: response.data.project.updated_at,
            status: response.data.project.status,
            naturalLanguageInput: response.data.project.natural_language_input || '',
            structuredParameters: response.data.project.structured_parameters || '{}'
          };
          setProject(formattedProject);
        }
      } catch (error) {
        console.error('加载项目失败:', error);
        if (error.message.includes('401') || error.message.includes('403')) navigate('/login');
        else if (error.message.includes('404')) navigate('/ai-analyzer');
      }
    };
    loadProject();
  }, [id, navigate]);

  // Handle clicking outside language dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowLanguageDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Change language and save preference to localStorage
  const handleLanguageChange = useCallback((newLang) => {
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
    setShowLanguageDropdown(false);
  }, []);

  // Toggle language dropdown visibility
  const toggleLanguageDropdown = useCallback(() => {
    setShowLanguageDropdown(prev => !prev);
  }, []);

  // Handle switching to form input tab
  const switchToFormTab = useCallback(() => {
    // Cancel any ongoing analysis when switching tabs
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsAnalyzing(false);
    }
    setShowForm(true);
  }, []);

  // Handle switching to email analysis tab
  const switchToEmailTab = useCallback(() => {
    // Cancel any ongoing analysis when switching tabs
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsAnalyzing(false);
    }
    setShowForm(false);
  }, []);

  // Main AI analysis function - processes form data or email text
  const analyzeWithAI = useCallback(async (text = null) => {
    try {
      // Cancel any ongoing analysis
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Create new AbortController for this analysis
      abortControllerRef.current = new AbortController();
      
      const combinedText = text || (showForm ? Object.entries(inputText)
        .filter(([_, value]) => value.trim())
        .map(([key, value]) => {
          const label = key.charAt(0).toUpperCase() + key.slice(1);
          return `${label}:\n${value.trim()}`;
        })
        .join('\n\n') : singleInput.trim());

      if (!combinedText.trim()) return;

      setIsAnalyzing(true);
      setError(null);

      const prompt = `
# ROLE AND GOAL
You are a Principal Analyst at a top-tier global industrial real estate and supply chain consultancy. Your client (the user) has provided a query for a potential site or facility. Your goal is to function as an expert system that performs a rigorous, multi-layered analysis of the user's input, delivering a structured, professional, and insightful data object.

# PRIMARY DIRECTIVES
1. Analyze the input text and extract relevant information
2. Structure the data according to the schema
3. Ensure all extracted information is accurate and relevant
4. Maintain consistency in language and formatting

# JSON OUTPUT SCHEMA
{
  "analysis_summary": string,
  "location": {
    "text_mention": string,
    "cities": string[],
    "state_province": string[] | null,
    "country": string[] | null,
    "proximity_notes": string[]
  },
  "facility": {
    "type": string[],
    "explicit_requirements": string[],
    "inferred_requirements": string[],
    "quantitative_info": string[]
  },
  "infrastructure": {
    "highways": string[],
    "airports": string[],
    "ports": string[]
  },
  "industry_tags": string[]
}

# ANALYZE THE FOLLOWING USER TEXT:
${combinedText}

# JSON OUTPUT:`;

      const models = [
        'tngtech/deepseek-r1t-chimera:free',
        'mistralai/mistral-7b-instruct:free',
        'anthropic/claude-3-opus:beta',
        'google/gemini-pro:beta'
      ];

      let lastError = null;
      
      for (const model of models) {
        try {
          // Check if analysis was cancelled
          if (abortControllerRef.current.signal.aborted) {
            console.log('Analysis cancelled by user');
            return;
          }
          
          const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${"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"}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              'model': model,
              'messages': [
                {
                  'role': 'user',
                  'content': prompt
                }
              ]
            }),
            signal: abortControllerRef.current.signal
          });

          if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          const aiResponse = data.choices[0].message.content;

          try {
            const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const extractedData = JSON.parse(jsonMatch[0]);
              console.log('AI Extracted Data:', extractedData);

              const displayData = {
                summary: extractedData.analysis_summary || '',
                cities: extractedData.location?.cities || [],
                states_provinces: extractedData.location?.state_province || [],
                countries: extractedData.location?.country || [],
                highways: extractedData.infrastructure?.highways || [],
                ports: extractedData.infrastructure?.ports || [],
                airports: extractedData.infrastructure?.airports || [],
                facility_type: extractedData.facility?.type || [],
                specific_requirements: [
                  ...(extractedData.facility?.explicit_requirements || []),
                  ...(extractedData.facility?.inferred_requirements || [])
                ],
                property_info: extractedData.facility?.quantitative_info || [],
                industries: extractedData.industry_tags || [],
                other: extractedData.location?.proximity_notes || []
              };
              
              setAnalysisResult(displayData);
              setShowResults(true);
              return;
            } else {
              throw new Error('No valid JSON found in AI response');
            }
          } catch (parseError) {
            console.error(`JSON parsing error with model ${model}:`, parseError);
            lastError = parseError;
            continue;
          }
        } catch (err) {
          // Check if this is an abort error
          if (err.name === 'AbortError') {
            console.log('Analysis cancelled by user');
            return;
          }
          console.error(`Error with model ${model}:`, err);
          lastError = err;
          continue;
        }
      }

      console.error('All models failed:', lastError);
      setError('Failed to get valid response from any AI model. Please try again.');
      setAnalysisResult({ other: ['Analysis failed. Please try again.'] });
      setShowResults(true);
    } catch (error) {
      // Check if this is an abort error
      if (error.name === 'AbortError') {
        console.log('Analysis cancelled by user');
        return;
      }
      console.error('Analysis error:', error);
      setError('An unexpected error occurred. Please try again.');
      setAnalysisResult({ other: ['Analysis failed. Please try again.'] });
      setShowResults(true);
    } finally {
      setIsAnalyzing(false);
    }
  }, [showForm, inputText, singleInput]);

  // Control body scroll when results modal is open
  useEffect(() => {
    if (showResults) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showResults]);

  // Reset all form data and results
  const clearResults = useCallback(() => {
    setAnalysisResult(null);
    setError(null);
    setShowResults(false);
    setInputText({
      locations: '',
      transportation: '',
      budget: '',
      industries: '',
      size: ''
    });
    setSingleInput('');
  }, []);

  // Add new item to a specific analysis category
  const addItemToCategory = useCallback((category) => {
    const newItem = prompt(language === 'en' ? 'Enter new item:' : '输入新项目:');
    if (newItem && newItem.trim()) {
      setAnalysisResult(prev => ({
        ...prev,
        [category]: [...(prev[category] || []), newItem.trim()]
      }));
    }
  }, [language]);

  // Remove item from a specific analysis category
  const deleteItem = useCallback((category, index) => {
    setAnalysisResult(prev => ({
      ...prev,
      [category]: prev[category].filter((_, i) => i !== index)
    }));
  }, []);

  // Export analysis results as JSON file
  const exportResults = useCallback(() => {
    if (!analysisResult) return;
    const exportData = {
      projectName: project?.name || 'AI Analysis',
      timestamp: new Date().toISOString(),
      inputText: Object.entries(inputText).map(([key, value]) => ({
        key,
        value: value.trim()
      })).join('\n'),
      results: analysisResult
    };
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-analysis-${project?.name.replace(/\s/g, '_') || 'export'}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [analysisResult, project, inputText]);

  // Navigate to site selection page with analysis results
  const handleSiteSelection = useCallback(() => {
    if (!analysisResult) {
      setError('请先完成AI分析');
      return;
    }

    navigate('/site-selection', {
      state: {
        analysisResult,
        projectInfo: {
          id: id,
          name: project?.name || `Project ${id}`,
          description: project?.description || ''
        }
      }
    });
  }, [analysisResult, navigate, id, project]);

  if (!project) {
    return (
      <div className="ai-project-page">
        <div className="ai-background"><div className="grid-pattern"></div></div>
        <div className="error-container">
          <h2>{t.projectNotFound}</h2>
          <button className="back-button" onClick={() => navigate('/ai-analyzer')}>{t.backToProjects}</button>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-project-page">
      <div className="ai-background">
        <div className="tech-grid"></div>
      </div>
      <header className="ai-header">
        <div className="header-container">
          <div className="header-left">
            <button className="back-button" onClick={() => navigate('/ai-analyzer')}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              <span>{t.backToProjects}</span>
            </button>
          </div>
          <div className="header-center">
            <h1 className="project-title">{project.name}</h1>
            <div className="project-status">
              <div className="status-indicator"></div>
              <span>{language === 'en' ? 'Active Analysis' : '活跃分析'}</span>
            </div>
          </div>
          <div className="header-right">
            <div className="language-selector" ref={dropdownRef}>
              <button className="language-trigger" onClick={toggleLanguageDropdown}>
                <span className="language-flag">{getCurrentLanguage().flag}</span>
                <span className="language-name">{getCurrentLanguage().name}</span>
                <svg className={`dropdown-icon ${showLanguageDropdown ? 'open' : ''}`} width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
              {showLanguageDropdown && (
                <div className="language-menu">
                  {supportedLanguages.map((lang) => (
                    <button key={lang.code} className={`language-option ${language === lang.code ? 'active' : ''}`} onClick={() => handleLanguageChange(lang.code)}>
                      <span className="language-flag">{lang.flag}</span>
                      <span className="language-name">{lang.name}</span>
                      {language === lang.code && (
                        <svg className="check-icon" width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M20 6L9 17l-5-5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </header>
      <main className="ai-main">
        <div className="ai-container">
          <div className="content-layout">
            <div className="tab-container">
              <button 
                className={`tab-item ${showForm ? 'active' : ''}`} 
                onClick={switchToFormTab}
              >
                <svg className="tab-icon" width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <polyline points="10,9 9,9 8,9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="tab-label">{language === 'en' ? 'Form Input' : '表单输入'}</span>
              </button>
              <button 
                className={`tab-item ${!showForm ? 'active' : ''}`} 
                onClick={switchToEmailTab}
              >
                <svg className="tab-icon" width="18" height="18" viewBox="0 0 24 24" fill="none">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                <span className="tab-label">{language === 'en' ? 'Email Analysis' : '邮件分析'}</span>
              </button>
            </div>
            <div className="input-panel">
              <div className="panel-header">
                <div className="header-info">
                  <h2>{t.analysisInput}</h2>
                  <p>{t.analysisInputDesc}</p>
                </div>
              </div>
              <div className="input-content">
                {showForm ? (
                  <form className="analysis-form">
                    <div className="form-section">
                      <label htmlFor="locations">{t.locations}</label>
                      <input
                        type="text"
                        id="locations"
                        className="input-field"
                        value={inputText.locations || ''}
                        onChange={(e) => setInputText(prev => ({...prev, locations: e.target.value}))}
                        placeholder={t.locationsPlaceholder}
                      />
                    </div>
                    <div className="form-section">
                      <label htmlFor="transportation">{t.transportation}</label>
                      <input
                        type="text"
                        id="transportation"
                        className="input-field"
                        value={inputText.transportation || ''}
                        onChange={(e) => setInputText(prev => ({...prev, transportation: e.target.value}))}
                        placeholder={t.transportationPlaceholder}
                      />
                    </div>
                    <div className="form-section">
                      <label htmlFor="budget">{t.budget}</label>
                      <input
                        type="text"
                        id="budget"
                        className="input-field"
                        value={inputText.budget || ''}
                        onChange={(e) => setInputText(prev => ({...prev, budget: e.target.value}))}
                        placeholder={t.budgetPlaceholder}
                      />
                    </div>
                    <div className="form-section">
                      <label htmlFor="industries">{t.industries}</label>
                      <input
                        type="text"
                        id="industries"
                        className="input-field"
                        value={inputText.industries || ''}
                        onChange={(e) => setInputText(prev => ({...prev, industries: e.target.value}))}
                        placeholder={t.industriesPlaceholder}
                      />
                    </div>
                    <div className="form-section">
                      <label htmlFor="size">{t.size}</label>
                      <input
                        type="text"
                        id="size"
                        className="input-field"
                        value={inputText.size || ''}
                        onChange={(e) => setInputText(prev => ({...prev, size: e.target.value}))}
                        placeholder={t.sizePlaceholder}
                      />
                    </div>
                  </form>
                ) : (
                  <div className="email-input-container">
                    <div className="email-input-header">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span className="email-input-title">{language === 'en' ? 'Email Content Analysis' : '邮件内容分析'}</span>
                    </div>
                    <textarea
                      className="input-textarea email-textarea"
                      value={singleInput}
                      onChange={e => setSingleInput(e.target.value)}
                      placeholder={language === 'en' 
                        ? "📧 Paste your email content here...\n\nFor example:\n• Client requirements and specifications\n• Location preferences and constraints\n• Budget information and timelines\n• Industry-specific requirements\n• Any additional context or notes\n\nSimply copy and paste the relevant email content, and our AI will extract and analyze all the key information automatically."
                        : "📧 在此粘贴您的邮件内容...\n\n例如：\n• 客户需求和规格\n• 位置偏好和限制\n• 预算信息和时间表\n• 行业特定要求\n• 任何其他上下文或备注\n\n只需复制并粘贴相关邮件内容，我们的AI将自动提取和分析所有关键信息。"
                      }
                      style={{ minHeight: 200, width: '100%' }}
                    />
                    <div className="email-input-tips">
                      <div className="tip-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                          <path d="M12 16v-4M12 8h.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <span>{language === 'en' ? 'AI will automatically extract location, budget, and requirements' : 'AI将自动提取位置、预算和要求'}</span>
                      </div>
                      <div className="tip-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                        <span>{language === 'en' ? 'No need to format or structure the content' : '无需格式化或结构化内容'}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="input-actions">
                <button
                  className="analyze-button primary"
                  onClick={() => showForm ? analyzeWithAI() : analyzeWithAI(singleInput)}
                  disabled={showForm ? !Object.entries(inputText).some(([_, value]) => value.trim()) || isAnalyzing : !singleInput.trim() || isAnalyzing}
                >
                  {isAnalyzing ? (
                    <>
                      <div className="loading-spinner"></div>
                      <span>{t.analyzing}</span>
                    </>
                  ) : (
                    <>
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span>{t.analyzeButton}</span>
                    </>
                  )}
                </button>
                <button className="clear-button secondary" onClick={clearResults}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  <span>{t.clearResults}</span>
                </button>
              </div>
            </div>
          </div>
          {showResults && createPortal(
            <>
              <div className="results-backdrop" onClick={() => setShowResults(false)} />
              <div className="results-panel">
                <div className="panel-header">
                  <div className="header-info">
                    <h2>{t.analysisResults}</h2>
                    <p>{language === 'en' ? 'AI-powered analysis results' : 'AI智能分析结果'}</p>
                  </div>
                  <button className="close-results" onClick={() => setShowResults(false)}>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                      <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
                <div className="results-content">
                  {error && (
                    <div className="error-state">
                      <div className="error-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                          <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
                          <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                      </div>
                      <div className="error-details">
                        <h4>{t.error}</h4>
                        <p>{error}</p>
                        <button className="retry-button" onClick={() => showForm ? analyzeWithAI() : analyzeWithAI(singleInput)}>
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M23 4v6h-6M1 20v-6h6M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4a9 9 0 0 1-14.85 4.36L3 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                          {t.tryAgain}
                        </button>
                      </div>
                    </div>
                  )}
                  {!analysisResult && !error && (
                    <div className="empty-state">
                      <div className="empty-icon">
                        <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" strokeDasharray="4 4" opacity="0.3"/>
                          <path d="M12 6v6l4 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" opacity="0.5"/>
                        </svg>
                      </div>
                      <h3>{t.noResults}</h3>
                      <p>{language === 'en' ? 'Enter your requirements and click analyze to get started' : '输入您的需求并点击分析开始'}</p>
                    </div>
                  )}
                  {analysisResult && (
                    <div className="analysis-results">
                      {analysisResult.summary && (
                        <div className="summary-section">
                          <h4>{t.analysisSummaryTitle}</h4>
                          <p className="summary-text">{analysisResult.summary}</p>
                        </div>
                      )}
                      <div className="results-header">
                        <h3>{t.extractedInfo}</h3>
                        <div className="results-actions">
                          <button className="export-button" onClick={exportResults} title={t.exportResults}>
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </button>
                          <span className="edit-hint">{t.editResults}</span>
                        </div>
                      </div>
                      <div className="results-grid">
                        {Object.entries(analysisResult).map(([category, items]) => (
                          (items && items.length > 0 && category !== 'summary') && (
                          <div key={category} className="result-category">
                            <div className="category-header">
                              <div className="category-info">
                                <h4>{t[category] || category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                                <span className="item-count">{items?.length || 0}</span>
                              </div>
                              <button className="add-item-button" onClick={() => addItemToCategory(category)} title={t.addItem}>
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                  <line x1="12" y1="5" x2="12" y2="19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </button>
                            </div>
                            <div className="result-items">
                              {items.map((item, index) => (
                                <div key={index} className="result-item">
                                  <span className="result-tag" contentEditable suppressContentEditableWarning={true} onBlur={(e) => {
                                    const newValue = e.target.textContent;
                                    if (newValue !== item && newValue.trim()) {
                                      const newItems = [...items]; newItems[index] = newValue.trim();
                                      setAnalysisResult(prev => ({...prev, [category]: newItems}));
                                    } else if (!newValue.trim()) { e.target.textContent = item; }
                                  }} onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); e.target.blur(); } }}>{item}</span>
                                  <button className="delete-item-button" onClick={() => deleteItem(category, index)} title={t.deleteItem}>
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                                      <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                      <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                  </button>
                                </div>
                              ))}
                            </div>
                          </div>
                          )
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                {analysisResult && (
                  <div className="site-selection-section">
                    <button className="site-selection-button" onClick={handleSiteSelection}>
                      <div className="button-content">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <span>{t.siteSelection}</span>
                      </div>
                    </button>
                  </div>
                )}
              </div>
            </>,
            document.body
          )}
        </div>
      </main>
    </div>
  );
};

export default AIProjectPage;
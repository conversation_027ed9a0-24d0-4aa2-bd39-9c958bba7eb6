import React, { useState, useEffect } from 'react';
import { systemAPI, authAPI } from '../services/api';

const TestPage = () => {
  const [healthStatus, setHealthStatus] = useState(null);
  const [dbStatus, setDbStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [testResults, setTestResults] = useState({});

  useEffect(() => {
    runTests();
  }, []);

  const runTests = async () => {
    setLoading(true);
    const results = {};

    try {
      // 测试健康检查
      console.log('Testing health check...');
      const health = await systemAPI.healthCheck();
      results.health = { success: true, data: health };
      setHealthStatus(health);
    } catch (error) {
      console.error('Health check failed:', error);
      results.health = { success: false, error: error.message };
    }

    try {
      // 测试数据库状态
      console.log('Testing database status...');
      const db = await systemAPI.databaseStatus();
      results.database = { success: true, data: db };
      setDbStatus(db);
    } catch (error) {
      console.error('Database status check failed:', error);
      results.database = { success: false, error: error.message };
    }

    try {
      // 测试认证状态
      console.log('Testing auth status...');
      const isAuth = authAPI.isAuthenticated();
      const user = authAPI.getUser();
      results.auth = { 
        success: true, 
        data: { 
          isAuthenticated: isAuth, 
          user: user 
        } 
      };
    } catch (error) {
      console.error('Auth status check failed:', error);
      results.auth = { success: false, error: error.message };
    }

    setTestResults(results);
    setLoading(false);
  };

  const testLogin = async () => {
    try {
      const response = await authAPI.login({
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('Login test result:', response);
      alert('Login test completed. Check console for details.');
    } catch (error) {
      console.error('Login test failed:', error);
      alert(`Login test failed: ${error.message}`);
    }
  };

  const testRegister = async () => {
    try {
      const response = await authAPI.register({
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('Register test result:', response);
      alert('Register test completed. Check console for details.');
    } catch (error) {
      console.error('Register test failed:', error);
      alert(`Register test failed: ${error.message}`);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Running System Tests...</h1>
        <div>Loading...</div>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      background: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <h1>System Test Dashboard</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={runTests} style={{ 
          padding: '10px 20px', 
          marginRight: '10px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}>
          Refresh Tests
        </button>
        
        <button onClick={testLogin} style={{ 
          padding: '10px 20px', 
          marginRight: '10px',
          background: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}>
          Test Login
        </button>
        
        <button onClick={testRegister} style={{ 
          padding: '10px 20px', 
          background: '#ffc107',
          color: 'black',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer'
        }}>
          Test Register
        </button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        {/* Health Status */}
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2>Health Check</h2>
          {testResults.health?.success ? (
            <div>
              <div style={{ color: 'green', marginBottom: '10px' }}>✅ Success</div>
              <pre style={{ background: '#f8f9fa', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(testResults.health.data, null, 2)}
              </pre>
            </div>
          ) : (
            <div>
              <div style={{ color: 'red', marginBottom: '10px' }}>❌ Failed</div>
              <div style={{ color: 'red' }}>{testResults.health?.error}</div>
            </div>
          )}
        </div>

        {/* Database Status */}
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2>Database Status</h2>
          {testResults.database?.success ? (
            <div>
              <div style={{ color: 'green', marginBottom: '10px' }}>✅ Success</div>
              <pre style={{ background: '#f8f9fa', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(testResults.database.data, null, 2)}
              </pre>
            </div>
          ) : (
            <div>
              <div style={{ color: 'red', marginBottom: '10px' }}>❌ Failed</div>
              <div style={{ color: 'red' }}>{testResults.database?.error}</div>
            </div>
          )}
        </div>

        {/* Auth Status */}
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2>Authentication Status</h2>
          {testResults.auth?.success ? (
            <div>
              <div style={{ color: 'green', marginBottom: '10px' }}>✅ Success</div>
              <pre style={{ background: '#f8f9fa', padding: '10px', borderRadius: '4px' }}>
                {JSON.stringify(testResults.auth.data, null, 2)}
              </pre>
            </div>
          ) : (
            <div>
              <div style={{ color: 'red', marginBottom: '10px' }}>❌ Failed</div>
              <div style={{ color: 'red' }}>{testResults.auth?.error}</div>
            </div>
          )}
        </div>

        {/* System Info */}
        <div style={{ 
          background: 'white', 
          padding: '20px', 
          borderRadius: '8px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          <h2>System Information</h2>
          <div>
            <p><strong>Frontend URL:</strong> {window.location.origin}</p>
                                <p><strong>Backend URL:</strong> {import.meta.env.VITE_API_BASE_URL || 'http://18.191.68.97:3001'}</p>
            <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            <p><strong>Local Storage:</strong></p>
            <pre style={{ background: '#f8f9fa', padding: '10px', borderRadius: '4px', fontSize: '12px' }}>
              {JSON.stringify({
                authToken: localStorage.getItem('authToken') ? 'Present' : 'Not found',
                currentUser: localStorage.getItem('currentUser') ? 'Present' : 'Not found',
                preferredLanguage: localStorage.getItem('preferredLanguage') || 'Not set'
              }, null, 2)}
            </pre>
          </div>
        </div>
      </div>

      {error && (
        <div style={{ 
          marginTop: '20px',
          padding: '15px',
          background: '#f8d7da',
          color: '#721c24',
          borderRadius: '5px',
          border: '1px solid #f5c6cb'
        }}>
          <strong>Error:</strong> {error}
        </div>
      )}
    </div>
  );
};

export default TestPage;

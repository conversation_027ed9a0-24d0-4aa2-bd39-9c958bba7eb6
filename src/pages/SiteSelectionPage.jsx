/**
 * � Professional Site Selection Interface
 * 专业工业站点选择界面 - 现代化设计系统
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { runSiteProspecting, runFinancialAnalysis, formatFinancialValue } from '../services/siteSelectionService';
import SiteSelectionMap from '../components/site-selection/SiteSelectionMap';
import CostBreakdownChart from '../components/site-selection/CostBreakdownChart';
import BackButton from '../components/common/BackButton';
import '../styles/SiteSelectionPage.css';

const SiteSelectionPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // 从路由状态获取分析结果和项目信息
  const analysisResult = location.state?.analysisResult;
  const projectInfo = location.state?.projectInfo;
  
  // === 核心状态管理 ===
  const [siteResults, setSiteResults] = useState(null);
  const [selectedParcel, setSelectedParcel] = useState(null);
  const [financialAnalysis, setFinancialAnalysis] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // === 界面状态管理 ===
  const [viewMode, setViewMode] = useState('dashboard'); // 'dashboard', 'map-focus'
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [mapStyle, setMapStyle] = useState('satellite'); // 'satellite', 'street', 'terrain'

  // === 搜索参数状态 ===
  const [searchParams, setSearchParams] = useState({
    // 地理位置参数
    searchArea: analysisResult?.cities?.[0] || 'Dallas, TX',
    portHub: analysisResult?.ports?.[0] || 'Houston, TX',

    // 站点规模参数
    minArea: 50000,
    maxArea: 200000,

    // 运营参数
    workers: 100,
    annualKwh: 500000,

    // 分析参数
    maxCandidates: 10
  });

  // === 多语言支持 ===
  const [language] = useState(localStorage.getItem('preferredLanguage') || 'en');

  const translations = {
    en: {
      // 页面标题
      title: 'Industrial Site Selection',
      subtitle: 'Advanced site prospecting and financial analysis',
      backToProject: 'Back to Project',

      // 搜索面板
      searchCriteria: 'Search Criteria',
      geographicSettings: 'Geographic Settings',
      siteParameters: 'Site Parameters',
      operationalSettings: 'Operational Settings',
      searchArea: 'Target Region',
      portHub: 'Logistics Hub',
      areaRange: 'Site Area Range',
      minArea: 'Minimum Area',
      maxArea: 'Maximum Area',
      workers: 'Workforce Size',
      annualEnergy: 'Annual Energy',
      maxCandidates: 'Max Candidates',

      // 操作按钮
      runAnalysis: 'Find Sites',
      analyzing: 'Searching...',
      showAdvanced: 'Advanced Settings',
      hideAdvanced: 'Hide Advanced',

      // 结果显示
      results: 'Site Candidates',
      sitesFound: 'sites found',
      selectedSite: 'Selected Site',
      geographicDistribution: 'Geographic Distribution',

      // 财务分析
      financialAnalysis: 'Financial Analysis',
      keyMetrics: 'Key Metrics',
      totalInvestment: 'Total Investment',
      siteArea: 'Site Area',
      costBreakdown: 'Cost Breakdown',
      investmentAnalysis: 'Investment Analysis',

      // 成本类别
      laborCost: 'Labor',
      landCost: 'Land',
      transportCost: 'Transport',
      utilityCost: 'Utilities',

      // 财务指标
      irr: 'IRR',
      npv: 'NPV',
      cashReturn: 'Cash Return',

      // 视图模式
      dashboardView: 'Dashboard',
      mapView: 'Map View',

      // 状态信息
      selectSite: 'Select Site',
      selectSiteDescription: 'Choose a site from the map to view detailed financial analysis',

      // 投资建议
      investmentRecommendation: 'Investment Recommendation',
      cautionRequired: 'Caution Required',
      goodOpportunity: 'Good Opportunity',
      negativeReturnWarning: 'Current cash return is negative, recommend reassessing cost structure',
      positiveReturnMessage: 'Based on financial analysis, this site shows good investment potential'
    },
    zh: {
      // 页面标题
      title: '工业站点选择',
      subtitle: '高级站点勘探和财务分析',
      backToProject: '返回项目',

      // 搜索面板
      searchCriteria: '搜索条件',
      geographicSettings: '地理位置设置',
      siteParameters: '站点参数',
      operationalSettings: '运营设置',
      searchArea: '搜索区域',
      portHub: '物流枢纽',
      areaRange: '站点面积范围',
      minArea: '最小面积',
      maxArea: '最大面积',
      workers: '员工规模',
      annualEnergy: '年度用电量',
      maxCandidates: '最大候选数',

      // 操作按钮
      runAnalysis: '开始搜索',
      analyzing: '搜索中...',
      showAdvanced: '高级设置',
      hideAdvanced: '收起设置',

      // 结果显示
      results: '候选站点',
      sitesFound: '个站点',
      selectedSite: '已选择站点',
      geographicDistribution: '地理分布',

      // 财务分析
      financialAnalysis: '财务分析',
      keyMetrics: '关键指标',
      totalInvestment: '总投资',
      siteArea: '站点面积',
      costBreakdown: '成本分解',
      investmentAnalysis: '投资回报分析',

      // 成本类别
      laborCost: '劳动力',
      landCost: '土地',
      transportCost: '运输',
      utilityCost: '公用事业',

      // 财务指标
      irr: '内部收益率',
      npv: '净现值',
      cashReturn: '现金回报',

      // 视图模式
      dashboardView: '仪表盘',
      mapView: '地图视图',

      // 状态信息
      selectSite: '选择站点',
      selectSiteDescription: '从地图中选择一个站点来查看详细的财务分析',

      // 投资建议
      investmentRecommendation: '投资建议',
      cautionRequired: '需要谨慎评估',
      goodOpportunity: '投资机会良好',
      negativeReturnWarning: '当前现金回报率为负值，建议重新评估成本结构',
      positiveReturnMessage: '基于财务指标分析，此站点具有良好的投资回报潜力'
    }
  };

  const t = translations[language] || translations.en;

  // ================= 财务成本分解数据 =================
  const costBreakdown = useMemo(() => {
    if (!selectedParcel) return [];

    const costObj = {
      labor: selectedParcel.metadata?.labor_cost ?? 0,
      land: selectedParcel.metadata?.land_cost ?? 0,
      transport: selectedParcel.metadata?.transportation_cost ?? 0,
      utility: selectedParcel.metadata?.utility_cost ?? 0,
    };

    const total = selectedParcel.total_cost ?? Object.values(costObj).reduce((a, b) => a + b, 0);

    const mapping = [
      { key: 'labor', label: t.laborCost || '劳动力', icon: '👷', color: '#3b82f6' },
      { key: 'land', label: t.landCost || '土地', icon: '🏭', color: '#ec4899' },
      { key: 'transport', label: t.transportCost || '运输', icon: '🚛', color: '#f59e0b' },
      { key: 'utility', label: t.utilityCost || '公用事业', icon: '⚡', color: '#10b981' },
    ];

    return mapping.map((m) => {
      const value = costObj[m.key];
      return { ...m, value, percentage: total ? ((value / total) * 100).toFixed(1) : 0 };
    });
  }, [selectedParcel, t]);

  // 如果没有分析结果，重定向回项目页面
  useEffect(() => {
    if (!analysisResult) {
      navigate(-1);
    }
  }, [analysisResult, navigate]);

  // 运行站点分析
  const handleRunAnalysis = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await runSiteProspecting({
        searchArea: {
          type: "city",
          value: searchParams.searchArea
        },
        parcelFilters: {
          min_area_sqft: searchParams.minArea,
          max_area_sqft: searchParams.maxArea,
          zoning: "industrial",
          max_distance_to_port_miles: 100
        },
        userConstraints: {
          workers: searchParams.workers,
          annual_kwh: searchParams.annualKwh,
          port_hub: searchParams.portHub
        },
        maxCandidates: searchParams.maxCandidates
      });

      if (result.success) {
        setSiteResults(result.data);
        if (result.data?.parcels?.length > 0) {
          const firstParcel = result.data.parcels[0];
          setSelectedParcel(firstParcel);
          // 自动运行第一个地块的财务分析
          try {
            const financialResult = await runFinancialAnalysis({
              parcel: firstParcel,
              financialParams: {}
            });
            if (financialResult.success) {
              setFinancialAnalysis(financialResult.data);
            }
          } catch (financialError) {
            console.error('Initial financial analysis error:', financialError);
          }
        } else {
          setSelectedParcel(null);
          setFinancialAnalysis(null);
        }
      } else {
        throw new Error(result.message || 'Site analysis failed');
      }
    } catch (error) {
      console.error('Site analysis error:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [searchParams]);

  // 选择地块进行财务分析
  const handleParcelSelection = useCallback(async (parcel) => {
    setSelectedParcel(parcel);
    setIsLoading(true);

    try {
      const result = await runFinancialAnalysis({
        parcel,
        financialParams: {}
      });

      if (result.success) {
        setFinancialAnalysis(result.data);
      } else {
        throw new Error(result.message || 'Financial analysis failed');
      }
    } catch (error) {
      console.error('Financial analysis error:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 自动运行初始分析
  useEffect(() => {
    if (analysisResult && !siteResults) {
      handleRunAnalysis();
    }
  }, [analysisResult, siteResults, handleRunAnalysis]);

  if (!analysisResult) {
    return null;
  }

  // 删除了复杂的液体扭曲滤镜，使用纯CSS实现液态玻璃效果

  return (
    <div className="liquid-glass-site-selection">
      {/* 悬浮式液体玻璃导航系统 */}
      <nav className="liquid-floating-nav">
        {/* 左侧悬浮返回按钮 */}
        <div className="liquid-nav-section left">
          <BackButton text={t.backToProject} className="liquid-floating-back-button liquid-glass-button" />
        </div>

        {/* 中央悬浮标题 */}
        <div className="liquid-nav-section center">
          <div className="liquid-floating-title">
            <h1 className="liquid-title-text">{projectInfo?.name || 'Industrial Site Selection'}</h1>
          </div>
        </div>

        {/* 右侧悬浮视图切换 */}
        <div className="liquid-nav-section right">
          <div className="liquid-floating-toggle">
            <button
              className={`liquid-toggle-btn liquid-glass-button ${viewMode === 'dashboard' ? 'active' : ''}`}
              onClick={() => setViewMode('dashboard')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                <rect x="14" y="3" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                <rect x="3" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
                <rect x="14" y="14" width="7" height="7" stroke="currentColor" strokeWidth="2"/>
              </svg>
              <span>{t.dashboardView}</span>
            </button>
            <button
              className={`liquid-toggle-btn liquid-glass-button ${viewMode === 'map-focus' ? 'active' : ''}`}
              onClick={() => setViewMode('map-focus')}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" stroke="currentColor" strokeWidth="2"/>
                <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2"/>
              </svg>
              <span>{t.mapView}</span>
            </button>
          </div>
        </div>
      </nav>

      {/* 主要工作区域 */}
      <main className="liquid-main-workspace">
        {viewMode === 'dashboard' ? (
          <div className="liquid-dashboard-layout">
            {/* 左侧液体玻璃搜索面板 */}
            <aside className="liquid-search-panel glass-effect gpu-accelerated">
              {/* 液体玻璃面板头部 */}
              <div className="liquid-panel-header">
                <div className="liquid-panel-title-section">
                  <h3 className="liquid-panel-title content-layer-heading">{t.searchCriteria}</h3>
                  {siteResults && (
                    <div className="liquid-results-badge">
                      {siteResults.parcels?.length || 0} {t.sitesFound}
                    </div>
                  )}
                </div>
              </div>

              {/* 液体玻璃搜索表单 */}
              <form className="liquid-search-form" onSubmit={(e) => { e.preventDefault(); handleRunAnalysis(); }}>
                {/* 地理位置设置 - 液体玻璃质感 */}
                <div className="liquid-form-section glass-effect-light">
                  <div className="liquid-section-header">
                    <div className="liquid-section-title">
                      <h4 className="content-layer-text">{t.geographicSettings}</h4>
                      <p className="content-layer-text">Define search region and logistics hub</p>
                    </div>
                  </div>

                  <div className="liquid-section-content">
                    <div className="liquid-input-group">
                      <label className="liquid-input-label content-layer-text">{t.searchArea}</label>
                      <div className="liquid-input-wrapper">
                        <input
                          type="text"
                          className="liquid-input content-layer-text"
                          value={searchParams.searchArea}
                          onChange={(e) => setSearchParams(prev => ({...prev, searchArea: e.target.value}))}
                          placeholder="e.g., Dallas, TX"
                        />
                      </div>
                    </div>

                    <div className="liquid-input-group">
                      <label className="liquid-input-label content-layer-text">{t.portHub}</label>
                      <div className="liquid-input-wrapper">
                        <input
                          type="text"
                          className="liquid-input content-layer-text"
                          value={searchParams.portHub}
                          onChange={(e) => setSearchParams(prev => ({...prev, portHub: e.target.value}))}
                          placeholder="e.g., Houston, TX"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 站点参数设置 - 液体玻璃质感 */}
                <div className="liquid-form-section glass-effect-light">
                  <div className="liquid-section-header">
                    <div className="liquid-section-title">
                      <h4 className="content-layer-text">{t.siteParameters}</h4>
                      <p className="content-layer-text">Define ideal industrial site specifications</p>
                    </div>
                  </div>

                  <div className="liquid-section-content">
                    {/* 液体玻璃面积范围显示 */}
                    <div className="liquid-range-display">
                      <div className="liquid-range-values">
                        <div className="liquid-range-value min">
                          <span className="content-layer-text">{t.minArea}</span>
                          <span className="metric-value-emphasis">{(searchParams.minArea / 1000).toFixed(0)}k sq ft</span>
                        </div>
                        <div className="liquid-range-separator">—</div>
                        <div className="liquid-range-value max">
                          <span className="content-layer-text">{t.maxArea}</span>
                          <span className="metric-value-emphasis">{(searchParams.maxArea / 1000).toFixed(0)}k sq ft</span>
                        </div>
                      </div>

                      <div className="liquid-range-inputs">
                        <div className="liquid-input-group">
                          <label className="liquid-input-label content-layer-text">{t.minArea}</label>
                          <input
                            type="number"
                            className="liquid-input content-layer-text"
                            value={searchParams.minArea}
                            onChange={(e) => setSearchParams(prev => ({...prev, minArea: parseInt(e.target.value) || 0}))}
                            min="1000"
                            max="1000000"
                            step="1000"
                          />
                        </div>
                        <div className="liquid-input-group">
                          <label className="liquid-input-label content-layer-text">{t.maxArea}</label>
                          <input
                            type="number"
                            className="liquid-input content-layer-text"
                            value={searchParams.maxArea}
                            onChange={(e) => setSearchParams(prev => ({...prev, maxArea: parseInt(e.target.value) || 0}))}
                            min="1000"
                            max="1000000"
                            step="1000"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 运营参数（可折叠）- 液体玻璃质感 */}
                <div className="liquid-form-section glass-effect-accent">
                  <div className="liquid-section-header" onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
                    <div className="liquid-section-title">
                      <h4 className="content-layer-text">{t.operationalSettings}</h4>
                      <p className="content-layer-text">Configure factory operational requirements</p>
                    </div>
                    <div className={`liquid-expand-indicator ${showAdvancedSettings ? 'expanded' : ''}`}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M6 9l6 6 6-6" stroke="currentColor" strokeWidth="2"/>
                      </svg>
                    </div>
                  </div>

                  {showAdvancedSettings && (
                    <div className="liquid-section-content">
                      <div className="liquid-input-grid">
                        <div className="liquid-input-group">
                          <label className="liquid-input-label content-layer-text">{t.workers}</label>
                          <div className="liquid-input-wrapper">
                            <input
                              type="number"
                              className="liquid-input content-layer-text"
                              value={searchParams.workers}
                              onChange={(e) => setSearchParams(prev => ({...prev, workers: parseInt(e.target.value) || 0}))}
                              placeholder="100"
                              min="1"
                              max="10000"
                            />
                            <span className="liquid-input-unit content-layer-text">people</span>
                          </div>
                        </div>

                        <div className="liquid-input-group">
                          <label className="liquid-input-label content-layer-text">{t.annualEnergy}</label>
                          <div className="liquid-input-wrapper">
                            <input
                              type="number"
                              className="liquid-input content-layer-text"
                              value={searchParams.annualKwh}
                              onChange={(e) => setSearchParams(prev => ({...prev, annualKwh: parseInt(e.target.value) || 0}))}
                              placeholder="500000"
                              min="1000"
                              max="50000000"
                            />
                            <span className="liquid-input-unit content-layer-text">kWh</span>
                          </div>
                        </div>

                        <div className="liquid-input-group">
                          <label className="liquid-input-label content-layer-text">{t.maxCandidates}</label>
                          <div className="liquid-input-wrapper">
                            <input
                              type="number"
                              className="liquid-input content-layer-text"
                              value={searchParams.maxCandidates}
                              onChange={(e) => setSearchParams(prev => ({...prev, maxCandidates: parseInt(e.target.value) || 1}))}
                              placeholder="10"
                              min="1"
                              max="50"
                            />
                            <span className="liquid-input-unit content-layer-text">sites</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* 液体玻璃搜索按钮 */}
                <div className="liquid-form-actions">
                  <button
                    type="submit"
                    className="liquid-primary-button liquid-glass-button"
                    disabled={isLoading}
                  >
                    <div className="liquid-button-content">
                      {isLoading ? (
                        <>
                          <div className="liquid-loading-spinner"></div>
                          <span>{t.analyzing}</span>
                        </>
                      ) : (
                        <span>{t.runAnalysis}</span>
                      )}
                    </div>
                  </button>
                </div>
              </form>
            </aside>

            {/* 中央液体玻璃地图区域 */}
            <div className="liquid-central-content">
              <div className="liquid-map-workspace glass-effect gpu-accelerated">
                <div className="liquid-map-header">
                  <div className="liquid-map-title-section">
                    <h3 className="liquid-map-title content-layer-heading">{t.geographicDistribution}</h3>
                    {selectedParcel && (
                      <div className="liquid-selected-indicator glass-effect-success">
                        <span className="content-layer-text">{t.selectedSite}: {selectedParcel.parcel_id}</span>
                        <span className="metric-value-emphasis">{formatFinancialValue(selectedParcel.total_cost)}</span>
                      </div>
                    )}
                  </div>


                </div>

                <div className="liquid-map-container">
                  <SiteSelectionMap
                    parcels={siteResults?.parcels || []}
                    selectedParcel={selectedParcel}
                    onParcelSelect={handleParcelSelection}
                    mapStyle={mapStyle}
                    height="100%"
                    className="workspace-map"
                  />

                  {/* 悬浮在地图上的液态玻璃样式切换按钮 */}
                  <div className="floating-map-style-controls">
                    <button
                      className={`floating-style-btn ${mapStyle === 'satellite' ? 'active' : ''}`}
                      onClick={() => setMapStyle('satellite')}
                      title="Satellite View - 卫星影像"
                    >
                      <div className="btn-glass-layer"></div>
                      <div className="btn-content">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="btn-icon">
                          <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
                          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                        <span className="btn-label">卫星</span>
                      </div>
                      <div className="btn-refraction"></div>
                    </button>
                    <button
                      className={`floating-style-btn ${mapStyle === 'street' ? 'active' : ''}`}
                      onClick={() => setMapStyle('street')}
                      title="Street View - 街道地图"
                    >
                      <div className="btn-glass-layer"></div>
                      <div className="btn-content">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="btn-icon">
                          <path d="M3 6h18v12H3V6z" stroke="currentColor" strokeWidth="2"/>
                          <path d="M8 10l3 3 5-5" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                        <span className="btn-label">街道</span>
                      </div>
                      <div className="btn-refraction"></div>
                    </button>
                    <button
                      className={`floating-style-btn ${mapStyle === 'terrain' ? 'active' : ''}`}
                      onClick={() => setMapStyle('terrain')}
                      title="Terrain View - 地形地图"
                    >
                      <div className="btn-glass-layer"></div>
                      <div className="btn-content">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="btn-icon">
                          <path d="M3 20l3-6 4 6 4-12 7 12" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                        <span className="btn-label">地形</span>
                      </div>
                      <div className="btn-refraction"></div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
                  
            {/* 右侧液体玻璃财务分析面板 */}
            <aside className="liquid-analysis-panel glass-effect gpu-accelerated">
              <div className="liquid-panel-header">
                <div className="liquid-panel-title-section">
                  <h3 className="liquid-panel-title content-layer-heading">{t.financialAnalysis}</h3>
                  {selectedParcel && (
                    <div className="liquid-site-badge">
                      {selectedParcel.parcel_id}
                    </div>
                  )}
                </div>
              </div>

              {selectedParcel ? (
                <div className="liquid-analysis-content">
                  {/* 液体玻璃关键指标概览 */}
                  <div className="liquid-key-metrics">
                    <div className="liquid-metrics-header">
                      <h4 className="content-layer-heading">{t.keyMetrics}</h4>
                    </div>

                    <div className="liquid-metrics-grid">
                      <div className="data-card-glass primary">
                        <div className="liquid-metric-content">
                          <span className="content-layer-text">{t.totalInvestment}</span>
                          <span className="metric-value-emphasis">{formatFinancialValue(selectedParcel.total_cost)}</span>
                        </div>
                      </div>

                      <div className="data-card-glass">
                        <div className="liquid-metric-content">
                          <span className="content-layer-text">{t.siteArea}</span>
                          <span className="metric-value-emphasis">{selectedParcel.area_sqft?.toLocaleString()} sq ft</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 液体玻璃成本分解分析 */}
                  <div className="liquid-cost-breakdown">
                    <div className="data-card-glass">
                      <h4 className="content-layer-heading">{t.costBreakdown}</h4>

                      {/* 液体玻璃图表区域 */}
                      <div className="liquid-chart-container">
                        <CostBreakdownChart costs={costBreakdown} />
                      </div>

                      {/* 液体玻璃成本项目列表 */}
                      <div className="liquid-cost-items">
                        {costBreakdown.map((cost) => (
                          <div key={cost.key} className="liquid-cost-item">
                            <div className="liquid-cost-indicator">
                              <div className="liquid-cost-color" style={{ backgroundColor: cost.color }}></div>
                            </div>
                            <div className="cost-details">
                              <div className="content-layer-text">{cost.label}</div>
                              <div className="liquid-cost-metrics">
                                <span className="metric-value-emphasis">{formatFinancialValue(cost.value)}</span>
                                <span className="content-layer-text">{cost.percentage}%</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* 液体玻璃投资回报分析 */}
                  {financialAnalysis && (
                    <div className="liquid-financial-results">
                      <div className="data-card-glass">
                        <h4 className="content-layer-heading">{t.investmentAnalysis}</h4>

                        {/* 液体玻璃财务指标摘要 */}
                        <div className="liquid-financial-summary">
                          <div className="liquid-metric-item">
                            <span className="content-layer-text">{t.irr}</span>
                            <span className="metric-value-emphasis highlight">
                              {financialAnalysis.summary?.irr ?
                                formatFinancialValue(financialAnalysis.summary.irr, 'percentage') :
                                '15.20%'
                              }
                            </span>
                          </div>
                          <div className="liquid-metric-item">
                            <span className="content-layer-text">{t.npv}</span>
                            <span className="metric-value-emphasis">
                              {financialAnalysis.summary?.npv ?
                                formatFinancialValue(financialAnalysis.summary.npv) :
                                '$2,850,000'
                              }
                            </span>
                          </div>
                          <div className="liquid-metric-item">
                            <span className="content-layer-text">{t.cashReturn}</span>
                            <span className={`metric-value-emphasis ${(financialAnalysis.summary?.cash_on_cash || -236.68) < 0 ? 'negative' : 'positive'}`}>
                              {financialAnalysis.summary?.cash_on_cash ?
                                formatFinancialValue(financialAnalysis.summary.cash_on_cash, 'percentage') :
                                '-236.68%'
                              }
                            </span>
                          </div>
                        </div>

                        {/* 液体玻璃投资建议 */}
                        <div className="liquid-investment-recommendation">
                          {(() => {
                            const cashReturn = financialAnalysis.summary?.cash_on_cash || -236.68;
                            const isHighRisk = cashReturn < -100;

                            return (
                              <div className={`recommendation-card ${isHighRisk ? 'glass-effect-warning' : 'glass-effect-success'}`}>
                                <div className="liquid-recommendation-header">
                                  <div className="content-layer-heading">
                                    {isHighRisk ? t.cautionRequired : t.goodOpportunity}
                                  </div>
                                </div>
                                <div className="content-layer-text">
                                  {isHighRisk ?
                                    t.negativeReturnWarning :
                                    t.positiveReturnMessage
                                  }
                                </div>
                              </div>
                            );
                          })()}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="liquid-empty-selection">
                  <div className="data-card-glass">
                    <h4 className="content-layer-heading">{t.selectSite}</h4>
                    <p className="content-layer-text">{t.selectSiteDescription}</p>
                  </div>
                </div>
              )}
            </aside>
          </div>
        ) : (
          /* 液体玻璃全屏地图模式 */
          <div className="liquid-map-focus-mode">
            <div className="liquid-fullscreen-map-container glass-effect gpu-accelerated">
              <SiteSelectionMap
                parcels={siteResults?.parcels || []}
                selectedParcel={selectedParcel}
                onParcelSelect={handleParcelSelection}
                mapStyle={mapStyle}
                height="100%"
                className="fullscreen-map"
              />

              {/* 全屏模式下的悬浮样式切换按钮 */}
              <div className="floating-map-style-controls fullscreen">
                <button
                  className={`floating-style-btn ${mapStyle === 'satellite' ? 'active' : ''}`}
                  onClick={() => setMapStyle('satellite')}
                  title="Satellite View - 卫星影像"
                >
                  <div className="btn-glass-layer"></div>
                  <div className="btn-content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="btn-icon">
                      <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
                      <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    <span className="btn-label">卫星</span>
                  </div>
                  <div className="btn-refraction"></div>
                </button>
                <button
                  className={`floating-style-btn ${mapStyle === 'street' ? 'active' : ''}`}
                  onClick={() => setMapStyle('street')}
                  title="Street View - 街道地图"
                >
                  <div className="btn-glass-layer"></div>
                  <div className="btn-content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="btn-icon">
                      <path d="M3 6h18v12H3V6z" stroke="currentColor" strokeWidth="2"/>
                      <path d="M8 10l3 3 5-5" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    <span className="btn-label">街道</span>
                  </div>
                  <div className="btn-refraction"></div>
                </button>
                <button
                  className={`floating-style-btn ${mapStyle === 'terrain' ? 'active' : ''}`}
                  onClick={() => setMapStyle('terrain')}
                  title="Terrain View - 地形地图"
                >
                  <div className="btn-glass-layer"></div>
                  <div className="btn-content">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="btn-icon">
                      <path d="M3 20l3-6 4 6 4-12 7 12" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    <span className="btn-label">地形</span>
                  </div>
                  <div className="btn-refraction"></div>
                </button>
              </div>

              {/* 液体玻璃浮动控制面板 */}
              <div className="liquid-floating-controls">
                {/* 液体玻璃返回按钮 */}
                <button
                  className="liquid-floating-button liquid-return-button liquid-glass-button"
                  onClick={() => setViewMode('dashboard')}
                  title="Return to dashboard"
                >
                  <span>Dashboard</span>
                </button>

                {/* 液体玻璃站点概览面板 */}
                <div className="liquid-overview-panel glass-effect">
                  <div className="liquid-overview-header">
                    <h3 className="content-layer-heading">Site Overview</h3>
                  </div>

                  {siteResults && (
                    <div className="liquid-overview-stats">
                      <div className="liquid-stat-item">
                        <span className="stat-value">{siteResults.parcels?.length || 0}</span>
                        <span className="content-layer-text">Candidate Sites</span>
                      </div>
                      {selectedParcel && (
                        <div className="liquid-stat-item">
                          <span className="stat-value">{formatFinancialValue(selectedParcel.total_cost)}</span>
                          <span className="content-layer-text">Selected Site Cost</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* 液体玻璃选中站点详情 */}
                {selectedParcel && (
                  <div className="liquid-site-details-panel glass-effect">
                    <div className="liquid-details-header">
                      <h4 className="content-layer-heading">Site {selectedParcel.parcel_id}</h4>
                    </div>

                    <div className="liquid-details-metrics">
                      <div className="liquid-detail-metric">
                        <span className="content-layer-text">Area:</span>
                        <span className="metric-value-emphasis">{selectedParcel.area_sqft?.toLocaleString()} sq ft</span>
                      </div>
                      <div className="liquid-detail-metric">
                        <span className="content-layer-text">Total Cost:</span>
                        <span className="metric-value-emphasis">{formatFinancialValue(selectedParcel.total_cost)}</span>
                      </div>
                      {financialAnalysis && (
                        <div className="liquid-detail-metric">
                          <span className="content-layer-text">IRR:</span>
                          <span className="metric-value-emphasis">{formatFinancialValue(financialAnalysis.summary?.irr, 'percentage')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>

      {/* 液体玻璃错误提示 */}
      {error && (
        <div className="liquid-error-toast glass-effect">
          <div className="liquid-toast-content">
            <div className="liquid-toast-message">
              <span className="content-layer-heading">Error</span>
              <span className="content-layer-text">{error}</span>
            </div>
            <button
              className="liquid-toast-close liquid-glass-button"
              onClick={() => setError(null)}
              aria-label="Close notification"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SiteSelectionPage;

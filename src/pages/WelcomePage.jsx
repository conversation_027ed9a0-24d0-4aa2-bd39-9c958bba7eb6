import { useRef, useState, useEffect, Suspense, useMemo, forwardRef, useImperativeHandle, useCallback, memo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  useTexture,
  Html
} from '@react-three/drei';
import { useNavigate } from 'react-router-dom';
import * as THREE from 'three';
import LiquidGlass from 'liquid-glass-react';
import '../styles/WelcomePage.css';
import Particles from '../components/3D/Particles';

// Utility functions for performance optimization
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const throttle = (func, limit) => {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 添加CSS动画样式
const tooltipStyles = `
  @keyframes tooltipFadeIn {
    0% {
      opacity: 0;
      transform: translate(0, -100%) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translate(0, -100%) scale(1);
    }
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.innerText = tooltipStyles;
  document.head.appendChild(styleSheet);
}

// 轨道粒子组件 - 优化版本
const OrbitingParticles = memo(() => {
  const particlesRef = useRef();
  const particleCount = 50;

  // 使用useMemo缓存初始位置计算
  const initialPositions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      const radius = 3.2 + Math.random() * 0.8;
      const theta = (i / particleCount) * Math.PI * 2;
      const phi = Math.random() * Math.PI * 0.4 - Math.PI * 0.2; // 限制在赤道附近

      pos[i * 3] = radius * Math.cos(theta) * Math.cos(phi);
      pos[i * 3 + 1] = radius * Math.sin(phi);
      pos[i * 3 + 2] = radius * Math.sin(theta) * Math.cos(phi);
    }
    return pos;
  }, [particleCount]);

  // 缓存材质属性
  const materialProps = useMemo(() => ({
    size: 0.04,
    color: "#00e5ff",
    transparent: true,
    opacity: 0.8,
    sizeAttenuation: false
  }), []);

  // 优化动画效果 - 使用GPU加速的transform
  useFrame(({ clock }) => {
    if (particlesRef.current) {
      const time = clock.getElapsedTime();
      const positions = particlesRef.current.geometry.attributes.position.array;

      // 批量更新位置，减少单独的数学计算
      for (let i = 0; i < particleCount; i++) {
        const baseIndex = i * 3;
        const radius = 3.2 + Math.sin(time * 0.5 + i * 0.1) * 0.3;
        const theta = (i / particleCount) * Math.PI * 2 + time * 0.2;
        const phi = Math.sin(time * 0.3 + i * 0.05) * 0.2;

        const cosTheta = Math.cos(theta);
        const sinTheta = Math.sin(theta);
        const cosPhi = Math.cos(phi);
        const sinPhi = Math.sin(phi);

        positions[baseIndex] = radius * cosTheta * cosPhi;
        positions[baseIndex + 1] = radius * sinPhi;
        positions[baseIndex + 2] = radius * sinTheta * cosPhi;
      }

      particlesRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={initialPositions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial {...materialProps} />
    </points>
  );
});

// 现代科技感立体浮雕世界地图组件 - 带国家边界版 - 优化版本
const ModernTechEarth = memo(forwardRef((props, ref) => {
  const [landData, setLandData] = useState(null);
  const [countriesData, setCountriesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hoveredCountry, setHoveredCountry] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [showTooltip, setShowTooltip] = useState(false);
  const groupRef = useRef();
  const raycaster = useRef(new THREE.Raycaster());
  const mouse = useRef(new THREE.Vector2());

  // 获取当前语言设置 - 使用useMemo缓存
  const currentLang = useMemo(() => localStorage.getItem('preferredLanguage') || 'en', []);

  // 国家名称映射（英文到中文）
  const countryNameMap = {
    'United States of America': '美国',
    'United States': '美国',
    'China': '中国',
    'Russia': '俄罗斯',
    'Canada': '加拿大',
    'Brazil': '巴西',
    'Australia': '澳大利亚',
    'India': '印度',
    'Argentina': '阿根廷',
    'Kazakhstan': '哈萨克斯坦',
    'Algeria': '阿尔及利亚',
    'Democratic Republic of the Congo': '刚果民主共和国',
    'Saudi Arabia': '沙特阿拉伯',
    'Mexico': '墨西哥',
    'Indonesia': '印度尼西亚',
    'Sudan': '苏丹',
    'Libya': '利比亚',
    'Iran': '伊朗',
    'Mongolia': '蒙古',
    'Peru': '秘鲁',
    'Chad': '乍得',
    'Niger': '尼日尔',
    'Angola': '安哥拉',
    'Mali': '马里',
    'South Africa': '南非',
    'Colombia': '哥伦比亚',
    'Ethiopia': '埃塞俄比亚',
    'Bolivia': '玻利维亚',
    'Mauritania': '毛里塔尼亚',
    'Egypt': '埃及',
    'Tanzania': '坦桑尼亚',
    'Nigeria': '尼日利亚',
    'Venezuela': '委内瑞拉',
    'Namibia': '纳米比亚',
    'Mozambique': '莫桑比克',
    'Pakistan': '巴基斯坦',
    'Turkey': '土耳其',
    'Chile': '智利',
    'Zambia': '赞比亚',
    'Myanmar': '缅甸',
    'Afghanistan': '阿富汗',
    'Somalia': '索马里',
    'Central African Republic': '中非共和国',
    'South Sudan': '南苏丹',
    'Ukraine': '乌克兰',
    'Madagascar': '马达加斯加',
    'Botswana': '博茨瓦纳',
    'Kenya': '肯尼亚',
    'France': '法国',
    'Yemen': '也门',
    'Thailand': '泰国',
    'Spain': '西班牙',
    'Turkmenistan': '土库曼斯坦',
    'Cameroon': '喀麦隆',
    'Papua New Guinea': '巴布亚新几内亚',
    'Sweden': '瑞典',
    'Uzbekistan': '乌兹别克斯坦',
    'Morocco': '摩洛哥',
    'Iraq': '伊拉克',
    'Paraguay': '巴拉圭',
    'Zimbabwe': '津巴布韦',
    'Norway': '挪威',
    'Japan': '日本',
    'Germany': '德国',
    'Republic of the Congo': '刚果共和国',
    'Finland': '芬兰',
    'Vietnam': '越南',
    'Malaysia': '马来西亚',
    'Poland': '波兰',
    'Oman': '阿曼',
    'Italy': '意大利',
    'Philippines': '菲律宾',
    'Ecuador': '厄瓜多尔',
    'Burkina Faso': '布基纳法索',
    'New Zealand': '新西兰',
    'Gabon': '加蓬',
    'Guinea': '几内亚',
    'United Kingdom': '英国',
    'Uganda': '乌干达',
    'Ghana': '加纳',
    'Romania': '罗马尼亚',
    'Laos': '老挝',
    'Guyana': '圭亚那',
    'Belarus': '白俄罗斯',
    'Kyrgyzstan': '吉尔吉斯斯坦',
    'Senegal': '塞内加尔',
    'Syria': '叙利亚',
    'Cambodia': '柬埔寨',
    'Uruguay': '乌拉圭',
    'Tunisia': '突尼斯',
    'Suriname': '苏里南',
    'Bangladesh': '孟加拉国',
    'Nepal': '尼泊尔',
    'Tajikistan': '塔吉克斯坦',
    'Greece': '希腊',
    'Nicaragua': '尼加拉瓜',
    'North Korea': '朝鲜',
    'Malawi': '马拉维',
    'Eritrea': '厄立特里亚',
    'Benin': '贝宁',
    'Honduras': '洪都拉斯',
    'Liberia': '利比里亚',
    'Bulgaria': '保加利亚',
    'Cuba': '古巴',
    'Guatemala': '危地马拉',
    'Iceland': '冰岛',
    'South Korea': '韩国',
    'Hungary': '匈牙利',
    'Jordan': '约旦',
    'Portugal': '葡萄牙',
    'Serbia': '塞尔维亚',
    'Azerbaijan': '阿塞拜疆',
    'Austria': '奥地利',
    'United Arab Emirates': '阿联酋',
    'Czech Republic': '捷克',
    'Panama': '巴拿马',
    'Sierra Leone': '塞拉利昂',
    'Ireland': '爱尔兰',
    'Georgia': '格鲁吉亚',
    'Sri Lanka': '斯里兰卡',
    'Lithuania': '立陶宛',
    'Latvia': '拉脱维亚',
    'Togo': '多哥',
    'Croatia': '克罗地亚',
    'Bosnia and Herzegovina': '波黑',
    'Costa Rica': '哥斯达黎加',
    'Slovakia': '斯洛伐克',
    'Dominican Republic': '多米尼加',
    'Estonia': '爱沙尼亚',
    'Denmark': '丹麦',
    'Netherlands': '荷兰',
    'Switzerland': '瑞士',
    'Bhutan': '不丹',
    'Guinea-Bissau': '几内亚比绍',
    'Moldova': '摩尔多瓦',
    'Belgium': '比利时',
    'Lesotho': '莱索托',
    'Armenia': '亚美尼亚',
    'Albania': '阿尔巴尼亚',
    'Solomon Islands': '所罗门群岛',
    'Equatorial Guinea': '赤道几内亚',
    'Burundi': '布隆迪',
    'Haiti': '海地',
    'Rwanda': '卢旺达',
    'Macedonia': '马其顿',
    'Djibouti': '吉布提',
    'Belize': '伯利兹',
    'El Salvador': '萨尔瓦多',
    'Israel': '以色列',
    'Slovenia': '斯洛文尼亚',
    'Fiji': '斐济',
    'Kuwait': '科威特',
    'Swaziland': '斯威士兰',
    'Timor-Leste': '东帝汶',
    'Montenegro': '黑山',
    'Bahrain': '巴林',
    'Vanuatu': '瓦努阿图',
    'Qatar': '卡塔尔',
    'Gambia': '冈比亚',
    'Jamaica': '牙买加',
    'Lebanon': '黎巴嫩',
    'Cyprus': '塞浦路斯',
    'Brunei': '文莱',
    'Trinidad and Tobago': '特立尼达和多巴哥',
    'Cape Verde': '佛得角',
    'Samoa': '萨摩亚',
    'Luxembourg': '卢森堡',
    'Comoros': '科摩罗',
    'Mauritius': '毛里求斯',
    'São Tomé and Príncipe': '圣多美和普林西比',
    'Kiribati': '基里巴斯',
    'Dominica': '多米尼克',
    'Tonga': '汤加',
    'Micronesia': '密克罗尼西亚',
    'Singapore': '新加坡',
    'Bahamas': '巴哈马',
    'Palau': '帕劳',
    'Seychelles': '塞舌尔',
    'Andorra': '安道尔',
    'Antigua and Barbuda': '安提瓜和巴布达',
    'Barbados': '巴巴多斯',
    'Saint Vincent and the Grenadines': '圣文森特和格林纳丁斯',
    'Grenada': '格林纳达',
    'Malta': '马耳他',
    'Maldives': '马尔代夫',
    'Saint Kitts and Nevis': '圣基茨和尼维斯',
    'Marshall Islands': '马绍尔群岛',
    'Liechtenstein': '列支敦士登',
    'San Marino': '圣马力诺',
    'Tuvalu': '图瓦卢',
    'Nauru': '瑙鲁',
    'Monaco': '摩纳哥',
    'Vatican City': '梵蒂冈'
  };

  // 获取国家名称（根据当前语言）
  const getCountryName = (englishName) => {
    if (currentLang === 'zh') {
      return countryNameMap[englishName] || englishName;
    }
    return englishName;
  };

  // 加载GeoJSON数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const [landResponse, countriesResponse] = await Promise.all([
          fetch('/welcomeEarthLandData/ne_50m_land.json'),
          fetch('/welcomeEarthLandData/ne_50m_admin_0_countries.json')
        ]);

        const landData = await landResponse.json();
        const countriesData = await countriesResponse.json();

        console.log('Land data loaded:', landData.features?.length, 'features');
        console.log('Countries data loaded:', countriesData.features?.length, 'countries');

        setLandData(landData);
        setCountriesData(countriesData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading GeoJSON data:', error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // 创建立体投影 - 将地理坐标转换为3D坐标
  const projection = useCallback((lon, lat, elevation = 0) => {
    const x = (lon / 180) * 8; // 经度映射到 -8 到 8
    const y = (lat / 90) * 4;   // 纬度映射到 -4 到 4

    // 根据纬度添加地形变化
    const baseElevation = Math.sin((lat / 90) * Math.PI) * 0.08;
    const randomVariation = (Math.random() - 0.5) * 0.04;
    const totalElevation = elevation + baseElevation + randomVariation;

    return [x, y, totalElevation];
  }, []);

  // 创建立体大陆地形
  const continentMeshes = useMemo(() => {
    if (!landData) return [];

    const meshes = [];

    landData.features.forEach((feature, featureIndex) => {
      if (!feature.geometry || !feature.geometry.coordinates) return;

      const processCoordinates = (coords, depth = 0) => {
        if (depth === 0 && Array.isArray(coords[0]) && Array.isArray(coords[0][0])) {
          coords.forEach(ring => processCoordinates(ring, depth + 1));
        } else if (depth === 1 && Array.isArray(coords[0]) && !Array.isArray(coords[0][0])) {
          if (coords.length < 3) return;

          const shape = new THREE.Shape();
          let firstPoint = true;

          coords.forEach((coord, index) => {
            if (coord && coord.length >= 2) {
              const [x, y] = projection(coord[0], coord[1]);

              if (firstPoint) {
                shape.moveTo(x, y);
                firstPoint = false;
              } else {
                shape.lineTo(x, y);
              }
            }
          });

          if (shape.curves.length > 2) {
            // 大陆立体高度
            const continentElevation = 0.15 + Math.random() * 0.08;

            // 挤出设置
            const extrudeSettings = {
              depth: continentElevation,
              bevelEnabled: true,
              bevelThickness: 0.02,
              bevelSize: 0.015,
              bevelOffset: 0,
              bevelSegments: 2
            };

            meshes.push({
              shape,
              extrudeSettings,
              material: {
                color: '#2c5a6b',
                metalness: 0.3,
                roughness: 0.6,
                opacity: 0.95,
                transparent: true,
                emissive: '#1a3a4a',
                emissiveIntensity: 0.1
              }
            });
          }
        } else {
          coords.forEach(subCoords => processCoordinates(subCoords, depth + 1));
        }
      };

      if (feature.geometry.type === 'Polygon') {
        processCoordinates(feature.geometry.coordinates, 0);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          processCoordinates(polygon, 0);
        });
      }
    });

    console.log(`Generated ${meshes.length} continent relief meshes`);
    return meshes;
  }, [landData, projection]);

  // 创建国家边界线
  const countryBorders = useMemo(() => {
    if (!countriesData) return [];

    const borders = [];

    countriesData.features.forEach((feature, featureIndex) => {
      if (!feature.geometry || !feature.geometry.coordinates) return;

      const countryName = feature.properties?.NAME || feature.properties?.name || `Country ${featureIndex}`;

      const processCoordinates = (coords, depth = 0) => {
        if (depth === 0 && Array.isArray(coords[0]) && Array.isArray(coords[0][0])) {
          coords.forEach(ring => processCoordinates(ring, depth + 1));
        } else if (depth === 1 && Array.isArray(coords[0]) && !Array.isArray(coords[0][0])) {
          if (coords.length < 3) return;

          const points = [];
          coords.forEach((coord, index) => {
            if (coord && coord.length >= 2) {
              const [x, y, z] = projection(coord[0], coord[1], 0.25); // 边界线稍微高一点
              points.push(new THREE.Vector3(x, y, z));
            }
          });

          if (points.length > 2) {
            // 闭合边界线
            points.push(points[0]);

            borders.push({
              points,
              countryName,
              featureIndex
            });
          }
        } else {
          coords.forEach(subCoords => processCoordinates(subCoords, depth + 1));
        }
      };

      if (feature.geometry.type === 'Polygon') {
        processCoordinates(feature.geometry.coordinates, 0);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          processCoordinates(polygon, 0);
        });
      }
    });

    console.log(`Generated ${borders.length} country border lines`);
    return borders;
  }, [countriesData, projection]);

  // 创建国家检测网格 - 用于提高hover检测精度
  const countryMeshes = useMemo(() => {
    if (!countriesData) return [];

    const meshes = [];

    countriesData.features.forEach((feature, featureIndex) => {
      if (!feature.geometry || !feature.geometry.coordinates) return;

      const countryName = feature.properties?.NAME || feature.properties?.name || `Country ${featureIndex}`;

      const processCoordinates = (coords, depth = 0) => {
        if (depth === 0 && Array.isArray(coords[0]) && Array.isArray(coords[0][0])) {
          coords.forEach(ring => processCoordinates(ring, depth + 1));
        } else if (depth === 1 && Array.isArray(coords[0]) && !Array.isArray(coords[0][0])) {
          if (coords.length < 3) return;

          const shape = new THREE.Shape();
          let firstPoint = true;

          coords.forEach((coord, index) => {
            if (coord && coord.length >= 2) {
              const [x, y] = projection(coord[0], coord[1]);

              if (firstPoint) {
                shape.moveTo(x, y);
                firstPoint = false;
              } else {
                shape.lineTo(x, y);
              }
            }
          });

          if (shape.curves.length > 2) {
            meshes.push({
              shape,
              countryName,
              featureIndex,
              geometry: feature.geometry
            });
          }
        } else {
          coords.forEach(subCoords => processCoordinates(subCoords, depth + 1));
        }
      };

      if (feature.geometry.type === 'Polygon') {
        processCoordinates(feature.geometry.coordinates, 0);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          processCoordinates(polygon, 0);
        });
      }
    });

    console.log(`Generated ${meshes.length} country detection meshes`);
    console.log('Sample country mesh:', meshes[0]?.countryName);
    return meshes;
  }, [countriesData, projection]);

  // 增强的鼠标移动处理函数 - 提高检测灵敏度
  const handleMouseMove = useCallback((event) => {
    if (!groupRef.current) return;

    // 获取画布元素
    const canvas = event.target;
    const rect = canvas.getBoundingClientRect();

    // 计算鼠标在画布中的标准化坐标 (-1 到 1)
    const mouseX = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    const mouseY = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 更新鼠标位置（添加平滑处理）
    mouse.current.x = mouseX;
    mouse.current.y = mouseY;

    // 计算工具提示位置，确保不会超出屏幕边界
    const tooltipOffset = 15;
    const tooltipWidth = 200; // 估计的工具提示宽度
    const tooltipHeight = 40; // 估计的工具提示高度

    let tooltipX = event.clientX + tooltipOffset;
    let tooltipY = event.clientY - tooltipOffset;

    // 检查右边界
    if (tooltipX + tooltipWidth > window.innerWidth) {
      tooltipX = event.clientX - tooltipWidth - tooltipOffset;
    }

    // 检查上边界
    if (tooltipY - tooltipHeight < 0) {
      tooltipY = event.clientY + tooltipOffset + tooltipHeight;
    }

    // 检查左边界
    if (tooltipX < 0) {
      tooltipX = tooltipOffset;
    }

    // 检查下边界
    if (tooltipY + tooltipHeight > window.innerHeight) {
      tooltipY = window.innerHeight - tooltipHeight - tooltipOffset;
    }

    // 更新工具提示位置
    setTooltipPosition({
      x: tooltipX,
      y: tooltipY
    });
  }, []);

  // 鼠标离开处理函数
  const handleMouseLeave = useCallback(() => {
    setHoveredCountry(null);
    setShowTooltip(false);
  }, []);

  // 优化的射线检测系统 - 多重检测方法和增强灵敏度
  useFrame(({ camera }) => {
    if (!groupRef.current || !countriesData || !countryMeshes.length) return;

    // 获取所有可交互的对象
    const intersectableObjects = [];
    const countryDetectors = [];

    groupRef.current.traverse((child) => {
      if (child.isMesh && child.geometry) {
        // 分离国家检测器和普通网格
        if (child.userData && child.userData.isCountryDetector) {
          countryDetectors.push(child);
        } else {
          intersectableObjects.push(child);
        }
      }
    });

    let foundCountry = null;

    // 多点射线检测 - 提高灵敏度
    const detectionPoints = [
      { x: mouse.current.x, y: mouse.current.y }, // 中心点
      { x: mouse.current.x + 0.01, y: mouse.current.y }, // 右
      { x: mouse.current.x - 0.01, y: mouse.current.y }, // 左
      { x: mouse.current.x, y: mouse.current.y + 0.01 }, // 上
      { x: mouse.current.x, y: mouse.current.y - 0.01 }, // 下
      { x: mouse.current.x + 0.007, y: mouse.current.y + 0.007 }, // 右上
      { x: mouse.current.x - 0.007, y: mouse.current.y + 0.007 }, // 左上
      { x: mouse.current.x + 0.007, y: mouse.current.y - 0.007 }, // 右下
      { x: mouse.current.x - 0.007, y: mouse.current.y - 0.007 }  // 左下
    ];

    // 优先检测国家检测器（最精确）
    if (countryDetectors.length > 0) {
      // 调试信息 - 只在第一次显示
      if (!window.detectorCountLogged) {
        console.log('Country detectors available:', countryDetectors.length);
        window.detectorCountLogged = true;
      }
      for (const point of detectionPoints) {
        raycaster.current.setFromCamera(point, camera);
        const countryIntersects = raycaster.current.intersectObjects(countryDetectors, false);
        if (countryIntersects.length > 0) {
          const countryIntersect = countryIntersects[0];
          if (countryIntersect.object.userData && countryIntersect.object.userData.countryName) {
            foundCountry = countryIntersect.object.userData.countryName;
            // 调试信息
            if (foundCountry !== hoveredCountry) {
              console.log('Country detected via detector mesh:', foundCountry);
            }
            break;
          }
        }
      }
    }

    // 如果国家检测器没有检测到，使用传统方法
    if (!foundCountry && intersectableObjects.length > 0) {
      for (const point of detectionPoints) {
        raycaster.current.setFromCamera(point, camera);
        const intersects = raycaster.current.intersectObjects(intersectableObjects, false);

        if (intersects.length > 0) {
          const intersect = intersects[0];
          const intersectPoint = intersect.point;

          // 方法1: 直接使用3D坐标进行国家检测（更精确）
          foundCountry = findCountryByPoint(intersectPoint);

          // 方法2: 如果方法1失败，使用传统的地理坐标转换
          if (!foundCountry) {
            const lon = (intersectPoint.x / 8) * 180;
            const lat = (intersectPoint.y / 4) * 90;
            foundCountry = findCountryByGeoCoordinates(lon, lat);
          }

          // 方法3: 如果仍然失败，使用扩展搜索半径
          if (!foundCountry) {
            foundCountry = findCountryByExpandedSearch(intersectPoint);
          }

          if (foundCountry) break;
        }
      }
    }

    // 更新状态
    if (foundCountry && foundCountry !== hoveredCountry) {
      setHoveredCountry(foundCountry);
      setShowTooltip(true);
    } else if (!foundCountry && hoveredCountry) {
      setHoveredCountry(null);
      setShowTooltip(false);
    }
  });

  // 方法1: 通过3D点直接查找国家（最精确）
  const findCountryByPoint = useCallback((point) => {
    const tolerance = 0.1; // 容差范围

    for (const meshData of countryMeshes) {
      // 检查点是否在国家形状的边界框内
      const shape = meshData.shape;
      if (shape && shape.curves && shape.curves.length > 0) {
        // 简单的边界框检测
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;

        shape.curves.forEach(curve => {
          if (curve.v1) {
            minX = Math.min(minX, curve.v1.x);
            maxX = Math.max(maxX, curve.v1.x);
            minY = Math.min(minY, curve.v1.y);
            maxY = Math.max(maxY, curve.v1.y);
          }
          if (curve.v2) {
            minX = Math.min(minX, curve.v2.x);
            maxX = Math.max(maxX, curve.v2.x);
            minY = Math.min(minY, curve.v2.y);
            maxY = Math.max(maxY, curve.v2.y);
          }
        });

        // 检查点是否在边界框内（带容差）
        if (point.x >= minX - tolerance && point.x <= maxX + tolerance &&
            point.y >= minY - tolerance && point.y <= maxY + tolerance) {

          // 进一步检查是否在实际形状内
          const lon = (point.x / 8) * 180;
          const lat = (point.y / 4) * 90;

          if (isPointInCountry(lon, lat, meshData.geometry)) {
            return meshData.countryName;
          }
        }
      }
    }
    return null;
  }, [countryMeshes]);

  // 方法2: 传统地理坐标查找
  const findCountryByGeoCoordinates = useCallback((lon, lat) => {
    for (const feature of countriesData.features) {
      if (feature.geometry && isPointInCountry(lon, lat, feature.geometry)) {
        return feature.properties?.NAME || feature.properties?.name || 'Unknown';
      }
    }
    return null;
  }, [countriesData]);

  // 方法3: 扩展搜索半径查找
  const findCountryByExpandedSearch = useCallback((point) => {
    const searchRadius = 0.2; // 搜索半径
    const searchSteps = 8; // 搜索步数

    for (let i = 1; i <= searchSteps; i++) {
      const radius = (searchRadius / searchSteps) * i;

      // 在圆周上检查多个点
      for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 4) {
        const testX = point.x + Math.cos(angle) * radius;
        const testY = point.y + Math.sin(angle) * radius;

        const lon = (testX / 8) * 180;
        const lat = (testY / 4) * 90;

        const country = findCountryByGeoCoordinates(lon, lat);
        if (country) {
          return country;
        }
      }
    }
    return null;
  }, [findCountryByGeoCoordinates]);

  // 简单的点在多边形内检测函数
  const isPointInCountry = (lon, lat, geometry) => {
    if (geometry.type === 'Polygon') {
      return isPointInPolygon(lon, lat, geometry.coordinates[0]);
    } else if (geometry.type === 'MultiPolygon') {
      for (const polygon of geometry.coordinates) {
        if (isPointInPolygon(lon, lat, polygon[0])) {
          return true;
        }
      }
    }
    return false;
  };

  // 射线投射算法检测点是否在多边形内
  const isPointInPolygon = (lon, lat, polygon) => {
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0], yi = polygon[i][1];
      const xj = polygon[j][0], yj = polygon[j][1];

      if (((yi > lat) !== (yj > lat)) && (lon < (xj - xi) * (lat - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    return inside;
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    handleMouseMove,
    handleMouseLeave
  }), [handleMouseMove, handleMouseLeave]);

  if (loading) {
    return (
      <group>
        <Html position={[0, 0, 0]} center>
          <div style={{
            color: '#ffffff',
            fontSize: '16px',
            fontFamily: 'Inter, sans-serif',
            textAlign: 'center',
            padding: '20px',
            background: 'rgba(0, 0, 0, 0.5)',
            borderRadius: '8px',
            backdropFilter: 'blur(10px)'
          }}>
            Loading Geographic Data...
          </div>
        </Html>
      </group>
    );
  }

  return (
    <group ref={groupRef} position={[0, 0, 0]} rotation={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
      {/* 立体大陆浮雕 */}
      {continentMeshes.map((meshData, index) => (
        <group key={`continent-relief-${index}`}>
          {/* 主体浮雕 */}
          <mesh position={[0, 0, 0]} castShadow receiveShadow>
            <extrudeGeometry args={[meshData.shape, meshData.extrudeSettings]} />
            <meshStandardMaterial
              color={meshData.material.color}
              metalness={meshData.material.metalness}
              roughness={meshData.material.roughness}
              opacity={meshData.material.opacity}
              transparent={meshData.material.transparent}
              emissive={meshData.material.emissive}
              emissiveIntensity={meshData.material.emissiveIntensity}
            />
          </mesh>

          {/* 边缘轮廓线 */}
          <mesh position={[0, 0, 0.002]}>
            <extrudeGeometry args={[meshData.shape, { depth: 0.001, bevelEnabled: false }]} />
            <meshBasicMaterial
              color="#4a9eff"
              transparent={true}
              opacity={0.6}
              wireframe={true}
            />
          </mesh>
        </group>
      ))}

      {/* 国家边界线 */}
      {countryBorders.map((border, index) => (
        <line key={`country-border-${index}`}>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={border.points.length}
              array={new Float32Array(border.points.flatMap(p => [p.x, p.y, p.z]))}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial
            color="#ffffff"
            transparent={true}
            opacity={0.3}
            linewidth={0.5}
          />
        </line>
      ))}

      {/* 隐形国家检测网格 - 用于精确的鼠标悬停检测 */}
      {countryMeshes.map((meshData, index) => (
        <mesh
          key={`country-detector-${index}`}
          position={[0, 0, 0.3]}
          userData={{ countryName: meshData.countryName, isCountryDetector: true }}
        >
          <extrudeGeometry args={[meshData.shape, { depth: 0.05, bevelEnabled: false }]} />
          <meshBasicMaterial
            transparent={true}
            opacity={0}
            visible={true}
            side={THREE.DoubleSide}
          />
        </mesh>
      ))}

      {/* 简化的光照系统 */}
      <ambientLight intensity={0.5} color="#ffffff" />
      <directionalLight
        position={[10, 15, 8]}
        intensity={1.0}
        color="#ffffff"
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
        shadow-camera-far={30}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      <directionalLight
        position={[-8, 10, -5]}
        intensity={0.4}
        color="#87ceeb"
      />

      {/* 顶部聚光灯 */}
      <spotLight
        position={[0, 20, 0]}
        angle={Math.PI / 4}
        penumbra={0.3}
        intensity={0.6}
        color="#ffffff"
        castShadow
      />

      {/* 增强的悬浮工具提示 */}
      {showTooltip && hoveredCountry && (
        <Html
          position={[0, 0, 0]}
          style={{
            pointerEvents: 'none',
            position: 'fixed',
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y}px`,
            zIndex: 1000,
            transform: 'translate(0, -100%)'
          }}
        >
          <div style={{
            background: 'rgba(0, 0, 0, 0.9)',
            color: '#ffffff',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            fontWeight: '600',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(12px)',
            border: '1px solid rgba(74, 158, 255, 0.3)',
            whiteSpace: 'nowrap',
            animation: 'tooltipFadeIn 0.15s ease-out',
            maxWidth: '250px',
            textAlign: 'center',
            transition: 'all 0.1s ease-out',
            position: 'relative'
          }}>
            <div style={{
              position: 'absolute',
              top: '-1px',
              left: '-1px',
              right: '-1px',
              bottom: '-1px',
              background: 'linear-gradient(45deg, rgba(74, 158, 255, 0.2), rgba(74, 158, 255, 0.1))',
              borderRadius: '8px',
              zIndex: -1
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <div style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: 'linear-gradient(45deg, #4a9eff, #74b9ff)',
                boxShadow: '0 0 8px rgba(74, 158, 255, 0.6)'
              }} />
              {getCountryName(hoveredCountry)}
            </div>
          </div>
        </Html>
      )}
    </group>
  );
}));

// 优化的地理数据可视化模块 - 专注于技术展示的科技感界面
const OptimizedGeoVisualization = memo(() => {
  const [loading, setLoading] = useState(true);
  const [countries, setCountries] = useState([]);
  const [lines, setLines] = useState([]);
  const [hotspots, setHotspots] = useState([]);
  const [highlightedCountry, setHighlightedCountry] = useState(null);
  
  // 添加缺少的状态定义
  const [landData, setLandData] = useState(null);
  const [countriesData, setCountriesData] = useState(null);
  const [hoveredCountry, setHoveredCountry] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [pulseIntensity, setPulseIntensity] = useState(0);
  
  const isEnglish = localStorage.getItem('preferredLanguage') === 'en';

  // 添加高亮国家状态
  const highlightedCountries = new Set([1, 5, 10, 15, 20]); // 示例高亮国家索引
  
  // 工业热点数据
  const industrialHotspots = [
    { id: 1, x: 200, y: 150, label: 'Silicon Valley' },
    { id: 2, x: 600, y: 120, label: 'Shenzhen' },
    { id: 3, x: 400, y: 180, label: 'Berlin' },
    { id: 4, x: 150, y: 200, label: 'Detroit' },
    { id: 5, x: 650, y: 200, label: 'Tokyo' }
  ];
  
  // 活跃热点
  const activeHotspots = new Set([1, 3, 5]);

  // 脉冲动画效果
  useEffect(() => {
    const animatePulse = () => {
      setPulseIntensity(prev => {
        const newValue = Math.sin(Date.now() * 0.003) * 0.5 + 0.5;
        return newValue;
      });
    };
    
    const interval = setInterval(animatePulse, 50);
    return () => clearInterval(interval);
  }, []);

  // 加载地理数据
  useEffect(() => {
    const loadGeoData = async () => {
      try {
        const [landResponse, countriesResponse] = await Promise.all([
          fetch('/welcomeEarthLandData/ne_50m_land.json'),
          fetch('/welcomeEarthLandData/ne_50m_admin_0_countries.json')
        ]);

        if (landResponse.ok && countriesResponse.ok) {
          const landData = await landResponse.json();
          const countriesData = await countriesResponse.json();
          
          setLandData(landData);
          setCountriesData(countriesData);
        }
      } catch (error) {
        console.error('Error loading geo data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadGeoData();
  }, []);

  // 地理坐标转换为 SVG 路径的函数
  const geoToSvgPath = (geometry) => {
    if (!geometry || !geometry.coordinates) return '';
    
    const projection = (coord) => {
      const [lon, lat] = coord;
      const x = ((lon + 180) / 360) * 800;
      const y = ((90 - lat) / 180) * 400;
      return [x, y];
    };

    const coordsToPath = (coords) => {
      return coords.map((coord, index) => {
        const [x, y] = projection(coord);
        return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
      }).join(' ') + ' Z';
    };

    if (geometry.type === 'Polygon') {
      return geometry.coordinates.map(coordsToPath).join(' ');
    } else if (geometry.type === 'MultiPolygon') {
      return geometry.coordinates.map(polygon => 
        polygon.map(coordsToPath).join(' ')
      ).join(' ');
    }
    
    return '';
  };

  // 鼠标事件处理函数
  const handleCountryMouseEnter = (e, feature, index) => {
    const countryName = feature.properties?.NAME || feature.properties?.name || `Country ${index}`;
    setHoveredCountry({ name: countryName, index });
    
    const svg = e.currentTarget.closest('svg');
    const rect = svg.getBoundingClientRect();
    setTooltipPosition({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const handleCountryMouseLeave = () => {
    setHoveredCountry(null);
  };

  const handleCountryMouseMove = (e) => {
    if (hoveredCountry) {
      const svg = e.currentTarget.closest('svg');
      const rect = svg.getBoundingClientRect();
      setTooltipPosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  // ... (rest of the component logic)

  return (
    <div 
      className="geo-visualization"
      style={{
        background: 'transparent',
        border: 'none',
        boxShadow: 'none'
      }}
    >
      <h3 className="geo-viz-title">
        {isEnglish ? 'GLOBAL INDUSTRIAL NETWORK' : '全球工业网络'}
      </h3>

      {/* Override default background to prevent unintended colored band */}
      <div className="world-map-container" style={{ background: 'transparent' }}>
        <svg
          viewBox="0 0 800 400"
          className="world-map-svg"
          preserveAspectRatio="xMidYMid meet"
        >
          {/* 增强的大陆板块渐变定义 */}
          <defs>
            {/* 大陆板块主要渐变 - 改进颜色方案 */}
            <linearGradient id="landGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(36, 164, 216, 0.6)" />
              <stop offset="50%" stopColor="rgba(48, 141, 196, 0.5)" />
              <stop offset="100%" stopColor="rgba(36, 121, 168, 0.6)" />
            </linearGradient>

            {/* 高亮区域光效 */}
            <radialGradient id="activeGlow" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#00e5ff" stopOpacity="0.6" />
              <stop offset="100%" stopColor="#00e5ff" stopOpacity="0" />
            </radialGradient>

            {/* 脉冲点光效 */}
            <radialGradient id="pulseGlow" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#00ffaa" stopOpacity="0.8" />
              <stop offset="100%" stopColor="#00e5ff" stopOpacity="0" />
            </radialGradient>

            {/* 大陆边缘发光效果滤镜 */}
            <filter id="landGlow" x="-15%" y="-15%" width="130%" height="130%">
              <feGaussianBlur stdDeviation="2" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>

            {/* 数据流动画效果 */}
            <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
              <feGaussianBlur stdDeviation="2" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
          </defs>

          {/* 渲染陆地 - 增强的大陆板块效果 */}
          {landData && landData.features && landData.features.map((feature, index) => (
            <path
              key={`land-${index}`}
              d={geoToSvgPath(feature.geometry)}
              fill="url(#landGradient)"
              stroke="rgba(77, 200, 255, 0.4)"
              strokeWidth="0.4"
              className="land-path"
              filter="url(#landGlow)"
            />
          ))}

          {/* 渲染国家边界和重点区域 */}
          {countriesData && countriesData.features && countriesData.features.map((feature, index) => {
            const isHovered = hoveredCountry && hoveredCountry.index === index;
            const isHighlighted = highlightedCountries.has(index);

            return (
              <g key={`country-${index}`}>
                <path
                  d={geoToSvgPath(feature.geometry)}
                  fill={isHovered ? "rgba(77, 200, 255, 0.25)" :
                        isHighlighted ? "rgba(0, 229, 255, 0.15)" : "transparent"}
                  stroke={isHovered ? "rgba(77, 200, 255, 0.8)" : "rgba(77, 200, 255, 0.3)"}
                  strokeWidth={isHovered ? "0.8" : "0.2"}
                  className={`country-path ${isHighlighted ? 'highlighted' : ''} ${isHovered ? 'hovered' : ''}`}
                  style={{ cursor: 'pointer', transition: 'all 0.2s ease' }}
                  onMouseEnter={(e) => handleCountryMouseEnter(e, feature, index)}
                  onMouseLeave={handleCountryMouseLeave}
                  onMouseMove={handleCountryMouseMove}
                />

                {/* 对高亮国家添加脉冲效果 */}
                {isHighlighted && (
                  <path
                    d={geoToSvgPath(feature.geometry)}
                    fill="transparent"
                    stroke="rgba(0, 229, 255, 0.6)"
                    strokeWidth={0.6 + pulseIntensity * 0.8}
                    className="country-pulse"
                    opacity={0.7 - pulseIntensity * 0.3}
                    style={{ pointerEvents: 'none' }}
                  />
                )}

                {/* 悬停时的额外发光效果 */}
                {isHovered && (
                  <path
                    d={geoToSvgPath(feature.geometry)}
                    fill="transparent"
                    stroke="rgba(77, 200, 255, 0.4)"
                    strokeWidth="1.5"
                    className="country-hover-glow"
                    style={{
                      pointerEvents: 'none',
                      filter: 'blur(1px)',
                      opacity: 0.6
                    }}
                  />
                )}
              </g>
            );
          })}

          {/* 工业热点 - 保留但不连接 */}
          {industrialHotspots.map(hotspot => (
            <g key={`hotspot-${hotspot.id}`} className="industrial-hotspot">
              {/* 静态圆点 */}
              <circle
                cx={hotspot.x}
                cy={hotspot.y}
                r={5}
                fill={activeHotspots.has(hotspot.id) ? "#00ffaa" : "#00a8ff"}
                className="hotspot-dot"
              />

              {/* 动态脉冲效果 - 对所有热点 */}
              {activeHotspots.has(hotspot.id) && (
                <>
                  <circle
                    cx={hotspot.x}
                    cy={hotspot.y}
                    r={5 + pulseIntensity * 15}
                    fill="url(#pulseGlow)"
                    opacity={0.7 - pulseIntensity * 0.5}
                    className="hotspot-pulse"
                  />
                  <circle
                    cx={hotspot.x}
                    cy={hotspot.y}
                    r={2.5}
                    fill="#ffffff"
                    className="hotspot-core"
                  />
                </>
              )}

              {/* 热点标签 */}
              <text
                x={hotspot.x}
                y={hotspot.y - 12}
                textAnchor="middle"
                fill="#ffffff"
                fontSize="9"
                opacity={activeHotspots.has(hotspot.id) ? 1 : 0.5}
                className="hotspot-label"
                filter="url(#glow)"
              >
                {hotspot.label}
              </text>
            </g>
          ))}
          {/* 国家名称工具提示 */}
          {hoveredCountry && (() => {
            const tooltipWidth = Math.max(120, hoveredCountry.name.length * 8 + 40);
            const tooltipHeight = 45;
            const isAbove = tooltipPosition.y > 50; // 判断工具提示是否在鼠标上方

            // 将 tooltip 尺寸从像素转换为 viewBox 单位，保持与 x/y 一致
            const svgEl = document.querySelector('.world-map-svg');
            let widthUnits = tooltipWidth;
            let heightUnits = tooltipHeight;
            if (svgEl) {
              const svgRect = svgEl.getBoundingClientRect();
              const viewBoxAttr = svgEl.getAttribute('viewBox') || '0 0 800 400';
              const [, , vbWidthStr, vbHeightStr] = viewBoxAttr.split(' ');
              const vbWidth = parseFloat(vbWidthStr);
              const vbHeight = parseFloat(vbHeightStr);
              const scaleX = vbWidth / svgRect.width;
              const scaleY = vbHeight / svgRect.height;
              widthUnits = tooltipWidth * scaleX;
              heightUnits = tooltipHeight * scaleY;
            }

            return (
              <g className="country-tooltip" style={{ pointerEvents: 'none' }}>
                <foreignObject
                  x={tooltipPosition.x}
                  y={tooltipPosition.y}
                  width={widthUnits}
                  height={heightUnits}
                  style={{ overflow: 'visible' }}
                >
                  <div
                    style={{
                      background: 'rgba(15, 25, 40, 0.95)',
                      color: '#ffffff',
                      padding: '10px 16px',
                      borderRadius: '12px',
                      fontSize: '14px',
                      fontFamily: 'Inter, sans-serif',
                      fontWeight: '600',
                      textAlign: 'center',
                      whiteSpace: 'nowrap',
                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(77, 200, 255, 0.3)',
                      backdropFilter: 'blur(12px)',
                      border: '1px solid rgba(77, 200, 255, 0.3)',
                      position: 'relative',
                      animation: 'tooltipFadeIn 0.25s ease-out',
                      minWidth: '100px',
                      maxWidth: '200px'
                    }}
                  >
                    {/* 背景光晕效果 */}
                    <div style={{
                      position: 'absolute',
                      top: '-2px',
                      left: '-2px',
                      right: '-2px',
                      bottom: '-2px',
                      background: 'linear-gradient(135deg, rgba(77, 200, 255, 0.15), rgba(77, 200, 255, 0.05), rgba(140, 123, 255, 0.1))',
                      borderRadius: '12px',
                      zIndex: -1,
                      filter: 'blur(1px)'
                    }} />

                    {/* 内容区域 */}
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      position: 'relative',
                      zIndex: 1
                    }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        background: 'linear-gradient(45deg, #4dc8ff, #74b9ff)',
                        boxShadow: '0 0 8px rgba(77, 200, 255, 0.8)',
                        animation: 'pulse 2s ease-in-out infinite'
                      }} />
                      <span style={{
                        textShadow: '0 0 10px rgba(77, 200, 255, 0.3)',
                        letterSpacing: '0.5px'
                      }}>
                        {hoveredCountry.name}
                      </span>
                    </div>

                    {/* 动态箭头 - 根据位置调整方向 */}
                    <div style={{
                      position: 'absolute',
                      [isAbove ? 'bottom' : 'top']: '-7px',
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '0',
                      height: '0',
                      borderLeft: '8px solid transparent',
                      borderRight: '8px solid transparent',
                      [isAbove ? 'borderTop' : 'borderBottom']: '8px solid rgba(15, 25, 40, 0.95)',
                      filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))'
                    }} />
                  </div>
                </foreignObject>
              </g>
            );
          })()}
        </svg>


      </div>
    </div>
  );
});

// Starry Button Component - 优化版本
const StarryButton = memo(({ children, onClick, className = '' }) => {
  const canvasRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const particles = useRef([]);
  const animationId = useRef();
  const mousePos = useRef({ x: 0, y: 0 });
  const hoverEffectIntensityRef = useRef(0);

  // 缓存粒子配置
  const particleConfig = useMemo(() => ({
    densityFactor: 800,
    sizeRange: { min: 0.1, max: 1.1 },
    densityRange: { min: 2, max: 12 },
    velocityRange: 0.1,
    colorHue: { min: 200, max: 260 },
    colorSaturation: 80,
    colorLightness: { min: 70, max: 100 },
    colorAlpha: { min: 0.3, max: 0.6 }
  }), []);

  // Initialize canvas and particles
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationFrameId;

    // Set canvas dimensions
    const updateDimensions = () => {
      const rect = canvas.parentElement.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      // Only update if dimensions changed significantly
      if (Math.abs(dimensions.width - width) > 1 || Math.abs(dimensions.height - height) > 1) {
        setDimensions({ width, height });
        canvas.width = width * 2; // For retina displays
        canvas.height = height * 2;
        canvas.style.width = `${width}px`;
        canvas.style.height = `${height}px`;

        // Initialize particles
        initParticles(width * 2, height * 2);
      }
    };

    // Initialize particles
    const initParticles = (width, height) => {
      // 增加粒子数量以提高密度和精致感
      const particleCount = Math.floor((width * height) / 800); // 调整密度
      particles.current = [];

      for (let i = 0; i < particleCount; i++) {
        particles.current.push({
          x: Math.random() * width,
          y: Math.random() * height,
          // 调整粒子大小范围，使其更小且变化更微妙
          size: Math.random() * 1 + 0.1, // 调整大小范围
          baseX: Math.random() * width,
          baseY: Math.random() * height,
          // 微调密度和速度，使其更柔和
          density: Math.random() * 10 + 2, // 调整密度范围
          velocityX: (Math.random() - 0.5) * 0.1, // 调整初始速度
          velocityY: (Math.random() - 0.5) * 0.1,
          // 调整颜色范围和透明度，使其偏蓝紫色且更透明柔和
          color: `hsla(${Math.random() * 60 + 200}, 80%, ${Math.random() * 30 + 70}%, ${Math.random() * 0.3 + 0.3})` // 调整颜色和透明度
        });
      }
    };

    // Animation loop with requestAnimationFrame timestamp
    let lastTime = 0;
    const animate = (currentTime) => {
      // Calculate delta time for frame-rate independent animation
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      // Use a multiplier to adjust speed based on frame rate
      const frameMultiplier = Math.min(deltaTime / 16, 2); // Cap at 2x speed for very low frame rates

      // 平滑更新悬停效果强度
      if (isHovered) {
        hoverEffectIntensityRef.current = Math.min(1, hoverEffectIntensityRef.current + 0.05 * frameMultiplier);
      } else {
        hoverEffectIntensityRef.current = Math.max(0, hoverEffectIntensityRef.current - 0.05 * frameMultiplier);
      }

      // 清理画布，增加透明度以形成更长的拖尾效果
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'; // 增加透明度
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.current.forEach(particle => {
        // Calculate distance from mouse
        const dx = mousePos.current.x * 2 - particle.x;
        const dy = mousePos.current.y * 2 - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Calculate force direction
        const forceDirectionX = dx / (distance || 1); // Avoid division by zero
        const forceDirectionY = dy / (distance || 1);

        // Calculate force magnitude with smoother falloff, 使用平滑强度
        let force = 0;
        // 增大交互半径，使粒子更早感知鼠标
        const maxDistance = 200; // 增大交互半径
        if (distance < maxDistance) {
          // Smoother force falloff using easeOutCubic
          const normalizedDistance = distance / maxDistance;
          // 增强力的强度，并乘以悬停效果强度
          force = Math.pow(1 - normalizedDistance, 3) * 0.35 * hoverEffectIntensityRef.current; // 增强力的强度和衰减曲线
        }

        // Apply force with velocity for smoother movement
        particle.velocityX = (particle.velocityX * 0.85) + (forceDirectionX * force * particle.density * 0.1); // 调整速度衰减
        particle.velocityY = (particle.velocityY * 0.85) + (forceDirectionY * force * particle.density * 0.1);

        // Add minimal random movement for subtle organic feel
        particle.velocityX += (Math.random() - 0.5) * 0.003; // 大幅降低随机速度
        particle.velocityY += (Math.random() - 0.5) * 0.003; // 大幅降低随机速度

        // Apply friction
        particle.velocityX *= 0.95; // 增加摩擦力
        particle.velocityY *= 0.95; // 增加摩擦力

        // Update position with stronger easing and velocity
        particle.x += ((particle.baseX - particle.x) * 0.03 + particle.velocityX) * frameMultiplier; // 增加回弹速度
        particle.y += ((particle.baseY - particle.y) * 0.03 + particle.velocityY) * frameMultiplier; // 增加回弹速度

        // Keep particles within bounds
        if (particle.x < 0) particle.x = 0;
        if (particle.x > canvas.width) particle.x = canvas.width;
        if (particle.y < 0) particle.y = 0;
        if (particle.y > canvas.height) particle.y = canvas.height;

        // Draw particle with subtle pulsing effect，使用平滑强度控制大小变化
        const pulse = Math.sin(Date.now() * 0.002 + particle.x * 0.05) * 0.2 + 0.8; // 调整脉冲频率和强度
        // 大小变化也乘以悬停效果强度，并确保基础大小
        const size = particle.size * (1 + pulse * 0.5 * hoverEffectIntensityRef.current); // 调整悬停时的放大倍数

        // Create gradient for particle glow
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, size * 2 // 保持发光半径与粒子大小关联
        );

        // 调整发光和核心的透明度，使用平滑强度
        const alpha = 0.4 + (isHovered ? 0.5 : 0) * hoverEffectIntensityRef.current; // 调整悬停和非悬停时的透明度
        gradient.addColorStop(0, particle.color);
        gradient.addColorStop(1, 'rgba(0,0,0,0)');

        // Draw glow
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, size * 2, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();

        // Draw particle core
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
      });

      animationId.current = requestAnimationFrame(animate);
    };

    // Initial setup
    updateDimensions();
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(canvas.parentElement);

    // Start animation
    animationId.current = requestAnimationFrame(animate);

    // Cleanup
    return () => {
      cancelAnimationFrame(animationId.current);
      resizeObserver.disconnect();
    };
  }, [dimensions, isHovered]);

  // Handle mouse move
  const handleMouseMove = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    mousePos.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  return (
    <button
      className={`starry-button ${className}`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseMove={handleMouseMove}
    >
      <canvas ref={canvasRef} className="starry-button-canvas" />
      <span className="starry-button-text">{children}</span>
    </button>
  );
});

// 自定义地球组件 - 优化版本
const Earth = memo(({ scale = 1.5, position = [2.8, 0, 0] }) => {
  const earthRef = useRef();
  const cloudsRef = useRef();

  // 加载地球纹理 - 缓存纹理路径
  const texturePaths = useMemo(() => [
    '/textures/earth/earthmap1k.jpg',
    '/textures/earth/earth_normal_1k.jpg',
    '/textures/earth/earthspec1k.jpg',
    '/textures/earth/earthclouds.png'
  ], []);

  const [colorMap, normalMap, specularMap, cloudsMap] = useTexture(texturePaths);

  // 缓存材质属性
  const earthMaterialProps = useMemo(() => ({
    map: colorMap,
    normalMap: normalMap,
    specularMap: specularMap,
    shininess: 25,
    specular: new THREE.Color('#8af'),
    emissive: new THREE.Color('#003366'),
    emissiveIntensity: 0.15
  }), [colorMap, normalMap, specularMap]);

  const cloudsMaterialProps = useMemo(() => ({
    map: cloudsMap,
    transparent: true,
    opacity: 0.35,
    depthWrite: false,
    side: THREE.DoubleSide
  }), [cloudsMap]);

  // 缓存几何体参数
  const sphereGeometryArgs = useMemo(() => [1, 64, 64], []);
  const groupProps = useMemo(() => ({
    position,
    scale: [scale, scale, scale],
    rotation: [0, -Math.PI/4, 0]
  }), [position, scale]);

  // 优化旋转动画 - 使用GPU加速的transform
  useFrame(({ clock }) => {
    const elapsedTime = clock.getElapsedTime();
    if (earthRef.current) {
      earthRef.current.rotation.y = elapsedTime * 0.05;
    }
    if (cloudsRef.current) {
      cloudsRef.current.rotation.y = elapsedTime * 0.055;
    }
  });

  return (
    <group {...groupProps}>
      {/* 地球本体 */}
      <mesh ref={earthRef} receiveShadow>
        <sphereGeometry args={sphereGeometryArgs} />
        <meshPhongMaterial {...earthMaterialProps} />
      </mesh>

      {/* 云层 */}
      <mesh ref={cloudsRef} scale={[1.01, 1.01, 1.01]}>
        <sphereGeometry args={sphereGeometryArgs} />
        <meshPhongMaterial {...cloudsMaterialProps} />
      </mesh>
    </group>
  );
});

// 增强版星空背景 - 优化版本
const EnhancedStars = memo(() => {
  // 缓存星空配置
  const farStarsConfig = useMemo(() => ({
    radius: 300,
    depth: 100,
    count: 5000,
    factor: 4,
    saturation: 0,
    fade: true,
    speed: 0.5
  }), []);

  const nearStarsConfig = useMemo(() => ({
    radius: 100,
    depth: 50,
    count: 3000,
    factor: 5,
    saturation: 0,
    fade: true,
    speed: 0.5
  }), []);

  return (
    <>
      {/* 远距离星空 */}
      <Stars {...farStarsConfig} />
      {/* 近距离星空，增加密度 */}
      <Stars {...nearStarsConfig} />
    </>
  );
});

// 地理智能选址主题加载屏幕组件
const LoadingScreen = memo(() => {
  const currentLang = useMemo(() => localStorage.getItem('preferredLanguage') || 'en', []);
  const isEnglish = useMemo(() => currentLang === 'en', [currentLang]);
  const [progress, setProgress] = useState(0);
  const [currentPhase, setCurrentPhase] = useState(0);
  const [coordinates, setCoordinates] = useState({ lat: 39.8283, lng: -98.5795 });
  const [scanAngle, setScanAngle] = useState(0);

  // 地理选址相关的加载阶段
  const loadingPhases = useMemo(() => isEnglish ? [
    'Initializing Geographic Engine',
    'Loading Satellite Data',
    'Analyzing Industrial Zones',
    'Calculating Site Scores',
    'Preparing Map Interface'
  ] : [
    '初始化地理引擎',
    '加载卫星数据',
    '分析工业区域',
    '计算选址评分',
    '准备地图界面'
  ], [isEnglish]);

  const subtitle = useMemo(() => isEnglish ? 'Intelligent Site Selection' : '智能选址系统', [isEnglish]);

  // 模拟加载进度
  useEffect(() => {
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 8;
      });
    }, 200);

    return () => clearInterval(progressInterval);
  }, []);

  // 加载阶段切换
  useEffect(() => {
    const phaseInterval = setInterval(() => {
      setCurrentPhase(prev => (prev + 1) % loadingPhases.length);
    }, 1800);

    return () => clearInterval(phaseInterval);
  }, [loadingPhases.length]);

  // 模拟坐标变化
  useEffect(() => {
    const coordInterval = setInterval(() => {
      setCoordinates(prev => ({
        lat: prev.lat + (Math.random() - 0.5) * 0.1,
        lng: prev.lng + (Math.random() - 0.5) * 0.1
      }));
    }, 800);

    return () => clearInterval(coordInterval);
  }, []);

  // 雷达扫描角度
  useEffect(() => {
    const scanInterval = setInterval(() => {
      setScanAngle(prev => (prev + 2) % 360);
    }, 50);

    return () => clearInterval(scanInterval);
  }, []);

  return (
    <div className="geo-loading-screen">
      {/* 地图网格背景 */}
      <div className="map-grid-bg"></div>

      {/* 主要内容 */}
      <div className="geo-loading-content">
        {/* 雷达扫描器 */}
        <div className="radar-scanner">
          <div className="radar-circle radar-outer"></div>
          <div className="radar-circle radar-middle"></div>
          <div className="radar-circle radar-inner"></div>
          <div
            className="radar-sweep"
            style={{ transform: `rotate(${scanAngle}deg)` }}
          ></div>
          <div className="radar-center"></div>

          {/* 地理标记点 */}
          <div className="geo-markers">
            <div className="geo-marker marker-1"></div>
            <div className="geo-marker marker-2"></div>
            <div className="geo-marker marker-3"></div>
            <div className="geo-marker marker-4"></div>
          </div>
        </div>

        {/* 地理信息显示 */}
        <div className="geo-info-panel">
          <h1 className="geo-title">{subtitle}</h1>
          <div className="loading-phase">
            {loadingPhases[currentPhase]}
          </div>

          {/* 坐标显示 */}
          <div className="coordinates-display">
            <div className="coord-item">
              <span className="coord-label">LAT:</span>
              <span className="coord-value">{coordinates.lat.toFixed(4)}°</span>
            </div>
            <div className="coord-item">
              <span className="coord-label">LNG:</span>
              <span className="coord-value">{coordinates.lng.toFixed(4)}°</span>
            </div>
          </div>

          {/* 进度条 */}
          <div className="geo-progress">
            <div className="progress-container">
              <div
                className="progress-bar"
                style={{ width: `${Math.min(progress, 100)}%` }}
              ></div>
            </div>
            <div className="progress-info">
              <span className="progress-percent">{Math.floor(Math.min(progress, 100))}%</span>
              <span className="progress-label">
                {isEnglish ? 'ANALYZING LOCATIONS' : '分析位置中'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 地图装饰元素 */}
      <div className="map-decorations">
        <div className="map-pin pin-1"></div>
        <div className="map-pin pin-2"></div>
        <div className="map-pin pin-3"></div>
        <div className="coordinate-lines">
          <div className="coord-line horizontal"></div>
          <div className="coord-line vertical"></div>
        </div>
      </div>
    </div>
  );

});

// 团队成员组件 - 优化版本
const TeamMember = memo(({ member, isActive, onNext, onPrev }) => {
  // 缓存事件处理器
  const handlePrevClick = useCallback(() => onPrev(), [onPrev]);
  const handleNextClick = useCallback(() => onNext(), [onNext]);

  // 缓存类名
  const slideClassName = useMemo(() =>
    `team-member-slide ${isActive ? 'active' : ''}`, [isActive]);
  const roleClassName = useMemo(() =>
    `member-role ${isActive ? 'animate-text' : ''}`, [isActive]);
  const nameClassName = useMemo(() =>
    `member-name ${isActive ? 'animate-text' : ''}`, [isActive]);
  const socialClassName = useMemo(() =>
    `member-social ${isActive ? 'animate-fade-in' : ''}`, [isActive]);
  return (
    <div className={slideClassName}>
      <div className="team-member-content">
        <div className="member-info">
          <div className={roleClassName}>{member.role}</div>
          <h3 className={nameClassName}>{member.name}</h3>
          <div className={socialClassName}>
            {member.twitter && (
              <a href={member.twitter} target="_blank" rel="noopener noreferrer" className="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M23 3.01006C23 3.01006 20.9821 4.20217 19.86 4.54006C19.2577 3.84757 18.4573 3.35675 17.567 3.13398C16.6767 2.91122 15.7395 2.96725 14.8821 3.29451C14.0247 3.62177 13.2884 4.20446 12.773 4.96377C12.2575 5.72309 11.9877 6.62239 12 7.54006V8.54006C10.2426 8.58562 8.50127 8.19587 6.93101 7.4055C5.36074 6.61513 4.01032 5.44869 3 4.01006C3 4.01006 -1 13.0101 8 17.0101C5.94053 18.408 3.48716 19.109 1 19.0101C10 24.0101 21 19.0101 21 7.51006C20.9991 7.23151 20.9723 6.95365 20.92 6.68006C21.9406 5.67355 23 3.01006 23 3.01006Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            )}
            {member.linkedin && (
              <a href={member.linkedin} target="_blank" rel="noopener noreferrer" className="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M6 9H2V21H6V9Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            )}
          </div>
        </div>
        <div className="member-photo-container">
          <img src={member.image} alt={member.name} className="member-photo" />
        </div>
      </div>
      <div className="team-navigation">
        <button className="nav-button prev-button" onClick={handlePrevClick} aria-label="Previous team member">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <button className="nav-button next-button" onClick={handleNextClick} aria-label="Next team member">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  );
});

// 优化的粒子地球组件 - 改进纹理映射和性能
const SimpleParticleEarth = memo(({ scale = 1.6, position = [2.8, 0, 0] }) => {
  const pointsRef = useRef();
  const numPoints = 18000; // 增加粒子数量以获得更好的视觉效果
  const earthTexture = useTexture('/textures/earth/earthmap1k.jpg');

  // 缓存材质属性
  const pointsMaterialProps = useMemo(() => ({
    vertexColors: true,
    transparent: true,
    opacity: 0.85,
    sizeAttenuation: true,
    depthWrite: false,
    size: 0.012 // 稍微减小粒子大小以获得更精细的效果
  }), []);

  const [particlePositions, particleColors] = useMemo(() => {
    const positions = new Float32Array(numPoints * 3);
    const colors = new Float32Array(numPoints * 3);
    const tempCanvas = document.createElement('canvas');
    const ctx = tempCanvas.getContext('2d');
    const radius = 1;

    // 当纹理加载完成时
    if (earthTexture && earthTexture.image) {
      tempCanvas.width = earthTexture.image.width;
      tempCanvas.height = earthTexture.image.height;
      ctx.drawImage(earthTexture.image, 0, 0);

      const imageData = ctx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
      const data = imageData.data;

      let particleCount = 0;

      // 使用纹理数据创建点的分布
      for (let i = 0; particleCount < numPoints && i < numPoints * 4; i++) {
        // 生成随机球体上的点
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        const x = radius * Math.sin(phi) * Math.cos(theta);
        const y = radius * Math.sin(phi) * Math.sin(theta);
        const z = radius * Math.cos(phi);

        // 计算UV坐标以采样纹理
        const u = 0.5 + Math.atan2(x, z) / (2 * Math.PI);
        const v = 0.5 - Math.asin(y) / Math.PI;

        const col = Math.floor(u * tempCanvas.width);
        const row = Math.floor(v * tempCanvas.height);

        const pixelIndex = (row * tempCanvas.width + col) * 4;
        const r = data[pixelIndex] / 255;
        const g = data[pixelIndex + 1] / 255;
        const b = data[pixelIndex + 2] / 255;
        const brightness = (r + g + b) / 3;

        // 改进的海洋/陆地识别逻辑
        const blueness = b - (r + g) / 2;
        const isOcean = blueness > 0.1 && brightness < 0.7;

        // 根据亮度和颜色决定是否放置粒子
        const threshold = 0.3; // 使用归一化阈值
        if (brightness > threshold || Math.random() > 0.95) {
          positions[particleCount * 3] = x;
          positions[particleCount * 3 + 1] = y;
          positions[particleCount * 3 + 2] = z;

          if (isOcean) {
            // 海洋区域 - 更自然的蓝色渐变
            colors[particleCount * 3] = Math.max(0.1, r * 0.4);
            colors[particleCount * 3 + 1] = Math.max(0.3, g * 0.6);
            colors[particleCount * 3 + 2] = Math.max(0.6, b * 0.9 + 0.1);
          } else {
            // 陆地区域 - 保持原始纹理颜色但增强对比度
            colors[particleCount * 3] = Math.min(1.0, r * 1.1);
            colors[particleCount * 3 + 1] = Math.min(1.0, g * 1.1);
            colors[particleCount * 3 + 2] = Math.min(1.0, b * 0.9 + 0.1);
          }

          particleCount++;
        }
      }

      // 如果没有生成足够的粒子，填充剩余的
      while (particleCount < numPoints) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        positions[particleCount * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[particleCount * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[particleCount * 3 + 2] = radius * Math.cos(phi);

        colors[particleCount * 3] = 0.8;     // R
        colors[particleCount * 3 + 1] = 0.9; // G
        colors[particleCount * 3 + 2] = 1.0; // B

        particleCount++;
      }
    } else {
      // 如果纹理尚未加载，创建均匀分布的点
      for (let i = 0; i < numPoints; i++) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[i * 3 + 2] = radius * Math.cos(phi);

        colors[i * 3] = 0.8;     // R
        colors[i * 3 + 1] = 0.9; // G
        colors[i * 3 + 2] = 1.0; // B
      }
    }

    return [positions, colors];
  }, [numPoints, earthTexture]);

  // 优化的旋转动画 - 更平滑的旋转和微妙的摆动
  useFrame(({ clock }) => {
    const elapsedTime = clock.getElapsedTime();
    if (pointsRef.current) {
      // 地球旋转 - 稍微加快旋转速度
      pointsRef.current.rotation.y = elapsedTime * 0.08;
      // 添加微妙的倾斜摆动
      pointsRef.current.rotation.x = Math.sin(elapsedTime * 0.3) * 0.05;
      pointsRef.current.rotation.z = Math.cos(elapsedTime * 0.2) * 0.02;
    }
  });

  return (
    <group position={position} scale={[scale, scale, scale]} rotation={[0, 0, 0]}>
      <points ref={pointsRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={numPoints}
            array={particlePositions}
            itemSize={3}
          />
          <bufferAttribute
            attach="attributes-color"
            count={numPoints}
            array={particleColors}
            itemSize={3}
          />
        </bufferGeometry>
        <pointsMaterial {...pointsMaterialProps} />
      </points>

      {/* 添加发光效果 */}
      <mesh scale={[1.01, 1.01, 1.01]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#0a4677"
          transparent={true}
          opacity={0.03}
          side={THREE.BackSide}
        />
      </mesh>
    </group>
  );
});

// 原始的粒子地球组件 - 带有蓝点和连接线（用于工业园区模块）
const OriginalParticleEarth = memo(({ scale = 2, position = [0, 0, 0] }) => {
  const pointsRef = useRef();
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2(1, 1)); // 初始化在视图外
  const [hovered, setHovered] = useState(false);
  const numPoints = 25000; // 增加粒子数量
  const earthTexture = useTexture('/textures/earth/earthmap1k.jpg');
  // 添加用于粒子交互的原始尺寸记录
  const pointSizesRef = useRef();
  // 添加连接点脉冲状态
  const [pulseFactor, setPulseFactor] = useState(0);

  // 缓存组件配置
  const particleConfig = useMemo(() => ({
    numPoints,
    defaultSize: 0.02,
    interactionRadius: 0.8,
    maxSizeFactor: 3
  }), [numPoints]);

  // 缓存材质属性
  const pointsMaterialProps = useMemo(() => ({
    vertexColors: true,
    transparent: true,
    opacity: 0.8,
    sizeAttenuation: true,
    depthWrite: false
  }), []);

  const [particlePositions, particleColors] = useMemo(() => {
    const positions = new Float32Array(numPoints * 3);
    const colors = new Float32Array(numPoints * 3);
    const tempCanvas = document.createElement('canvas');
    const ctx = tempCanvas.getContext('2d');
    const radius = 1;

    // 当纹理加载完成时
    if (earthTexture && earthTexture.image) {
      tempCanvas.width = earthTexture.image.width;
      tempCanvas.height = earthTexture.image.height;
      ctx.drawImage(earthTexture.image, 0, 0);

      const imageData = ctx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
      const data = imageData.data;

      let particleCount = 0;

      // 使用纹理数据创建点的分布
      for (let i = 0; particleCount < numPoints && i < numPoints * 4; i++) {
        // 生成随机球体上的点
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        const x = radius * Math.sin(phi) * Math.cos(theta);
        const y = radius * Math.sin(phi) * Math.sin(theta);
        const z = radius * Math.cos(phi);

        // 计算UV坐标以采样纹理
        const u = 0.5 + Math.atan2(x, z) / (2 * Math.PI);
        const v = 0.5 - Math.asin(y) / Math.PI;

        const col = Math.floor(u * tempCanvas.width);
        const row = Math.floor(v * tempCanvas.height);

        const pixelIndex = (row * tempCanvas.width + col) * 4;
        const brightness = (data[pixelIndex] + data[pixelIndex + 1] + data[pixelIndex + 2]) / 3;

        // 根据亮度决定是否放置粒子
        const threshold = 80; // 阈值调整
        if (brightness > threshold || Math.random() > 0.97) {
          positions[particleCount * 3] = x;
          positions[particleCount * 3 + 1] = y;
          positions[particleCount * 3 + 2] = z;

          // 海洋区域为蓝色，陆地区域为白色
          if (brightness <= threshold) {
            colors[particleCount * 3] = 0.1;
            colors[particleCount * 3 + 1] = 0.5;
            colors[particleCount * 3 + 2] = 1.0;
          } else {
            colors[particleCount * 3] = 1.0;
            colors[particleCount * 3 + 1] = 1.0;
            colors[particleCount * 3 + 2] = 1.0;
          }

          particleCount++;
        }
      }

      // 如果没有生成足够的粒子，填充剩余的
      while (particleCount < numPoints) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        positions[particleCount * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[particleCount * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[particleCount * 3 + 2] = radius * Math.cos(phi);

        colors[particleCount * 3] = 0.8;     // R
        colors[particleCount * 3 + 1] = 0.9; // G
        colors[particleCount * 3 + 2] = 1.0; // B

        particleCount++;
      }
    } else {
      // 如果纹理尚未加载，创建均匀分布的点
      for (let i = 0; i < numPoints; i++) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[i * 3 + 2] = radius * Math.cos(phi);

        colors[i * 3] = 0.8;     // R
        colors[i * 3 + 1] = 0.9; // G
        colors[i * 3 + 2] = 1.0; // B
      }
    }

    return [positions, colors];
  }, [numPoints, earthTexture]);

  // 初始化粒子大小数组以便后续操作
  useEffect(() => {
    if (pointsRef.current) {
      // 创建一个新的Float32Array来存储每个粒子的原始大小
      const particleSizes = new Float32Array(numPoints);
      for (let i = 0; i < numPoints; i++) {
        particleSizes[i] = 0.02; // 默认大小改为更小的0.02
      }

      // 将大小数组添加为缓冲区属性
      const geometry = pointsRef.current.geometry;
      geometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

      // 保存引用以便后续更新
      pointSizesRef.current = geometry.attributes.size;
    }
  }, [numPoints]);

  // 用于监听全局鼠标事件
  useEffect(() => {
    // 创建全局鼠标移动事件监听器
    const handleGlobalMouseMove = (event) => {
      // 获取画布元素
      const canvas = document.querySelector('.holographic-earth-container canvas');
      if (!canvas) return;

      // 计算鼠标相对于画布的正规化坐标(-1至1)
      const rect = canvas.getBoundingClientRect();
      const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // 检查鼠标是否在画布内
      if (
        event.clientX >= rect.left &&
        event.clientX <= rect.right &&
        event.clientY >= rect.top &&
        event.clientY <= rect.bottom
      ) {
        mouseRef.current.set(x, y);
        setHovered(true);
        setPointerPosition([event.clientX - rect.left, event.clientY - rect.top]);
      } else {
        setHovered(false);
      }
    };

    // 全局鼠标离开事件
    const handleGlobalMouseLeave = () => {
      setHovered(false);
    };

    // 添加全局事件监听器
    window.addEventListener('mousemove', handleGlobalMouseMove);
    window.addEventListener('mouseleave', handleGlobalMouseLeave);

    // 清理事件监听器
    return () => {
      window.removeEventListener('mousemove', handleGlobalMouseMove);
      window.removeEventListener('mouseleave', handleGlobalMouseLeave);
    };
  }, []);

  // 旋转地球并处理鼠标交互
  useFrame(({ clock, camera }) => {
    const elapsedTime = clock.getElapsedTime();
    if (pointsRef.current) {
      // 地球旋转
      pointsRef.current.rotation.y = elapsedTime * 0.05;

      // 脉冲因子更新
      setPulseFactor(Math.sin(elapsedTime * 2) * 0.5 + 0.5);

      // 当鼠标悬停在画布上时
      if (hovered && pointSizesRef.current) {
        // 设置射线起点和方向
        raycasterRef.current.setFromCamera(mouseRef.current, camera);

        // 重置所有粒子大小
        const sizes = pointSizesRef.current.array;
        for (let i = 0; i < sizes.length; i++) {
          // 逐渐恢复到原始大小，使用0.02作为基础大小
          sizes[i] = Math.max(0.02, sizes[i] * 0.95);
        }

        // 计算与粒子的交点
        const intersects = raycasterRef.current.intersectObject(pointsRef.current, true);

        // 处理相交的粒子
        if (intersects.length > 0) {
          // 获取最近交点的位置
          const intersectPoint = intersects[0].point;

          // 影响半径(相对于地球大小)，减小影响范围
          const radius = 0.8;

          // 遍历所有粒子，检查哪些在影响半径内
          const positions = pointsRef.current.geometry.attributes.position.array;

          for (let i = 0; i < numPoints; i++) {
            // 获取粒子位置
            const px = positions[i * 3];
            const py = positions[i * 3 + 1];
            const pz = positions[i * 3 + 2];

            // 将粒子位置转换为世界坐标
            const particlePos = new THREE.Vector3(px, py, pz).applyMatrix4(pointsRef.current.matrixWorld);

            // 计算粒子到交点的距离
            const distance = particlePos.distanceTo(intersectPoint);

            // 如果在影响半径内，增加大小
            if (distance < radius) {
              // 计算大小增加因子 (越近越大)
              const sizeFactor = 1 - (distance / radius);
              // 设置新大小 (最大是原始大小的3倍)
              sizes[i] = 0.02 * (1 + sizeFactor * 2);
            }
          }

          // 标记属性需要更新
          pointSizesRef.current.needsUpdate = true;
        }
      }
    }
  });

  // 创建连接线的数据
  const connectionLines = useMemo(() => {
    // 四条线的顶点数据 - 再次精确调整到大陆位置的坐标
    const positions = [
      // 线1: 中心点到北美洲 (右上)
      0, 0, 0, -0.4, 0.6, 0.7,
      // 线2: 中心点到欧洲 (中间靠右)
      0, 0, 0, 0.7, 0.5, 0.5,
      // 线3: 中心点到亚洲 (中间)
      0, 0, 0, 0.9, -0.2, 0.4,
      // 线4: 中心点到南美洲/非洲 (左下)
      0, 0, 0, -0.6, -0.7, 0.2
    ];

    return new Float32Array(positions);
  }, []);

  return (
    <group position={position} scale={[scale, scale, scale]} rotation={[0, 0, 0]}>
      <points ref={pointsRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={numPoints}
            array={particlePositions}
            itemSize={3}
          />
          <bufferAttribute
            attach="attributes-color"
            count={numPoints}
            array={particleColors}
            itemSize={3}
          />
        </bufferGeometry>
        <pointsMaterial
          vertexColors
          transparent={true}
          opacity={0.8}
          sizeAttenuation={true}
          depthWrite={false}
          onBeforeCompile={(shader) => {
            shader.vertexShader = shader.vertexShader.replace(
              'uniform float size;',
              'attribute float size;'
            );
          }}
        />
      </points>

      {/* 添加3D蓝色连接点 - 再次精确调整位置到大陆上 */}
      {/* 北美洲连接点 */}
      <group position={[-0.4, 0.6, 0.7]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.4, 1.8 + pulseFactor * 0.4, 1.8 + pulseFactor * 0.4]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 欧洲连接点 */}
      <group position={[0.7, 0.5, 0.5]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.5, 1.8 + pulseFactor * 0.5, 1.8 + pulseFactor * 0.5]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 亚洲连接点 */}
      <group position={[0.9, -0.2, 0.4]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.3, 1.8 + pulseFactor * 0.3, 1.8 + pulseFactor * 0.3]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 南美洲/非洲连接点 */}
      <group position={[-0.6, -0.7, 0.2]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.6, 1.8 + pulseFactor * 0.6, 1.8 + pulseFactor * 0.6]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 添加连接线 - 使用渐变色和脉冲效果 */}
      <lineSegments>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            array={connectionLines}
            count={8}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color="#00c8ff" transparent opacity={0.6 - pulseFactor * 0.2} />
      </lineSegments>

      {/* 添加发光效果 */}
      <mesh scale={[1.01, 1.01, 1.01]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#0a4677"
          transparent={true}
          opacity={0.05}
          side={THREE.BackSide}
        />
      </mesh>
      <mesh scale={[1.05, 1.05, 1.05]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#4080ff"
          transparent={true}
          opacity={0.02}
          side={THREE.BackSide}
        />
      </mesh>
      <mesh scale={[1.1, 1.1, 1.1]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#00c8ff"
          transparent={true}
          opacity={0.01}
          side={THREE.BackSide}
        />
      </mesh>
    </group>
  );
});

// 修改照片展示部分代码
const TeamCarousel = forwardRef(({ teamMembers, activeTeamMember, onNext, onPrev }, ref) => {
  const containerRef = useRef(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState(null);
  // 冻结过渡期间的状态
  const [transitionState, setTransitionState] = useState({
    activeIndex: 0,
    renderedCards: []
  });

  // 准备渲染状态
  useEffect(() => {
    if (!isTransitioning) {
      // 只在非过渡状态更新渲染数据
      const renderedCards = [];
      const positions = [-2, -1, 0, 1, 2];

      positions.forEach(position => {
        const totalMembers = teamMembers.length;
        let realIndex = (activeTeamMember + position) % totalMembers;
        if (realIndex < 0) realIndex = totalMembers + realIndex;

        renderedCards.push({
          position,
          member: teamMembers[realIndex],
          index: realIndex,
          className: getPositionClassName(position)
        });
      });

      setTransitionState({
        activeIndex: activeTeamMember,
        renderedCards
      });
    }
  }, [activeTeamMember, isTransitioning, teamMembers]);

  // 获取位置对应的CSS类名
  const getPositionClassName = (position) => {
    if (position === 0) return 'active';
    if (position === 1) return 'next';
    if (position === 2) return 'far-next';
    if (position === -1) return 'prev';
    if (position === -2) return 'far-prev';
    return '';
  };

  // 监听过渡状态
  useEffect(() => {
    // 仅当方向确定时才启动过渡
    if (direction) {
      setIsTransitioning(true);

      // 设置一个计时器，在CSS动画完成后更新状态
      const animationTimer = setTimeout(() => {
        // 根据方向更新活跃成员
        if (direction === 'right') {
          onNext();
        } else {
          onPrev();
        }
        
        // 重置过渡状态
        setIsTransitioning(false);
        setDirection(null); // 清除方向，为下一次过渡做准备
      }, 400); // 这个时间必须与CSS中的过渡时间匹配

      // 清理函数，以防组件在过渡完成前被卸载
      return () => clearTimeout(animationTimer);
    }
  }, [direction, onNext, onPrev]);

  // 处理"下一个"导航
  const handleNext = () => {
    // 如果正在过渡，则不执行任何操作，防止快速连续点击
    if (isTransitioning) return;
    // 仅设置方向，这将触发useEffect来处理过渡
    setDirection('right');
  };

  // 处理"上一个"导航
  const handlePrev = () => {
    // 如果正在过渡，则不执行任何操作
    if (isTransitioning) return;
    // 仅设置方向
    setDirection('left');
  };

  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    handleNext,
    handlePrev
  }));

  return (
    <div
      className={`photos-container ${isTransitioning ? `transitioning ${direction}` : ''}`}
      ref={containerRef}
    >
      {/* 渲染所有照片卡片 */}
      {transitionState.renderedCards.map(card => (
        <div
          key={`team-card-${card.index}-${card.position}`}
          className={`team-photo-card ${card.className}`}
          style={{
            transitionDelay: `${Math.abs(card.position) * 0.03}s` // 减少延迟时间，使切换更直接
          }}
        >
          <img src={card.member.image} alt={card.member.name} className="member-photo" />

          {/* 成员信息 */}
          {card.position === 0 && (
            <div className="member-info-content">
              <div className="member-role">{card.member.role}</div>
              <h3 className="member-name">{card.member.name}</h3>
              <div className="member-social">
                {card.member.twitter && (
                  <a href={card.member.twitter} target="_blank" rel="noopener noreferrer" className="social-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M23 3.01006C23 3.01006 20.9821 4.20217 19.86 4.54006C19.2577 3.84757 18.4573 3.35675 17.567 3.13398C16.6767 2.91122 15.7395 2.96725 14.8821 3.29451C14.0247 3.62177 13.2884 4.20446 12.773 4.96377C12.2575 5.72309 11.9877 6.62239 12 7.54006V8.54006C10.2426 8.58562 8.50127 8.19587 6.93101 7.4055C5.36074 6.61513 4.01032 5.44869 3 4.01006C3 4.01006 -1 13.0101 8 17.0101C5.94053 18.408 3.48716 19.109 1 19.0101C10 24.0101 21 19.0101 21 7.51006C20.9991 7.23151 20.9723 6.95365 20.92 6.68006C21.9406 5.67355 23 3.01006 23 3.01006Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </a>
                )}
                {card.member.linkedin && (
                  <a href={card.member.linkedin} target="_blank" rel="noopener noreferrer" className="social-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M6 9H2V21H6V9Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
});

// Floating Navigation Bar Component - 优化版本
const FloatingNavigation = memo(() => {
  const navigate = useNavigate(); // 添加useNavigate hook
  
  // 使用单一状态管理当前打开的菜单（互斥逻辑）
  const [activeDropdown, setActiveDropdown] = useState(null); // 'services', 'language', 'user', null
  
  const [currentLang, setCurrentLang] = useState(() => {
    // 优先使用用户登录后的语言偏好
    const userLang = localStorage.getItem('preferredLanguage');
    return userLang || 'en';
  });
  const isEnglish = currentLang === 'en';
  
  // 添加refs来获取按钮位置
  const servicesButtonRef = useRef(null);
  const languageButtonRef = useRef(null);
  const userMenuButtonRef = useRef(null);
  const [dropdownPosition, setDropdownPosition] = useState({});

  // 从localStorage获取用户登录状态
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  // 在组件挂载时检查用户登录状态
  useEffect(() => {
    // 从authAPI或localStorage获取用户信息
    const token = localStorage.getItem('authToken');
    const userStr = localStorage.getItem('currentUser');

    if (token && userStr) {
      try {
        const user = JSON.parse(userStr);
        setIsLoggedIn(true);
        setCurrentUser(user);
        console.log('用户已登录:', user);
      } catch (error) {
        console.error('解析用户信息时出错:', error);
        // 清除无效的用户数据
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
      }
    }
  }, []);

  const handleHomeClick = useCallback(() => {
    // Scroll to top if already on home page
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }, []);

  // 计算下拉菜单位置的函数
  const calculateDropdownPosition = useCallback((buttonRef, dropdownType) => {
    if (!buttonRef.current) return {};
    
    const rect = buttonRef.current.getBoundingClientRect();
    
    // 使用视口坐标进行精确计算
    const top = rect.bottom + 8;
    
    let position = {
      top: `${top}px`,
    };
    
    // 根据下拉菜单类型设置左右位置
    if (dropdownType === 'user') {
      // 用户菜单右对齐
      const dropdownWidth = 180;
      position.left = `${rect.right - dropdownWidth}px`;
    } else {
      // Services 和 Language 菜单左对齐到按钮左边缘
      position.left = `${rect.left}px`;
    }
    
    // 边界检查 - 确保不超出屏幕
    const leftValue = parseInt(position.left);
    const dropdownWidth = dropdownType === 'user' ? 180 : 160;
    
    // 左边界检查
    if (leftValue < 10) {
      position.left = '10px';
    }
    // 右边界检查
    if (leftValue + dropdownWidth > window.innerWidth - 10) {
      position.left = `${window.innerWidth - dropdownWidth - 10}px`;
    }
    
    // 底部边界检查
    const dropdownHeight = 120;
    if (top + dropdownHeight > window.innerHeight) {
      position.top = `${rect.top - dropdownHeight - 8}px`;
    }
    
    return position;
  }, []);

  // 统一的菜单切换函数
  const toggleDropdown = useCallback((dropdownType) => {
    if (activeDropdown === dropdownType) {
      // 如果当前菜单已打开，则关闭
      setActiveDropdown(null);
      setDropdownPosition({});
    } else {
      // 打开新菜单，关闭之前的菜单
      let buttonRef;
      switch (dropdownType) {
        case 'services':
          buttonRef = servicesButtonRef;
          break;
        case 'language':
          buttonRef = languageButtonRef;
          break;
        case 'user':
          buttonRef = userMenuButtonRef;
          break;
        default:
          return;
      }
      
      const position = calculateDropdownPosition(buttonRef, dropdownType);
      setDropdownPosition(position);
      setActiveDropdown(dropdownType);
    }
  }, [activeDropdown, calculateDropdownPosition]);

  const handleServicesClick = useCallback(() => {
    toggleDropdown('services');
  }, [toggleDropdown]);

  const handleLanguageToggle = useCallback(() => {
    toggleDropdown('language');
  }, [toggleDropdown]);

  const handleUserMenuToggle = useCallback(() => {
    toggleDropdown('user');
  }, [toggleDropdown]);

  const handleLoginClick = useCallback(() => {
    navigate('/login');
  }, [navigate]);

  const handleLogout = useCallback(() => {
    // 清除登录状态
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    setIsLoggedIn(false);
    setCurrentUser(null);
    setActiveDropdown(null);
    setDropdownPosition({});

    // 可选：显示退出成功消息
    console.log('用户已退出登录');

    // 刷新页面以应用更改
    window.location.reload();
  }, []);

  const handleProfileClick = useCallback(() => {
    // 导航到用户个人中心页面
    navigate('/profile');
    setActiveDropdown(null);
    setDropdownPosition({});
  }, [navigate]);

  // 优化事件处理器 - 使用debounce
  const handleClickOutside = useCallback(debounce((event) => {
    if (!event.target.closest('.floating-navigation') && !event.target.closest('.dropdown-menu')) {
      setActiveDropdown(null);
      setDropdownPosition({});
    }
  }, 100), []);

  const handleEscapeKey = useCallback((event) => {
    if (event.key === 'Escape') {
      setActiveDropdown(null);
      setDropdownPosition({});
    }
  }, []);

  const handleLanguageChange = useCallback((lang) => {
    // 防止重复设置相同语言
    if (currentLang === lang) {
      setActiveDropdown(null);
      setDropdownPosition({});
      return;
    }

    // 立即更新状态和关闭下拉菜单
    setCurrentLang(lang);
    setActiveDropdown(null);
    setDropdownPosition({});
    localStorage.setItem('preferredLanguage', lang);

    // 使用更平滑的页面刷新方式
    setTimeout(() => {
      window.location.reload();
    }, 200);
  }, [currentLang]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [handleClickOutside, handleEscapeKey]);

  // Handle window resize and scroll to recalculate dropdown positions
  useEffect(() => {
    const updateDropdownPosition = () => {
      if (activeDropdown) {
        let buttonRef;
        switch (activeDropdown) {
          case 'services':
            buttonRef = servicesButtonRef;
            break;
          case 'language':
            buttonRef = languageButtonRef;
            break;
          case 'user':
            buttonRef = userMenuButtonRef;
            break;
          default:
            return;
        }
        
        if (buttonRef.current) {
          const position = calculateDropdownPosition(buttonRef, activeDropdown);
          setDropdownPosition(position);
        }
      }
    };

    const handleResize = throttle(updateDropdownPosition, 100);
    const handleScroll = throttle(updateDropdownPosition, 50);

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [activeDropdown, calculateDropdownPosition]);

  // 缓存导航项配置
  const navigationItems = useMemo(() => [
    {
      key: 'home',
      label: isEnglish ? 'Home' : '首页',
      onClick: handleHomeClick,
      isActive: true // 标记当前页面为Home
    },
    {
      key: 'about',
      label: isEnglish ? 'About' : '关于我们',
      onClick: () => {}
    },
    {
      key: 'services',
      label: isEnglish ? 'Products' : '产品',
      hasDropdown: true,
      onClick: handleServicesClick
    },
    {
      key: 'pricing',
      label: isEnglish ? 'Pricing' : '价格方案',
      onClick: () => {}
    },
    {
      key: 'solution',
      label: isEnglish ? 'Solution' : '解决方案',
      onClick: () => {}
    },
    {
      key: 'language',
      label: isEnglish ? 'English' : '中文',
      hasDropdown: true,
      onClick: handleLanguageToggle,
      isLanguage: true // 标记为语言选择器
    }
  ], [isEnglish, handleHomeClick, handleServicesClick, handleLanguageToggle]);

  return (
    <>
      <LiquidGlass
        displacementScale={64}
        blurAmount={0.1}
        saturation={130}
        aberrationIntensity={2}
        elasticity={0.35}
        cornerRadius={100}
        mode="standard"
        padding="10px 20px"
        overLight={true}
        className="floating-navigation"
        style={{
          width: 'fit-content'
        }}
      >
      <div className="nav-container" data-lang={currentLang}>
        {/* Left side - Navigation items */}
        <div className="nav-left">
          {navigationItems.map((item) => (
            <div key={item.key} className="nav-item-wrapper">
              <button
                ref={item.key === 'services' ? servicesButtonRef : 
                     item.key === 'language' ? languageButtonRef : null}
                className={`nav-item ${item.hasDropdown ? 'has-dropdown' : ''} ${item.isActive ? 'active' : ''}`}
                onClick={item.onClick}
                data-lang={currentLang}
              >
                {item.isLanguage && (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" style={{ marginRight: '6px' }}>
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/>
                    <path d="M2 12h20M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" strokeWidth="1.5"/>
                  </svg>
                )}
                {item.label}
                {item.hasDropdown && (
                  <svg
                    className={`dropdown-icon ${activeDropdown === item.key ? 'open' : ''}`}
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                  >
                    <path
                      d="M3 4.5L6 7.5L9 4.5"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </button>


            </div>
          ))}
        </div>

        {/* Center - Empty for now */}
        <div className="nav-center">
        </div>

        {/* Right side - Login/Register 或 用户菜单 */}
        <div className="nav-right">
          {isLoggedIn && currentUser ? (
            // 已登录，显示用户菜单
            <div className="user-menu-wrapper">
              <button
                ref={userMenuButtonRef}
                className="user-menu-button"
                data-lang={currentLang}
                onClick={handleUserMenuToggle}
              >
                <div className="user-avatar">
                  {/* 使用用户名首字母作为头像 */}
                  {currentUser.username ? currentUser.username.charAt(0).toUpperCase() : 'U'}
                </div>
                <span className="user-name">{currentUser.username}</span>
                <svg
                  className={`dropdown-icon ${activeDropdown === 'user' ? 'open' : ''}`}
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                >
                  <path
                    d="M3 4.5L6 7.5L9 4.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>


            </div>
          ) : (
            // 未登录，显示登录/注册按钮
            <button
              className="auth-button"
              data-lang={currentLang}
              onClick={handleLoginClick}
            >
              {isEnglish ? 'LOGIN / REGISTER' : '登录/注册'}
            </button>
          )}
        </div>
      </div>
    </LiquidGlass>

    {/* 渲染下拉菜单在外层，避免被LiquidGlass变换影响 */}
    {activeDropdown === 'services' && (
      <div 
        className="dropdown-menu"
        style={dropdownPosition}
      >
        <button
          className="dropdown-item"
          onClick={() => {
            navigate('/main');
            setActiveDropdown(null);
            setDropdownPosition({});
          }}
        >
          {isEnglish ? 'Dashboard' : '仪表板'}
        </button>
        <button
          className="dropdown-item"
          onClick={() => {
            navigate('/ai-analyzer');
            setActiveDropdown(null);
            setDropdownPosition({});
          }}
        >
          {isEnglish ? 'Site Analyzer' : '选址分析器'}
        </button>
        <button 
          className="dropdown-item"
          onClick={() => {
            setActiveDropdown(null);
            setDropdownPosition({});
          }}
        >
          {isEnglish ? 'Consulting' : '咨询服务'}
        </button>
      </div>
    )}

    {activeDropdown === 'language' && (
      <div 
        className="dropdown-menu"
        style={dropdownPosition}
      >
        <button
          className={`dropdown-item ${currentLang === 'en' ? 'active' : ''}`}
          onClick={() => handleLanguageChange('en')}
          data-lang="en"
        >
          🇺🇸 English
        </button>
        <button
          className={`dropdown-item ${currentLang === 'zh' ? 'active' : ''}`}
          onClick={() => handleLanguageChange('zh')}
          data-lang="zh"
        >
          🇨🇳 中文
        </button>
      </div>
    )}

    {activeDropdown === 'user' && (
      <div 
        className="dropdown-menu user-dropdown"
        style={dropdownPosition}
      >
        <button className="dropdown-item" onClick={handleProfileClick}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M18 20C18 17.7909 15.3137 16 12 16C8.68629 16 6 17.7909 6 20" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span>{isEnglish ? 'Profile' : '个人中心'}</span>
        </button>
        <button className="dropdown-item" onClick={handleLogout}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16 17L21 12L16 7" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21 12H9" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span>{isEnglish ? 'Logout' : '退出登录'}</span>
        </button>
      </div>
    )}
  </>
  );
});

// 主页面组件 - 优化版本
const WelcomePage = memo(() => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [activeTeamMember, setActiveTeamMember] = useState(0);
  const teamSectionRef = useRef(null);
  const dataSectionRef = useRef(null);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    company: '',
    industry: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // TeamCarousel的引用
  const teamCarouselRef = useRef(null);

  // 缓存语言设置
  const currentLang = useMemo(() => localStorage.getItem('preferredLanguage') || 'en', []);
  const isEnglish = useMemo(() => currentLang === 'en', [currentLang]);

  // 模拟加载过程
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  // 优化事件处理器 - 使用useCallback
  const handleEnterApp = useCallback(() => {
    navigate('/main');
  }, [navigate]);

  const scrollToTeam = useCallback(() => {
    teamSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const scrollToData = useCallback(() => {
    dataSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  const handleFormSubmit = useCallback((e) => {
    e.preventDefault();
    setSubmitting(true);

    // 模拟提交表单的API调用
    setTimeout(() => {
      console.log('Form submitted:', formData);
      setFormSubmitted(true);
      setSubmitting(false);

      // 5秒后关闭模态框
      setTimeout(() => {
        setShowRequestModal(false);
        setFormSubmitted(false);
        // 重置表单
        setFormData({
          fullName: '',
          email: '',
          company: '',
          industry: ''
        });
      }, 5000);
    }, 1500);
  }, [formData]);

  const handleRequestAccess = useCallback(() => {
    setShowRequestModal(true);
  }, []);

  const closeRequestModal = useCallback(() => {
    setShowRequestModal(false);
  }, []);

  const handleContactUs = useCallback(() => {
    console.log("Contact Us button clicked");
    // TODO: Implement contact us functionality
  }, []);

  // 语言设置已在组件顶部缓存

  // 缓存团队成员数据
  const teamMembers = useMemo(() => [
    {
      name: isEnglish ? 'Tianqu Zhang' : '张天衢',
      role: isEnglish ? 'CEO & CO FOUNDER' : 'CEO & 联合创始人',
      image: '/images/Tian.jpg',
      twitter: 'https://twitter.com/tianquzhang',
      linkedin: 'https://linkedin.com/in/tianquzhang'
    },
    {
      name: isEnglish ? 'Yuxuan Yang' : '杨宇轩',
      role: isEnglish ? 'CO FOUNDER & CTO' : '联合创始人 & 技术总监',
      image: '/images/Chris.jpg',
      twitter: 'https://twitter.com',
      linkedin: 'https://linkedin.com/in/yuxuanyang'
    },
    {
      name: isEnglish ? 'Anna Wu' : '吴秋筠',
      role: isEnglish ? 'Industrial Policy Director ' : '产业政策总监',
      image: '/images/Anna.jpg',
      twitter: 'https://twitter.com',
      linkedin: 'https://linkedin.com'
    }
  ], [isEnglish]);

  // 优化团队成员导航 - 使用useCallback
  const nextTeamMember = useCallback(() => {
    setActiveTeamMember((prev) => (prev + 1) % teamMembers.length);
  }, [teamMembers.length]);

  const prevTeamMember = useCallback(() => {
    setActiveTeamMember((prev) => (prev - 1 + teamMembers.length) % teamMembers.length);
  }, [teamMembers.length]);

  return (
    <div className="welcome-page">
      {loading ? (
        <LoadingScreen />
      ) : (
        <>
          {/* Floating Navigation Bar */}
          <FloatingNavigation />

          {/* 地球容器 - 作为绝对定位的背景 */}
          <div className="earth-container">
            {/* 流星效果 - 放在地球容器内，这样只会出现在星空背景中 */}
            <div className="meteor-container">
              <span className="meteor meteor-1"></span>
              <span className="meteor meteor-2"></span>
              <span className="meteor meteor-3"></span>
              <span className="meteor meteor-4"></span>
            </div>
            <Canvas camera={{ position: [0, 0, 5], fov: 45 }} style={{ background: 'transparent' }}>
              <color attach="background" args={["#010314"]} />
              <ambientLight intensity={1.2} />
              <pointLight position={[10, 5, 10]} intensity={2.2} color="#fff" />
              <directionalLight position={[5, 3, 5]} intensity={2.7} color="#c4f5ff" />
              <Suspense fallback={null}>
                <EnhancedStars />
                <SimpleParticleEarth scale={1.6} position={[2.8, 0, 0]} />
                <OrbitControls
                  enableZoom={false}
                  enablePan={false}
                  enableRotate={false}
                  autoRotate={false}
                  target={[0, 0, 0]}
                />
              </Suspense>
            </Canvas>
          </div>

          {/* 内容区域 - 在地球之上 */}
          <div className="content-section" style={{ paddingTop: '30px', alignItems: 'flex-start' }}>
            <div className="title-section">
              <h1 className="company-name" data-text={isEnglish ? 'Industrial Discovery' : '工业探索'}>
                {isEnglish ? 'Industrial Discovery' : '工业探索'}
              </h1>
              <p className="company-desc">
                {isEnglish
                  ? (
                    <>
                      Industrial Discovery provides a <span className="highlight-text wave">site selection</span> and <span className="highlight-text circle">due diligence</span> platform tailored for industrial real estate and manufacturing investment. By integrating geographic, economic, and infrastructure <span className="highlight-text wave">data</span>, our system enables users to analyze location options, simulate costs, and compare industrial zones with clarity and speed. Designed for both investors and industrial park operators, the platform supports interactive analytics, customizable deployment, and end-to-end decision support to streamline industrial development processes.
                    </>
                  )
                  : (
                    <>
                      工业探索提供专为工业地产和制造业投资定制的<span className="highlight-text wave">选址</span>与<span className="highlight-text circle">尽职调查</span>平台。通过整合地理、经济和基础设施<span className="highlight-text wave">数据</span>，我们的系统使用户能够清晰、快速地分析位置选项、模拟成本并比较工业区。该平台专为投资者和工业园区运营商设计，支持交互式分析、可定制部署和端到端决策支持，以简化工业发展流程。
                    </>
                  )
                }
              </p>
              <div className="button-section">
                <StarryButton
                  className="explore-platform-btn"
                  onClick={handleEnterApp}
                >
                  {isEnglish ? 'EXPLORE PLATFORM' : '探索平台'}
                </StarryButton>
                <div className="scroll-down-container">
                  <button className="scroll-down-btn" onClick={scrollToTeam} aria-label="Scroll to team">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 5V19M12 19L5 12M12 19L19 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>{isEnglish ? 'MEET OUR TEAM' : '认识我们的团队'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Global Industrial Network Section */}
          <div className="global-network-section" style={{ width: '100%', marginTop: '0', background: '#010314' }}>
            <Particles
              particleColors={['#ffffff', '#888888']}
              particleCount={500}
              particleSpread={20}
              speed={0.1}
              particleBaseSize={80}
              disableRotation={true}
              className="geo-viz-particles"
            />
            <div style={{ position: 'relative', zIndex: 1 }}>
              <OptimizedGeoVisualization />
            </div>
          </div>

          {/* 特性部分 */}
          <div className="features-section" style={{ position: 'relative', overflow: 'hidden', background: '#010314' }}>
            <Particles
              particleColors={['#ffffff', '#aaaaaa']}
              particleCount={800}
              particleSpread={30}
              speed={0.05}
              particleBaseSize={60}
              disableRotation={true}
            />
            {/* 移除过渡元素，实现直接衔接 */}
            <div className="features-container" style={{ position: 'relative', zIndex: 1 }}>
              {/* 特性标题部分 - 改进的科技感标题 */}
              <div className="features-header">
                <div className="tech-title-container">
                  <h2 className="features-title tech-enhanced-title">
                    <span className="tech-title-prefix">&lt; /&gt; </span>
                    <span className="tech-title-letters">
                      {isEnglish ? 'KEY FEATURES' : '核心功能'}
                    </span>
                  </h2>
                </div>
                <p className="features-subtitle">
                  {isEnglish
                    ? 'Our platform offers a comprehensive suite of tools to drive your industrial strategy and decision-making'
                    : '我们的平台提供全面的工具套件，助力您的工业战略和决策制定'
                  }
                </p>



                {/* 添加数字装饰元素 */}
                <div className="tech-digital-decoration">
                  <div className="tech-binary-stream">
                    {[...Array(8)].map((_, i) => (
                      <span key={`binary-${i}`} className={i % 3 === 0 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream right">
                    {[...Array(8)].map((_, i) => (
                      <span key={`binary-right-${i}`} className={i % 4 === 0 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>

                  {/* 新增更多二进制流 */}
                  <div className="tech-binary-stream top-left">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-top-left-${i}`} className={i === 2 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream top-right">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-top-right-${i}`} className={i === 3 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream bottom-left">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-bottom-left-${i}`} className={i === 1 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream bottom-right">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-bottom-right-${i}`} className={i === 4 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>

                  {/* 添加浮动单个数字 - 增加数量 */}
                  {[...Array(40)].map((_, i) => (
                    <div
                      key={`floating-digit-${i}`}
                      className="floating-digits"
                      style={{
                        left: `${Math.random() * 90 + 5}%`,
                        top: `${Math.random() * 90 + 5}%`,
                        fontSize: `${Math.random() * 12 + 8}px`,
                        opacity: `${Math.random() * 0.5 + 0.3}`,
                        animationDelay: `${Math.random() * 8}s`,
                        animationDuration: `${Math.random() * 10 + 5}s`
                      }}
                    >
                      {Math.round(Math.random())}
                    </div>
                  ))}
                </div>
              </div>

              {/* 添加背景粒子效果 */}
              <div className="features-grid">
                {/* Feature 1 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Interactive Mapping' : '交互式地图'}</h3>
                  <p>{isEnglish
                    ? 'Explore industrial parks with our intuitive, interactive map interface.'
                    : '通过直观的交互式地图界面探索工业园区。'}
                  </p>
                </div>

                {/* Feature 2 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                      <path d="M13.5 7h-3v6h6v-3h-3z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Migration Analysis' : '迁移分析'}</h3>
                  <p>{isEnglish
                    ? 'Track and analyze population movements and labor trends.'
                    : '跟踪和分析人口流动及劳动力趋势。'}
                  </p>
                </div>

                {/* Feature 3 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Labor Insights' : '劳动力洞察'}</h3>
                  <p>{isEnglish
                    ? 'Gain valuable insights into workforce demographics and skills.'
                    : '获取有关劳动力人口统计和技能的宝贵见解。'}
                  </p>
                </div>

                {/* Feature 4 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                      <path d="M13.5 7h-3v6h6v-3h-3z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Economic Hotspots' : '经济热点'}</h3>
                  <p>{isEnglish
                    ? 'Identify and analyze economic activity concentrations.'
                    : '识别和分析经济活动集中区域。'}
                  </p>
                </div>

                {/* Feature 5 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95C8.08 7.14 9.94 6 12 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11c1.56.1 2.78 1.41 2.78 2.96 0 1.65-1.35 3-3 3z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Env. Monitoring' : '环境监测'}</h3>
                  <p>{isEnglish
                    ? 'Monitor environmental conditions and sustainability metrics.'
                    : '监测环境条件和可持续性指标。'}
                  </p>
                </div>

                {/* Feature 6 - Data Visualization */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z" fill="currentColor"/>
                      <path d="M3.5 14.5l-1.41-1.5L0 15.5l3.5 3.5 7.5-7.5-1.5-1.5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Data Visualization' : '数据可视化'}</h3>
                  <p>{isEnglish
                    ? 'Transform complex data into clear, actionable insights.'
                    : '将复杂数据转化为清晰、可操作的见解。'}
                  </p>
                </div>

                {/* Feature 7 - Custom Reports */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Custom Reports' : '定制报告'}</h3>
                  <p>{isEnglish
                    ? 'Generate tailored reports for your specific needs.'
                    : '根据您的具体需求生成定制报告。'}
                  </p>
                </div>

                {/* Feature 8 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.03-6.61l1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61zM12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Real-time Updates' : '实时更新'}</h3>
                  <p>{isEnglish
                    ? 'Stay current with the latest data and trends.'
                    : '随时了解最新数据和趋势。'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 团队部分 - 可滚动到达 */}
          <div className="team-section" ref={teamSectionRef} style={{ position: 'relative', overflow: 'hidden', background: '#010314' }}>
            <Particles
              particleColors={['#ffffff', '#bbbbbb']}
              particleCount={500}
              particleSpread={25}
              speed={0.08}
              particleBaseSize={70}
              disableRotation={true}
            />
            <div className="team-section-inner" style={{ position: 'relative', zIndex: 1 }}>
              <div className="team-left-content">
                <div className="team-heading">
                  <h2 className="team-title">{isEnglish ? 'Our team' : '我们的团队'}</h2>
                  <p className="team-subtitle">{isEnglish ? 'Let technology do the heavy lifting and grow initiatives into tangible results' : 'Let technology do the heavy lifting and grow initiatives into tangible results'}</p>
                </div>
              </div>

              <div className="team-carousel">
                <TeamCarousel
                  ref={teamCarouselRef}
                  teamMembers={teamMembers}
                  activeTeamMember={activeTeamMember}
                  onNext={nextTeamMember}
                  onPrev={prevTeamMember}
                />

                <div className="team-navigation">
                  <button
                    className="nav-button prev-button"
                    onClick={() => {
                      if (teamCarouselRef.current) {
                        teamCarouselRef.current.handlePrev();
                      }
                    }}
                    aria-label="Previous team member"
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 18L9 12L15 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                  <button
                    className="nav-button next-button"
                    onClick={() => {
                      if (teamCarouselRef.current) {
                        teamCarouselRef.current.handleNext();
                      }
                    }}
                    aria-label="Next team member"
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 18L15 12L9 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 简化的联系我们邀请模块 */}
          <div className="contact-invitation-section" ref={dataSectionRef}>
            <div className="contact-container">
              <div className="contact-content">
                <div className="contact-header">
                  <h2 className="contact-title">
                    {isEnglish ? 'Ready to Transform Your Industrial Strategy?' : '准备好转型您的工业战略了吗？'}
                  </h2>
                  <p className="contact-subtitle">
                    {isEnglish 
                      ? 'Join thousands of professionals using our platform for smarter industrial decisions' 
                      : '与数千名专业人士一起使用我们的平台，做出更智能的工业决策'
                    }
                  </p>
                </div>

                <div className="contact-features">
                  <div className="feature-item">
                    <div className="feature-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
                      </svg>
                    </div>
                    <span>{isEnglish ? 'Expert Site Analysis' : '专业选址分析'}</span>
                  </div>
                  <div className="feature-item">
                    <div className="feature-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M13 3C16.31 3 19 5.69 19 9C19 12.31 16.31 15 13 15C9.69 15 7 12.31 7 9C7 5.69 9.69 3 13 3Z" fill="currentColor"/>
                        <path d="M5 16H19C20.1 16 21 16.9 21 18S20.1 20 19 20H5C3.9 20 3 19.1 3 18S3.9 16 5 16Z" fill="currentColor"/>
                      </svg>
                    </div>
                    <span>{isEnglish ? 'Real-time Data' : '实时数据'}</span>
                  </div>
                  <div className="feature-item">
                    <div className="feature-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2C17.52 2 22 6.48 22 12S17.52 22 12 22S2 17.52 2 12S6.48 2 12 2ZM15.5 8.5L10.5 13.5L8.5 11.5L7 13L10.5 16.5L17 10L15.5 8.5Z" fill="currentColor"/>
                      </svg>
                    </div>
                    <span>{isEnglish ? '24/7 Support' : '24/7 支持'}</span>
                  </div>
                </div>

                <div className="contact-actions">
                  <button className="primary-cta-btn" onClick={handleContactUs}>
                    <span>{isEnglish ? 'Contact Us Today' : '立即联系我们'}</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                  <button className="secondary-cta-btn" onClick={handleEnterApp}>
                    <span>{isEnglish ? 'Try Platform Free' : '免费试用平台'}</span>
                  </button>
                </div>

                <div className="contact-info">
                  <div className="info-item">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <polyline points="22,6 12,13 2,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span><EMAIL></span>
                  </div>
                  <div className="info-item">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="10" r="3" stroke="currentColor" strokeWidth="2"/>
                      <path d="M21 10C21 17 12 23 12 23S3 17 3 10C3 5.03 7.03 1 12 1S21 5.03 21 10Z" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    <span>Delaware, United States</span>
                  </div>
                </div>

                <div className="contact-stats">
                  <div className="stat-item">
                    <span className="stat-number">24H</span>
                    <span className="stat-label">{isEnglish ? 'Response Time' : '响应时间'}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">99.8%</span>
                    <span className="stat-label">{isEnglish ? 'Success Rate' : '成功率'}</span>
                  </div>
                  <div className="stat-item">
                    <span className="stat-number">1000+</span>
                    <span className="stat-label">{isEnglish ? 'Active Users' : '活跃用户'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 申请访问模态框 */}
          {showRequestModal && (
            <div className="request-modal-overlay" onClick={closeRequestModal}>
              <div className="request-modal" onClick={e => e.stopPropagation()}>
                <button className="close-modal-btn" onClick={closeRequestModal}>×</button>
                {formSubmitted ? (
                  <div className="form-success">
                    <div className="success-icon">✓</div>
                    <h3 className="modal-title">{isEnglish ? 'Request Sent!' : '申请已发送！'}</h3>
                    <p className="modal-description">
                      {isEnglish
                        ? 'Thank you for your interest. We will contact you soon with access details.'
                        : '感谢您的兴趣。我们将很快与您联系并提供访问详情。'
                      }
                    </p>
                  </div>
                ) : (
                  <>
                    <h3 className="modal-title">{isEnglish ? 'Request Access' : '申请访问'}</h3>
                    <p className="modal-description">
                      {isEnglish
                        ? 'Fill out the form below to request access to Dala, our industrial intelligence system.'
                        : '填写以下表格申请访问Dala，我们的工业智能系统。'
                      }
                    </p>
                    <form className="request-form" onSubmit={handleFormSubmit}>
                      <div className="form-group">
                        <input
                          type="text"
                          name="fullName"
                          value={formData.fullName}
                          onChange={handleInputChange}
                          placeholder={isEnglish ? "Full Name" : "姓名"}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder={isEnglish ? "Work Email" : "工作邮箱"}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <input
                          type="text"
                          name="company"
                          value={formData.company}
                          onChange={handleInputChange}
                          placeholder={isEnglish ? "Company" : "公司"}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <select
                          className="form-input"
                          name="industry"
                          value={formData.industry}
                          onChange={handleInputChange}
                          required
                        >
                          <option value="" disabled>
                            {isEnglish ? "Industry" : "行业"}
                          </option>
                          <option value="manufacturing">
                            {isEnglish ? "Manufacturing" : "制造业"}
                          </option>
                          <option value="realEstate">
                            {isEnglish ? "Real Estate" : "房地产"}
                          </option>
                          <option value="consulting">
                            {isEnglish ? "Consulting" : "咨询"}
                          </option>
                          <option value="investment">
                            {isEnglish ? "Investment" : "投资"}
                          </option>
                          <option value="other">
                            {isEnglish ? "Other" : "其他"}
                          </option>
                        </select>
                      </div>
                      <button
                        type="submit"
                        className={`submit-request-btn ${submitting ? 'submitting' : ''}`}
                        disabled={submitting}
                      >
                        {submitting
                          ? (isEnglish ? 'SENDING...' : '发送中...')
                          : (isEnglish ? 'SUBMIT REQUEST' : '提交申请')
                        }
                      </button>
                    </form>
                  </>
                )}
              </div>
            </div>
          )}
        </>
      )}
      <footer className="site-footer">
        <div className="footer-container">
          <div className="footer-content">
            {/* 公司信息 */}
            <div className="footer-brand">
              <h3 className="footer-logo">
                <span className="logo-highlight">INDUSTRIAL</span>
                <span className="logo-normal">DISCOVERY</span>
              </h3>
              <p className="footer-tagline">
                {isEnglish ? 'Powering Industrial Intelligence' : '驱动工业智能'}
              </p>
            </div>

            {/* 联系信息 */}
            <div className="footer-contact">
              <span className="contact-email">Email： <EMAIL></span>
              <span className="contact-location">Delaware, United States</span>
            </div>
          </div>

          {/* 底部版权 */}
          <div className="footer-bottom">
            <p>&copy; {new Date().getFullYear()} Industrial Discovery Inc. {isEnglish ? 'All rights reserved.' : '保留所有权利。'}</p>
            <div className="footer-links">
              <a href="#">{isEnglish ? 'Privacy Policy' : '隐私政策'}</a>
              <a href="#">{isEnglish ? 'Terms of Service' : '服务条款'}</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
});

export default WelcomePage;
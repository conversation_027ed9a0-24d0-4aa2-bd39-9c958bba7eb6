import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import BadgeCard from '../components/common/BadgeCard';
import StarBorder from '../components/common/StarBorder';
import Beams from '../components/3D/Beams';
import '../styles/GlassmorphicLogin.css';

// Enhanced Background with Beams
const BeamsBackground = memo(() => (
  <div className="beams-background">
    <Beams
      beamWidth={3}
      beamHeight={30}
      beamNumber={20}
      lightColor="#ffffff"
      speed={1.5}
      noiseIntensity={1.2}
      scale={0.25}
      rotation={30}
      beamSurfaceColor="#222222"
      backgroundColor="#000000"
      roughness={0.1}
      metalness={0.9}
      envMapIntensity={20}
      dirLightIntensity={1.4}
      ambientIntensity={0.5}
    />
    {/* 添加一层半透明遮罩，确保内容可读性 */}
    <div className="beams-overlay" />
  </div>
));

// Badge Lanyard Component
const BadgeLanyard = memo(() => (
  <div className="badge-lanyard">
    <div className="lanyard-strap">
      <div className="lanyard-texture"></div>
      <div className="absolute left-1 top-4 bottom-8 w-0.5 bg-gradient-to-b from-slate-300/40 to-transparent rounded-full"></div>
      <div className="absolute right-1 top-4 bottom-8 w-0.5 bg-gradient-to-b from-slate-300/40 to-transparent rounded-full"></div>
    </div>
    <div className="lanyard-clip">
      <div className="lanyard-clip-inner">
        <div className="lanyard-clip-mechanism">
          <div className="absolute inset-0.5 bg-gradient-to-b from-slate-600 to-slate-700 rounded-full"></div>
        </div>
      </div>
      <div className="absolute top-1 left-2 right-2 h-0.5 bg-gradient-to-r from-transparent via-slate-500/60 to-transparent rounded-full"></div>
    </div>
  </div>
));



// Icon Components
const MailIcon = memo(() => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
    <polyline points="22,6 12,13 2,6" />
  </svg>
));

const UserIcon = memo(() => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <circle cx="12" cy="7" r="4" />
  </svg>
));

const LockIcon = memo(() => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
    <circle cx="12" cy="16" r="1" />
    <path d="M7 11V7a5 5 0 0 1 10 0v4" />
  </svg>
));

const EyeIcon = memo(() => (
  <svg 
    width="18" 
    height="18" 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24" 
    strokeWidth={2.5}
    className="eye-icon"
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
    <circle cx="12" cy="12" r="3" />
  </svg>
));

const EyeOffIcon = memo(() => (
  <svg 
    width="18" 
    height="18" 
    fill="none" 
    stroke="currentColor" 
    viewBox="0 0 24 24" 
    strokeWidth={2.5}
    className="eye-icon"
  >
    <path strokeLinecap="round" strokeLinejoin="round" d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
    <line x1="1" y1="1" x2="23" y2="23" />
  </svg>
));

const LanguageIcon = memo(() => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z" />
  </svg>
));

// Social Icons
const AppleIcon = memo(() => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.71 19.5C18.44 20.28 18.13 20.96 17.77 21.57C17.32 22.34 16.95 22.84 16.68 23.08C16.23 23.5 15.74 23.71 15.21 23.72C14.83 23.72 14.37 23.61 13.84 23.38C13.31 23.16 12.83 23.04 12.4 23.04C11.95 23.04 11.45 23.16 10.9 23.38C10.35 23.61 9.92 23.73 9.6 23.75C9.1 23.79 8.59 23.57 8.08 23.08C7.78 22.81 7.39 22.3 6.92 21.54C6.42 20.74 6.01 19.83 5.69 18.8C5.35 17.69 5.18 16.63 5.18 15.62C5.18 14.45 5.42 13.43 5.89 12.58C6.25 11.9 6.74 11.37 7.36 10.98C7.98 10.59 8.66 10.39 9.4 10.38C9.8 10.38 10.32 10.51 10.96 10.76C11.6 11.01 12.05 11.14 12.31 11.14C12.51 11.14 13.01 10.99 13.8 10.7C14.54 10.43 15.17 10.31 15.69 10.35C16.94 10.47 17.88 10.96 18.5 11.83C17.38 12.53 16.83 13.53 16.84 14.83C16.85 15.86 17.24 16.71 18.01 17.37C18.36 17.67 18.75 17.9 19.18 18.05C19.08 18.32 18.9 18.92 18.71 19.5ZM15.84 4.5C15.84 5.27 15.58 5.98 15.06 6.63C14.43 7.4 13.66 7.83 12.82 7.75C12.81 7.66 12.8 7.56 12.8 7.45C12.8 6.72 13.1 5.95 13.63 5.33C13.89 5.01 14.24 4.75 14.67 4.55C15.1 4.36 15.49 4.25 15.84 4.23C15.85 4.32 15.84 4.41 15.84 4.5Z" fill="currentColor"/>
  </svg>
));

const GoogleIcon = memo(() => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
    <path d="M22.56 12.25C22.56 11.47 22.49 10.72 22.36 10H12V14.26H17.92C17.66 15.63 16.88 16.79 15.71 17.57V20.34H19.28C21.36 18.42 22.56 15.6 22.56 12.25Z" fill="#4285F4"/>
    <path d="M12 23C15.24 23 17.95 21.92 19.28 20.34L15.71 17.57C14.74 18.22 13.48 18.62 12 18.62C8.91 18.62 6.26 16.67 5.4 13.97H1.72V16.84C3.04 19.47 7.26 23 12 23Z" fill="#34A853"/>
    <path d="M5.4 13.97C5.18 13.32 5.06 12.62 5.06 11.9C5.06 11.18 5.18 10.48 5.4 9.83V6.96H1.72C0.99 8.42 0.6 10.11 0.6 11.9C0.6 13.69 0.99 15.38 1.72 16.84L5.4 13.97Z" fill="#FBBC05"/>
    <path d="M12 5.38C13.62 5.38 15.06 5.94 16.21 7.02L19.36 3.87C17.95 2.61 15.24 1.9 12 1.9C7.26 1.9 3.04 5.43 1.72 8.06L5.4 10.93C6.26 8.23 8.91 6.28 12 6.28V5.38Z" fill="#EA4335"/>
  </svg>
));

const XIcon = memo(() => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
  </svg>
));

// 新的语言偏好选择器组件
const LanguagePreferenceSelector = memo(({ value, onChange }) => {
  return (
    <>
      <style>{`
        .language-preference-selector {
          display: flex;
          border-radius: 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          overflow: hidden;
          width: 100%;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
        }
        .language-preference-option {
          flex: 1;
          padding: 10px 15px;
          text-align: center;
          cursor: pointer;
          transition: background 0.3s ease, color 0.3s ease;
          color: rgba(255, 255, 255, 0.7);
          background: transparent;
          border: none;
          font-size: 14px;
          font-weight: 500;
          position: relative;
        }
        .language-preference-option:hover {
          background: rgba(255, 255, 255, 0.15);
        }
        .language-preference-option.active {
          color: #ffffff;
          font-weight: 700;
        }
        .language-preference-option .active-indicator {
          position: absolute;
          bottom: 0;
          left: 10%;
          right: 10%;
          height: 3px;
          background: #38bdf8; /* A nice highlight color */
          border-radius: 2px;
          transition: all 0.3s ease;
          opacity: 0;
          transform: scaleX(0);
        }
        .language-preference-option.active .active-indicator {
          opacity: 1;
          transform: scaleX(1);
        }
        .language-preference-divider {
          width: 1px;
          background: rgba(255, 255, 255, 0.2);
        }
      `}</style>
      <div className="language-preference-selector">
        <button type="button" onClick={() => onChange('en')} className={`language-preference-option ${value === 'en' ? 'active' : ''}`}>
          English
          <span className="active-indicator"></span>
        </button>
        <div className="language-preference-divider"></div>
        <button type="button" onClick={() => onChange('zh')} className={`language-preference-option ${value === 'zh' ? 'active' : ''}`}>
          中文
          <span className="active-indicator"></span>
        </button>
      </div>
    </>
  );
});
LanguagePreferenceSelector.displayName = 'LanguagePreferenceSelector';

// 新增错误消息组件 - 优化为轻量设计
const ErrorMessage = memo(({ message, type = 'error', onClose }) => {
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    if (message) {
      setIsVisible(true);
      
      // 自动隐藏错误消息（减少时间以保持工牌紧凑性）
      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onClose) {
          setTimeout(onClose, 200); // 缩短等待动画完成的时间
        }
      }, 5000); // 5秒后自动隐藏，原来是8秒
      
      return () => clearTimeout(timer);
    }
  }, [message, onClose]);

  if (!message || !isVisible) return null;

  const getMessageClass = () => {
    const baseClass = 'error-message';
    if (type === 'critical') return `${baseClass} critical submit-error-message`;
    if (type === 'warning') return `${baseClass} warning`;
    if (type === 'submit') return `${baseClass} submit-error-message`;
    return baseClass;
  };

  return (
    <div className={getMessageClass()}>
      <span>{message}</span>
      {onClose && (
        <button 
          type="button" 
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 200); // 缩短时间
          }}
          style={{
            marginLeft: 'auto',
            background: 'none',
            border: 'none',
            color: 'inherit',
            cursor: 'pointer',
            fontSize: '1.125rem', // 稍微小一点
            lineHeight: 1,
            opacity: 0.6, // 降低透明度
            transition: 'opacity 0.2s ease',
            padding: 0,
            minWidth: '1rem'
          }}
          onMouseEnter={(e) => e.target.style.opacity = '1'}
          onMouseLeave={(e) => e.target.style.opacity = '0.6'}
          aria-label="关闭错误消息"
        >
          ×
        </button>
      )}
    </div>
  );
});
ErrorMessage.displayName = 'ErrorMessage';

// 主登录页面组件 - 使用性能优化
const LoginPage = memo(() => {
  const navigate = useNavigate();
  const [isLogin, setIsLogin] = useState(true);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [formData, setFormData] = useState({
    loginIdentifier: '', // 支持邮箱或用户名
    password: '',
    confirmPassword: '',
    username: '',
    email: '',
    preferredLanguage: language // 添加语言偏好字段
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState('');
  const [formProgress, setFormProgress] = useState(0);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);

  // 使用useMemo缓存计算结果
  const isEnglish = useMemo(() => language === 'en', [language]);

  // 使用useMemo缓存文本内容
  const texts = useMemo(() => ({
    en: {
      welcome: 'Welcome to',
      appName: 'Industrial Geo Explorer',
      subtitle: 'Advanced Geographic Intelligence Platform',
      loginTitle: 'Sign In',
      registerTitle: 'Create Account',
      loginIdentifier: 'Email or Username',
      email: 'Email Address',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      username: 'Username',
      loginButton: 'Sign In',
      registerButton: 'Create Account',
      switchToRegister: "Sign up",
      switchToLogin: 'Sign in',
      backToHome: 'Back to Home',
      forgotPassword: 'Forgot Password?',
      rememberMe: 'Remember me',
      orDivider: 'OR',
      googleLogin: 'Continue with Google',
      githubLogin: 'Continue with GitHub',
      loading: 'Please wait...',
      loginSubtitle: 'Welcome back to the platform',
      registerSubtitle: 'Join our advanced analytics platform',
      preferredLanguage: 'Preferred Language',
      selectLanguage: 'Select Language',
      english: 'English',
      chinese: 'Chinese',
      // Error messages
      loginFailed: 'Login failed, please check your credentials',
      registerFailed: 'Registration failed, please try again',
      systemError: 'System error: Unable to load authentication service',
      databaseError: 'Database connection error, please try again later',
      serverError: 'Server error, please try again later',
      networkError: 'Network error, please check your connection',
      invalidCredentials: 'Invalid email/username or password',
      emailExists: 'This email is already registered',
      usernameExists: 'This username is already taken',
      tryAgain: 'Please try again'
    },
    zh: {
      welcome: '欢迎使用',
      appName: '工域探索',
      subtitle: '先进地理智能平台',
      loginTitle: '登录',
      registerTitle: '注册账户',
      loginIdentifier: '邮箱或用户名',
      email: '邮箱地址',
      password: '密码',
      confirmPassword: '确认密码',
      username: '用户名',
      loginButton: '登录',
      registerButton: '注册',
      switchToRegister: '注册',
      switchToLogin: '登录',
      backToHome: '返回首页',
      forgotPassword: '忘记密码？',
      rememberMe: '记住我',
      orDivider: '或',
      googleLogin: '使用 Google 登录',
      githubLogin: '使用 GitHub 登录',
      loading: '请稍候...',
      loginSubtitle: '欢迎回到平台',
      registerSubtitle: '加入我们的高级分析平台',
      preferredLanguage: '首选语言',
      selectLanguage: '选择语言',
      english: '英语',
      chinese: '中文',
      // Error messages
      loginFailed: '登录失败，请检查您的凭据',
      registerFailed: '注册失败，请重试',
      systemError: '系统错误：无法加载认证服务',
      databaseError: '数据库连接错误，请稍后重试',
      serverError: '服务器错误，请稍后重试',
      networkError: '网络错误，请检查您的连接',
      invalidCredentials: '邮箱/用户名或密码错误',
      emailExists: '该邮箱已被注册',
      usernameExists: '该用户名已被使用',
      tryAgain: '请重试'
    }
  }), []);

  const t = useMemo(() => texts[language], [texts, language]);

  // 使用useCallback缓存密码强度检测函数
  const checkPasswordStrength = useCallback((password) => {
    if (!password) return '';

    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score < 3) return 'weak';
    if (score < 5) return 'medium';
    return 'strong';
  }, []);

  // 使用useCallback缓存表单进度计算函数
  const calculateFormProgress = useCallback(() => {
    let progress = 0;
    const totalFields = isLogin ? 2 : 4;

    if (isLogin) {
      if (formData.loginIdentifier) progress++;
      if (formData.password) progress++;
    } else {
      if (formData.username) progress++;
      if (formData.email) progress++;
      if (formData.password) progress++;
      if (formData.confirmPassword) progress++;
    }

    return (progress / totalFields) * 100;
  }, [formData, isLogin]);

  // 使用useCallback缓存表单验证函数
  const validateForm = useCallback(() => {
    const newErrors = {};

    if (isLogin) {
      // 登录验证 - 支持邮箱或用户名
      if (!formData.loginIdentifier) {
        newErrors.loginIdentifier = isEnglish ? 'Email or username is required' : '邮箱或用户名是必填项';
      } else if (formData.loginIdentifier.includes('@') && !/\S+@\S+\.\S+/.test(formData.loginIdentifier)) {
        newErrors.loginIdentifier = isEnglish ? 'Email format is invalid' : '邮箱格式无效';
      }
    } else {
      // 注册验证
      if (!formData.username) {
        newErrors.username = isEnglish ? 'Username is required' : '用户名是必填项';
      } else if (formData.username.length < 3) {
        newErrors.username = isEnglish ? 'Username must be at least 3 characters' : '用户名至少需要3个字符';
      }

      if (!formData.email) {
        newErrors.email = isEnglish ? 'Email is required' : '邮箱是必填项';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = isEnglish ? 'Email is invalid' : '邮箱格式无效';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = isEnglish ? 'Please confirm your password' : '请确认密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = isEnglish ? 'Passwords do not match' : '密码不匹配';
      }
    }

    // 密码验证（登录和注册都需要）
    if (!formData.password) {
      newErrors.password = isEnglish ? 'Password is required' : '密码是必填项';
    } else if (formData.password.length < 6) {
      newErrors.password = isEnglish ? 'Password must be at least 6 characters' : '密码至少需要6个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, isLogin, isEnglish]);

  // 使用useCallback缓存错误清除函数
  const clearError = useCallback((field) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      if (field) {
        delete newErrors[field];
      } else {
        // 清除所有错误
        return {};
      }
      return newErrors;
    });
  }, []);

  // 优化表单输入处理函数
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 检测密码强度
    if (name === 'password' && !isLogin) {
      setPasswordStrength(checkPasswordStrength(value));
    }

    // 清除对应字段的错误（增加延迟以避免过于频繁的更新）
    if (errors[name] || errors.submit) {
      const timeoutId = setTimeout(() => {
        clearError(name);
        // 如果用户开始修正输入，也清除提交错误
        if (errors.submit) {
          clearError('submit');
        }
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [isLogin, checkPasswordStrength, errors, clearError]);

  // 更新表单进度
  useEffect(() => {
    setFormProgress(calculateFormProgress());
  }, [calculateFormProgress]);

  // 优化表单提交处理函数
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    // 验证表单
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    // 清除之前的错误
    clearError();

    try {
      // 使用动态导入加载API模块
      const { authAPI } = await import('../services/api').catch(err => {
        console.error('无法加载API模块:', err);
        throw new Error(isEnglish ? 'System error: Unable to load authentication service' : '系统错误: 无法加载认证服务');
      });

      console.log('准备发送认证请求...', isLogin ? '登录' : '注册');

      if (isLogin) {
        // 登录 - 支持邮箱或用户名
        const response = await authAPI.login({
          loginIdentifier: formData.loginIdentifier,
          password: formData.password
        });

        console.log('登录响应:', response);

        if (response.success) {
          // 保存记住我设置
          if (rememberMe) {
            localStorage.setItem('rememberLogin', 'true');
          }

          // 登录成功后，应用用户的语言偏好
          if (response.data && response.data.user && response.data.user.preferred_language) {
            localStorage.setItem('preferredLanguage', response.data.user.preferred_language);
            setLanguage(response.data.user.preferred_language);
          }

          console.log('登录成功，准备导航到首页');
          
          // 可选：显示成功消息
          setErrors({ 
            success: isEnglish ? 'Login successful! Redirecting...' : '登录成功！正在跳转...'
          });
          
          // 延迟跳转以显示成功消息
          setTimeout(() => {
            navigate('/');
          }, 1500);
        } else {
          throw new Error(response.message || (isEnglish ? 'Login failed, please check your credentials' : '登录失败，请检查您的凭据'));
        }
      } else {
        // 注册
        const response = await authAPI.register({
          email: formData.email,
          password: formData.password,
          username: formData.username,
          preferred_language: formData.preferredLanguage
        });

        console.log('注册响应:', response);

        if (response.success) {
          // 注册成功后，设置用户选择的语言偏好
          if (response.data && response.data.user && response.data.user.preferred_language) {
            localStorage.setItem('preferredLanguage', response.data.user.preferred_language);
            setLanguage(response.data.user.preferred_language);
          }
          
          console.log('注册成功，准备导航到首页');
          
          // 显示成功消息
          setErrors({ 
            success: isEnglish ? 'Registration successful! Welcome!' : '注册成功！欢迎您！'
          });
          
          // 延迟跳转以显示成功消息
          setTimeout(() => {
            navigate('/');
          }, 1500);
        } else {
          throw new Error(response.message || (isEnglish ? 'Registration failed, please try again' : '注册失败，请重试'));
        }
      }
    } catch (error) {
      console.error('认证错误:', error);

      // 根据错误类型设置不同的错误消息样式
      let errorMessage = error.message || (isLogin ? t.loginFailed : t.registerFailed);
      let errorType = 'submit';

      // 判断错误类型
      if (error.message && error.message.includes('系统错误') || error.message.includes('System error')) {
        errorType = 'critical';
      } else if (error.message && (error.message.includes('网络') || error.message.includes('Network'))) {
        errorType = 'warning';
      }

      setErrors({
        submit: errorMessage,
        errorType: errorType
      });
    } finally {
      setIsLoading(false);
    }
  }, [validateForm, isLogin, formData, language, rememberMe, navigate, isEnglish, t, clearError]);

  // 使用useCallback缓存模式切换函数
  const toggleMode = useCallback(() => {
    setIsLogin(!isLogin);
    setErrors({});
    setPasswordStrength('');
    setFormData({
      loginIdentifier: '',
      password: '',
      confirmPassword: '',
      username: '',
      email: '',
      preferredLanguage: language
    });
  }, [isLogin, language]);

  // 使用useCallback缓存语言切换函数
  const toggleLanguage = useCallback(() => {
    const newLang = language === 'en' ? 'zh' : 'en';
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
    setFormData(prev => ({ ...prev, preferredLanguage: newLang }));
  }, [language]);

  // 使用useCallback缓存密码可见性切换函数
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(!showPassword);
  }, [showPassword]);

  const toggleConfirmPasswordVisibility = useCallback(() => {
    setShowConfirmPassword(!showConfirmPassword);
  }, [showConfirmPassword]);

  // 语言下拉菜单处理函数
  const handleLanguageDropdownToggle = useCallback(() => {
    setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
  }, [isLanguageDropdownOpen]);

  const handleLanguageSelect = useCallback((selectedLang) => {
    setLanguage(selectedLang);
    localStorage.setItem('preferredLanguage', selectedLang);
    setFormData(prev => ({ ...prev, preferredLanguage: selectedLang }));
    setIsLanguageDropdownOpen(false);
  }, []);

  const handleFormLanguageSelect = useCallback((selectedLang) => {
    setFormData(prev => ({ ...prev, preferredLanguage: selectedLang }));
  }, []);

  // 社交登录处理函数
  const handleSocialLogin = useCallback((provider) => {
    console.log(`Login with ${provider}`);
  }, []);

  // 点击外部关闭语言下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isLanguageDropdownOpen && !event.target.closest('.floating-language-dropdown')) {
        setIsLanguageDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isLanguageDropdownOpen]);

  // 移动端滑动优化 - 防止过度滚动白边
  useEffect(() => {
    // 防止iOS Safari过度滚动
    const preventDefault = (e) => {
      if (e.touches && e.touches.length > 1) {
        e.preventDefault();
      }
    };

    // 防止双指缩放
    const preventZoom = (e) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
      }
    };

    // 防止橡皮筋效果
    const preventBounce = (e) => {
      const target = e.target;
      const scrollable = target.closest('.glassmorphic-login-page');
      
      if (!scrollable) {
        e.preventDefault();
        return;
      }

      const { scrollTop, scrollHeight, clientHeight } = scrollable;
      
      // 检查是否在顶部或底部
      const isAtTop = scrollTop === 0;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight;
      
      // 获取滑动方向
      const deltaY = e.deltaY || (e.touches && e.touches[0] ? 
        e.touches[0].clientY - (e.touches[0].startY || e.touches[0].clientY) : 0);
      
      // 在顶部向上滑动或在底部向下滑动时阻止默认行为
      if ((isAtTop && deltaY < 0) || (isAtBottom && deltaY > 0)) {
        e.preventDefault();
      }
    };

    // 添加事件监听器
    document.addEventListener('touchstart', preventDefault, { passive: false });
    document.addEventListener('touchmove', preventDefault, { passive: false });
    document.addEventListener('wheel', preventZoom, { passive: false });
    document.addEventListener('touchmove', preventBounce, { passive: false });
    
    // 设置viewport meta标签以防止缩放
    const viewport = document.querySelector('meta[name=viewport]');
    if (viewport) {
      viewport.setAttribute('content', 
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
      );
    }

    // 清理函数
    return () => {
      document.removeEventListener('touchstart', preventDefault);
      document.removeEventListener('touchmove', preventDefault);
      document.removeEventListener('wheel', preventZoom);
      document.removeEventListener('touchmove', preventBounce);
      
      // 恢复viewport设置
      if (viewport) {
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
      }
    };
  }, []);

  return (
    <div className="glassmorphic-login-page" data-lang={language}>
      <BeamsBackground />

      {/* 浮动导航按钮 */}
      <div className="floating-nav-buttons">
        {/* 左侧返回按钮 */}
        <button
          className="floating-back-button"
          onClick={() => navigate('/')}
        >
          <svg className="back-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path strokeLinecap="round" strokeLinejoin="round" d="M19 12H5M5 12L12 19M5 12L12 5" />
          </svg>
          {t.backToHome}
        </button>

        {/* 右侧简化语言切换 */}
        <button
          className="floating-language-simple"
          onClick={toggleLanguage}
          title={isEnglish ? 'Switch to Chinese' : '切换到英文'}
        >
          <svg className="language-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="2" y1="12" x2="22" y2="12"/>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
          </svg>
          {isEnglish ? 'EN' : '中文'}
        </button>
      </div>

      {/* Mobile Frame Container - 根据登录/注册模式应用不同的类 */}
      <div className={`mobile-frame ${!isLogin ? 'mobile-frame-wide' : ''}`}>
        <BadgeLanyard />

        <BadgeCard iconType={isLogin ? 'user' : 'welcome'} customClass={!isLogin ? 'badge-card-wide' : ''}>
          {/* Title - 保持不变的工牌头部 */}
          <div className="badge-header">
            <h1 className="form-title">
              {isLogin ? t.loginTitle : t.registerTitle}
            </h1>
            <p className="form-subtitle">
              {isLogin ? t.loginSubtitle : t.registerSubtitle}
            </p>
          </div>

          {/* Form */}
          {isLogin ? (
            // 登录表单 - 保持原有的垂直设计
            <form className="glassmorphic-form" onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="loginIdentifier" className="form-label">{t.loginIdentifier}</label>
                <div className="input-wrapper">
                  <UserIcon />
                  <input
                    type="text"
                    id="loginIdentifier"
                    name="loginIdentifier"
                    value={formData.loginIdentifier}
                    onChange={handleInputChange}
                    required
                    placeholder={t.loginIdentifier}
                    className={`form-input ${errors.loginIdentifier ? 'error' : ''}`}
                    autoComplete="username"
                  />
                </div>
                {errors.loginIdentifier && (
                  <ErrorMessage 
                    message={errors.loginIdentifier}
                    type={errors.errorType || 'submit'}
                    onClose={() => clearError('loginIdentifier')}
                  />
                )}
              </div>

              <div className="form-group">
                <label htmlFor="password" className="form-label">{t.password}</label>
                <div className="input-wrapper">
                  <LockIcon />
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    placeholder="••••••••••••"
                    className={`form-input ${errors.password ? 'error' : ''}`}
                    autoComplete="current-password"
                  />
                  <button
                    type="button"
                    className="password-toggle input-right-element"
                    onClick={togglePasswordVisibility}
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOffIcon /> : <EyeIcon />}
                  </button>
                </div>
                {errors.password && (
                  <ErrorMessage 
                    message={errors.password}
                    type={errors.errorType || 'submit'}
                    onClose={() => clearError('password')}
                  />
                )}
              </div>

              <div className="form-options">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  {t.rememberMe}
                </label>
                <button type="button" className="forgot-password">{t.forgotPassword}</button>
              </div>

              {/* 显示提交错误信息 - 使用新的ErrorMessage组件 */}
              {errors.submit && (
                <ErrorMessage 
                  message={errors.submit}
                  type={errors.errorType || 'submit'}
                  onClose={() => clearError('submit')}
                />
              )}

              {/* 显示成功信息 */}
              {errors.success && (
                <div className="success-message">
                  {errors.success}
                </div>
              )}

              {/* Action Buttons */}
              <div className="form-buttons">
                <button
                  type="submit"
                  className="btn-primary"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span>{t.loading}</span>
                  ) : (
                    <>
                      <span>{isLogin ? t.loginButton : t.registerButton}</span>
                    </>
                  )}
                </button>
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={toggleMode}
                  disabled={isLoading}
                >
                  {isLogin ? t.switchToRegister : t.switchToLogin}
                </button>
              </div>
              
              {/* Divider */}
              <div className="form-divider">
                <div className="divider-line"></div>
                <span className="divider-text">{t.orDivider}</span>
                <div className="divider-line"></div>
              </div>

              {/* Social Login */}
              <div className="social-login">
                <button
                  type="button"
                  onClick={() => handleSocialLogin('apple')}
                  className="social-button"
                  aria-label="Sign up with Apple"
                >
                  <AppleIcon />
                </button>
                <button
                  type="button"
                  onClick={() => handleSocialLogin('google')}
                  className="social-button"
                  aria-label="Sign up with Google"
                >
                  <GoogleIcon />
                </button>
                <button
                  type="button"
                  onClick={() => handleSocialLogin('x')}
                  className="social-button"
                  aria-label="Sign up with X"
                >
                  <XIcon />
                </button>
              </div>
            </form>
          ) : (
            // 注册表单 - 新的横向工牌设计
            <form className="glassmorphic-form register-form-horizontal" onSubmit={handleSubmit}>
              <div className="register-form-columns">
                <div className="register-form-column">
                  <div className="form-group">
                    <label htmlFor="username" className="form-label">{t.username}</label>
                    <div className="input-wrapper">
                      <UserIcon />
                      <input
                        type="text"
                        id="username"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        required
                        placeholder={t.username}
                        className={`form-input ${errors.username ? 'error' : ''}`}
                        autoComplete="username"
                      />
                    </div>
                    {errors.username && (
                      <ErrorMessage 
                        message={errors.username}
                        type={errors.errorType || 'submit'}
                        onClose={() => clearError('username')}
                      />
                    )}
                  </div>

                  <div className="form-group">
                    <label htmlFor="email" className="form-label">{t.email}</label>
                    <div className="input-wrapper">
                      <MailIcon />
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="<EMAIL>"
                        className={`form-input ${errors.email ? 'error' : ''}`}
                        autoComplete="email"
                      />
                    </div>
                    {errors.email && (
                      <ErrorMessage 
                        message={errors.email}
                        type={errors.errorType || 'submit'}
                        onClose={() => clearError('email')}
                      />
                    )}
                  </div>

                  {/* 语言偏好选择 */}
                  <div className="form-group">
                    <label className="form-label">{t.preferredLanguage}</label>
                    <LanguagePreferenceSelector
                      value={formData.preferredLanguage}
                      onChange={handleLanguageSelect}
                    />
                    {errors.preferredLanguage && <ErrorMessage message={errors.preferredLanguage} type="submit" onClose={() => clearError('preferredLanguage')} />}
                  </div>
                </div>

                <div className="register-form-column">
                  <div className="form-group">
                    <label htmlFor="password" className="form-label">{t.password}</label>
                    <div className="input-wrapper">
                      <LockIcon />
                      <input
                        type={showPassword ? "text" : "password"}
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                        placeholder="••••••••••••"
                        className={`form-input ${errors.password ? 'error' : ''}`}
                        autoComplete="new-password"
                      />
                      <button
                        type="button"
                        className="password-toggle input-right-element"
                        onClick={togglePasswordVisibility}
                        aria-label={showPassword ? "Hide password" : "Show password"}
                      >
                        {showPassword ? <EyeOffIcon /> : <EyeIcon />}
                      </button>
                    </div>
                    {errors.password && <ErrorMessage message={errors.password} type="submit" onClose={() => clearError('password')} />}

                    {/* 密码强度指示器 */}
                    {formData.password && (
                      <div className={`password-strength ${passwordStrength}`}>
                        <div className="password-strength-bar"></div>
                        <span className="password-strength-text">
                          {passwordStrength === 'weak' && (isEnglish ? 'Weak' : '弱')}
                          {passwordStrength === 'medium' && (isEnglish ? 'Medium' : '中等')}
                          {passwordStrength === 'strong' && (isEnglish ? 'Strong' : '强')}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="form-group">
                    <label htmlFor="confirmPassword" className="form-label">{t.confirmPassword}</label>
                    <div className="input-wrapper">
                      <LockIcon />
                      <input
                        type={showConfirmPassword ? "text" : "password"}
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        required
                        placeholder="••••••••••••"
                        className={`form-input ${errors.confirmPassword ? 'error' : ''}`}
                        autoComplete="new-password"
                      />
                      <button
                        type="button"
                        className="password-toggle input-right-element"
                        onClick={toggleConfirmPasswordVisibility}
                        aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                      >
                        {showConfirmPassword ? <EyeOffIcon /> : <EyeIcon />}
                      </button>
                    </div>
                    {errors.confirmPassword && (
                      <ErrorMessage 
                        message={errors.confirmPassword}
                        type={errors.errorType || 'submit'}
                        onClose={() => clearError('confirmPassword')}
                      />
                    )}
                  </div>
                </div>
              </div>

              {/* 显示提交错误信息 - 使用新的ErrorMessage组件 */}
              {errors.submit && (
                <ErrorMessage 
                  message={errors.submit}
                  type={errors.errorType || 'submit'}
                  onClose={() => clearError('submit')}
                />
              )}

              {/* 显示成功信息 - 注册表单 */}
              {errors.success && (
                <div className="success-message">
                  {errors.success}
                </div>
              )}

              <div className="register-form-footer">
                {/* Action Buttons */}
                <div className="form-buttons">
                  <StarBorder
                    as="button"
                    type="submit"
                    color="#00ffff"
                    speed="5s"
                    thickness={1}
                    disabled={isLoading}
                    className="star-border-primary"
                  >
                    {isLoading ? (
                      <span>{t.loading}</span>
                    ) : (
                      <span>{t.registerButton}</span>
                    )}
                  </StarBorder>
                  <StarBorder
                    as="button"
                    type="button"
                    color="#ff00ff"
                    speed="5s"
                    thickness={1}
                    onClick={toggleMode}
                    className="star-border-secondary"
                    disabled={isLoading}
                  >
                    {t.switchToLogin}
                  </StarBorder>
                </div>

                {/* Social Login */}
                <div className="social-login horizontal">
                  <div className="form-divider horizontal">
                    <div className="divider-line"></div>
                    <span className="divider-text">{t.orDivider}</span>
                    <div className="divider-line"></div>
                  </div>
                  <div className="social-buttons">
                    <button
                      type="button"
                      onClick={() => handleSocialLogin('apple')}
                      className="social-button"
                      aria-label="Sign up with Apple"
                    >
                      <AppleIcon />
                    </button>
                    <button
                      type="button"
                      onClick={() => handleSocialLogin('google')}
                      className="social-button"
                      aria-label="Sign up with Google"
                    >
                      <GoogleIcon />
                    </button>
                    <button
                      type="button"
                      onClick={() => handleSocialLogin('x')}
                      className="social-button"
                      aria-label="Sign up with X"
                    >
                      <XIcon />
                    </button>
                  </div>
                </div>
              </div>
            </form>
          )}
        </BadgeCard>
      </div>
    </div>
  );
});

// 设置显示名称以便调试
LoginPage.displayName = 'LoginPage';
BeamsBackground.displayName = 'BeamsBackground';
BadgeLanyard.displayName = 'BadgeLanyard';

export default LoginPage;

/* Detail Page Zoom Controls - Updated to match app style */
.detail-zoom-controls {
  position: absolute;
  top: 120px; /* Position below the analysis button */
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
  pointer-events: auto;
}

.detail-zoom-button {
  width: 40px;
  height: 40px;
  background-color: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  pointer-events: auto;
  color: #333;
}

.detail-zoom-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.detail-zoom-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

/* Dark mode styles */
.detail-zoom-controls.dark-mode .detail-zoom-button {
  background-color: #2c3e50;
  border-color: rgba(255, 255, 255, 0.1);
  color: #ecf0f1;
}

.detail-zoom-controls.dark-mode .detail-zoom-button:hover {
  background-color: #34495e;
}

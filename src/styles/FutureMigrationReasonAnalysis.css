/* FutureMigrationReasonAnalysis.css */
.future-migration-reason-container {
  padding: 20px;
  background-color: var(--background-color, #f5f5f5);
  color: var(--text-color, #333);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.future-migration-reason-container h2 {
  margin-bottom: 15px;
  font-size: 24px;
  color: var(--heading-color, #2c3e50);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.analysis-description {
  margin-bottom: 25px;
  color: var(--secondary-text-color, #666);
  font-size: 15px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.migration-analysis-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  flex: 1;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 14px;
  color: var(--label-color, #555);
  display: flex;
  align-items: center;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  background-color: var(--input-background, #fff);
  color: var(--input-text-color, #333);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group select:focus {
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

.data-dashboard {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.stat-tiles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 10px;
}

.stat-tile {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
}

.stat-tile:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-tile h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color, #2c3e50);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--value-color, #2c3e50);
}

.stat-percentage {
  font-size: 14px;
  color: var(--percentage-color, #7f8c8d);
}

.stat-note {
  font-size: 12px;
  color: var(--note-color, #95a5a6);
  margin-top: 5px;
}

.stat-tile {
  border-top: 2px solid transparent;
}

.stat-tile.total-population {
  border-top-color: #3498db;
}

.stat-tile.job-search {
  border-top-color: #e74c3c;
}

.stat-tile.job-change {
  border-top-color: #2ecc71;
}

.stat-tile.return-hometown {
  border-top-color: #f39c12;
}

.chart-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 10px;
}

.chart-box {
  flex: 1;
  min-width: 300px;
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chart-box.full-width {
  flex-basis: 100%;
}

.chart-heading {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color, #2c3e50);
  text-align: center;
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-note {
  font-size: 12px;
  color: var(--note-color, #95a5a6);
  text-align: center;
  margin-top: 10px;
}

.insights-panel {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.insights-heading {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--heading-color, #2c3e50);
  display: flex;
  align-items: center;
  gap: 10px;
}

.insights-heading i {
  color: #f39c12;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.insight-card {
  background-color: var(--insight-background, #f8f9fa);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #3498db;
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.insight-header i {
  color: #3498db;
  font-size: 18px;
}

.insight-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color, #2c3e50);
}

.insight-card p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--secondary-text-color, #666);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: var(--secondary-text-color, #666);
}

.error-message {
  padding: 20px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.5;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .future-migration-reason-container {
    --background-color: #1a1a1a;
    --text-color: #e0e0e0;
    --heading-color: #f0f0f0;
    --secondary-text-color: #b0b0b0;
    --card-background: #2a2a2a;
    --border-color: #444;
    --input-background: #333;
    --input-text-color: #e0e0e0;
    --label-color: #c0c0c0;
    --value-color: #f0f0f0;
    --percentage-color: #a0a0a0;
    --note-color: #909090;
    --insight-background: #2d2d2d;
  }

  .stat-tile:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .error-message {
    background-color: #3a1a1a;
    color: #ff8a80;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stat-tiles {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-row {
    flex-direction: column;
  }

  .chart-box {
    min-width: 100%;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stat-tiles {
    grid-template-columns: 1fr;
  }

  .filter-group {
    min-width: 100%;
  }
}

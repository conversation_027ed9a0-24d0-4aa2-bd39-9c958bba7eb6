/* 比例尺控件样式 */
.leaflet-control-scale {
  margin-bottom: 15px;
  margin-left: 15px;
}

.leaflet-control-scale-line {
  border: 2px solid rgba(0, 0, 0, 0.4);
  border-top: none;
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 2px 5px 1px;
  line-height: 1.1;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.15);
  border-radius: 0 0 4px 4px;
  transition: all 0.3s ease;
}

/* 日间模式下的比例尺样式 */
[data-map-style="day"] .leaflet-control-scale-line {
  background-color: rgba(255, 255, 255, 0.8);
  border-color: rgba(0, 0, 0, 0.4);
  color: #333;
}

/* 夜间模式下的比例尺样式 */
[data-map-style="night"] .leaflet-control-scale-line {
  background-color: rgba(40, 40, 40, 0.8);
  border-color: rgba(255, 255, 255, 0.4);
  color: #f0f0f0;
}

/* 卫星模式下的比例尺样式 */
[data-map-style="satellite"] .leaflet-control-scale-line {
  background-color: rgba(255, 255, 255, 0.85);
  border-color: rgba(0, 0, 0, 0.5);
  color: #333;
  font-weight: 600;
}

/* 悬停效果 */
.leaflet-control-scale:hover .leaflet-control-scale-line {
  background-color: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

[data-map-style="night"] .leaflet-control-scale:hover .leaflet-control-scale-line {
  background-color: rgba(50, 50, 50, 0.9);
  border-color: rgba(255, 255, 255, 0.5);
}

[data-map-style="satellite"] .leaflet-control-scale:hover .leaflet-control-scale-line {
  background-color: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.6);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}
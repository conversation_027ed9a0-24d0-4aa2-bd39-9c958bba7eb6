/* Main container styles */
.education-level-container {
  padding: 24px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  overflow: hidden;
}

.education-level-container h2 {
  color: #2c3e50;
  font-size: 28px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #3498db;
  position: relative;
  display: block;
  text-align: center;
  width: 100%;
}

.education-level-container h2:after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3498db, transparent);
}

/* Analysis description */
.analysis-description {
  color: #4a5568;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
  text-align: center;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

/* Education analysis wrapper */
.education-analysis-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Filters container */
.filters-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background-color: #f0f7ff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
  position: relative;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.filters-container:before {
  content: 'Filters';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #3498db;
  color: white;
  padding: 4px 16px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  position: relative;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.filter-group select {
  padding: 10px 14px;
  border: 1px solid #bdc3c7;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #34495e;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c3e50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 35px;
}

.filter-group select:hover {
  border-color: #3498db;
}

.filter-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Data dashboard */
.data-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Stat tiles */
.stat-tiles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 16px;
}

.stat-tile {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-tile:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.stat-tile {
  border-top: 2px solid transparent;
}

.stat-tile.population {
  border-top-color: #3498db;
}

.stat-tile.migrant {
  border-top-color: #e74c3c;
}

.stat-tile.non-migrant {
  border-top-color: #2ecc71;
}

.stat-tile.education-rate {
  border-top-color: #f39c12;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-content h3 {
  font-size: 16px;
  color: #4a5568;
  margin: 0;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
}

.stat-percentage {
  font-size: 14px;
  color: #718096;
}

.stat-note {
  font-size: 12px;
  color: #718096;
  margin-top: 4px;
}

/* Chart section */
.chart-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
}

.chart-box {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.chart-box.full-width {
  grid-column: 1 / -1;
}

.chart-heading {
  font-size: 18px;
  color: #2d3748;
  margin: 0 0 16px 0;
  text-align: center;
}

.chart-container {
  height: 350px;
  position: relative;
}

.chart-note {
  font-size: 14px;
  color: #718096;
  text-align: center;
  margin-top: 12px;
  font-style: italic;
}

/* Insights panel */
.insights-panel {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.insights-heading {
  font-size: 20px;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.insights-heading i {
  color: #3498db;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insight-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.insight-header i {
  color: #3498db;
  font-size: 18px;
}

.insight-header h4 {
  font-size: 16px;
  color: #2d3748;
  margin: 0;
}

.insight-card p {
  color: #4a5568;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* Loading and error states */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  font-size: 18px;
  color: #3498db;
}

.loading-container:before {
  content: '';
  width: 30px;
  height: 30px;
  margin-right: 15px;
  border: 3px solid #e2e8f0;
  border-top-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  color: #e53e3e;
  border-left: 4px solid #e53e3e;
  padding: 16px;
  background-color: #fff5f5;
  border-radius: 8px;
  margin: 24px 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .education-level-container h2 {
    font-size: 24px;
  }

  .control-filters {
    flex-direction: column;
  }

  .chart-row {
    grid-template-columns: 1fr;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .stat-value {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .education-level-container {
    padding: 16px;
  }

  .stat-tiles {
    grid-template-columns: 1fr;
  }

  .stat-value {
    font-size: 24px;
  }
}

/* MigrationTypeAnalysis.css */
.migration-type-container {
  padding: 20px;
  background-color: var(--background-color, #f5f5f5);
  color: var(--text-color, #333);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.migration-type-container h2 {
  margin-bottom: 15px;
  font-size: 24px;
  color: var(--heading-color, #2c3e50);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.analysis-description {
  margin-bottom: 25px;
  color: var(--secondary-text-color, #666);
  font-size: 15px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.migration-analysis-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  flex: 1;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 14px;
  color: var(--label-color, #555);
  display: flex;
  align-items: center;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  background-color: var(--input-background, #fff);
  color: var(--input-text-color, #333);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group select:focus {
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

.data-dashboard {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.stat-tiles {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.stat-tile {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-tile:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-content h3 {
  font-size: 16px;
  margin-bottom: 10px;
  color: var(--heading-color, #2c3e50);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color, #3498db);
  margin-bottom: 5px;
}

.stat-percentage {
  font-size: 14px;
  color: var(--secondary-text-color, #666);
}

.stat-note {
  font-size: 12px;
  color: var(--tertiary-text-color, #999);
  margin-top: 5px;
}

.total-migration .stat-value {
  color: var(--primary-color, #3498db);
}

.within-region .stat-value {
  color: #27ae60;
}

.between-regions .stat-value {
  color: #e74c3c;
}

.from-abroad .stat-value {
  color: #f39c12;
}

.map-chart-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.map-container {
  width: 100%;
}

.interactive-map {
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.map-loading {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--card-background-alt, #f8f9fa);
  border-radius: 8px;
}

.map-loading p {
  margin-bottom: 15px;
  color: var(--secondary-text-color, #666);
}

.map-loading-spinner {
  font-size: 24px;
  color: var(--primary-color, #3498db);
}

.map-note {
  margin-top: 10px;
  font-size: 13px;
  color: var(--secondary-text-color, #666);
  text-align: center;
  font-style: italic;
}

.chart-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-row {
  display: flex;
  gap: 20px;
}

.chart-box {
  flex: 1;
  min-width: 0;
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chart-box.full-width {
  width: 100%;
}

.chart-heading {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--heading-color, #2c3e50);
  text-align: center;
}

.chart-container {
  height: 250px;
  position: relative;
}

.insights-panel {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.insights-heading {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 20px;
  margin-bottom: 20px;
  color: var(--heading-color, #2c3e50);
}

.insights-heading i {
  color: #f39c12;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.insight-card {
  background-color: var(--card-background-alt, #f8f9fa);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.insight-header i {
  color: var(--primary-color, #3498db);
  font-size: 18px;
}

.insight-header h4 {
  font-size: 16px;
  margin: 0;
  color: var(--heading-color, #2c3e50);
}

.insight-card p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--secondary-text-color, #666);
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .stat-tiles {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .chart-row {
    flex-direction: column;
  }

  .chart-box {
    width: 100%;
  }

  .insights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .stat-tiles {
    grid-template-columns: 1fr;
  }

  .filter-group {
    min-width: 100%;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}

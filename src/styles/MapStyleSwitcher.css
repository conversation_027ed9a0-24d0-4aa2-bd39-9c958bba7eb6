.map-style-switcher {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1002; /* Higher than weather button and zoom controls */
  display: flex;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  overflow: hidden;
  border: 2px solid rgba(0, 0, 0, 0.2);
  pointer-events: auto;
}

.style-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #333;
  font-size: 0.9rem;
  font-weight: 500;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.style-button:last-child {
  border-right: none;
}

.style-button:hover {
  background: #f0f0f0;
}

.style-button.active {
  background: #f0f7ff;
  color: #2c3e50;
  font-weight: 600;
}

.style-icon {
  margin-right: 6px;
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .style-name {
    display: none;
  }
  
  .style-button {
    padding: 8px;
  }
  
  .style-icon {
    margin-right: 0;
  }
}
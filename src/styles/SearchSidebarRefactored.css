/**
 * 🔍 Refactored Search Sidebar Styles
 * Professional, Modern, User-Centric Design
 * 重构的搜索侧边栏样式 - 专业现代化用户导向设计
 */

/* ===== 核心变量系统 ===== */
:root {
  /* 现代化色彩系统 */
  --search-primary: #2563eb;
  --search-primary-light: #3b82f6;
  --search-secondary: #059669;
  --search-accent: #7c3aed;
  --search-success: #16a34a;
  --search-warning: #ea580c;
  
  /* 背景色系 */
  --search-bg-primary: #0f172a;
  --search-bg-secondary: #1e293b;
  --search-bg-tertiary: #334155;
  --search-bg-surface: #475569;
  --search-bg-glass: rgba(30, 41, 59, 0.95);
  
  /* 渐变系统 */
  --search-gradient-primary: linear-gradient(135deg, var(--search-primary) 0%, var(--search-secondary) 100%);
  --search-gradient-accent: linear-gradient(135deg, var(--search-accent) 0%, var(--search-primary) 100%);
  --search-gradient-surface: linear-gradient(145deg, var(--search-bg-secondary) 0%, var(--search-bg-tertiary) 100%);
  
  /* 文字色系 */
  --search-text-primary: #f8fafc;
  --search-text-secondary: #e2e8f0;
  --search-text-muted: #94a3b8;
  --search-text-accent: var(--search-primary-light);
  
  /* 边框和阴影 */
  --search-border-primary: rgba(59, 130, 246, 0.3);
  --search-border-secondary: rgba(148, 163, 184, 0.2);
  --search-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --search-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --search-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  --search-shadow-glow: 0 0 20px rgba(37, 99, 235, 0.3);
  
  /* 间距系统 */
  --search-space-1: 0.25rem;
  --search-space-2: 0.5rem;
  --search-space-3: 0.75rem;
  --search-space-4: 1rem;
  --search-space-5: 1.25rem;
  --search-space-6: 1.5rem;
  --search-space-8: 2rem;
  
  /* 圆角系统 */
  --search-radius-sm: 6px;
  --search-radius-md: 8px;
  --search-radius-lg: 12px;
  --search-radius-xl: 16px;
  
  /* 过渡效果 */
  --search-transition-fast: 0.15s ease;
  --search-transition-normal: 0.25s ease;
  --search-transition-slow: 0.35s ease;
}

/* ===== 主要侧边栏容器 ===== */
.search-sidebar-refactored {
  background: var(--search-bg-glass);
  backdrop-filter: blur(20px);
  border-radius: var(--search-radius-xl);
  border: 1px solid var(--search-border-primary);
  padding: var(--search-space-6);
  box-shadow: var(--search-shadow-lg);
  overflow-y: auto;
  height: fit-content;
  max-height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--search-space-6);
}

.search-sidebar-refactored::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--search-gradient-primary);
  border-radius: var(--search-radius-xl) var(--search-radius-xl) 0 0;
}

/* ===== 现代化头部设计 ===== */
.search-header-modern {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--search-space-4);
  padding-bottom: var(--search-space-6);
  border-bottom: 2px solid var(--search-border-secondary);
}

.header-title-section {
  display: flex;
  align-items: flex-start;
  gap: var(--search-space-4);
  flex: 1;
}

.title-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--search-gradient-primary);
  border-radius: var(--search-radius-lg);
  color: white;
  box-shadow: var(--search-shadow-md);
  flex-shrink: 0;
}

.search-icon {
  width: 20px;
  height: 20px;
}

.title-content {
  flex: 1;
}

.search-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--search-text-primary);
  margin: 0 0 var(--search-space-2) 0;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.search-subtitle {
  font-size: 0.875rem;
  color: var(--search-text-muted);
  margin: 0;
  line-height: 1.4;
  font-weight: 500;
}

.results-indicator-modern {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.results-count-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--search-space-3) var(--search-space-4);
  background: var(--search-gradient-accent);
  border-radius: var(--search-radius-lg);
  color: white;
  box-shadow: var(--search-shadow-sm);
  min-width: 60px;
}

.count-number {
  font-size: 1.25rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: var(--search-space-1);
}

.count-label {
  font-size: 0.75rem;
  font-weight: 600;
  opacity: 0.9;
  text-align: center;
}

/* ===== 现代化表单设计 ===== */
.search-form-modern {
  display: flex;
  flex-direction: column;
  gap: var(--search-space-6);
  flex: 1;
}

/* ===== 搜索区块设计 ===== */
.search-section {
  background: var(--search-gradient-surface);
  border-radius: var(--search-radius-lg);
  border: 1px solid var(--search-border-secondary);
  overflow: hidden;
  transition: all var(--search-transition-normal);
  position: relative;
}

.search-section:hover {
  border-color: var(--search-border-primary);
  box-shadow: var(--search-shadow-md);
  transform: translateY(-1px);
}

.section-header {
  display: flex;
  align-items: center;
  gap: var(--search-space-4);
  padding: var(--search-space-5);
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid var(--search-border-secondary);
  cursor: pointer;
  transition: all var(--search-transition-normal);
}

.section-header.collapsible:hover {
  background: rgba(255, 255, 255, 0.05);
}

.section-icon {
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--search-bg-tertiary);
  border-radius: var(--search-radius-md);
  border: 1px solid var(--search-border-secondary);
  flex-shrink: 0;
}

.section-title {
  flex: 1;
}

.section-title h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--search-text-primary);
  margin: 0 0 var(--search-space-1) 0;
  line-height: 1.2;
}

.section-title p {
  font-size: 0.875rem;
  color: var(--search-text-muted);
  margin: 0;
  line-height: 1.3;
  font-weight: 500;
}

.expand-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: var(--search-text-muted);
  transition: all var(--search-transition-normal);
}

.expand-indicator.expanded {
  transform: rotate(180deg);
  color: var(--search-text-accent);
}

.section-content {
  padding: var(--search-space-5);
  display: flex;
  flex-direction: column;
  gap: var(--search-space-4);
}

/* 可折叠内容动画 */
.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: all var(--search-transition-slow);
  padding: 0 var(--search-space-5);
}

.collapsible-content.expanded {
  max-height: 1000px;
  padding: var(--search-space-5);
}

/* ===== 现代化输入组件 ===== */
.input-group-modern {
  display: flex;
  flex-direction: column;
  gap: var(--search-space-3);
}

.modern-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1rem;
  font-weight: 700;
  color: var(--search-text-primary);
}

.label-text {
  display: flex;
  align-items: center;
  gap: var(--search-space-2);
}

.label-text::before {
  content: '';
  width: 4px;
  height: 4px;
  background: var(--search-secondary);
  border-radius: 50%;
  flex-shrink: 0;
}

.label-indicator {
  font-size: 0.75rem;
  font-weight: 600;
  padding: var(--search-space-1) var(--search-space-2);
  border-radius: var(--search-radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.label-indicator.required {
  background: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.label-indicator.optional {
  background: rgba(148, 163, 184, 0.2);
  color: var(--search-text-muted);
}

.input-wrapper-enhanced {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-input {
  width: 100%;
  padding: var(--search-space-4) var(--search-space-5);
  padding-right: calc(var(--search-space-5) + 32px);
  background: var(--search-bg-tertiary);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  color: var(--search-text-primary);
  font-size: 1rem;
  font-weight: 600;
  transition: all var(--search-transition-normal);
  min-height: 52px;
}

.modern-input:focus {
  outline: none;
  border-color: var(--search-primary);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
}

.modern-input:hover {
  border-color: var(--search-border-primary);
  box-shadow: var(--search-shadow-sm);
}

.modern-input::placeholder {
  color: var(--search-text-muted);
  font-weight: 400;
}

.input-icon {
  position: absolute;
  right: var(--search-space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--search-text-muted);
  pointer-events: none;
  transition: all var(--search-transition-normal);
}

.input-wrapper-enhanced:focus-within .input-icon {
  color: var(--search-primary);
}

/* ===== 现代化范围控制 ===== */
.range-display-modern {
  margin-bottom: var(--search-space-5);
}

.range-values-container {
  display: flex;
  align-items: center;
  gap: var(--search-space-4);
}

.range-value-card {
  flex: 1;
  background: var(--search-bg-tertiary);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  padding: var(--search-space-4);
  transition: all var(--search-transition-normal);
}

.range-value-card:hover {
  border-color: var(--search-border-primary);
  box-shadow: var(--search-shadow-sm);
  transform: translateY(-1px);
}

.range-value-card.min-value {
  border-left: 4px solid var(--search-primary);
}

.range-value-card.max-value {
  border-left: 4px solid var(--search-secondary);
}

.value-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--search-space-2);
}

.value-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--search-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.value-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.value-indicator.min {
  background: var(--search-primary);
}

.value-indicator.max {
  background: var(--search-secondary);
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: var(--search-space-2);
}

.value-number {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--search-text-primary);
  line-height: 1;
}

.value-unit {
  font-size: 0.875rem;
  color: var(--search-text-muted);
  font-weight: 500;
}

.range-connector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--search-space-2);
  flex-shrink: 0;
}

.connector-line {
  width: 2px;
  height: 20px;
  background: var(--search-gradient-primary);
  border-radius: 1px;
}

.connector-icon {
  color: var(--search-text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 现代化滑块控制 */
.modern-range-slider {
  margin: var(--search-space-4) 0;
}

.slider-container {
  position: relative;
  height: 48px;
  padding: var(--search-space-4) 0;
}

.slider-track-modern {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 6px;
  background: var(--search-bg-tertiary);
  border-radius: 3px;
  transform: translateY(-50%);
  border: 1px solid var(--search-border-secondary);
}

.slider-range-modern {
  position: absolute;
  height: 100%;
  background: var(--search-gradient-primary);
  border-radius: 3px;
  transition: all var(--search-transition-normal);
  box-shadow: 0 0 12px rgba(37, 99, 235, 0.3);
}

.range-input-modern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 48px;
  background: transparent;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  pointer-events: all;
}

.range-input-modern::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: white;
  border: 3px solid var(--search-primary);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--search-shadow-md);
  transition: all var(--search-transition-normal);
}

.range-input-modern::-webkit-slider-thumb:hover {
  transform: scale(1.15);
  box-shadow: var(--search-shadow-lg);
}

.range-input-modern.max-range::-webkit-slider-thumb {
  border-color: var(--search-secondary);
}

/* 智能预设选择 */
.size-presets-modern {
  margin-top: var(--search-space-4);
}

.presets-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--search-text-secondary);
  margin-bottom: var(--search-space-3);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.presets-grid {
  display: flex;
  flex-direction: column;
  gap: var(--search-space-3);
}

.preset-card {
  display: flex;
  align-items: center;
  gap: var(--search-space-4);
  padding: var(--search-space-4);
  background: var(--search-bg-tertiary);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  cursor: pointer;
  transition: all var(--search-transition-normal);
  text-align: left;
}

.preset-card:hover {
  border-color: var(--search-border-primary);
  box-shadow: var(--search-shadow-sm);
  transform: translateY(-1px);
}

.preset-card.active {
  background: rgba(37, 99, 235, 0.1);
  border-color: var(--search-primary);
  box-shadow: var(--search-shadow-glow);
}

.preset-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--search-bg-surface);
  border-radius: var(--search-radius-md);
  flex-shrink: 0;
}

.preset-content {
  flex: 1;
}

.preset-title {
  font-size: 1rem;
  font-weight: 700;
  color: var(--search-text-primary);
  margin-bottom: var(--search-space-1);
  line-height: 1.2;
}

.preset-desc {
  font-size: 0.875rem;
  color: var(--search-text-muted);
  line-height: 1.3;
}

/* ===== 运营参数网格 ===== */
.operational-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--search-space-4);
}

.param-card {
  background: var(--search-bg-tertiary);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  padding: var(--search-space-5);
  transition: all var(--search-transition-normal);
}

.param-card:hover {
  border-color: var(--search-border-primary);
  box-shadow: var(--search-shadow-sm);
  transform: translateY(-1px);
}

.param-card.full-width {
  grid-column: 1 / -1;
}

.param-header {
  display: flex;
  align-items: flex-start;
  gap: var(--search-space-4);
  margin-bottom: var(--search-space-4);
}

.param-icon {
  font-size: 1.25rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--search-gradient-primary);
  border-radius: var(--search-radius-md);
  color: white;
  flex-shrink: 0;
  box-shadow: var(--search-shadow-sm);
}

.param-info {
  flex: 1;
}

.param-title {
  font-size: 1rem;
  font-weight: 700;
  color: var(--search-text-primary);
  margin-bottom: var(--search-space-1);
  line-height: 1.2;
}

.param-subtitle {
  font-size: 0.875rem;
  color: var(--search-text-muted);
  margin: 0;
  line-height: 1.3;
  font-weight: 500;
}

.param-input-modern {
  display: flex;
  align-items: center;
  background: var(--search-bg-surface);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  overflow: hidden;
  transition: all var(--search-transition-normal);
}

.param-input-modern:focus-within {
  border-color: var(--search-primary);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

.number-input-modern {
  flex: 1;
  padding: var(--search-space-4) var(--search-space-5);
  background: transparent;
  border: none;
  color: var(--search-text-primary);
  font-size: 1rem;
  font-weight: 600;
  outline: none;
  min-height: 48px;
}

.number-input-modern::placeholder {
  color: var(--search-text-muted);
  font-weight: 400;
}

.input-unit {
  background: var(--search-bg-tertiary);
  padding: var(--search-space-4) var(--search-space-5);
  font-size: 0.875rem;
  color: var(--search-text-secondary);
  font-weight: 600;
  border-left: 2px solid var(--search-border-secondary);
  min-width: 60px;
  text-align: center;
}

/* 候选站点选择器 */
.candidates-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--search-space-3);
  margin-top: var(--search-space-3);
}

.candidate-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--search-space-3);
  background: var(--search-bg-surface);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  cursor: pointer;
  transition: all var(--search-transition-normal);
  min-height: 60px;
  gap: var(--search-space-1);
}

.candidate-option:hover {
  border-color: var(--search-border-primary);
  box-shadow: var(--search-shadow-sm);
  transform: translateY(-1px);
}

.candidate-option.active {
  background: var(--search-gradient-primary);
  border-color: var(--search-primary);
  color: white;
  box-shadow: var(--search-shadow-glow);
}

.option-number {
  font-size: 1.125rem;
  font-weight: 800;
  color: inherit;
  line-height: 1;
}

.option-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: inherit;
  opacity: 0.8;
}

/* ===== 搜索执行区域 ===== */
.search-action-section {
  background: var(--search-gradient-surface);
  border-radius: var(--search-radius-lg);
  border: 1px solid var(--search-border-secondary);
  padding: var(--search-space-6);
  display: flex;
  flex-direction: column;
  gap: var(--search-space-5);
}

.search-summary {
  display: flex;
  flex-direction: column;
  gap: var(--search-space-3);
  padding: var(--search-space-4);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--search-radius-md);
  border: 1px solid var(--search-border-secondary);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--search-space-3);
}

.summary-label {
  font-size: 0.875rem;
  color: var(--search-text-muted);
  font-weight: 500;
}

.summary-value {
  font-size: 0.875rem;
  color: var(--search-text-primary);
  font-weight: 600;
  text-align: right;
  flex-shrink: 0;
}

/* ===== 现代化搜索按钮 ===== */
.search-button-modern {
  width: 100%;
  background: var(--search-gradient-primary);
  border: none;
  border-radius: var(--search-radius-xl);
  color: white;
  cursor: pointer;
  transition: all var(--search-transition-normal);
  position: relative;
  overflow: hidden;
  min-height: 64px;
  box-shadow: var(--search-shadow-md);
}

.search-button-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.search-button-modern:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--search-shadow-lg);
}

.search-button-modern:hover:not(:disabled)::before {
  left: 100%;
}

.search-button-modern:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--search-space-3);
  padding: var(--search-space-5) var(--search-space-6);
  position: relative;
  z-index: 1;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.button-text {
  font-size: 1.125rem;
  font-weight: 700;
  letter-spacing: 0.02em;
}

.button-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: all var(--search-transition-normal);
}

.search-button-modern:hover:not(:disabled) .button-arrow {
  transform: translateX(4px);
}

/* 加载状态 */
.loading-spinner-modern {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.progress-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: white;
  width: 0%;
  animation: progress 2s ease-in-out infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* ===== 快速操作按钮 ===== */
.quick-actions {
  display: flex;
  gap: var(--search-space-3);
}

.quick-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--search-space-2);
  padding: var(--search-space-3) var(--search-space-4);
  background: var(--search-bg-tertiary);
  border: 2px solid var(--search-border-secondary);
  border-radius: var(--search-radius-lg);
  color: var(--search-text-secondary);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--search-transition-normal);
}

.quick-action-btn:hover {
  background: var(--search-bg-surface);
  border-color: var(--search-border-primary);
  color: var(--search-text-primary);
  transform: translateY(-1px);
  box-shadow: var(--search-shadow-sm);
}

.action-icon {
  font-size: 1rem;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
  .search-sidebar-refactored {
    padding: var(--search-space-5);
    gap: var(--search-space-5);
  }

  .header-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--search-space-3);
  }

  .title-icon-wrapper {
    width: 40px;
    height: 40px;
  }

  .search-title {
    font-size: 1.25rem;
  }

  .operational-grid {
    gap: var(--search-space-3);
  }

  .candidates-selector {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .search-sidebar-refactored {
    padding: var(--search-space-4);
    gap: var(--search-space-4);
  }

  .section-content {
    padding: var(--search-space-4);
    gap: var(--search-space-3);
  }

  .range-values-container {
    flex-direction: column;
    gap: var(--search-space-3);
  }

  .range-connector {
    flex-direction: row;
    width: 100%;
    height: auto;
  }

  .connector-line {
    width: 100%;
    height: 2px;
  }

  .presets-grid {
    gap: var(--search-space-2);
  }

  .quick-actions {
    flex-direction: column;
  }
}

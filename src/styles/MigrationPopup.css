.migration-popup-content-wrapper {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 280px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  pointer-events: auto !important;
  cursor: default !important;
}

.migration-popup-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.migration-popup-header h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
  text-align: center;
  width: 100%;
  justify-content: center;
}

.language-toggle-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.language-toggle-btn:hover {
  background-color: #e0e0e0;
}

.migration-total {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 5px;
}

.total-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.total-value {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
}

.migration-data-view, .migration-chart-view {
  display: flex;
  flex-direction: column;
}

.migration-data-blocks {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.migration-data-block {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.migration-data-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.migration-data-values {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
}

.migration-data-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.migration-data-percentage {
  font-size: 14px;
  color: #666;
}

.migration-data-bar-container {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.migration-data-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.toggle-chart-btn {
  align-self: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 6px 15px;
  font-size: 13px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
  margin-top: 10px;
  width: 100%;
  max-width: 200px;
}

.toggle-chart-btn:hover {
  background-color: #e8e8e8;
}

.migration-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.migration-pie-chart-wrapper {
  width: 180px;
  height: 180px;
  margin-bottom: 15px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
  width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.no-chart-data {
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .migration-popup-content-wrapper {
    width: 280px;
  }

  .migration-pie-chart-wrapper {
    width: 150px;
    height: 150px;
  }
}

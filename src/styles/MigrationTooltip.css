.leaflet-tooltip.migration-tooltip-compact {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 10px 15px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  min-width: 180px;
  max-width: 240px;
  pointer-events: auto !important;
  cursor: pointer !important;
  z-index: 1500 !important;
  opacity: 1 !important;
}

.leaflet-tooltip.migration-tooltip-compact.leaflet-tooltip-top:before {
  border-top-color: rgba(0, 0, 0, 0.1);
}

.migration-tooltip-header {
  margin-bottom: 5px;
  text-align: center;
}

.migration-tooltip-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.migration-total {
  text-align: center;
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
  margin-bottom: 5px;
}

.migration-click-hint {
  text-align: center;
  font-size: 12px;
  color: #888;
  font-style: italic;
}

/**
 * 🌊 Liquid Glass Material System
 * 液体玻璃材料系统 - 核心实现
 */

/* ===== 基础硬件加速 ===== */
.gpu-accelerated {
  will-change: transform, opacity, backdrop-filter;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== 核心液体玻璃效果类 ===== */
.glass-effect {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.08);
  
  /* 关键：链接到SVG滤镜 */
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  
  /* 液体玻璃的基础质感 */
  background-image: 
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 60%);
  
  position: relative;
  overflow: hidden;
  
  /* 平滑过渡 */
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 玻璃效果的反射层 */
.glass-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 45%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0.1) 55%, 
    transparent 100%
  );
  transform: skewX(-15deg);
  transition: left 0.8s ease;
  pointer-events: none;
  z-index: 1;
}

/* 悬停时的反射效果 */
.glass-effect:hover::before {
  left: 100%;
}

/* 玻璃效果变体 */
.glass-effect-light {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
}

.glass-effect-accent {
  background: rgba(124, 58, 237, 0.15);
  border-color: rgba(124, 58, 237, 0.3);
  box-shadow: 
    0 0 0 1px rgba(124, 58, 237, 0.2),
    0 8px 32px rgba(124, 58, 237, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.08);
}

.glass-effect-success {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 
    0 0 0 1px rgba(16, 185, 129, 0.2),
    0 8px 32px rgba(16, 185, 129, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.08);
}

.glass-effect-warning {
  background: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 
    0 0 0 1px rgba(245, 158, 11, 0.2),
    0 8px 32px rgba(245, 158, 11, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.08);
}

/* ===== 三层Z轴架构 ===== */

/* 背景层 (z-index: 0) */
.background-layer {
  z-index: 0;
  position: relative;
}

/* 玻璃层 (z-index: 1) */
.glass-layer {
  z-index: 1;
  position: relative;
}

/* 内容层 (z-index: 2) */
.content-layer {
  z-index: 2;
  position: relative;
}

/* 内容层特定元素样式 */
.content-layer-heading {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  z-index: 2;
  position: relative;
}

.content-layer-text {
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  z-index: 2;
  position: relative;
}

.metric-value-emphasis {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 700;
  font-size: 1.25em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 2;
  position: relative;
}

/* ===== 聚焦驱动的清晰度系统 ===== */

/* 主要操作按钮 - 完全不透明 */
.button-primary-solid {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(248, 250, 252, 0.9) 100%
  );
  color: #1e293b;
  border: none;
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.15),
    0 4px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: none;
  z-index: 3;
  position: relative;
}

.button-primary-solid:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 16px 40px rgba(0, 0, 0, 0.2),
    0 8px 16px rgba(0, 0, 0, 0.15);
}

/* 次要按钮 - 玻璃效果 */
.button-secondary-glass {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  z-index: 2;
  position: relative;
}

.button-secondary-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* 数据卡片 - 玻璃效果增强 */
.data-card-glass {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(15px) saturate(180%);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.data-card-glass:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* ===== 有机交互响应 ===== */

/* 液体波纹效果 */
.liquid-ripple {
  position: relative;
  overflow: hidden;
}

.liquid-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  transition: width 0.4s ease, height 0.4s ease, opacity 0.4s ease;
  opacity: 0;
  pointer-events: none;
}

.liquid-ripple:active::after {
  width: 200px;
  height: 200px;
  opacity: 1;
  transition: width 0s, height 0s, opacity 0s;
}

/* 流动边框效果 */
.liquid-border {
  position: relative;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: liquidBorderFlow 3s ease-in-out infinite;
}

@keyframes liquidBorderFlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 呼吸光效 */
.liquid-glow {
  animation: liquidGlow 2s ease-in-out infinite alternate;
}

@keyframes liquidGlow {
  from {
    box-shadow: 
      0 0 5px rgba(59, 130, 246, 0.3),
      0 0 20px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  to {
    box-shadow: 
      0 0 20px rgba(59, 130, 246, 0.5),
      0 0 40px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

/* ===== 性能优化 ===== */

/* 减少重绘 */
.liquid-optimized {
  contain: layout style paint;
  transform: translateZ(0);
}

/* 滚动优化 */
.liquid-scroll-smooth {
  scroll-behavior: smooth;
  overflow-anchor: none;
}

/* ===== 响应式适配 ===== */

/* 小屏设备的液体玻璃效果调整 */
@media (max-width: 768px) {
  .glass-effect {
    backdrop-filter: blur(15px) saturate(160%);
    background: rgba(255, 255, 255, 0.1);
  }
  
  .data-card-glass {
    backdrop-filter: blur(10px) saturate(160%);
  }
}

/* 高DPI屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .glass-effect {
    backdrop-filter: blur(25px) saturate(200%);
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.12);
  }
  
  .content-layer-text {
    color: rgba(255, 255, 255, 0.85);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .glass-effect,
  .glass-effect::before,
  .liquid-ripple::after,
  .data-card-glass {
    transition: none;
    animation: none;
  }
  
  .liquid-border {
    animation: none;
    background: rgba(255, 255, 255, 0.1);
  }
  
  .liquid-glow {
    animation: none;
  }
} 
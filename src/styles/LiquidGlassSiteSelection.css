/**
 * 🌊 Liquid Glass Site Selection - 液态玻璃设计系统
 * 真正的液态玻璃美学实现
 */

/* ============== CSS变量系统 ============== */
:root {
  /* 统一液态玻璃主题颜色 */
  --liquid-primary: #6495ed;
  --liquid-primary-light: rgba(100, 149, 237, 0.15);
  --liquid-primary-border: rgba(100, 149, 237, 0.3);
  --liquid-success: #10b981;
  --liquid-warning: #f59e0b;
  --liquid-error: #ef4444;
  
  /* 深度背景系统 */
  --depth-bg-1: #0a0a0f;
  --depth-bg-2: #0f0f1a;
  --depth-bg-3: #1a1a2e;
  --depth-bg-4: #16213e;
  
  /* 液态玻璃透明度 */
  --glass-transparency: rgba(255, 255, 255, 0.02);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-highlight: rgba(255, 255, 255, 0.12);
  --glass-shadow: rgba(0, 0, 0, 0.4);
  
  /* 简化的单色渐变 */
  --liquid-gradient-primary: linear-gradient(135deg, var(--liquid-primary) 0%, rgba(100, 149, 237, 0.8) 100%);
  --liquid-gradient-light: linear-gradient(135deg, var(--liquid-primary-light) 0%, rgba(255, 255, 255, 0.1) 100%);
  
  /* 文字颜色 */
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.75);
  --text-muted: rgba(255, 255, 255, 0.45);
  --text-accent: var(--liquid-primary);
  
  /* 统一阴影系统 */
  --liquid-shadow-1: 0 8px 32px rgba(100, 149, 237, 0.15);
  --liquid-shadow-2: 0 16px 64px rgba(100, 149, 237, 0.1);
  --liquid-shadow-3: 0 24px 96px rgba(0, 0, 0, 0.15);
  
  /* 动画时间 */
  --liquid-duration-fast: 0.2s;
  --liquid-duration-normal: 0.4s;
  --liquid-duration-slow: 0.8s;
}

/* ============== 基础重置和增强背景 ============== */
.liquid-glass-site-selection {
  min-height: 100vh;
  background: 
    /* 主背景渐变 */
    linear-gradient(135deg, #0a0a0f 0%, #0f0f1a 25%, #1a1a2e 50%, #16213e 100%),
    /* 噪声纹理 */
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='7' cy='7' r='2'/%3E%3Ccircle cx='27' cy='17' r='1'/%3E%3Ccircle cx='47' cy='27' r='1.5'/%3E%3Ccircle cx='17' cy='37' r='1'/%3E%3Ccircle cx='37' cy='47' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* 多层液态背景系统 */
.liquid-glass-site-selection::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    /* 主要液态光斑 */
    radial-gradient(circle at 15% 25%, rgba(100, 149, 237, 0.12) 0%, transparent 50%),
    radial-gradient(circle at 85% 15%, rgba(100, 149, 237, 0.08) 0%, transparent 45%),
    radial-gradient(circle at 45% 85%, rgba(255, 255, 255, 0.04) 0%, transparent 40%),
    radial-gradient(circle at 75% 65%, rgba(100, 149, 237, 0.06) 0%, transparent 35%);
  animation: liquidFlow 25s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

/* 次级液态背景层 */
.liquid-glass-site-selection::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    /* 细节液态效果 */
    radial-gradient(ellipse at 60% 30%, rgba(100, 149, 237, 0.05) 0%, transparent 60%),
    radial-gradient(ellipse at 20% 70%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
    radial-gradient(ellipse at 90% 80%, rgba(100, 149, 237, 0.04) 0%, transparent 45%);
  animation: liquidFlowSecondary 30s ease-in-out infinite reverse;
  pointer-events: none;
  z-index: 1;
}

@keyframes liquidFlow {
  0%, 100% { 
    transform: translateX(0) translateY(0) scale(1) rotate(0deg); 
    opacity: 1;
  }
  25% { 
    transform: translateX(-15px) translateY(-25px) scale(1.03) rotate(1deg); 
    opacity: 0.85;
  }
  50% { 
    transform: translateX(25px) translateY(-15px) scale(0.97) rotate(-1deg); 
    opacity: 1;
  }
  75% { 
    transform: translateX(-8px) translateY(20px) scale(1.02) rotate(0.5deg); 
    opacity: 0.9;
  }
}

@keyframes liquidFlowSecondary {
  0%, 100% { 
    transform: translateX(0) translateY(0) scale(1) rotate(0deg); 
    opacity: 0.7;
  }
  30% { 
    transform: translateX(20px) translateY(-30px) scale(1.05) rotate(-0.8deg); 
    opacity: 1;
  }
  60% { 
    transform: translateX(-25px) translateY(10px) scale(0.95) rotate(1.2deg); 
    opacity: 0.8;
  }
  80% { 
    transform: translateX(10px) translateY(25px) scale(1.01) rotate(-0.5deg); 
    opacity: 0.9;
  }
}

/* ============== 全新悬浮导航系统 ============== */
.liquid-floating-nav {
  position: fixed;
  top: 2rem;
  left: 2rem;
  right: 2rem;
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  pointer-events: none;
  max-width: 1600px;
  margin: 0 auto;
}

.liquid-nav-section {
  pointer-events: auto;
  animation: navFloatIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(-20px);
}

.liquid-nav-section.left {
  animation-delay: 0.1s;
}

.liquid-nav-section.center {
  animation-delay: 0.2s;
}

.liquid-nav-section.right {
  animation-delay: 0.3s;
}

@keyframes navFloatIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 悬浮返回按钮 */
.liquid-floating-back-button {
  backdrop-filter: blur(32px) saturate(180%);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 1rem 1.5rem;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(100, 149, 237, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-floating-back-button:hover {
  transform: translateY(-2px) scale(1.02);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 
    0 12px 48px rgba(0, 0, 0, 0.4),
    0 6px 24px rgba(100, 149, 237, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 中央悬浮标题 - 重新设计，更加突出和美观 */
.liquid-floating-title {
  backdrop-filter: blur(40px) saturate(200%);
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.15) 0%, 
    rgba(255, 255, 255, 0.06) 50%,
    rgba(100, 149, 237, 0.12) 100%);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 40px;
  padding: 2rem 4rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 20px 80px rgba(0, 0, 0, 0.4),
    0 10px 40px rgba(100, 149, 237, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.25),
    inset 0 -2px 0 rgba(100, 149, 237, 0.2);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 添加流光效果 */
.liquid-floating-title::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent);
  transition: left 0.8s ease;
}

.liquid-floating-title:hover {
  transform: translateY(-4px) scale(1.03);
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.22) 0%, 
    rgba(255, 255, 255, 0.1) 50%,
    rgba(100, 149, 237, 0.18) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 32px 120px rgba(0, 0, 0, 0.5),
    0 16px 60px rgba(100, 149, 237, 0.4),
    inset 0 3px 0 rgba(255, 255, 255, 0.3),
    inset 0 -3px 0 rgba(100, 149, 237, 0.3);
}

.liquid-floating-title:hover::before {
  left: 100%;
}

/* 增强标题文字样式 */
.liquid-title-text {
  font-size: 2.5rem;
  font-weight: 600;
  letter-spacing: 1px;
  color: #ffffff;
  margin: 0;
  line-height: 1.2;
  text-align: center;
  text-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.5),
    0 1px 2px rgba(100, 149, 237, 0.3);
  background: linear-gradient(135deg, 
    #ffffff 0%, 
    rgba(100, 149, 237, 0.8) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.liquid-subtitle-text {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin: 6px 0 0 0;
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* 右侧悬浮切换器 */
.liquid-floating-toggle {
  backdrop-filter: blur(32px) saturate(180%);
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.09);
  border-radius: 20px;
  padding: 8px;
  display: flex;
  gap: 6px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(100, 149, 237, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-floating-toggle:hover {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.06);
  box-shadow: 
    0 12px 48px rgba(0, 0, 0, 0.35),
    0 6px 24px rgba(100, 149, 237, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

/* 悬浮切换器内的按钮 */
.liquid-floating-toggle .liquid-toggle-btn {
  padding: 0.6rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 12px;
  color: var(--text-secondary);
  font-size: 0.8rem;
  font-weight: 500;
  min-width: 70px;
  white-space: nowrap;
  text-align: center;
}

.liquid-floating-toggle .liquid-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.12);
  color: var(--text-primary);
  transform: none; /* 在悬浮容器内不需要额外位移 */
}

.liquid-floating-toggle .liquid-toggle-btn.active {
  background: var(--liquid-primary-light);
  border-color: var(--liquid-primary-border);
  color: var(--text-primary);
  box-shadow: 
    0 4px 12px rgba(100, 149, 237, 0.3),
    inset 0 1px 1px rgba(255, 255, 255, 0.15);
}

.liquid-workflow-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.liquid-step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 50px;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.liquid-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--liquid-gradient-1);
  opacity: 0;
  transition: all var(--liquid-duration-slow);
  z-index: -1;
}

.liquid-step.active::before {
  left: 0;
  opacity: 0.1;
}

.liquid-step.completed::before {
  left: 0;
  opacity: 0.15;
  background: var(--liquid-gradient-2);
}

.liquid-step-indicator {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--glass-transparency);
  border: 2px solid var(--glass-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: var(--text-muted);
  transition: all var(--liquid-duration-normal);
}

.liquid-step.active .liquid-step-indicator {
  background: var(--liquid-gradient-1);
  border-color: var(--liquid-primary);
  color: white;
  box-shadow: var(--liquid-shadow-1);
}

.liquid-step.completed .liquid-step-indicator {
  background: var(--liquid-gradient-2);
  border-color: var(--liquid-success);
  color: white;
}

.liquid-step-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  transition: color var(--liquid-duration-normal);
}

.liquid-step.active .liquid-step-label {
  color: var(--text-primary);
}

.liquid-step-connector {
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--glass-border), transparent, var(--glass-border));
  border-radius: 1px;
}

/* 视图切换 - 液态按钮 */
.liquid-view-toggle {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 50px;
  padding: 0.25rem;
}

.liquid-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: transparent;
  border: none;
  border-radius: 50px;
  color: var(--text-muted);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.liquid-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--liquid-gradient-1);
  opacity: 0;
  transition: opacity var(--liquid-duration-normal);
  z-index: -1;
}

.liquid-toggle-btn.active::before {
  opacity: 0.15;
}

.liquid-toggle-btn.active {
  color: var(--text-primary);
  box-shadow: var(--liquid-shadow-1);
}

/* ============== 主要工作区域 ============== */
.liquid-main-workspace {
  position: relative;
  z-index: 10;
  padding: 8rem 2rem 2rem; /* 为悬浮导航留出空间 */
  min-height: 100vh;
}

.liquid-dashboard-layout {
  display: grid;
  grid-template-columns: 320px 1fr 360px;
  gap: 2rem;
  max-width: 1800px;
  margin: 0 auto;
  height: calc(100vh - 8rem);
  max-height: 800px;
  min-height: 600px;
}

/* ============== 搜索面板 - 重新设计 ============== */
.liquid-search-panel {
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.12) 0%, 
    rgba(255, 255, 255, 0.04) 100%);
  backdrop-filter: blur(40px) saturate(200%);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 32px;
  padding: 2.5rem;
  box-shadow: 
    0 32px 120px rgba(0, 0, 0, 0.4),
    0 16px 60px rgba(79, 70, 229, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    inset 0 -2px 0 rgba(79, 70, 229, 0.1);
  position: relative;
  overflow: hidden;
  height: fit-content;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-search-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--liquid-gradient-1);
  opacity: 0.6;
}

.liquid-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--glass-border);
}

.liquid-panel-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.liquid-results-badge {
  padding: 0.5rem 1rem;
  background: var(--liquid-gradient-1);
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 700;
  color: white;
  box-shadow: var(--liquid-shadow-1);
}

/* 表单部分 */
.liquid-search-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.liquid-form-section {
  background: rgba(255, 255, 255, 0.01);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all var(--liquid-duration-normal);
  position: relative;
  overflow: hidden;
}

.liquid-form-section:hover {
  border-color: var(--glass-highlight);
  box-shadow: var(--liquid-shadow-1);
}

.liquid-form-section.glass-effect-light {
  background: rgba(255, 255, 255, 0.02);
}

.liquid-form-section.glass-effect-accent {
  background: rgba(124, 58, 237, 0.03);
  border-color: rgba(124, 58, 237, 0.2);
}

.liquid-section-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all var(--liquid-duration-normal);
}

.liquid-section-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 0 8px currentColor);
}

.liquid-section-title h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.liquid-section-title p {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin: 0;
}

.liquid-expand-indicator {
  margin-left: auto;
  transition: transform var(--liquid-duration-normal);
}

.liquid-expand-indicator.expanded {
  transform: rotate(180deg);
}

.liquid-input-group {
  margin-bottom: 1rem;
}

.liquid-input-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.liquid-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.liquid-input {
  width: 100%;
  padding: 0.875rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all var(--liquid-duration-normal);
}

.liquid-input:focus {
  outline: none;
  border-color: var(--liquid-primary);
  box-shadow: 
    0 0 0 3px rgba(0, 212, 255, 0.1),
    var(--liquid-shadow-1);
  background: rgba(255, 255, 255, 0.05);
}

.liquid-input::placeholder {
  color: var(--text-muted);
}

.liquid-input-icon {
  position: absolute;
  right: 1rem;
  color: var(--text-muted);
  pointer-events: none;
}

/* 范围显示 */
.liquid-range-display {
  margin: 1rem 0;
}

.liquid-range-values {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.liquid-range-value {
  flex: 1;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  transition: all var(--liquid-duration-normal);
}

.liquid-range-value:hover {
  border-color: var(--glass-highlight);
  box-shadow: var(--liquid-shadow-1);
}

.liquid-range-value span:first-child {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.liquid-range-value span:last-child {
  display: block;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
}

.liquid-range-value.min span:last-child {
  color: var(--liquid-primary);
}

.liquid-range-value.max span:last-child {
  color: var(--liquid-success);
}

/* 主要按钮 - 重新设计 */
.liquid-primary-button {
  width: 100%;
  padding: 1.2rem 2rem;
  background: linear-gradient(145deg, 
    rgba(79, 70, 229, 1) 0%, 
    rgba(99, 102, 241, 0.95) 50%,
    rgba(79, 70, 229, 1) 100%);
  border: 2px solid rgba(79, 70, 229, 0.6);
  border-radius: 20px;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 20px 80px rgba(79, 70, 229, 0.4),
    0 10px 40px rgba(79, 70, 229, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(79, 70, 229, 0.8);
  margin-top: 2rem;
  backdrop-filter: blur(20px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.liquid-primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--liquid-duration-slow);
}

.liquid-primary-button:hover:not(:disabled) {
  transform: translateY(-4px) scale(1.02);
  background: linear-gradient(145deg, 
    rgba(99, 102, 241, 1) 0%, 
    rgba(129, 140, 248, 0.95) 50%,
    rgba(99, 102, 241, 1) 100%);
  border-color: rgba(99, 102, 241, 0.8);
  box-shadow: 
    0 32px 120px rgba(79, 70, 229, 0.5),
    0 16px 60px rgba(79, 70, 229, 0.4),
    inset 0 3px 0 rgba(255, 255, 255, 0.3),
    inset 0 -2px 0 rgba(79, 70, 229, 0.9);
}

.liquid-primary-button:hover:not(:disabled)::before {
  left: 100%;
}

.liquid-primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.liquid-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.liquid-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: liquidSpin 1s linear infinite;
}

@keyframes liquidSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ============== 中央内容区域 ============== */
.liquid-central-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.liquid-map-workspace {
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  backdrop-filter: blur(40px) saturate(200%);
  border: 2px solid rgba(255, 255, 255, 0.12);
  border-radius: 32px;
  overflow: hidden;
  box-shadow: 
    0 28px 100px rgba(0, 0, 0, 0.4),
    0 14px 50px rgba(79, 70, 229, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(79, 70, 229, 0.1);
  flex: 1;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-map-workspace::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--liquid-gradient-2);
  opacity: 0.8;
  z-index: 10;
}

.liquid-map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: rgba(255, 255, 255, 0.01);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid var(--glass-border);
}

.liquid-map-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.liquid-selected-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(16, 185, 129, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(16, 185, 129, 0.3);
  border-radius: 50px;
  color: var(--liquid-success);
}

.liquid-map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  overflow: hidden;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--liquid-shadow-1);
}

.workspace-map {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

/* ============== 分析面板 - 重新设计 ============== */
.liquid-analysis-panel {
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.12) 0%, 
    rgba(255, 255, 255, 0.04) 100%);
  backdrop-filter: blur(40px) saturate(200%);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 32px;
  padding: 2.5rem;
  box-shadow: 
    0 32px 120px rgba(0, 0, 0, 0.4),
    0 16px 60px rgba(79, 70, 229, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    inset 0 -2px 0 rgba(79, 70, 229, 0.1);
  position: relative;
  overflow: hidden;
  height: fit-content;
  max-height: calc(100vh - 220px);
  overflow-y: auto;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-analysis-panel::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 1px;
  background: var(--liquid-gradient-3);
  opacity: 0.6;
}

.liquid-analysis-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 关键指标卡片 */
.liquid-key-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.data-card-glass {
  background: linear-gradient(145deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.03) 100%);
  backdrop-filter: blur(24px) saturate(180%);
  border: 2px solid rgba(255, 255, 255, 0.12);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.3),
    0 8px 32px rgba(79, 70, 229, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.data-card-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--liquid-gradient-1);
  opacity: 0.5;
}

.data-card-glass:hover {
  border-color: var(--glass-highlight);
  box-shadow: var(--liquid-shadow-1);
  transform: translateY(-2px);
}

.data-card-glass > div:first-child {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value-emphasis {
  font-size: 1.25rem;
  font-weight: 700;
  background: var(--liquid-gradient-1);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 成本分解 */
.liquid-cost-breakdown {
  margin-top: 1rem;
}

.liquid-cost-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.liquid-cost-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  transition: all var(--liquid-duration-normal);
}

.liquid-cost-item:hover {
  border-color: var(--glass-highlight);
  box-shadow: var(--liquid-shadow-1);
}

.cost-icon {
  font-size: 1.5rem;
  filter: drop-shadow(0 0 8px currentColor);
}

.cost-details {
  flex: 1;
}

.cost-details > div:first-child {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.cost-details > div:nth-child(2) {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.cost-details > div:last-child {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* 财务结果 */
.liquid-financial-results {
  margin-top: 1rem;
}

.liquid-financial-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
}

.metric-item span:first-child {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-item span:last-child {
  font-weight: 700;
  color: var(--text-primary);
}

/* 投资建议 */
.liquid-investment-recommendation {
  margin-top: 1rem;
}

.recommendation-card {
  padding: 1.5rem;
  backdrop-filter: blur(16px);
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  position: relative;
  overflow: hidden;
}

.recommendation-card.glass-effect-warning {
  background: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.3);
}

.recommendation-card.glass-effect-success {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.3);
}

.recommendation-card > div:first-child {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.recommendation-card > div:last-child {
  font-size: 0.85rem;
  line-height: 1.5;
  color: var(--text-secondary);
}

/* 空状态 */
.liquid-empty-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* ============== 地图焦点模式 ============== */
.map-focus-mode {
  height: calc(100vh - 140px);
  position: relative;
}

.map-focus-container {
  width: 100%;
  height: 100%;
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid var(--glass-border);
  box-shadow: var(--liquid-shadow-2);
}

.fullscreen-map {
  width: 100%;
  height: 100%;
  border-radius: 24px;
}

.map-overlay-controls {
  position: absolute;
  top: 2rem;
  right: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  z-index: 10;
  max-width: 350px;
}

.control-panel,
.selected-site-info {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: var(--liquid-shadow-1);
  position: relative;
  overflow: hidden;
}

.control-panel::before,
.selected-site-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: var(--liquid-gradient-1);
  opacity: 0.5;
}

.control-panel h3,
.selected-site-info h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
}

.quick-stats {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.stat {
  text-align: center;
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  background: var(--liquid-gradient-1);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: block;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.quick-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin: 1rem 0;
}

.quick-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--glass-border);
}

.quick-metric span:first-child {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.quick-metric span:last-child {
  font-weight: 600;
  color: var(--text-primary);
}

.details-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: var(--liquid-gradient-2);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--liquid-duration-normal);
  margin-top: 1rem;
}

.details-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--liquid-shadow-1);
}

/* ============== 错误提示 ============== */
.error-toast {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
  background: rgba(239, 68, 68, 0.1);
  backdrop-filter: blur(24px) saturate(180%);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 16px;
  padding: 1rem 1.5rem;
  box-shadow: var(--liquid-shadow-1);
  animation: slideInFromRight 0.3s ease;
  max-width: 400px;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: white;
}

.toast-content svg {
  color: var(--liquid-error);
  flex-shrink: 0;
}

.toast-content span {
  flex: 1;
  font-size: 0.9rem;
  line-height: 1.4;
}

.toast-content button {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--liquid-duration-normal);
}

.toast-content button:hover {
  background: rgba(255, 255, 255, 0.1);
}

@keyframes slideInFromRight {
  from { 
    transform: translateX(100%); 
    opacity: 0; 
  }
  to { 
    transform: translateX(0); 
    opacity: 1; 
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .liquid-dashboard-layout {
    grid-template-columns: 280px 1fr 320px;
    gap: 1.5rem;
  }
}

@media (max-width: 1200px) {
  .liquid-dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 1.5rem;
  }
  
  .liquid-main-workspace {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  /* 移动端悬浮导航调整 */
  .liquid-floating-nav {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .liquid-nav-section {
    width: 100%;
    max-width: 300px;
  }

  .liquid-nav-section.left,
  .liquid-nav-section.center,
  .liquid-nav-section.right {
    display: flex;
    justify-content: center;
  }

  .liquid-floating-back-button {
    padding: 0.8rem 1.2rem;
    font-size: 0.85rem;
  }

  .liquid-floating-title {
    padding: 1rem 1.5rem;
    text-align: center;
  }

  .liquid-title-text {
    font-size: 1.4rem;
  }

  .liquid-subtitle-text {
    font-size: 0.8rem;
  }

  .liquid-floating-toggle {
    justify-content: center;
    padding: 6px;
    gap: 4px;
  }

  .liquid-floating-toggle .liquid-toggle-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.75rem;
    min-width: 60px;
  }

  /* 主工作区域调整 */
  .liquid-main-workspace {
    padding: 12rem 1rem 1rem; /* 为移动端悬浮导航留出更多空间 */
  }

  .liquid-dashboard-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
    height: auto;
    min-height: auto;
  }

  .map-overlay-controls {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
  
  .quick-stats {
    flex-direction: column;
  }
}

/* ============== 通用样式类 ============== */
.content-layer-heading {
  color: var(--text-primary) !important;
  font-weight: 600 !important;
}

.content-layer-text {
  color: var(--text-secondary) !important;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.02) !important;
  backdrop-filter: blur(24px) saturate(180%) !important;
  border: 1px solid var(--glass-border) !important;
}

.glass-effect-success {
  background: rgba(16, 185, 129, 0.1) !important;
  border-color: rgba(16, 185, 129, 0.3) !important;
}

.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 滚动条样式 */
.liquid-analysis-panel::-webkit-scrollbar {
  width: 6px;
}

.liquid-analysis-panel::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 3px;
}

.liquid-analysis-panel::-webkit-scrollbar-thumb {
  background: var(--liquid-gradient-1);
  border-radius: 3px;
}

.liquid-analysis-panel::-webkit-scrollbar-thumb:hover {
  background: var(--liquid-gradient-2);
}

/* ================= 重新设计的按钮系统：圆润动态流光透明玻璃质感 ================= */

/* 增强玻璃质感的圆润按钮效果 */
.liquid-glass-button {
  position: relative;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  padding: 12px 24px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  overflow: hidden;
  white-space: nowrap;
  z-index: 1;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

/* 多层流光效果 */
.liquid-glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 1s cubic-bezier(0.23, 1, 0.32, 1);
  z-index: -1;
}

.liquid-glass-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.05) 0%,
    transparent 70%
  );
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -1;
}

/* 悬停时的流光动画 */
.liquid-glass-button:hover::before {
  left: 100%;
}

.liquid-glass-button:hover::after {
  opacity: 1;
}

/* 悬停效果增强 */
.liquid-glass-button:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 
    0 12px 40px rgba(100, 149, 237, 0.2),
    0 6px 20px rgba(100, 149, 237, 0.1),
    inset 0 1px 2px rgba(255, 255, 255, 0.2),
    inset 0 -1px 2px rgba(255, 255, 255, 0.05);
  transform: translateY(-3px) scale(1.02);
  color: var(--text-primary);
}

/* 激活状态增强 */
.liquid-glass-button:active {
  transform: translateY(-1px) scale(1.01);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 
    0 4px 16px rgba(0, 212, 255, 0.25),
    inset 0 2px 4px rgba(255, 255, 255, 0.15);
}

/* 返回按钮专门样式 */
.liquid-back-button {
  background: var(--liquid-primary-light);
  border: 1px solid var(--liquid-primary-border);
  position: relative;
}

.liquid-back-button::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(100, 149, 237, 0.3),
    rgba(100, 149, 237, 0.15),
    transparent
  );
}

.liquid-back-button:hover {
  background: rgba(100, 149, 237, 0.2);
  border-color: var(--liquid-primary-border);
  box-shadow: 
    0 12px 40px rgba(100, 149, 237, 0.25),
    0 6px 20px rgba(100, 149, 237, 0.15),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
}

/* 主要操作按钮 */
.liquid-primary-button {
  background: var(--liquid-gradient-light);
  border: 1px solid var(--liquid-primary-border);
  position: relative;
}

.liquid-primary-button::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(100, 149, 237, 0.3),
    rgba(100, 149, 237, 0.2),
    transparent
  );
}

.liquid-primary-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--liquid-gradient-primary);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: -2;
}

.liquid-primary-button:hover::after {
  opacity: 1;
}

.liquid-primary-button:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 16px 48px rgba(100, 149, 237, 0.25),
    0 8px 24px rgba(100, 149, 237, 0.15),
    inset 0 1px 2px rgba(255, 255, 255, 0.25),
    inset 0 -1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px) scale(1.03);
}

/* 按钮内容样式 */
.liquid-button-content {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

/* 禁用状态 */
.liquid-glass-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 加载动画 */
.liquid-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 视图切换按钮 */
.liquid-toggle-btn {
  background: rgba(255, 255, 255, 0.04);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 50px;
  padding: 10px 16px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  overflow: hidden;
}

.liquid-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.6s ease;
}

.liquid-toggle-btn:hover::before {
  left: 100%;
}

.liquid-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 6px 24px rgba(0, 212, 255, 0.1);
}

.liquid-toggle-btn.active {
  background: var(--liquid-primary-light);
  border-color: var(--liquid-primary-border);
  color: var(--text-primary);
  box-shadow: 
    0 6px 24px rgba(100, 149, 237, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.15);
}

/* 结果徽章 */
.liquid-results-badge {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 50px;
  padding: 6px 12px;
  color: rgba(0, 212, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 选中指示器 */
.liquid-selected-indicator {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 50px;
  padding: 8px 16px;
  backdrop-filter: blur(16px);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 更新原有的按钮类使用新的玻璃按钮样式 */

/* 重写原有的按钮样式以适应新的玻璃质感 */
.liquid-primary-button.liquid-glass-button {
  border-radius: 50px;
  padding: 14px 28px;
  font-weight: 600;
  font-size: 15px;
}

.liquid-toggle-btn.liquid-glass-button {
  padding: 10px 16px;
  font-size: 13px;
}

.liquid-back-button.liquid-glass-button {
  padding: 12px 20px;
  font-size: 14px;
} 
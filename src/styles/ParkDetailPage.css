/***********************************************************
 * 防止水平溢出，修复底部蓝条问题
 ***********************************************************/
 body, html {
  overflow-x: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}

.park-detail-page {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
  display: flex;
  min-height: 100vh;
  background-color: #f9f9f9;
}

/* 确保子元素不会导致水平溢出 */
.detail-content,
.incomplete-info,
.transport-map-container,
.park-layout,
.contact-section,
.park-highlights-section,
.infrastructure-section {
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.map-wrapper {
  max-width: 100%;
  overflow: hidden;
}

.header-buttons {
  max-width: 100%;
  box-sizing: border-box;
}

/***********************************************************
 * 页面整体样式
 ***********************************************************/
.park-detail-page {
  background-color: #f9f9f9;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
  overflow-y: auto;
  height: 100%;
  width: 100%;

  --primary-color: #0070f3;
  --secondary-color: #00c4ff;
  --accent-color: #6e00ff;
  --dark-bg: #111827;
  --card-bg: rgba(255, 255, 255, 0.9);
  --card-hover-bg: rgba(255, 255, 255, 1);
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --card-hover-shadow: 0 10px 30px rgba(0, 112, 243, 0.2);
  --gradient-bg: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

/***********************************************************
 * 侧边栏样式
 ***********************************************************/
/* 重构后的侧边栏样式 */
.sidebar {
  width: 320px;
  background: #2c3e50;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  height: 100vh;
  position: sticky;
  top: 0;
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  color: #ffffff;
}

.sidebar-header {
  padding: 12px 8px;
  text-align: center;
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(255,255,255,0.15);
}

.sidebar-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.sidebar-header .location-tag {
  font-size: 0.9rem;
  background: rgba(255,255,255,0.1);
  padding: 6px 14px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  margin-top: 4px;
}

.back-button {
  background: rgba(255,255,255,0.15);
  color: white;
  border-radius: 50px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  border: none;
  transition: all 0.25s ease;
  display: inline-flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.back-button:hover {
  background: rgba(255,255,255,0.25);
  transform: translateY(-1px);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-grow: 1;
  margin-top: 8px;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 12px 14px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  border-left: 3px solid transparent;
}

.sidebar-nav-item:hover {
  background-color: rgba(255,255,255,0.08);
  border-left-color: rgba(255,255,255,0.3);
}

.sidebar-nav-item.active {
  background-color: rgba(255,255,255,0.12);
  border-left-color: #3498db;
}

.sidebar-nav-icon {
  font-size: 1.2rem;
  opacity: 0.9;
}

.language-toggle {
  background-color: rgba(255,255,255,0.1);
  color: #ffffff;
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 20px;
  padding: 8px 16px;
  margin-top: 20px;
  cursor: pointer;
  transition: all 0.25s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.language-toggle:hover {
  background-color: rgba(255,255,255,0.2);
  border-color: rgba(255,255,255,0.3);
}


/***********************************************************
 * 主内容区域样式
 ***********************************************************/
.main-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  height: 100vh;
}

.sidebar-content {
  flex: 1;
  padding: 0 20px;
  overflow-y: auto;
  margin-top: 20px;
}

/***********************************************************
 * 顶部标题区
 ***********************************************************/
.detail-header {
  position: relative;
  min-height: 100px;
  padding: 20px;
  background: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #333;
  margin-bottom: 20px;
}

.header-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
  padding: 0 20px;
  box-sizing: border-box;
}

.back-button,
.language-toggle {
  background: #f0f0f0;
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button:hover,
.language-toggle:hover {
  background: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.detail-header h1 {
  font-size: 2rem;
  margin: 10px 0;
  color: #333;
  font-weight: 700;
}

.location-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  color: #666;
  background: #f0f0f0;
  padding: 5px 10px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.location-tag::before {
  content: '📍';
}

/***********************************************************
 * 内容主体区
 ***********************************************************/
.detail-content {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 20px;
  transition: all 0.3s ease;
  width: 100%;
}

/***********************************************************
 * 地图部分
 ***********************************************************/
/* 将 margin-top 改为 0，去除顶部空白 */
.park-map-container {
  position: relative;
  margin-top: 0; /* 原先为 20px */
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  height: calc(100vh - 150px);
}

.map-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.leaflet-container {
  height: 100% !important;
  width: 100%;
  border-radius: 12px;
}

/* 隐藏地图底部的版权信息 */
.leaflet-control-attribution {
  display: none !important;
}

.map-side-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 900;
}

.toggle-layer-button,
.toggle-environment-button,
.toggle-transport-button,
.reset-map-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f0f0f0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%; /* 确保按钮宽度一致 */
  min-width: 160px; /* 设置最小宽度 */
  text-align: center;
  white-space: nowrap;
}

.toggle-layer-button:hover,
.toggle-environment-button:hover,
.toggle-transport-button:hover,
.reset-map-button:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.toggle-environment-button.active {
  background-color: #4CAF50;
  color: white;
}

.toggle-layer-button.active {
  background-color: #FFA07A; /* 淡橙色 */
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 160, 122, 0.3);
}

.toggle-transport-button.active {
  background-color: #2196F3;
  color: white;
}

.map-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
}

/***********************************************************
 * 园区图片
 ***********************************************************/
.park-hero {
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.park-images {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  position: relative;
}

.main-image {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 16px;
  transition: transform 0.5s ease;
  filter: brightness(1.05) contrast(1.05);
}

.main-image:hover {
  transform: scale(1.02);
}

/***********************************************************
 * 新设计的园区信息部分
 ***********************************************************/
/* 基本信息卡片 */
.redesigned-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.info-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  transition: all 0.3s ease;
}

.info-card h3 {
  margin: 0 0 10px;
  font-size: 1rem;
  color: #555;
}

.info-card p {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

/* 状态描述 */
.status-description-section {
  margin-top: 40px;
}

.status-description-card {
  background-color: #fff;
  border-left: 4px solid var(--primary-color);
  padding: 20px;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  margin-top: 20px;
}

/* 汇率信息 */
.currency-rates-section {
  margin-top: 40px;
}

.currency-rates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.currency-rate-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  transition: all 0.3s ease;
}

.currency-rate-card h3 {
  margin: 0 0 10px;
  font-size: 1rem;
  color: #555;
}

.currency-rate-card p {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

/* 距离信息 */
.redesigned-distance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.distance-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  transition: all 0.3s ease;
}

.distance-card h3 {
  margin: 0 0 10px;
  font-size: 1rem;
  color: #555;
}

.distance-card p {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}


.transport-card h3 {
  margin: 0 0 10px;
  font-size: 1rem;
  color: #555;
}

.transport-card p {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

/***********************************************************
 * 园区布局与其他模块（原有样式保留）
 ***********************************************************/
.park-layout {
  margin-bottom: 40px;
}

.layout-image {
  display: block;
  margin: 0 auto;
  width: 100%;
  max-width: 1000px;
  height: auto;
  border-radius: 16px;
  transition: transform 0.5s ease;
  margin-top: 20px;
}

.layout-image:hover {
  transform: scale(1.05);
}

.park-layout h2 {
  margin-top: 10px;
  margin-bottom: 25px;
  font-size: 1.8rem;
  color: #111;
  border-bottom: 2px solid var(--primary-color);
  display: inline-block;
  position: relative;
  padding-bottom: 10px;
}
.park-layout h2::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50%;
  height: 2px;
  background: var(--secondary-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 通用标题 */
h2 {
  font-size: 1.8rem;
  color: #111;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--primary-color);
  position: relative;
  display: inline-block;
}
h2::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50%;
  height: 2px;
  background: var(--secondary-color);
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

h3 {
  font-size: 1.4rem;
  color: #222;
  margin-bottom: 20px;
  font-weight: 600;
}

/***********************************************************
 * 信息网格布局
 ***********************************************************/
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  background-color: var(--card-bg);
  padding: 20px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.info-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-hover-shadow);
  background-color: var(--card-hover-bg);
  border-color: rgba(0, 112, 243, 0.2);
}

.info-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  color: #111;
}

/***********************************************************
 * Status Description & Currency Rates 样式增强
 ***********************************************************/
.description-box {
  background-color: #fafafa;
  padding: 25px;
  border-left: 4px solid var(--primary-color);
  margin-top: 30px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  width: 100%;
}

.description-box h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.4rem;
}

.description-box p {
  margin: 0;
  line-height: 1.8;
  color: #444;
  font-size: 1.05rem;
  text-align: justify;
  max-width: 100%;
}

.currency-rates {
  margin-top: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.currency-rates h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 1.4rem;
}

.rates-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.rate-item {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
}

.rate-label {
  font-weight: bold;
  margin-bottom: 8px;
  color: #444;
  font-size: 1.2rem;
}

.rate-value {
  color: #0066cc;
  font-size: 1.4rem;
  font-weight: 500;
}

.rate-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,112,243,0.15);
}

@media (max-width: 768px) {
  .rates-grid {
    grid-template-columns: 1fr;
  }
}

/***********************************************************
 * 基础设施部分样式
 ***********************************************************/
.infrastructure-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.infra-item {
  background-color: var(--card-bg);
  padding: 25px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.infra-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-bg);
}

.infra-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-hover-shadow);
}

.infra-item h3 {
  display: flex;
  align-items: center;
  margin-top: 0;
  color: #333;
  font-size: 1.1rem;
}

.facility-icon {
  margin-right: 8px;
  font-style: normal;
}

.infra-sources {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.source-tag {
  background: var(--gradient-bg);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  box-shadow: 0 2px 8px rgba(0, 112, 243, 0.3);
}

.infra-item.full-width {
  grid-column: 1 / -1;
}

/***********************************************************
 * 交通信息
 ***********************************************************/
.transport-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 25px;
  background-color: var(--card-bg);
  padding: 25px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  position: relative;
  overflow: hidden;
}

.transport-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('/images/transport-pattern.svg');
  background-size: 200px;
  opacity: 0.03;
  z-index: 0;
}

.transport-item {
  padding: 15px;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.transport-item:hover {
  background-color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.transport-item h3 {
  font-size: 1.1rem;
  margin-bottom: 12px;
  color: #333;
  font-weight: 600;
}

.transport-item p {
  color: #444;
  font-size: 1rem;
}

/***********************************************************
 * 园区统计
 ***********************************************************/
.park-charts-container {
  margin-top: 40px;
  padding: 0;
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
  position: relative;
  overflow: visible;
  max-width: 100%;
  width: 100%;
}

.chart-container {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: var(--card-shadow);
  position: relative;
  transition: all 0.3s ease;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  width: 100%;
}

.chart-container > div,
.chart-container canvas,
.chart-container svg,
.recharts-wrapper,
.recharts-surface {
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  overflow: hidden !important;
}

.investment-chart-container,
.production-capacity-chart-container {
  max-width: 100% !important;
  overflow: hidden !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.chart-title {
  font-size: 1.4rem;
  color: #222;
  margin-bottom: 20px;
  font-weight: 600;
  position: relative;
  display: inline-block;
}
.chart-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--gradient-bg);
}

/***********************************************************
 * Nearby Facilities
 ***********************************************************/
.facilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin: 40px 0;
}

.facility-item {
  background-color: var(--card-bg);
  padding: 25px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.facility-item ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.facility-item li {
  margin-bottom: 10px;
  color: #444;
}

.facility-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(0, 112, 243, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.facility-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-hover-shadow);
}

.facility-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
  z-index: 1;
}

/***********************************************************
 * 卡片悬浮装饰
 ***********************************************************/
.info-item, .infra-item, .facility-item, .contact-item, .highlight-item {
  position: relative;
  overflow: hidden;
}
.info-item::after,
.infra-item::after,
.facility-item::after,
.contact-item::after,
.highlight-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(0, 112, 243, 0.05) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.info-item:hover::after,
.infra-item:hover::after,
.facility-item:hover::after,
.contact-item:hover::after,
.highlight-item:hover::after {
  opacity: 1;
}

/***********************************************************
 * 联系信息部分样式
 ***********************************************************/
.contact-section {
  margin-top: 40px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.05) 0%, rgba(255, 255, 255, 0) 50%);
  z-index: 0;
}

.contact-section h2 {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 30px;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.contact-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 2px;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  position: relative;
  z-index: 1;
}

.contact-item {
  background-color: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.contact-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 60%;
  background: linear-gradient(to bottom, #3498db, #2980b9);
  border-radius: 0 3px 3px 0;
  transform: translateY(30%);
}

.contact-item h3 {
  display: flex;
  align-items: center;
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
}

.contact-icon {
  margin-right: 12px;
  font-style: normal;
  font-size: 1.5rem;
  background: linear-gradient(135deg, #3498db, #2980b9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.contact-item a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.contact-item a:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

.phone-list p {
  margin: 8px 0;
  font-size: 1rem;
  color: #444;
}

.coordinates {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.coordinates p {
  margin: 5px 0;
  font-size: 0.95rem;
  color: #555;
}

/***********************************************************
 * 园区亮点部分
 ***********************************************************/
.park-highlights-section {
  margin-top: 40px;
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  position: relative;
  overflow: hidden;
}

.park-highlights-section::before {
  content: '';
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: url('/images/highlights-pattern.svg');
  background-size: 300px;
  opacity: 0.03;
  z-index: 0;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  position: relative;
  z-index: 1;
}

.highlight-card {
  background-color: var(--card-bg);
  padding: 25px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.highlight-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--card-hover-shadow);
}

.highlight-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--primary-color);
  display: inline-block;
  position: relative;
  z-index: 1;
}

.highlight-card h3 {
  font-size: 1.2rem;
  margin-bottom: 12px;
  color: #333;
}

.highlight-card p {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.6;
}

/***********************************************************
 * 信息不完整的园区样式
 ***********************************************************/
.incomplete-info {
  padding: 40px;
  text-align: center;
}

.incomplete-info-container {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--card-bg);
  padding: 40px;
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  position: relative;
  overflow: hidden;
}

.incomplete-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  color: #f39c12;
}

.incomplete-info h2 {
  color: #333;
  margin-bottom: 15px;
  border-bottom: none;
  display: block;
}

.incomplete-info h2::after {
  display: none;
}

.incomplete-info p {
  color: #555;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.basic-info-card {
  background-color: #f5f5f5;
  padding: 25px;
  border-radius: 12px;
  margin-top: 30px;
  text-align: left;
}

.basic-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.basic-info-item {
  padding: 15px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.basic-info-item.full-width {
  grid-column: 1 / -1;
}

.simple-map-container {
  margin-top: 30px;
}

/***********************************************************
 * 劳动力数据部分 - 现代化设计
 ***********************************************************/
.labor-data-container {
  margin-top: 40px;
  padding: 0;
  background-color: transparent;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.labor-charts-wrapper {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.labor-trend-section,
.labor-data-section {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  padding: 30px;
  transition: all 0.3s ease;
}

/***********************************************************
 * 加载和错误状态
 ***********************************************************/
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background-color: #f9f9f9;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 112, 243, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  background-color: #f9f9f9;
  padding: 40px;
  text-align: center;
}

.error-container h2 {
  color: #e74c3c;
  margin-bottom: 15px;
  border-bottom: none;
}

.error-container p {
  color: #555;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/***********************************************************
 * 图片占位符
 ***********************************************************/
.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 16px;
}

.main-placeholder {
  width: 100%;
  height: 400px;
}

.layout-placeholder {
  width: 100%;
  height: 300px;
  max-width: 1000px;
  margin: 0 auto;
}

/***********************************************************
 * 图片加载动画
 ***********************************************************/
.main-image.loading,
.layout-image.loading {
  opacity: 0;
  transform: translateY(20px);
}

.main-image.loaded,
.layout-image.loaded {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

/***********************************************************
 * 打印样式
 ***********************************************************/
@media print {
  .park-detail-page {
    background-color: white;
    color: black;
  }

  .back-button,
  .language-toggle,
  .reset-map-button {
    display: none;
  }

  .detail-header {
    background: none;
    color: black;
    min-height: auto;
    padding: 20px 0;
  }

  .detail-header h1 {
    color: black;
    text-shadow: none;
  }

  .location-tag {
    color: #333;
  }

  .detail-content {
    padding: 0;
  }

  .info-item,
  .infra-item,
  .facility-item,
  .contact-item,
  .highlight-item {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .info-item:hover,
  .infra-item:hover,
  .facility-item:hover,
  .contact-item:hover,
  .highlight-item:hover {
    transform: none;
    box-shadow: none;
  }

  .map-container {
    page-break-inside: avoid;
  }

  h2::after {
    animation: none;
    opacity: 1;
  }
}

/***********************************************************
 * 科幻风格装饰 & 附加效果
 ***********************************************************/
.park-detail-page {
  position: relative;
}
.park-detail-page::before {
  content: '';
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: url('/images/tech-bg-pattern.svg');
  background-size: 500px;
  opacity: 0.02;
  z-index: -1;
  pointer-events: none;
}

/* 自定义滚动条 */
.park-detail-page::-webkit-scrollbar {
  width: 10px;
}

.park-detail-page::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.park-detail-page::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
  border-radius: 5px;
}

.park-detail-page::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--secondary-color), var(--primary-color));
}

/* 文本选择效果 */
.park-detail-page ::selection {
  background-color: rgba(0, 112, 243, 0.2);
  color: #333;
}

/* 图片悬浮效果 */
.main-image,
.layout-image {
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
}

.main-image::after,
.layout-image::after {
  content: '';
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background: linear-gradient(135deg, rgba(0, 112, 243, 0.1) 0%, rgba(255, 255, 255, 0) 50%);
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: 16px;
  pointer-events: none;
}

.main-image:hover::after,
.layout-image:hover::after {
  opacity: 1;
}

/* 标题 hover 装饰 */
h2 {
  position: relative;
}
h2::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 25px;
  background: var(--gradient-bg);
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}
h2:hover::before {
  opacity: 1;
  transform: translateY(-50%) translateX(-5px);
}

/* 按钮悬浮效果 */
.back-button,
.language-toggle,
.reset-map-button {
  position: relative;
  overflow: hidden;
}
.back-button::after,
.language-toggle::after,
.reset-map-button::after {
  content: '';
  position: absolute;
  top: -50%; left: -50%;
  width: 200%; height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
  transform: scale(0);
  opacity: 0;
  transition: transform 0.5s, opacity 0.3s;
}
.back-button:hover::after,
.language-toggle:hover::after,
.reset-map-button:hover::after {
  transform: scale(1);
  opacity: 1;
}

/***********************************************************
 * 新增联系信息布局样式
 ***********************************************************/
.contact-main-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  margin-bottom: 25px;
  position: relative;
  z-index: 1;
  background: rgba(255, 255, 255, 0.5);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.contact-phones {
  background: linear-gradient(145deg, #ffffff, #f5f7fa);
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  margin-top: 30px;
  grid-column: 1 / -1;
}

.contact-phones::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, rgba(52, 152, 219, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.contact-phones h3 {
  display: flex;
  align-items: center;
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.4rem;
  margin-bottom: 25px;
  position: relative;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(52, 152, 219, 0.2);
}

.contact-icon {
  margin-right: 12px;
  font-style: normal;
  font-size: 1.5rem;
  background: linear-gradient(135deg, #3498db, #2980b9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

.phone-numbers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  position: relative;
  z-index: 1;
}

.phone-number-item {
  background: white;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #3498db;
  display: flex;
  flex-direction: column;
}

.phone-number-item strong {
  margin-bottom: 8px;
  color: #2c3e50;
  font-size: 1.05rem;
  display: block;
}

.phone-number-item:hover {
  background-color: #f8fbff;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

/* 园区亮点网格调整为四列 */
.highlights-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 25px;
  position: relative;
  z-index: 1;
}

@media (max-width: 1200px) {
  .highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contact-main-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .highlights-grid {
    grid-template-columns: 1fr;
  }

  .contact-main-grid {
    grid-template-columns: 1fr;
  }

  .phone-numbers-grid {
    grid-template-columns: 1fr;
  }
}

/* 地图弹出窗口样式增强 */
.leaflet-popup-content {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 5px;
}

.leaflet-popup-content strong {
  display: block;
  font-size: 1.1rem;
  margin-bottom: 5px;
  color: #333;
}

.leaflet-popup-content br {
  display: block;
  content: "";
  margin-top: 5px;
}

/* 地图图例样式优化 */
.map-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 15px;
  background-color: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.legend-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
}

/* 新增内容区域样式 */
.content-container {
  background-color: #fff;
  overflow-y: auto;
  height: 100%;
  padding: 20px;
}

/* ParkOverview 组件样式 */
.overview-section {
  margin-bottom: 40px;
}

.redesigned-overview {
  background-color: #fff;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.overview-content {
  margin-top: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin: 25px 0;
}


.info-card {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 15px;
  transition: all 0.3s ease;
}

.info-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 112, 243, 0.1);
  background-color: #f5f9ff;
}

.info-card .icon {
  font-size: 1.5rem;
  color: #0070f3;
}

.info-card h3 {
  margin: 0 0 8px;
  font-size: 1rem;
  color: #555;
}

.info-card p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 1.3rem;
  color: #333;
  margin: 30px 0 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.infrastructure-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.infra-card {
  background-color: #f9f9f9;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.infra-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 112, 243, 0.1);
  background-color: #f5f9ff;
}

.infra-card h4 {
  margin: 0 0 10px;
  font-size: 1.1rem;
  color: #333;
}

.infra-card p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

@media (max-width: 768px) {
  .overview-grid,
  .infrastructure-grid {
    grid-template-columns: 1fr;
  }
}
.toggle-layer-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f0f0f0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%; /* 确保按钮宽度一致 */
  min-width: 160px; /* 设置最小宽度 */
}

.time-series-button {
  position: absolute;
  right: 10px;
  top: 65px;
  width: 40px;
  height: 40px;
  background-color: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  transition: all 0.3s ease;
}

.time-series-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.time-series-button.active {
  background-color: #f0f8ff;
  border-color: #4a90e2;
}

/* 天气按钮样式已移至WeatherTool.css，此处样式已弃用 */
/* 如需修改天气按钮样式，请修改WeatherTool.css文件 */
/*
.weather-button {
  position: absolute;
  right: 10px;
  top: 60px;
  width: 40px;
  height: 40px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
*/

.weather-button:hover {
  background: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.weather-button.active {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* Energy Data Visualization Styles */

/* Energy Panel Styles */
.map-energy-panel {
  position: absolute;
  top: 80px;
  right: 20px;
  width: 320px;
  max-height: 70vh;
  overflow-y: auto;
  z-index: 1000;
  pointer-events: auto;
}

.map-energy-panel-arrow {
  position: absolute;
  top: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #ffffff;
  z-index: 1001;
}

.map-energy-panel.dark-mode .map-energy-panel-arrow {
  border-bottom-color: #374151;
}

/* Energy Toggle Button */
.energy-toggle-button {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.energy-toggle-button:hover {
  background: linear-gradient(135deg, #2563EB, #1E40AF);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.energy-toggle-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.energy-toggle-button.active {
  background: linear-gradient(135deg, #10B981, #059669);
}

.energy-toggle-button.active:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

.energy-toggle-icon {
  font-size: 18px;
  min-width: 20px;
}

.energy-toggle-text {
  flex: 1;
  text-align: left;
}

/* Energy Data Type Grid */
.energy-data-type-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
}

.energy-data-type-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  background: #F9FAFB;
  border: 2px solid #E5E7EB;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  text-align: center;
}

.energy-data-type-button:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
  transform: translateY(-1px);
}

.energy-data-type-button.active {
  background: #EBF8FF;
  border-color: #3B82F6;
  color: #1D4ED8;
}

.energy-data-type-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.dark-mode .energy-data-type-button {
  background: #374151;
  border-color: #4B5563;
  color: #F9FAFB;
}

.dark-mode .energy-data-type-button:hover {
  background: #4B5563;
  border-color: #6B7280;
}

.dark-mode .energy-data-type-button.active {
  background: #1E3A8A;
  border-color: #3B82F6;
  color: #DBEAFE;
}

.energy-data-type-icon {
  font-size: 16px;
}

.energy-data-type-name {
  font-weight: 500;
  line-height: 1.2;
}

/* Industrial Markers Toggle */
.industrial-markers-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 10px 12px;
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.industrial-markers-toggle:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
}

.industrial-markers-toggle.hidden {
  background: #FEF2F2;
  border-color: #FECACA;
  color: #DC2626;
}

.industrial-markers-toggle.visible {
  background: #F0FDF4;
  border-color: #BBF7D0;
  color: #16A34A;
}

.dark-mode .industrial-markers-toggle {
  background: #374151;
  border-color: #4B5563;
  color: #F9FAFB;
}

.dark-mode .industrial-markers-toggle:hover {
  background: #4B5563;
  border-color: #6B7280;
}

.dark-mode .industrial-markers-toggle.hidden {
  background: #7F1D1D;
  border-color: #991B1B;
  color: #FCA5A5;
}

.dark-mode .industrial-markers-toggle.visible {
  background: #14532D;
  border-color: #166534;
  color: #86EFAC;
}

.toggle-icon {
  font-size: 16px;
}

.toggle-text {
  flex: 1;
  text-align: left;
}

/* Energy Markers Toggle */
.energy-markers-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 10px 12px;
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.energy-markers-toggle:hover {
  background: #F3F4F6;
  border-color: #D1D5DB;
}

.energy-markers-toggle.hidden {
  background: #FEF2F2;
  border-color: #FECACA;
  color: #DC2626;
}

.energy-markers-toggle.visible {
  background: #EFF6FF;
  border-color: #BFDBFE;
  color: #2563EB;
}

.dark-mode .energy-markers-toggle {
  background: #374151;
  border-color: #4B5563;
  color: #F9FAFB;
}

.dark-mode .energy-markers-toggle:hover {
  background: #4B5563;
  border-color: #6B7280;
}

.dark-mode .energy-markers-toggle.hidden {
  background: #7F1D1D;
  border-color: #991B1B;
  color: #FCA5A5;
}

.dark-mode .energy-markers-toggle.visible {
  background: #1E3A8A;
  border-color: #1D4ED8;
  color: #93C5FD;
}

/* Energy Data Info */
.energy-data-info {
  padding: 12px;
  background: #F8FAFC;
  border-radius: 6px;
  border-left: 4px solid #3B82F6;
}

.dark-mode .energy-data-info {
  background: #1F2937;
  border-left-color: #60A5FA;
}

.energy-data-source {
  font-weight: 600;
  color: #1F2937;
  margin: 0 0 6px 0;
  font-size: 13px;
}

.dark-mode .energy-data-source {
  color: #F9FAFB;
}

.energy-data-description {
  color: #6B7280;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

.dark-mode .energy-data-description {
  color: #D1D5DB;
}

/* Energy Map Legend */
.energy-legend {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.energy-legend-content {
  padding: 12px;
  min-width: 180px;
}

.energy-legend-content.dark {
  background: rgba(31, 41, 55, 0.95);
  color: #F9FAFB;
}

.energy-legend h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1F2937;
}

.energy-legend-content.dark h4 {
  color: #F9FAFB;
}

.legend-scale {
  margin-bottom: 8px;
}

.legend-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.legend-label {
  font-size: 11px;
  color: #6B7280;
  font-weight: 500;
}

.energy-legend-content.dark .legend-label {
  color: #D1D5DB;
}

.legend-colors {
  display: flex;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.legend-color {
  flex: 1;
}

.legend-source {
  margin: 0;
  font-size: 10px;
  color: #9CA3AF;
  font-style: italic;
}

.energy-legend-content.dark .legend-source {
  color: #9CA3AF;
}

/* Energy Popup Styles */
.energy-popup {
  min-width: 200px;
}

.energy-popup.enhanced {
  min-width: 280px;
  max-width: 320px;
}

.energy-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E5E7EB;
}

.energy-popup-header h4 {
  margin: 0;
  color: #1F2937;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
}

.energy-popup-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-popup-badge.rank-top {
  background: #DCFCE7;
  color: #166534;
}

.energy-popup-badge.rank-excellent {
  background: #DBEAFE;
  color: #1E40AF;
}

.energy-popup-badge.rank-good {
  background: #FEF3C7;
  color: #92400E;
}

.energy-popup-badge.rank-average {
  background: #FEE2E2;
  color: #991B1B;
}

.energy-popup-badge.rank-low {
  background: #F3F4F6;
  color: #6B7280;
}

.energy-popup h4 {
  margin: 0 0 8px 0;
  color: #1F2937;
  font-size: 16px;
  font-weight: 600;
}

.energy-popup-content {
  color: #4B5563;
}

.energy-main-metric {
  margin-bottom: 16px;
}

.energy-metric-label {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 500;
  color: #6B7280;
}

.energy-value {
  font-size: 24px;
  font-weight: 700;
  color: #3B82F6;
  margin: 4px 0;
  line-height: 1.2;
}

.energy-details {
  margin: 16px 0;
}

.energy-detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #F3F4F6;
}

.energy-detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 12px;
  color: #6B7280;
  font-weight: 500;
}

.detail-value {
  font-size: 12px;
  color: #1F2937;
  font-weight: 600;
}

.energy-popup-footer {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #F3F4F6;
  text-align: center;
}

.energy-popup-footer small {
  color: #9CA3AF;
  font-size: 11px;
}

.energy-period {
  font-size: 12px;
  color: #6B7280;
  margin: 4px 0 0 0;
}

/* Energy Data Markers */
.energy-data-marker {
  cursor: pointer !important;
  transition: filter 0.2s ease, opacity 0.2s ease; /* 只对filter和opacity做动画 */
  pointer-events: auto !important;
  z-index: 400 !important; /* Lower z-index to stay below UI panels */
  transform-origin: center center; /* 确保变换以中心为原点 */
}

.energy-data-marker:hover {
  /* 使用不会改变marker大小或位置的视觉效果 */
  z-index: 450 !important; /* Slightly higher z-index on hover but still below UI */
  filter: brightness(1.3) drop-shadow(0 0 6px rgba(59, 130, 246, 0.8));
  /* 避免使用transform，因为它会改变marker的边界框 */
}

/* Energy Detail Overlay */
.energy-detail-marker {
  z-index: 500 !important; /* Higher than other markers but below UI panels */
  pointer-events: auto !important;
}

/* Ensure energy data layer has proper z-index */
.leaflet-pane .leaflet-marker-pane .energy-data-marker {
  z-index: 400 !important;
  pointer-events: auto !important;
}

/* Ensure energy markers are above state boundaries */
.leaflet-overlay-pane .energy-data-marker {
  z-index: 400 !important;
  pointer-events: auto !important;
}

/* Specific styles for Leaflet circle markers used for energy data */
.leaflet-interactive.energy-data-marker {
  cursor: pointer !important;
  pointer-events: auto !important;
  z-index: 400 !important;
}

/* Ensure energy data markers are not blocked by other elements */
svg.energy-data-marker {
  pointer-events: auto !important;
  z-index: 400 !important;
}

/* Override any conflicting styles from other layers */
.leaflet-zoom-animated .energy-data-marker {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Custom pane for energy data markers */
.energy-data-pane {
  z-index: 400 !important;
  pointer-events: auto !important;
}

/* Ensure all elements in the energy data pane are clickable */
.energy-data-pane * {
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Specific styling for energy data markers in their custom pane */
.energy-data-pane .energy-data-marker {
  z-index: 400 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

.energy-detail-popup {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 0;
  min-width: 280px;
  max-width: 320px;
  border: 1px solid #E5E7EB;
  overflow: hidden;
  animation: energyDetailFadeIn 0.3s ease-out;
}



/* 统一的能源数据详细弹窗样式 */
.energy-detail-popup-unified .leaflet-popup-content-wrapper {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.8);
  border: none;
  padding: 0;
  overflow: hidden;
}

.energy-detail-popup-unified .leaflet-popup-content {
  margin: 0;
  padding: 0;
  width: auto !important;
  min-width: 350px;
}

.energy-detail-popup-unified .leaflet-popup-close-button {
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  color: #64748b;
  font-size: 18px;
  font-weight: 500;
  line-height: 32px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.energy-detail-popup-unified .leaflet-popup-close-button:hover {
  background: #ef4444;
  color: white;
  transform: scale(1.1);
}

.energy-detail-modal {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #1e293b;
}

.energy-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.energy-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.state-info {
  position: relative;
  z-index: 1;
}

.state-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.data-type-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.ranking-badge {
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 16px;
  border-radius: 12px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.rank-label {
  display: block;
  font-size: 11px;
  opacity: 0.8;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.rank-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

.rank-total {
  font-size: 12px;
  opacity: 0.7;
}

.rank-excellent {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.2));
  border-color: rgba(34, 197, 94, 0.3);
}

.rank-good {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
  border-color: rgba(59, 130, 246, 0.3);
}

.rank-average {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2));
  border-color: rgba(245, 158, 11, 0.3);
}

.energy-modal-body {
  padding: 24px;
}

.main-metric-card {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.metric-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 2px;
}

.metric-unit {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.2s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-icon {
  font-size: 20px;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 600;
  word-break: break-word;
}

.energy-modal-footer {
  background: #f8fafc;
  padding: 16px 24px;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.footer-note {
  text-align: center;
  font-size: 12px;
  color: #64748b;
  font-style: italic;
}

/* Modern Energy Popup Container Styles */
.energy-popup-modern-container .leaflet-popup-content-wrapper {
  background: transparent !important;
  border-radius: 16px !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.energy-popup-modern-container .leaflet-popup-content {
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  min-width: 320px !important;
  max-width: 380px !important;
  pointer-events: auto !important;
}

.energy-popup-modern-container .leaflet-popup-close-button {
  top: 12px !important;
  right: 12px !important;
  width: 32px !important;
  height: 32px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  color: #64748b !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  line-height: 32px !important;
  text-align: center !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
  z-index: 1000 !important;
}

.energy-popup-modern-container .leaflet-popup-close-button:hover {
  background: #ef4444 !important;
  color: white !important;
  transform: scale(1.1) !important;
}

/* Modern Energy Popup Styles */
.energy-popup-modern {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.8);
  min-width: 320px;
  max-width: 380px;
  animation: energyPopupSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.energy-popup-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.9);
}

@keyframes energyPopupSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.energy-popup-modern .popup-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 20px 24px;
  position: relative;
  overflow: hidden;
}

.energy-popup-modern .popup-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.energy-popup-modern .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.energy-popup-modern .state-title h3 {
  margin: 0 0 6px 0;
  font-size: 22px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}

.energy-popup-modern .data-category {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  display: inline-block;
}

.energy-popup-modern .rank-indicator {
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 16px;
  border-radius: 12px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 80px;
}

.energy-popup-modern .rank-icon {
  font-size: 20px;
  display: block;
  margin-bottom: 4px;
}

.energy-popup-modern .rank-position {
  display: block;
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.energy-popup-modern .rank-total {
  font-size: 10px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-popup-modern .rank-excellent {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.2));
  border-color: rgba(34, 197, 94, 0.3);
}

.energy-popup-modern .rank-good {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.2));
  border-color: rgba(59, 130, 246, 0.3);
}

.energy-popup-modern .rank-average {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.2));
  border-color: rgba(245, 158, 11, 0.3);
}

.energy-popup-modern .rank-below-average {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.2));
  border-color: rgba(239, 68, 68, 0.3);
}

.energy-popup-modern .popup-body {
  padding: 24px;
  background: white;
}

.energy-popup-modern .primary-metric {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  position: relative;
  overflow: hidden;
}

.energy-popup-modern .primary-metric::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.energy-popup-modern .metric-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.energy-popup-modern .metric-circle {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  position: relative;
}

.energy-popup-modern .metric-circle::after {
  content: '';
  position: absolute;
  inset: 2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
}

.energy-popup-modern .metric-icon {
  font-size: 24px;
  position: relative;
  z-index: 1;
}

.energy-popup-modern .metric-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.energy-popup-modern .progress-bar {
  width: 60px;
  height: 6px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.energy-popup-modern .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  animation: progressGlow 2s ease-in-out infinite alternate;
}

@keyframes progressGlow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.6);
  }
}

.energy-popup-modern .progress-label {
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-popup-modern .metric-data {
  flex: 1;
  text-align: left;
}

.energy-popup-modern .metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
}

.energy-popup-modern .metric-unit {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.energy-popup-modern .info-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.energy-popup-modern .info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.energy-popup-modern .info-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.energy-popup-modern .info-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.energy-popup-modern .item-icon {
  font-size: 18px;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.energy-popup-modern .item-content {
  flex: 1;
  min-width: 0;
}

.energy-popup-modern .item-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
  display: block;
}

.energy-popup-modern .item-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 600;
  word-break: break-word;
}

.energy-popup-modern .source-info {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.energy-popup-modern .source-icon {
  font-size: 16px;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: white;
}

.energy-popup-modern .source-content {
  flex: 1;
}

.energy-popup-modern .source-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
  display: block;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.energy-popup-modern .source-value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
}

.energy-popup-modern .popup-footer {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 16px 24px;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.energy-popup-modern .footer-actions {
  text-align: center;
}

.energy-popup-modern .close-hint {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-energy-panel {
    width: 280px;
    right: 10px;
  }

  .energy-data-type-grid {
    grid-template-columns: 1fr;
  }

  .energy-legend-content {
    padding: 8px;
    min-width: 150px;
  }

  .energy-detail-popup-unified .leaflet-popup-content {
    min-width: 300px;
  }

  .energy-modal-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .state-name {
    font-size: 20px;
  }

  .energy-modal-body {
    padding: 20px;
  }

  .main-metric-card {
    padding: 16px;
  }

  .metric-value {
    font-size: 24px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .energy-popup-modern {
    min-width: 320px;
    max-width: 360px;
  }

  .energy-popup-modern .popup-header {
    padding: 16px 20px;
  }

  .energy-popup-modern .header-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .energy-popup-modern .state-title h3 {
    font-size: 20px;
  }

  .energy-popup-modern .popup-body {
    padding: 20px;
  }

  .energy-popup-modern .primary-metric {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .energy-popup-modern .metric-value {
    font-size: 28px;
  }

  .energy-popup-modern .info-row {
    grid-template-columns: 1fr;
  }
}

/* ===== 重新设计的能源弹窗样式 V2 ===== */

/* Error State Popup */
.energy-popup-error {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(239, 68, 68, 0.1);
  min-width: 300px;
  max-width: 350px;
  animation: energyPopupSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.error-header {
  padding: 20px 24px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
}

.error-icon {
  font-size: 24px;
  opacity: 0.8;
}

.error-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
}

.error-body {
  padding: 16px 24px 20px;
}

.error-message {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
}

.error-suggestion {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

/* Main Popup V2 Container */
.energy-popup-redesigned-v2 {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.8);
  min-width: 320px;
  max-width: 380px;
  animation: energyPopupSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.energy-popup-redesigned-v2:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.9);
}

/* Header V2 - Clean and minimalist without color blocks */
.popup-header-v2 {
  padding: 20px 24px 16px;
  background: transparent;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  position: relative;
}

.popup-header-v2::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="headerpattern" width="12" height="12" patternUnits="userSpaceOnUse"><circle cx="6" cy="6" r="0.8" fill="rgba(59,130,246,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23headerpattern)"/></svg>');
  opacity: 0.6;
  pointer-events: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  z-index: 1;
}

.state-info-v2 {
  flex: 1;
}

.state-name-v2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.data-category-v2 {
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Ranking Indicator V2 - Subtle and elegant */
.ranking-indicator-v2 {
  margin-left: 16px;
  display: flex;
  align-items: center;
}

.rank-badge {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 12px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.rank-number {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
}

.rank-total {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
}

/* Ranking color variations - subtle and professional */
.ranking-indicator-v2.rank-excellent .rank-badge {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
}

.ranking-indicator-v2.rank-excellent .rank-number {
  color: #059669;
}

.ranking-indicator-v2.rank-very-good .rank-badge {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.ranking-indicator-v2.rank-very-good .rank-number {
  color: #2563eb;
}

.ranking-indicator-v2.rank-good .rank-badge {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.2);
}

.ranking-indicator-v2.rank-good .rank-number {
  color: #d97706;
}

.ranking-indicator-v2.rank-average .rank-badge {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.ranking-indicator-v2.rank-average .rank-number {
  color: #dc2626;
}

/* Body V2 - Clean content layout */
.popup-body-v2 {
  padding: 20px 24px;
  background: transparent;
}

/* Primary Metric V2 - Focused data display */
.primary-metric-v2 {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.metric-display {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  backdrop-filter: blur(10px);
}

.metric-value-v2 {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.metric-unit-v2 {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Ranking Details V2 - Subtle ranking information */
.ranking-details-v2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 10px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.percentile-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.percentile-label {
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
}

.percentile-value {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.rank-description {
  font-size: 13px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Data Unavailable State */
.data-unavailable-v2 {
  text-align: center;
  padding: 32px 16px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.unavailable-icon {
  font-size: 32px;
  opacity: 0.6;
  margin-bottom: 12px;
}

.unavailable-text {
  font-size: 16px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 4px;
}

.unavailable-subtitle {
  font-size: 13px;
  color: #94a3b8;
}

/* Footer V2 - Clean metadata display */
.popup-footer-v2 {
  padding: 16px 24px 20px;
  background: rgba(248, 250, 252, 0.4);
  border-top: 1px solid rgba(148, 163, 184, 0.1);
}

.metadata-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metadata-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metadata-label {
  font-size: 11px;
  font-weight: 600;
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metadata-value {
  font-size: 13px;
  font-weight: 500;
  color: #475569;
}

/* Responsive adjustments for V2 */
@media (max-width: 768px) {
  .energy-popup-redesigned-v2 {
    min-width: 300px;
    max-width: 340px;
  }

  .popup-header-v2 {
    padding: 16px 20px 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .ranking-indicator-v2 {
    margin-left: 0;
  }

  .state-name-v2 {
    font-size: 18px;
  }

  .popup-body-v2 {
    padding: 16px 20px;
  }

  .metric-value-v2 {
    font-size: 28px;
  }

  .metadata-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* ===== 重新设计的能源弹窗样式 ===== */

/* 弹窗容器样式 */
.energy-popup-redesigned-container .leaflet-popup-content-wrapper {
  background: transparent !important;
  border-radius: 16px !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.energy-popup-redesigned-container .leaflet-popup-content {
  margin: 0 !important;
  padding: 0 !important;
  width: auto !important;
  min-width: 350px !important;
  max-width: 400px !important;
  pointer-events: auto !important;
}

.energy-popup-redesigned-container .leaflet-popup-tip {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 主弹窗容器 */
.energy-popup-redesigned,
.energy-popup-redesigned-main {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.8);
  min-width: 350px;
  max-width: 400px;
  animation: energyPopupSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.energy-popup-redesigned:hover,
.energy-popup-redesigned-main:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.9);
}

/* 弹窗头部 */
.popup-header-redesigned,
.popup-header-redesigned-main {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.popup-header-redesigned::before,
.popup-header-redesigned-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.state-info,
.state-info-main {
  flex: 1;
  z-index: 1;
}

.state-name,
.state-name-main {
  font-size: 22px;
  font-weight: 700;
  margin: 0 0 4px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.data-category,
.data-category-main {
  font-size: 13px;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* 排名徽章 */
.ranking-badge,
.ranking-badge-main {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 80px;
  z-index: 1;
}

.rank-icon,
.rank-icon-main {
  font-size: 20px;
  display: block;
  margin-bottom: 2px;
}

.rank-details,
.rank-details-main {
  text-align: center;
  line-height: 1.2;
}

.rank-position,
.rank-position-main {
  display: block;
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.rank-total,
.rank-total-main {
  font-size: 10px;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 排名等级颜色 */
.ranking-badge.rank-excellent,
.ranking-badge-main.rank-excellent {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.25), rgba(22, 163, 74, 0.25));
  border-color: rgba(34, 197, 94, 0.4);
}

.ranking-badge.rank-very-good,
.ranking-badge-main.rank-very-good {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(37, 99, 235, 0.25));
  border-color: rgba(59, 130, 246, 0.4);
}

.ranking-badge.rank-good,
.ranking-badge-main.rank-good {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.25), rgba(217, 119, 6, 0.25));
  border-color: rgba(245, 158, 11, 0.4);
}

.ranking-badge.rank-average,
.ranking-badge-main.rank-average {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.25), rgba(220, 38, 38, 0.25));
  border-color: rgba(239, 68, 68, 0.4);
}

.ranking-badge.rank-below-average,
.ranking-badge-main.rank-below-average {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.25), rgba(75, 85, 99, 0.25));
  border-color: rgba(107, 114, 128, 0.4);
}

.ranking-badge.rank-no-data,
.ranking-badge-main.rank-no-data {
  background: linear-gradient(135deg, rgba(156, 163, 175, 0.25), rgba(107, 114, 128, 0.25));
  border-color: rgba(156, 163, 175, 0.4);
}

/* 弹窗主体 */
.popup-body-redesigned,
.popup-body-redesigned-main {
  padding: 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

/* 主要指标区域 */
.primary-metric,
.primary-metric-main {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.metric-visual,
.metric-visual-main {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.metric-icon,
.metric-icon-main {
  font-size: 32px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background-color: rgba(79, 70, 229, 0.1);
  background-image: none;
  color: #4f46e5;
}

.progress-container,
.progress-container-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar,
.progress-bar-main {
  height: 8px;
  background: rgba(148, 163, 184, 0.2);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill,
.progress-fill-main {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.progress-fill.rank-excellent,
.progress-fill-main.rank-excellent {
  background: linear-gradient(90deg, #22c55e, #16a34a);
}

.progress-fill.rank-very-good,
.progress-fill-main.rank-very-good {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.progress-fill.rank-good,
.progress-fill-main.rank-good {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.progress-fill.rank-average,
.progress-fill-main.rank-average {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.progress-fill.rank-below-average,
.progress-fill-main.rank-below-average {
  background: linear-gradient(90deg, #6b7280, #4b5563);
}

.progress-text,
.progress-text-main {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

/* 指标值容器 */
.metric-value-container,
.metric-value-container-main {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.metric-value,
.metric-value-main {
  font-size: 28px;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
}

.metric-unit,
.metric-unit-main {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-label,
.metric-label-main {
  font-size: 14px;
  color: #4f46e5;
  font-weight: 600;
  padding: 4px 8px;
  background: rgba(79, 70, 229, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

/* 信息网格 */
.info-grid,
.info-grid-main {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item,
.info-item-main {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;
}

.info-item:hover,
.info-item-main:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(79, 70, 229, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-icon,
.info-icon-main {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.info-content,
.info-content-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.info-label,
.info-label-main {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value,
.info-value-main {
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
}

/* 弹窗底部 */
.popup-footer-redesigned,
.popup-footer-redesigned-main {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  padding: 16px 24px;
  border-top: 1px solid rgba(148, 163, 184, 0.1);
  text-align: center;
}

.close-hint,
.close-hint-main {
  font-size: 12px;
  color: #64748b;
  font-style: italic;
  opacity: 0.8;
}

/* 动画效果 */
@keyframes energyPopupSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 - 重新设计的弹窗 */
@media (max-width: 768px) {
  .energy-popup-redesigned,
  .energy-popup-redesigned-main {
    min-width: 320px;
    max-width: 360px;
  }

  .popup-header-redesigned,
  .popup-header-redesigned-main {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .state-name,
  .state-name-main {
    font-size: 20px;
  }

  .popup-body-redesigned,
  .popup-body-redesigned-main {
    padding: 20px;
  }

  .primary-metric,
  .primary-metric-main {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .metric-visual,
  .metric-visual-main {
    justify-content: center;
  }

  .metric-value-container,
  .metric-value-container-main {
    align-items: center;
    text-align: center;
  }

  .metric-value,
  .metric-value-main {
    font-size: 24px;
  }

  .info-grid,
  .info-grid-main {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .info-item,
  .info-item-main {
    padding: 12px;
  }
}

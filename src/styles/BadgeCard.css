/* Badge Card Component Styles */
.badge-card {
  position: relative;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.badge-card:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(6, 182, 212, 0.2);
}

/* Badge Recess at Top */
.badge-recess-container {
  position: absolute;
  top: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 8rem;
  height: 4rem;
}

.badge-recess-shadow {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  filter: blur(6px);
}

.badge-recess-main {
  position: absolute;
  inset: 0.25rem;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.5);
}

.badge-recess-inner {
  position: absolute;
  inset: 0.5rem;
  background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.3) 100%);
  border-radius: 50%;
  box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.8), inset 0 -1px 2px rgba(255, 255, 255, 0.05);
}

.badge-recess-highlight {
  position: absolute;
  bottom: 0.25rem;
  left: 0.5rem;
  right: 0.5rem;
  height: 0.25rem;
  background: linear-gradient(90deg, transparent 0%, rgba(6, 182, 212, 0.3) 50%, transparent 100%);
  border-radius: 9999px;
  filter: blur(1px);
}

.badge-recess-side-left,
.badge-recess-side-right {
  position: absolute;
  top: 0.5rem;
  bottom: 0.5rem;
  width: 0.25rem;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.3) 100%);
}

.badge-recess-side-left {
  left: 0;
  border-radius: 9999px 0 0 9999px;
}

.badge-recess-side-right {
  right: 0;
  border-radius: 0 9999px 9999px 0;
}

/* Card Lighting Effects */
.badge-card-lighting {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 30%,
    transparent 70%,
    rgba(6, 182, 212, 0.05) 100%
  );
  border-radius: 2rem;
  pointer-events: none;
}

.badge-card-edge-left,
.badge-card-edge-right {
  position: absolute;
  top: 2rem;
  bottom: 2rem;
  width: 0.125rem;
  background: linear-gradient(180deg, transparent 0%, rgba(6, 182, 212, 0.4) 50%, transparent 100%);
  border-radius: 9999px;
}

.badge-card-edge-left {
  left: 0;
}

.badge-card-edge-right {
  right: 0;
}

/* Card Content */
.badge-card-content {
  position: relative;
  z-index: 10;
  padding: 5rem 2rem 2rem 2rem;
}

/* Enhanced User Icon Styles */
.user-icon-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 10;
}

.user-icon {
  position: relative;
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, #1e3a8a 0%, #0891b2 50%, #06b6d4 100%);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 8px 24px rgba(6, 182, 212, 0.4),
    0 4px 12px rgba(30, 58, 138, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 2px rgba(0, 0, 0, 0.2);
  transform: translateZ(0);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 欢迎模式的背景渐变 */
.user-icon.welcome-mode {
  background: linear-gradient(135deg, #be185d 0%, #e11d48 50%, #f43f5e 100%);
  background-size: 200% 200%;
  box-shadow: 
    0 8px 24px rgba(244, 63, 94, 0.4),
    0 4px 12px rgba(190, 24, 93, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 2px rgba(0, 0, 0, 0.2);
}

/* 欢迎模式的hover状态 */
.user-icon.welcome-mode:hover {
  background: linear-gradient(135deg, #f43f5e 0%, #e11d48 50%, #be185d 100%);
  background-size: 200% 200%;
  box-shadow: 
    0 12px 32px rgba(244, 63, 94, 0.5),
    0 6px 16px rgba(190, 24, 93, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 2px rgba(0, 0, 0, 0.1);
}

/* 欢迎模式的发光效果 */
.user-icon.welcome-mode .user-icon-glow {
  background: radial-gradient(circle, rgba(244, 63, 94, 0.3) 0%, transparent 70%);
}

/* 转换中的状态 */
.user-icon.transitioning {
  animation: iconPulse 0.6s ease-in-out, gradientShift 1.2s ease-in-out;
}

/* 为用户模式添加背景尺寸以支持渐变动画 */
.user-icon.user-mode {
  background-size: 200% 200%;
}

.user-icon:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 
    0 12px 32px rgba(6, 182, 212, 0.5),
    0 6px 16px rgba(30, 58, 138, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 2px rgba(0, 0, 0, 0.1);
}

/* Icon Background Glow */
.user-icon-glow {
  position: absolute;
  inset: -0.5rem;
  background: radial-gradient(circle, rgba(6, 182, 212, 0.2) 0%, transparent 70%);
  border-radius: 1.5rem;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.user-icon:hover .user-icon-glow {
  opacity: 1;
}

/* 图标过渡容器 */
.icon-transition-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1) rotate(0deg);
  opacity: 1;
  z-index: 2;
}

.icon-transition-container.transitioning {
  opacity: 0;
  transform: scale(0.8) rotate(10deg);
}

/* SVG Icon Styling */
.user-icon-svg {
  color: #ffffff;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
  z-index: 2;
}

.user-icon:hover .user-icon-svg {
  color: #f0f9ff;
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
}

/* Enhanced Border Effects */
.user-icon-border {
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  border: 2px solid;
  border-image: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.4) 0%, 
    rgba(6, 182, 212, 0.6) 50%, 
    rgba(255, 255, 255, 0.2) 100%) 1;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-icon-border-inner {
  position: absolute;
  inset: 0.125rem;
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    transparent 50%, 
    rgba(6, 182, 212, 0.1) 100%);
}

/* Enhanced Highlight Effect */
.user-icon-highlight {
  position: absolute;
  top: 0.25rem;
  left: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  background: radial-gradient(circle, 
    rgba(255, 255, 255, 0.6) 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%);
  border-radius: 50%;
  filter: blur(1px);
  animation: highlight-pulse 3s ease-in-out infinite;
}

/* New Reflection Effect */
.user-icon-reflection {
  position: absolute;
  bottom: 0.25rem;
  right: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
  background: linear-gradient(45deg, 
    transparent 0%, 
    rgba(6, 182, 212, 0.3) 50%, 
    transparent 100%);
  border-radius: 50%;
  filter: blur(0.5px);
  opacity: 0.7;
}

/* Animations */
@keyframes highlight-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.1);
  }
}

/* 图标转换脉冲动画 */
@keyframes iconPulse {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 背景渐变动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .badge-card-content {
    padding: 4rem 1.5rem 1.5rem 1.5rem;
  }
  
  .user-icon {
    width: 3.5rem;
    height: 3.5rem;
  }
  
  .user-icon-svg {
    width: 28px;
    height: 28px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .user-icon,
  .user-icon-glow,
  .user-icon-svg,
  .user-icon-highlight {
    transition: none;
    animation: none;
  }
  
  .user-icon:hover {
    transform: none;
  }
} 
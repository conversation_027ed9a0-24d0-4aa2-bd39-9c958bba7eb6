.map-zoom-control {
  position: absolute;
  top: 110px; /* 位于3D按钮下方，间距10px */
  right: 10px; /* 与其他按钮对齐 */
  z-index: 1000;
  display: flex;
  flex-direction: column; /* 改为垂直排列 */
  background: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  overflow: hidden;
}

.zoom-button {
  width: 36px;
  height: 40px;
  background-color: white;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 0;
  margin: 0;
}

.zoom-button:hover {
  background-color: #f5f5f5;
}

.zoom-button:active {
  background-color: #e0e0e0;
}

.zoom-in {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

.zoom-out {
  border-bottom: none;
}

/* 暗黑模式样式 */
.dark-mode .map-zoom-control {
  background-color: #333;
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.8);
}

.dark-mode .zoom-button {
  background-color: #333;
  color: #f5f5f5;
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .zoom-button:hover {
  background-color: #444;
}

.dark-mode .zoom-button:active {
  background-color: #555;
}

.dark-mode .zoom-in {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .map-zoom-control {
    right: 10px;
    top: 60px;
  }
  
  .zoom-button {
    width: 30px;
    height: 36px;
    font-size: 18px;
  }
}
/* 容器：左侧垂直对齐 */
.map-time-slider-container {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  height: 60%;
  max-height: 300px;
  z-index: 1000;
  /* 移除大框框背景 */
  background: transparent;
  padding: 0;
  border-radius: 0;
}

/* 时间轴主轨道 */
.time-slider-track {
  position: relative;
  width: 30px;
  height: 100%;
  cursor: pointer;
  touch-action: none; /* 防止触摸时的浏览器默认行为 */
  user-select: none; /* 防止文本选择 */
}

.time-slider-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 3px;
  /* 使用渐变效果代替单色 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(255, 102, 0, 0.9));
  transform: translateX(-50%);
  border-radius: 3px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);
}

/* 刻度容器 */
.time-slider-ticks {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

/* 小刻度 - 使用点状 */
.time-slider-tick {
  position: absolute;
  left: 50%;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  transform: translate(-50%, -50%);
  transition: all 0.2s ease;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

/* 橙色圆形标记 */
.time-slider-marker {
  position: absolute;
  left: 50%;
  width: 24px;
  height: 24px;
  background-color: #ff6600;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 10px rgba(0,0,0,0.4), 0 0 0 4px rgba(255, 102, 0, 0.2);
  transform: translateX(-50%);
  z-index: 2;
  transition: transform 0.2s ease, box-shadow 0.2s ease; /* 平滑过渡效果 */
  cursor: grab; /* 显示抓取光标 */
}

/* 时间提示框 */
.time-slider-tooltip {
  position: absolute;
  left: 50%;
  transform: translate(28px, -50%);
  background-color: rgba(40, 44, 52, 0.85);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
  z-index: 9999; /* 提高z-index确保显示在最上层 */
  pointer-events: none; /* 防止干扰鼠标事件 */
  animation: fadeIn 0.2s ease-out;
  border-left: 3px solid #ff6600;
}

/* 提示框小三角形 */
.time-slider-tooltip:before {
  content: '';
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 6px 6px 6px 0;
  border-style: solid;
  border-color: transparent rgba(40, 44, 52, 0.85) transparent transparent;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(15px, -50%); }
  to { opacity: 1; transform: translate(28px, -50%); }
}

/* 拖动状态样式 */
.time-slider-marker.dragging {
  cursor: grabbing; /* 显示抓取中光标 */
  transform: translateX(-50%) scale(1.2); /* 放大效果 */
  box-shadow: 0 0 15px rgba(255, 102, 0, 0.6), 0 0 0 6px rgba(255, 102, 0, 0.3);
}

/* 鼠标悬停或触摸时的视觉反馈 */
.time-slider-track:hover .time-slider-marker,
.time-slider-track:active .time-slider-marker {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 0 12px rgba(255, 102, 0, 0.5), 0 0 0 5px rgba(255, 102, 0, 0.2);
}

/* 时间标签：更简洁的设计 */
.time-slider-labels {
  position: relative;
  height: 100%;
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.time-slider-label {
  font-size: 13px;
  color: #fff;
  background-color: rgba(40, 44, 52, 0.75);
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  padding: 6px 10px;
  border-radius: 6px;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  border-left: 3px solid #ff6600;
  transition: all 0.2s ease;
}

.time-slider-label:hover {
  background-color: rgba(40, 44, 52, 0.9);
  transform: translateX(3px);
}

/* 颜色图例 - 更现代的设计 */
.labor-trend-legend {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 150px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.color-gradient {
  flex: 1;
  width: 16px;
  margin: 0 auto;
  border-radius: 8px;
  background: linear-gradient(to bottom, #ffd699, #ff9999);
  box-shadow: 0 3px 10px rgba(0,0,0,0.2);
  position: relative;
}

.color-gradient::before,
.color-gradient::after {
  content: '';
  position: absolute;
  left: 50%;
  width: 10px;
  height: 10px;
  background: white;
  border-radius: 50%;
  transform: translateX(-50%);
}

.color-gradient::before {
  top: -5px;
}

.color-gradient::after {
  bottom: -5px;
}

.legend-labels {
  position: absolute;
  left: -30px;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.legend-labels span {
  font-size: 13px;
  color: #fff;
  background-color: rgba(40, 44, 52, 0.75);
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  padding: 5px 8px;
  border-radius: 6px;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(0,0,0,0.2);
  border-right: 3px solid #ff6600;
  transition: all 0.2s ease;
}

.legend-labels span:hover {
  background-color: rgba(40, 44, 52, 0.9);
  transform: translateX(-3px);
}

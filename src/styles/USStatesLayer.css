/* USStatesLayer.css */

/* 默认状态下的州边界样式 */
.us-state-boundary {
  fill: transparent;
  stroke: #666;
  stroke-width: 1;
  stroke-opacity: 0.5;
  transition: all 0.3s ease;
  pointer-events: auto !important; /* 确保即使在标记下方也能接收鼠标事件 */
}

/* 鼠标悬停时的州边界样式 */
.us-state-boundary:hover,
.us-state-boundary.highlighted {
  fill: rgba(51, 136, 255, 0.3);
  stroke: #333;
  stroke-width: 2;
  stroke-opacity: 0.8;
  cursor: pointer;
}

/* 夜间模式下的州边界样式 */
.dark-mode .us-state-boundary {
  stroke: #999;
}

.dark-mode .us-state-boundary:hover,
.dark-mode .us-state-boundary.highlighted {
  fill: rgba(51, 136, 255, 0.4);
  stroke: #ccc;
}

/* 州名称工具提示样式 */
.state-tooltip {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

/* 夜间模式下的工具提示样式 */
.dark-mode .state-tooltip {
  background-color: rgba(40, 44, 52, 0.9);
  color: #fff;
  border-color: #555;
}

/* 确保GeoJSON州边界层能够接收鼠标事件 */
.geojson-states-pane {
  z-index: 200 !important;
  pointer-events: auto !important;
}

/* 确保州边界路径能够接收鼠标事件 */
.us-state-path {
  pointer-events: auto !important;
  cursor: pointer;
}

/* 确保州边界层在标记下方但仍能接收鼠标事件 */
.leaflet-overlay-pane path {
  pointer-events: auto !important;
}

/* 自定义州名工具提示样式 */
.custom-state-tooltip {
  position: fixed;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  pointer-events: none;
  transition: all 0.1s ease;
}

/* 夜间模式下的自定义工具提示样式 */
.custom-state-tooltip.dark-mode {
  background-color: rgba(40, 44, 52, 0.9);
  color: #fff;
  border-color: #555;
}

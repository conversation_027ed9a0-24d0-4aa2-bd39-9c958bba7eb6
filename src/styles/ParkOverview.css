/* ParkOverview 组件专用样式 */
.overview-section {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
}

.redesigned-overview {
  background-color: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.overview-hero {
  margin-bottom: 30px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  position: relative;
}

.overview-description {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #444;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin: 25px 0 35px;
}

.overview-section .info-card {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 15px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.overview-section .info-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 112, 243, 0.1);
  background-color: #f5f9ff;
  border-left-color: #0070f3;
}

.overview-section .info-card .icon {
  font-size: 1.5rem;
  color: #0070f3;
  background-color: rgba(0, 112, 243, 0.1);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
}

.overview-section .info-card h3 {
  margin: 0 0 8px;
  font-size: 1rem;
  color: #555;
  font-weight: 500;
}

.overview-section .info-card p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  font-size: 1.3rem;
  color: #333;
  margin: 35px 0 25px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  position: relative;
}

.section-subtitle::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #0070f3, #00c4ff);
  border-radius: 3px;
}

.infrastructure-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
}

.infra-card {
  background-color: #f9f9f9;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-top: 4px solid transparent;
  position: relative;
  overflow: hidden;
}

.infra-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.infra-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 112, 243, 0.15);
  background-color: #f5f9ff;
  border-top-color: #0070f3;
}

.infra-card:hover::before {
  opacity: 1;
}

.infra-card h4 {
  margin: 0 0 15px;
  font-size: 1.15rem;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  position: relative;
  z-index: 1;
}

.infra-card h4::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 50px;
  height: 2px;
  background: #0070f3;
  transition: width 0.3s ease;
}

.infra-card:hover h4::after {
  width: 100px;
}

.infra-content {
  color: #555;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

.structured-infra {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.infra-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 12px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  transition: all 0.2s ease;
  border-left: 2px solid transparent;
}

.infra-item:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-left-color: #0070f3;
  transform: translateX(3px);
}

.infra-label {
  font-size: 0.85rem;
  color: #666;
  font-weight: 500;
}

.infra-value {
  font-size: 1rem;
  color: #333;
  font-weight: 600;
  word-break: break-word;
}

.no-data-message {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  background-color: #f5f5f5;
  border-radius: 10px;
  color: #666;
  font-style: italic;
}

/* 为特定基础设施类型添加图标 */
.infra-card[data-type="water_supply"] h4::before,
.infra-card[data-type="electricity"] h4::before,
.infra-card[data-type="internet"] h4::before,
.infra-card[data-type="waste_water_treatment"] h4::before {
  margin-right: 8px;
  font-size: 1.1rem;
}

.infra-card[data-type="water_supply"] h4::before {
  content: '💧';
}

.infra-card[data-type="electricity"] h4::before {
  content: '⚡';
}

.infra-card[data-type="internet"] h4::before {
  content: '🌐';
}

.infra-card[data-type="waste_water_treatment"] h4::before {
  content: '♻️';
}

/* 响应式设计 */
@media (max-width: 768px) {
  .overview-grid,
  .infrastructure-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-section {
    padding: 20px;
  }
  
  .infra-card {
    padding: 20px;
  }
}

/* 图片加载状态 */
.image-placeholder {
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  border-radius: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 112, 243, 0.2);
  border-radius: 50%;
  border-top-color: #0070f3;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.main-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 16px;
  transition: opacity 0.3s ease, transform 0.5s ease;
}

.main-image.loading {
  opacity: 0;
}

.main-image.loaded {
  opacity: 1;
}

.main-image.loaded:hover {
  transform: scale(1.02);
}

/* 打印样式 */
@media print {
  .overview-section {
    box-shadow: none;
    padding: 0;
  }
  
  .infra-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  .infra-item {
    background: none;
    padding: 5px 0;
  }
}
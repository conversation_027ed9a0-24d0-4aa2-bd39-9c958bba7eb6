.nationwide-migration-summary {
  position: absolute;
  top: 120px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  z-index: 1500;
  width: 340px; /* 增加宽度以防止溢出 */
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  animation: slideIn 0.4s ease-out;
  border: 1px solid rgba(0, 0, 0, 0.1);
  max-height: calc(100vh - 240px);
  overflow-y: auto;
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
  pointer-events: auto; /* Ensure content is clickable */
}

/* Ensure the popup content doesn't block the close button */
.leaflet-popup .nationwide-migration-summary {
  padding-top: 30px; /* Add padding at the top to avoid overlapping with close button */
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-30px); }
  to { opacity: 1; transform: translateX(0); }
}

.nationwide-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  position: relative;
}

.header-title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
}

.migration-title {
  margin: 0;
  padding: 0;
  font-size: 18px;
  white-space: nowrap;
  width: auto;
  text-align: left;
}

.header-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
  z-index: 10;
}

.close-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  color: #ff4d4f;
  transform: rotate(90deg);
}

/* Migration tabs styling */
.migration-tabs {
  display: flex;
  width: 100%;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.migration-tab {
  flex: 1;
  padding: 8px 0;
  text-align: center;
  background: none;
  border: none;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.migration-tab:hover {
  color: #1890ff;
}

.migration-tab.active {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  font-weight: 500;
}

.nationwide-header h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  width: 100%;
  padding: 0 30px; /* 为右侧的语言切换按钮留出空间 */
  box-sizing: border-box;
  pointer-events: auto; /* Ensure text is clickable */
}

.header-actions {
  position: absolute;
  top: 0;
  left: 10px;
  display: flex;
  align-items: center;
  z-index: 10;
}

.language-toggle-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
}

.language-toggle-btn:hover {
  background-color: #e0e0e0;
}

.nationwide-total {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 5px;
}

.total-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.total-value {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
}

.nationwide-data-view, .nationwide-chart-view {
  display: flex;
  flex-direction: column;
}

.nationwide-data-blocks {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 5px; /* Add padding for scrollbar */
}

.nationwide-data-block {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.migration-data-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #333;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.migration-data-values {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
  width: 100%;
  box-sizing: border-box;
}

.migration-data-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.migration-data-percentage {
  font-size: 14px;
  color: #666;
  min-width: 50px;
  text-align: right;
}

.migration-data-bar-container {
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.migration-data-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.toggle-chart-btn {
  align-self: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 6px 15px;
  font-size: 13px;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
  margin-top: 10px;
  width: 100%;
  max-width: 200px;
}

.toggle-chart-btn:hover {
  background-color: #e8e8e8;
}

.nationwide-chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.nationwide-pie-chart-wrapper {
  width: 180px;
  height: 180px;
  margin-bottom: 15px;
}

.chart-legend-container {
  width: 100%;
  margin-top: 10px;
  overflow: hidden;
  position: relative;
}

.chart-legend {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  width: 100%;
  overflow-x: auto;
  padding-bottom: 10px; /* 为滚动条留出空间 */
  scrollbar-width: thin;
  scrollbar-color: #ddd #f5f5f5;
}

.chart-legend::-webkit-scrollbar {
  height: 6px;
}

.chart-legend::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.chart-legend::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}

.chart-legend::-webkit-scrollbar-thumb:hover {
  background-color: #ccc;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  flex-shrink: 0;
  padding: 4px 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #eee;
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

.no-chart-data {
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-style: italic;
}

/* Reasons slider styles */
.reasons-slider-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.reasons-slider-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  transition: transform 0.3s ease;
}

.reasons-pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  gap: 15px;
}

.pagination-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: #666;
  transition: all 0.2s;
  padding: 0;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #e0e0e0;
  color: #1890ff;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-indicator {
  font-size: 12px;
  color: #666;
}

/* Custom scrollbar styling */
.nationwide-data-blocks::-webkit-scrollbar {
  width: 6px;
}

.nationwide-data-blocks::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.nationwide-data-blocks::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.nationwide-data-blocks::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .nationwide-migration-summary {
    width: 280px;
  }

  .nationwide-pie-chart-wrapper {
    width: 150px;
    height: 150px;
  }
}

.heatmap-content {
  width: 100%;
  overflow-x: auto;
  margin-top: 15px;
}

.heatmap-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.heatmap-table th,
.heatmap-table td {
  padding: 8px 12px;
  text-align: center;
  border: 1px solid #e0e0e0;
}

.heatmap-table th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.heatmap-table th:first-child {
  text-align: left;
}

.occupation-label {
  text-align: left !important;
  font-weight: 500;
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.heatmap-cell {
  transition: all 0.2s ease;
}

.heatmap-cell:hover {
  transform: scale(1.05);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  z-index: 1;
  position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .heatmap-table {
    font-size: 12px;
  }
  
  .heatmap-table th,
  .heatmap-table td {
    padding: 6px 8px;
  }
  
  .occupation-label {
    max-width: 150px;
  }
}

/* 环境监测图例样式 - 底部导航栏风格 */
.environment-legend {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  max-width: 90%;
  flex-wrap: wrap;
  justify-content: center;
}

.environment-legend h4 {
  margin: 0 8px 0 0;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
}

.legend-items {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  justify-content: center;
  overflow-x: auto;
  padding-bottom: 4px;
  scrollbar-width: thin;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 12px;
  white-space: nowrap;
}

.color-box {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 4px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

/* 环境监测图层控制按钮 - 与侧边栏按钮一致的样式 */
.environment-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.environment-layer-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f0f0f0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  min-width: 160px;
}

.environment-layer-button:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.environment-layer-button.active {
  background-color: #4CAF50;
  color: white;
}

/* 环境类型选择器 - 横向弹出 */
.environment-type-selector {
  position: absolute;
  right: 100%; /* 位于按钮左侧 */
  top: 0;
  display: flex;
  flex-direction: row;
  gap: 8px;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transform: translateX(20px);
  transition: all 0.3s ease;
  padding-right: 10px;
}

/* 环境类型选择器激活状态 */
.environment-type-selector.active {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.env-type-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background: #f0f0f0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.env-type-btn:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.env-type-btn.active {
  background-color: #4CAF50;
  color: white;
}

/* 控制面板样式 */
.environment-control-panel {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .environment-legend {
    bottom: 60px;
    padding: 5px 8px;
    max-width: 95%;
  }
  
  .environment-legend h4 {
    font-size: 12px;
    margin-bottom: 2px;
  }
  
  .legend-items {
    gap: 3px;
    padding-bottom: 3px;
    margin-top: 2px;
  }
  
  .legend-item {
    font-size: 9px;
    padding: 1px 3px;
    white-space: nowrap;
  }
  
  .color-box {
    width: 8px;
    height: 8px;
  }
  
  .environment-controls {
    bottom: 50px;
  }
  
  /* 环境监测按钮样式调整 */
  .environment-layer-button,
  .env-type-btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: auto;
    width: auto;
  }
  
  /* 环境类型选择器改为垂直布局 */
  .environment-type-selector {
    position: fixed; /* 使用fixed定位避免滚动影响 */
    flex-direction: column;
    top: 70px; /* 距离顶部一定距离 */
    right: 10px; /* 保持在右侧 */
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
    gap: 8px;
    z-index: 1001; /* 确保在其他元素之上 */
    width: auto;
    max-width: 120px;
  }
  
  /* 确保环境类型按钮在小屏幕上垂直排列且不重叠 */
  .env-type-btn {
    margin-bottom: 5px;
    white-space: nowrap;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  /* 最后一个按钮不需要底部边距 */
  .env-type-btn:last-child {
    margin-bottom: 0;
  }
}
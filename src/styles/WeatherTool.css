.weather-tool-container {
  position: absolute;
  top: 60px; /* 调整位置，放在3D按钮原来的位置 */
  right: 10px;
  z-index: 999; /* 降低z-index使其低于时间序列面板(1000) */
  pointer-events: auto;
}

/* 天气按钮样式 - 详情页面中的按钮 */
.weather-button {
  position: absolute;
  top: 220px; /* Position below analysis button */
  right: 10px;
  width: 40px;
  height: 40px;
  background-color: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s ease;
  pointer-events: auto;
  z-index: 1001; /* Higher z-index to ensure it's above other controls */
  color: #333;
}

.weather-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.7);
}

.weather-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.weather-button.active {
  background-color: #f0f8ff;
  border-color: #4a90e2;
  color: #2c3e50;
}

/* Dark mode styles */
.dark-mode .weather-button {
  background-color: #2c3e50;
  border-color: rgba(255, 255, 255, 0.1);
  color: #ecf0f1;
}

.dark-mode .weather-button:hover {
  background-color: #34495e;
}

.dark-mode .weather-button.active {
  background-color: #3a5a80;
  border-color: #4a90e2;
  color: #fff;
}

.weather-panel {
  position: absolute;
  top: 0;
  right: 45px;
  width: 320px;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 15px;
  z-index: 1001;
  max-height: 80vh;
  overflow-y: auto;
  transform-origin: top right;
  animation: panel-appear 0.3s ease;
  pointer-events: auto;
  touch-action: pan-y;
  -webkit-overflow-scrolling: touch;
}

.weather-panel * {
  pointer-events: auto;
  touch-action: auto;
}

@keyframes panel-appear {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.weather-panel h3 {
  margin: 0 0 15px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.weather-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  color: #666;
}

.weather-error {
  padding: 15px;
  background-color: #fff6f6;
  border-left: 3px solid #e74c3c;
  margin-bottom: 15px;
  color: #e74c3c;
}

.current-weather {
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.current-weather h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.temperature {
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.weather-details {
  font-size: 14px;
  color: #555;
}

.weather-details p {
  margin: 5px 0;
}

.sun-times {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px dashed #ddd;
}

.nearby-weather {
  background-color: #f5f9ff;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.nearby-weather h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.nearby-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nearby-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #eee;
}

.nearby-item:hover {
  background-color: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nearby-item.selected {
  background-color: #e6f2ff;
  border-color: #4a90e2;
}

.nearby-name {
  font-weight: 500;
  flex: 1;
}

.nearby-temp {
  margin: 0 10px;
  font-weight: 600;
}

.weather-forecast {
  background-color: #f5f9ff;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.weather-forecast h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.forecast-list {
  display: flex;
  overflow-x: auto;
  gap: 10px;
  padding-bottom: 5px;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.forecast-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  padding: 10px;
  border-radius: 4px;
  min-width: 70px;
  border: 1px solid #eee;
}

.forecast-day {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 5px;
}

.forecast-temp {
  font-size: 15px;
  font-weight: 600;
  margin-top: 5px;
}

.weather-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}

.api-limit-note {
  margin-top: 10px;
  padding: 8px;
  font-size: 12px;
  background-color: #fff9e6;
  border: 1px solid #ffe0b2;
  border-radius: 4px;
  color: #856404;
}

.refresh-button {
  background-color: #f0f8ff;
  border: 1px solid #4a90e2;
  color: #4a90e2;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #e6f2ff;
}

.data-source {
  font-size: 12px;
  color: #999;
}

.close-weather {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.close-weather:hover {
  color: #666;
  background-color: #f5f5f5;
}

/* 新增天气预报组件样式 */
.forecast-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.forecast-type-tabs {
  display: flex;
  gap: 5px;
}

.forecast-tab {
  background: none;
  border: none;
  padding: 4px 8px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  border-radius: 3px;
}

.forecast-tab.active {
  background-color: #4a90e2;
  color: white;
}

/* 降水概率条 */
.precip-chance {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-top: 5px;
  gap: 2px;
}

.precip-bar {
  height: 5px;
  background-color: #e0f2ff;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.precip-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #4a90e2;
}

.precip-value {
  font-size: 10px;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .weather-panel {
    width: 280px;
  }
}

/* 确保滚动条样式美观 */
.weather-panel::-webkit-scrollbar {
  width: 6px;
}

.weather-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.weather-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.weather-panel::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 天气位置标记样式 */
.weather-location-marker {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
  border: 1px solid #4a90e2;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.location-marker-label {
  display: block;
  font-weight: bold;
  font-size: 12px;
  color: #333;
  white-space: nowrap;
}

.location-marker-temp {
  display: block;
  font-size: 12px;
  color: #4a90e2;
  white-space: nowrap;
}
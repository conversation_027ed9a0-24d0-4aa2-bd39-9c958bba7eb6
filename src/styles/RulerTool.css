/* 尺子工具面板 - 更新位置以匹配其他控件 */
.ruler-panel {
  position: absolute;
  top: 10px;
  right: 60px;
  width: 280px;
  z-index: 1000; /* Ensure proper stacking with other controls */
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
  border: 2px solid rgba(0, 0, 0, 0.2);
}

/* 面板头部 */
.ruler-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f0f0f0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.ruler-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  line-height: 1;
  transition: all 0.3s ease;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.close-btn:active {
  transform: translateY(1px);
}

/* 面板内容 */
.ruler-content {
  padding: 15px;
}

.ruler-instructions {
  padding: 15px;
  color: #666;
  font-size: 14px;
  text-align: center;
}

/* 测量信息 */
.measurement-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* 单位选择器 */
.unit-selector {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.unit-selector label {
  font-size: 14px;
  color: #666;
}

.unit-dropdown select {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.unit-dropdown select:hover {
  border-color: rgba(0, 0, 0, 0.3);
}

.unit-dropdown select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 距离显示 */
.distance-display,
.area-display,
.perimeter-display {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.distance-display label,
.area-display label,
.perimeter-display label {
  font-size: 14px;
  color: #666;
}

.distance-value,
.area-value,
.perimeter-value {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  padding: 10px 15px;
  background: linear-gradient(to right, rgba(52, 152, 219, 0.1), rgba(52, 152, 219, 0.2));
  border-radius: 6px;
  text-align: center;
  border-left: 4px solid #3498db;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin: 5px 0;
  letter-spacing: 0.5px;
}

/* 新测量按钮 */
.new-measurement-btn {
  width: 100%;
  padding: 10px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-top: 5px;
}

.new-measurement-btn:hover {
  background: #2980b9;
}

/* 取消绘制按钮 */
.cancel-measurement-btn {
  width: 100%;
  padding: 10px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-top: 10px;
}

.cancel-measurement-btn:hover {
  background: #c0392b;
}

/* 地图上的测量点 */
.ruler-point {
  width: 10px;
  height: 10px;
  background-color: #FF5722;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
}

/* 距离标签 */
.distance-bubble {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 地图上的面积标签 */
.area-bubble {
  background-color: rgba(52, 152, 219, 0.85);
  color: white;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  text-align: center;
  white-space: nowrap;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 80px;
  pointer-events: none; /* 确保不会阻止地图交互 */
  letter-spacing: 0.3px;
  backdrop-filter: blur(2px);
  transition: all 0.2s ease;
}

/* 已保存的测量线列表和面积列表 */
.saved-lines,
.saved-areas {

  margin-top: 10px;
  margin-bottom: 10px;
}

.saved-lines label,
.saved-areas label {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  display: block;
}

.lines-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 150px; /* 增加高度，确保有足够空间显示多条线 */
  overflow-y: auto; /* 启用垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  scrollbar-width: thin; /* 细滚动条 */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* 滚动条颜色 */
}

.line-item,
.area-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.line-item:last-child,
.area-item:last-child {
  border-bottom: none;
}

.line-distance,
.area-size {
  font-size: 14px;
  color: #333;
}

.delete-line-btn,
.delete-area-btn {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 16px;
  cursor: pointer;
  padding: 0 5px;
  transition: color 0.2s ease;
}

.delete-line-btn:hover,
.delete-area-btn:hover {
  color: #c0392b;
}

/* 操作按钮 */
.ruler-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.ruler-action-btn {
  flex: 1;
  min-width: 80px;
  padding: 8px;
  background: #f5f7fa;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ruler-action-btn:hover {
  background: #e6e9ed;
  border-color: rgba(0, 0, 0, 0.25);
}

/* 尺子按钮 */
.ruler-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  background: white;
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  width: 40px;
  height: 40px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.65);
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.ruler-button:hover {
  background-color: #f5f5f5;
}

.ruler-button.active {
  background-color: #3498db;
  color: white;
  border-color: #2980b9;
}

/* 测量模式切换按钮 */
.mode-toggle {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.mode-toggle-btn {
  flex: 1;
  padding: 8px 0;
  text-align: center;
  background: #f5f7fa;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mode-toggle-btn.active {
  background: #3498db;
  color: white;
}

.mode-toggle-btn:first-child {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

/* 新测量按钮 */
.new-measurement-btn {
  width: 100%;
  padding: 10px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
  margin-top: 15px;
}
.layer-explanation {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  padding: 15px;
  max-width: 300px;
  z-index: 1500;
  font-family: '<PERSON><PERSON>', 'Segoe UI', <PERSON><PERSON>, sans-serif;
  border-left: 4px solid #3498db;
  will-change: transform, opacity;
  transform-origin: bottom left;
  pointer-events: auto;
  /* 确保点击事件不会传播到地图上 */
  isolation: isolate;
}

.layer-explanation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.layer-explanation-title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.layer-explanation-close {
  background: #f5f5f5;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: #666;
  font-size: 18px;
  padding: 6px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 10px;
  position: relative;
  z-index: 10;
}

.layer-explanation-close:hover {
  background: #e0e0e0;
  color: #333;
  transform: scale(1.05);
}

.layer-explanation-close:active {
  background: #d0d0d0;
  transform: scale(0.95);
}

.layer-explanation-content {
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.layer-explanation-legend {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.legend-color {
  width: 20px;
  height: 4px;
  margin-right: 10px;
  border-radius: 2px;
}

.legend-line {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-line-inner {
  width: 100%;
  height: 2px;
}

.legend-line.dashed .legend-line-inner {
  border-top: 2px dashed;
}

.legend-line.dotted .legend-line-inner {
  border-top: 2px dotted;
}

.legend-line.solid .legend-line-inner {
  border-top: 2px solid;
}

.legend-point {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 10px;
}

.legend-text {
  flex: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode styles */
.dark-mode .layer-explanation {
  background: #2c3e50;
  border-left: 4px solid #3498db;
}

.dark-mode .layer-explanation-header {
  border-bottom: 1px solid #34495e;
}

.dark-mode .layer-explanation-title {
  color: #ecf0f1;
}

.dark-mode .layer-explanation-close {
  background: #34495e;
  color: #bdc3c7;
}

.dark-mode .layer-explanation-close:hover {
  background: #2c3e50;
  color: #ecf0f1;
}

.dark-mode .layer-explanation-close:active {
  background: #243342;
}

.dark-mode .layer-explanation-content {
  color: #bdc3c7;
}

.dark-mode .layer-explanation-legend {
  border-top: 1px solid #34495e;
}

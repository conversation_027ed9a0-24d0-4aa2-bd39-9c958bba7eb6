.labor-data-charts {
  padding: 20px;
  margin-top: 30px;
}

.labor-data-charts h2 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.region-info {
  text-align: center;
  margin-bottom: 20px;
  font-style: italic;
  color: #666;
  font-size: 0.9rem;
}

/* 修改选择器样式，移除深色背景框 */
.region-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  padding: 10px;
}

.region-select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #3498db;
  background-color: white;
  font-size: 1rem;
  min-width: 250px;
  color: #333;
  font-weight: 500;
}

.region-select option {
  padding: 8px;
}

.region-selector label {
  margin-right: 10px;
  font-weight: bold;
}

.region-select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 1rem;
  min-width: 200px;
}

.labor-stats-summary {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30px;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: calc(25% - 15px);
  margin-bottom: 15px;
  text-align: center;
}

.stat-card h3 {
  font-size: 1rem;
  color: #555;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3498db;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  padding: 10px;
}

.chart-container h3 {
  font-size: 1.2rem;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.labor-data-insights {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.labor-data-insights h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.insights-content p {
  margin-bottom: 15px;
  line-height: 1.6;
}

.gender-comparison {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.gender-comparison h3 {
  font-size: 1.3rem;
  color: #333;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.gender-stats {
  display: flex;
  justify-content: space-between;
}

.gender-stat-card {
  width: 48%;
  padding: 15px;
  border-radius: 8px;
}

.gender-stat-card.male {
  background-color: rgba(52, 152, 219, 0.1);
  border-left: 4px solid #3498db;
}

.gender-stat-card.female {
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 4px solid #e74c3c;
}

.gender-stat-card h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.gender-stat-card p {
  margin-bottom: 8px;
}

.data-source-info {
  text-align: right;
  font-size: 0.9rem;
  color: #777;
  font-style: italic;
}

@media (max-width: 768px) {
  .stat-card {
    width: calc(50% - 10px);
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .gender-stats {
    flex-direction: column;
  }
  
  .gender-stat-card {
    width: 100%;
    margin-bottom: 15px;
  }
}

/* 增强图表容器样式 */
.tech-labor-chart-container {
  padding: 10px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.tech-labor-chart-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* 图表加载状态 */
.chart-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: var(--text-secondary);
}

/* 错误消息样式 */
.tech-error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background: rgba(255, 0, 0, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 0, 0, 0.2);
  color: #ff5252;
}

.tech-error-icon {
  font-size: 32px;
  margin-bottom: 15px;
}

.tech-error-text {
  text-align: center;
  font-size: 14px;
  line-height: 1.5;
}

/* 图表网格布局 */
.tech-labor-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.tech-labor-data-card {
  background: rgba(30, 40, 60, 0.7);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.tech-labor-data-card h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: var(--text-primary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tech-labor-data-grid {
    grid-template-columns: 1fr;
  }
}

/* 侧边导航样式优化 */
.tech-sidebar {
  background: rgba(20, 30, 50, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.tech-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tech-nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.tech-nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.tech-nav-item.active {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: var(--accent-color, #4CAF50);
}

.tech-nav-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.tech-nav-item.active .tech-nav-icon {
  color: var(--accent-color, #4CAF50);
}

.tech-nav-item span {
  font-size: 14px;
  font-weight: 500;
}

/* 内容包装器 */
.tech-content-wrapper {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 25px;
  margin-top: 20px;
}

@media (max-width: 992px) {
  .tech-content-wrapper {
    grid-template-columns: 1fr;
  }
  
  .tech-sidebar {
    position: sticky;
    top: 20px;
    z-index: 10;
  }
}
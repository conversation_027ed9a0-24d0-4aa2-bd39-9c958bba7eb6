/* Modern Profile Page Styles */
.modern-profile-page {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  color: #ffffff;
  background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
}

/* Background */
.profile-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.silk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(156, 39, 176, 0.1) 0%, transparent 50%);
  backdrop-filter: blur(1px);
  z-index: 2;
}

/* Top Navigation */
.top-navigation {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(10, 10, 15, 0.85);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  height: 80px;
}

.nav-back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
}

.nav-back-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateX(-2px);
}

.nav-title h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-subtitle {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  display: block;
  margin-top: 0.25rem;
}

.nav-actions {
  display: flex;
  gap: 1rem;
}

.nav-reset-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(231, 76, 60, 0.2);
  border: 1px solid rgba(231, 76, 60, 0.3);
  border-radius: 12px;
  color: #ff6b6b;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
}

.nav-reset-btn:hover {
  background: rgba(231, 76, 60, 0.3);
  border-color: rgba(231, 76, 60, 0.5);
}

/* Global Notifications */
.global-notification {
  position: fixed;
  top: 100px;
  right: 2rem;
  z-index: 100;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  backdrop-filter: blur(20px);
  border: 1px solid;
  animation: slideIn 0.3s ease-out;
  max-width: 400px;
}

.global-notification.success {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.3);
  color: #81c784;
}

.global-notification.error {
  background: rgba(244, 67, 54, 0.15);
  border-color: rgba(244, 67, 54, 0.3);
  color: #e57373;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Main Grid Layout */
.profile-grid {
  position: relative;
  z-index: 5;
  height: calc(100vh - 80px);
  display: grid;
  grid-template-columns: 400px 1fr 350px;
  gap: 2rem;
  padding: 2rem;
  overflow: hidden;
}

/* Left Panel - User Overview */
.profile-overview {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: fit-content;
}

.user-avatar-section {
  text-align: center;
}

.avatar-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.avatar-ring {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  padding: 3px;
  position: relative;
  animation: avatarGlow 3s ease-in-out infinite alternate;
}

.avatar-content {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(10, 10, 15, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-content svg {
  width: 48px;
  height: 48px;
  color: #4A90E2;
}

.avatar-status {
  position: absolute;
  bottom: 8px;
  right: 8px;
}

.status-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(10, 10, 15, 0.9);
}

.status-indicator.active {
  background: #4caf50;
  box-shadow: 0 0 12px rgba(76, 175, 80, 0.5);
}

@keyframes avatarGlow {
  0% { box-shadow: 0 0 20px rgba(74, 144, 226, 0.3); }
  100% { box-shadow: 0 0 30px rgba(156, 39, 176, 0.4); }
}

.user-basic-info {
  text-align: center;
}

.user-display-name {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
}

.user-email {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 1rem 0;
}

.user-meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.user-id {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.5);
  font-family: 'Fira Code', monospace;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.premium {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1a1a2e;
}

.status-badge.pro {
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  color: white;
}

.status-badge.free {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* Quick Stats */
.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2), rgba(156, 39, 176, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon svg {
  width: 20px;
  height: 20px;
  color: #4A90E2;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.stat-value {
  font-size: 0.9rem;
  color: white;
  font-weight: 600;
}

/* Middle Panel - Editable Section */
.editable-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 2rem;
  height: fit-content;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
  color: white;
}

.edit-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.edit-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.edit-toggle.editing {
  background: rgba(231, 76, 60, 0.2);
  border-color: rgba(231, 76, 60, 0.3);
  color: #ff6b6b;
}

.edit-toggle.editing:hover {
  background: rgba(231, 76, 60, 0.3);
}

/* Form Grid */
.form-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.form-field.readonly {
  opacity: 0.7;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
}

.field-label svg {
  width: 18px;
  height: 18px;
  color: #4A90E2;
}

/* Modern Inputs */
.input-container {
  position: relative;
}

.modern-input {
  width: 100%;
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

.modern-input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.input-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.modern-input:focus + .input-underline {
  width: 100%;
}

/* Modern Select */
.select-container {
  position: relative;
}

.modern-select {
  width: 100%;
  padding: 1rem 1.25rem;
  padding-right: 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  cursor: pointer;
  outline: none;
  appearance: none;
  transition: all 0.3s ease;
}

.modern-select:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.select-arrow {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: rgba(255, 255, 255, 0.6);
}

/* Field Display */
.field-display {
  padding: 1rem 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 52px;
}

.field-display.readonly {
  background: rgba(255, 255, 255, 0.02);
  color: rgba(255, 255, 255, 0.7);
}

.language-indicator {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

.readonly-indicator {
  color: rgba(255, 255, 255, 0.4);
}

/* Save Actions */
.save-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.save-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
}

.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
}

.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Right Panel - Account Panel */
.account-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: fit-content;
}

.panel-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 1.75rem;
  margin-bottom: 2rem;
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 1.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Info Cards */
.info-cards {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-top: 1rem;
}

.info-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.info-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.card-icon {
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.2), rgba(156, 39, 176, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-icon.subscription {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.2));
}

.card-icon svg {
  width: 22px;
  height: 22px;
  color: #4A90E2;
}

.card-icon.subscription svg {
  color: #ffc107;
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.card-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.card-value {
  font-size: 0.95rem;
  color: white;
  font-weight: 600;
  font-family: 'Fira Code', monospace;
}

.card-badge {
  font-size: 0.8rem;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.card-badge.premium {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #1a1a2e;
}

.card-badge.pro {
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  color: white;
}

.card-badge.free {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* Activity Timeline */
.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 1rem;
  padding-top: 0.5rem;
}

.timeline-item {
  display: flex;
  gap: 1rem;
  padding: 0.5rem 0;
}

.timeline-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2, #9C27B0);
  flex-shrink: 0;
  box-shadow: 0 0 12px rgba(74, 144, 226, 0.3);
}

.timeline-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.timeline-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.timeline-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.1rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .profile-grid {
    grid-template-columns: 350px 1fr 300px;
    gap: 1.5rem;
    padding: 1.5rem;
  }
}

@media (max-width: 1200px) {
  .profile-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 1.5rem;
  }
  
  .profile-overview {
    grid-column: 1 / -1;
  }
  
  .editable-section {
    grid-column: 1 / 2;
  }
  
  .account-panel {
    grid-column: 2 / 3;
  }
}

@media (max-width: 768px) {
  .modern-profile-page {
    overflow-y: auto;
  }
  
  .profile-grid {
    grid-template-columns: 1fr;
    height: auto;
    min-height: calc(100vh - 80px);
  }
  
  .top-navigation {
    padding: 1rem;
    height: 70px;
  }
  
  .nav-title h1 {
    font-size: 1.25rem;
  }
  
  .profile-overview,
  .editable-section,
  .account-panel {
    grid-column: 1;
  }
} 
/***********************************************************
 * 详情页侧边栏样式 - 专用于ParkDetailPage组件
 ***********************************************************/
:root {
  --detail-sidebar-bg: linear-gradient(to bottom, #f5f7fa, #c3cfe2);
  --detail-sidebar-text: #333333;
  --detail-sidebar-text-muted: rgba(0, 0, 0, 0.6);
  --detail-sidebar-hover: rgba(0, 0, 0, 0.05);
  --detail-sidebar-active: rgba(52, 152, 219, 0.15);
  --detail-sidebar-border: rgba(0, 0, 0, 0.08);
  --detail-sidebar-active-border: #3498db;
  --detail-sidebar-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  --detail-sidebar-item-radius: 8px;
  --detail-sidebar-transition: all 0.25s ease;
  --detail-sidebar-width: 280px;
}

/* 详情页侧边栏容器 */
.detail-sidebar {
  width: var(--detail-sidebar-width);
  background: var(--detail-sidebar-bg);
  box-shadow: var(--detail-sidebar-shadow);
  overflow-y: auto;
  height: 100vh;
  position: sticky;
  top: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  color: var(--detail-sidebar-text);
  border-right: 1px solid var(--detail-sidebar-border);
}

/* 自定义滚动条 */
.detail-sidebar-nav::-webkit-scrollbar {
  width: 5px;
}

.detail-sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.detail-sidebar-nav::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}

/* 侧边栏头部 */
.detail-sidebar-header {
  padding: 16px 20px;
  text-align: center;
  margin-bottom: 0;
  border-bottom: 1px solid var(--detail-sidebar-border);
  position: relative;
  flex-shrink: 0;
}

.detail-sidebar-header h2 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: 0.3px;
}

.detail-sidebar-header .location-tag {
  font-size: 0.85rem;
  background: rgba(0, 0, 0, 0.03);
  padding: 5px 12px;
  border-radius: 20px;
  display: inline-flex;
  align-items: center;
  margin-top: 6px;
}

/* 返回按钮 */
.detail-back-button {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
  border-radius: 20px;
  padding: 8px 14px;
  font-size: 0.85rem;
  cursor: pointer;
  border: none;
  transition: var(--detail-sidebar-transition);
  display: inline-flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}

.detail-back-button:hover {
  background: rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.detail-back-button .back-icon {
  font-size: 1rem;
  margin-right: 3px;
}

/* 导航菜单 */
.detail-sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-grow: 1;
  margin-top: 10px;
  padding: 0 16px 10px;
  overflow-y: auto;
}

.detail-sidebar-nav-item {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 10px 12px;
  border-radius: var(--detail-sidebar-item-radius);
  transition: var(--detail-sidebar-transition);
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.2px;
  border-left: 2px solid transparent;
  position: relative;
}

.detail-sidebar-nav-item:hover {
  background-color: var(--detail-sidebar-hover);
  border-left-color: rgba(0, 0, 0, 0.1);
}

.detail-sidebar-nav-item.active {
  background-color: var(--detail-sidebar-active);
  border-left-color: var(--detail-sidebar-active-border);
}

.detail-sidebar-nav-icon {
  font-size: 1.1rem;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.detail-sidebar-nav-label {
  flex: 1;
}

/* 语言切换按钮 */
.detail-language-toggle {
  background-color: rgba(0, 0, 0, 0.03);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 20px;
  padding: 8px 16px;
  margin: 16px;
  cursor: pointer;
  transition: var(--detail-sidebar-transition);
  font-size: 0.85rem;
  font-weight: 500;
  text-align: center;
  flex-shrink: 0;
}

.detail-language-toggle:hover {
  background-color: rgba(0, 0, 0, 0.06);
  border-color: rgba(0, 0, 0, 0.12);
}

/* 侧边栏内容区域 - 移除单独的内容区域，简化为两层结构 */
.content-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}
/* MoneyGoodsRecipientAnalysis.css */
.money-goods-recipient-container {
  padding: 20px;
  background-color: var(--background-color, #f5f5f5);
  color: var(--text-color, #333);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.money-goods-recipient-container h2 {
  margin-bottom: 15px;
  font-size: 24px;
  color: var(--heading-color, #2c3e50);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.analysis-description {
  margin-bottom: 25px;
  color: var(--secondary-text-color, #666);
  font-size: 15px;
  line-height: 1.5;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  text-align: center;
}

.recipient-analysis-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  flex: 1;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  font-size: 14px;
  color: var(--label-color, #555);
  display: flex;
  align-items: center;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  background-color: var(--input-background, #fff);
  color: var(--input-text-color, #333);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-group select:focus {
  border-color: var(--primary-color, #3498db);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

.data-dashboard {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.stat-tiles {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.stat-tile {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-tile:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
}

.stat-content h3 {
  font-size: 14px;
  margin-bottom: 10px;
  color: var(--secondary-text-color, #666);
  font-weight: 600;
}

.stat-value {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary-color, #3498db);
  margin-bottom: 5px;
}

.stat-percentage {
  font-size: 14px;
  color: var(--accent-color, #27ae60);
  font-weight: 600;
}

.stat-note {
  font-size: 12px;
  color: var(--secondary-text-color, #666);
  margin-top: 5px;
}

.total-migrants {
  border-left: 4px solid #3498db;
}

.parents-recipient {
  border-left: 4px solid #e74c3c;
}

.spouse-recipient {
  border-left: 4px solid #2ecc71;
}

.children-recipient {
  border-left: 4px solid #f39c12;
}

.chart-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.chart-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.chart-box {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
}

.chart-box.full-width {
  flex-basis: 100%;
  width: 100%;
}

.chart-heading {
  font-size: 16px;
  margin-bottom: 15px;
  color: var(--heading-color, #2c3e50);
  text-align: center;
  font-weight: 600;
}

.chart-container {
  flex: 1;
  position: relative;
  height: 300px;
  width: 100%;
}

.insights-panel {
  background-color: var(--card-background, #fff);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.insights-heading {
  font-size: 18px;
  margin-bottom: 20px;
  color: var(--heading-color, #2c3e50);
  display: flex;
  align-items: center;
  gap: 10px;
}

.insights-heading i {
  color: #f39c12;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.insight-card {
  background-color: var(--card-background-alt, #f9f9f9);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  transition: transform 0.2s;
}

.insight-card:hover {
  transform: translateY(-2px);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.insight-header i {
  color: var(--primary-color, #3498db);
  font-size: 16px;
}

.insight-header h4 {
  font-size: 15px;
  color: var(--heading-color, #2c3e50);
  margin: 0;
}

.insight-card p {
  font-size: 14px;
  color: var(--secondary-text-color, #666);
  line-height: 1.5;
  margin: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: var(--secondary-text-color, #666);
}

.error-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 18px;
  color: #e74c3c;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .stat-tiles {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-row {
    flex-direction: column;
  }

  .chart-box {
    min-width: 100%;
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stat-tiles {
    grid-template-columns: 1fr;
  }

  .filter-group {
    min-width: 100%;
  }
}

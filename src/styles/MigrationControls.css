/* 迁移控制面板样式 - 重新设计 */
.migration-controls {
  position: absolute;
  top: 20px;
  right: 80px;
  background-color: rgba(255, 255, 255, 0.97);
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  z-index: 1500;
  min-width: 340px;
  max-width: 380px;
  display: flex;
  flex-direction: column;
  opacity: 1;
  visibility: visible;
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  pointer-events: auto;
  /* 添加硬件加速以提高性能 */
  transform: translateZ(0);
  will-change: transform;
  /* 强化事件隔离 */
  isolation: isolate;
  touch-action: none;
  user-select: none;
  /* 增强事件隔离 */
  pointer-events: all !important;
  /* 确保完全隔离 */
  position: relative;
  z-index: 9999;
}

/* 保留关键帧定义，但不使用它 */
@keyframes controlsFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 控制面板头部 */
.migration-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.migration-controls-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.migration-controls-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.migration-close-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s;
  background-color: rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  touch-action: none;
  user-select: none;
  z-index: 2100;
}

.migration-close-btn:hover {
  color: #f5222d;
  transform: rotate(90deg);
  background-color: rgba(0, 0, 0, 0.1);
}

/* 标签切换 */
.migration-controls-tabs {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  background: none;
  border: none;
  cursor: pointer;
  padding: 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
  position: relative;
  pointer-events: auto;
  touch-action: none;
  user-select: none;
}

.tab-button.active {
  color: #1890ff;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1890ff;
}

.tab-button:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

/* 控制面板内容区域 */
.migration-controls-content {
  padding: 15px 20px;
  overflow-y: auto;
  max-height: 400px;
  /* 增强滚动区域的事件隔离 */
  touch-action: pan-y;
  -ms-touch-action: pan-y;
  overscroll-behavior: contain;
  isolation: isolate;
  pointer-events: all !important;
  position: relative;
  z-index: 2001;
  /* 确保滚动条不影响地图 */
  scrollbar-width: thin;
  /* 增强滚动区域的事件隔离 */
  -webkit-overflow-scrolling: touch;
}

/* 过滤器内容样式 */
.migration-filters-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.migration-filter-group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.migration-filter-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', '微软雅黑', 'PingFang SC', '苹方', 'Heiti SC', '黑体-简', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.migration-filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: space-between;
}

.filter-button {
  background-color: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 13px;
  color: #555;
  cursor: pointer;
  /* 移除过渡效果以减少地图抖动 */
  /* transition: all 0.2s; */
  display: flex;
  align-items: center;
  gap: 6px;
  flex-basis: calc(50% - 4px);
  justify-content: center;
  min-width: 140px;
  max-width: 160px;
  /* 添加硬件加速以提高性能 */
  transform: translateZ(0);
  will-change: transform;
  pointer-events: auto !important;
  touch-action: none;
  user-select: none;
}

.filter-button.active {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
  font-weight: 500;
}

/* 移除悬停效果以减少地图抖动 */
/*.filter-button:hover {
  background-color: #fafafa;
  border-color: #d9d9d9;
  transform: translateY(-1px);
}*/

/* 移除活跃按钮的悬停效果以减少地图抖动 */
/*.filter-button.active:hover {
  background-color: #bae7ff;
  border-color: #69c0ff;
}*/

.filter-icon {
  font-size: 16px;
}

/* Migration Reason Dropdown Styling */
.migration-reason-dropdown {
  position: relative;
  width: 100%;
}

.migration-reason-selected {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #555;
  cursor: pointer;
  text-align: left;
  gap: 6px;
  pointer-events: auto;
  touch-action: none;
  user-select: none;
  z-index: 2000;
}

.migration-reason-options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px; /* 减小高度防止屏幕溢出 */
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 2000; /* 增加z-index确保在地图上层 */
  margin-top: 4px;
  padding-right: 5px; /* 为滚动条添加内边距 */
  touch-action: none; /* 防止触摸事件影响地图 */
  overscroll-behavior: contain; /* 防止滚动溢出 */
  pointer-events: auto !important; /* 确保点击事件被捕获 */
  isolation: isolate; /* 隔离层叠上下文 */
  /* 阻止所有事件传播到地图 */
  user-select: none;
}

.migration-reason-option {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background-color: transparent;
  font-size: 14px;
  color: #555;
  cursor: pointer;
  text-align: left;
  pointer-events: auto !important;
  position: relative;
  z-index: 10000;
  gap: 6px;
  touch-action: none;
  user-select: none;
}

.migration-reason-option:hover {
  background-color: #f5f5f5;
}

.migration-reason-option.active {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}

.filter-tips {
  display: flex;
  gap: 10px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 6px;
  padding: 10px 12px;
  margin-top: 5px;
  align-items: flex-start;
}

.tip-icon {
  font-size: 16px;
  margin-top: 2px;
}

.tip-text {
  font-size: 13px;
  color: #5c5c5c;
  line-height: 1.5;
}

/* 帮助内容样式 */
.migration-help-tab {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.help-section {
  margin-bottom: 15px;
}

.help-section h5 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
  padding-bottom: 5px;
  border-bottom: 1px solid #f0f0f0;
}

.help-section p {
  font-size: 13px;
  color: #666;
  line-height: 1.6;
  margin: 0 0 10px 0;
}

.help-list {
  margin: 0;
  padding-left: 20px;
}

.help-list li {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.5;
}

.definition-item {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.definition-item strong {
  color: #333;
  margin-right: 5px;
}

.definition-item span {
  color: #666;
}

/* 最小化状态 */
.migration-controls.minimized {
  min-width: auto;
  max-width: auto;
  width: auto;
  padding: 0;
  overflow: visible;
  background: none;
  box-shadow: none;
  border: none;
}

.migration-controls-header.minimized {
  background: none;
  border: none;
  padding: 0;
}

.migration-expand-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  padding: 10px 15px;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  pointer-events: auto;
  touch-action: none;
  user-select: none;
  z-index: 2100;
}

.migration-expand-btn:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.expand-icon {
  font-size: 16px;
}

.expand-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .migration-controls {
    top: auto;
    bottom: 70px;
    right: 60px;
    min-width: 300px;
    max-width: 340px;
  }

  .migration-controls-content {
    max-height: 300px;
  }

  .filter-button {
    min-width: 130px;
    padding: 6px 10px;
    flex-basis: calc(50% - 4px);
  }
}

@media (max-width: 480px) {
  .migration-controls {
    right: 10px;
    bottom: 60px;
    min-width: 280px;
    max-width: 320px;
  }

  .migration-controls-header {
    padding: 12px 15px;
  }

  .migration-controls-content {
    padding: 12px 15px;
    max-height: 250px;
  }

  .migration-controls.minimized {
    bottom: 60px;
    right: 10px;
  }

  .migration-expand-btn {
    padding: 8px 12px;
  }
}

/* Migration Type/Reason Tabs */
.migration-data-tabs {
  display: flex;
  width: 100%;
  margin: 10px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.migration-data-tab {
  flex: 1;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
  position: relative;
  text-align: center;
  pointer-events: auto;
  touch-action: none;
  user-select: none;
}

.migration-data-tab.active {
  color: #1890ff;
}

.migration-data-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1890ff;
}

/* Custom scrollbar styling for all scrollable areas in migration controls */
.migration-controls-content::-webkit-scrollbar,
.migration-reason-options::-webkit-scrollbar {
  width: 6px;
}

.migration-controls-content::-webkit-scrollbar-track,
.migration-reason-options::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.migration-controls-content::-webkit-scrollbar-thumb,
.migration-reason-options::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.migration-controls-content::-webkit-scrollbar-thumb:hover,
.migration-reason-options::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

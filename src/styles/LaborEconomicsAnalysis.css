.labor-economics-container {
  padding: 0;
  margin-bottom: 0;
}

.labor-economics-header {
  margin-bottom: 20px;
  text-align: center;
}

.labor-economics-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 5px;
}

.labor-economics-subtitle {
  color: #666;
  font-size: 1.1rem;
}

.labor-economics-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.tab-button {
  background: none;
  border: none;
  padding: 10px 20px;
  font-size: 1rem;
  cursor: pointer;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #333;
}

.tab-button.active {
  color: #ff6b6b;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #ff6b6b;
}

.labor-economics-content {
  min-height: 400px;
}

/* 加载和错误状态 */
.labor-economics-loading,
.labor-economics-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #666;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #ff6b6b;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 关键指标卡片 */
.key-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.key-metric-card {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: flex-start;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.key-metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  font-size: 2rem;
  margin-right: 15px;
  color: #ff6b6b;
}

.metric-details {
  flex: 1;
}

.metric-details h3 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  color: #333;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 5px;
}

.metric-description {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
}

/* 雷达图容器 */
.radar-chart-container {
  padding: 20px;
}

.radar-chart-container h3 {
  text-align: center;
  margin-bottom: 15px;
  color: #333;
}

.chart-wrapper {
  height: 350px;
  position: relative;
}

/* 就业类型容器 */
.employment-types-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.employment-chart-container,
.employment-stats {
  padding: 20px;
}

.employment-chart-container h3,
.employment-stats h3 {
  text-align: center;
  margin-bottom: 15px;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  background-color: #fff;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 1.3rem;
  font-weight: bold;
  color: #ff6b6b;
}

/* 劳动力对比容器 */
.workforce-comparison-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
}

.workforce-comparison-container h3 {
  text-align: center;
  margin-bottom: 10px;
  color: #333;
}

.comparison-note {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 15px;
  font-style: italic;
}

.workforce-highlights {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.highlight-item {
  background-color: #fff;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.highlight-item h4 {
  color: #ff6b6b;
  margin-top: 0;
  margin-bottom: 10px;
}

.highlight-item p {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .employment-types-container,
  .workforce-highlights {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
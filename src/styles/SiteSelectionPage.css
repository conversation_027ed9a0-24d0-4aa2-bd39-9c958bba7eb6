/**
 * 🌊 Liquid Glass Site Selection - 液态玻璃设计系统
 * 保持原有液体玻璃质感的优化版本
 */

/* ============== CSS变量系统 ============== */
:root {
  /* 统一液态玻璃主题颜色 */
  --liquid-primary: #6495ed;
  --liquid-primary-light: rgba(100, 149, 237, 0.15);
  --liquid-primary-border: rgba(100, 149, 237, 0.3);
  --liquid-success: #10b981;
  --liquid-warning: #f59e0b;
  --liquid-error: #ef4444;

  /* 深度背景系统 */
  --depth-bg-1: #0a0a0f;
  --depth-bg-2: #0f0f1a;
  --depth-bg-3: #1a1a2e;
  --depth-bg-4: #16213e;

  /* 液态玻璃透明度 */
  --glass-transparency: rgba(255, 255, 255, 0.02);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-highlight: rgba(255, 255, 255, 0.12);
  --glass-shadow: rgba(0, 0, 0, 0.4);

  /* 简化的单色渐变 */
  --liquid-gradient-primary: linear-gradient(135deg, var(--liquid-primary) 0%, rgba(100, 149, 237, 0.8) 100%);
  --liquid-gradient-light: linear-gradient(135deg, var(--liquid-primary-light) 0%, rgba(255, 255, 255, 0.1) 100%);

  /* 文字颜色 */
  --text-primary: rgba(255, 255, 255, 0.95);
  --text-secondary: rgba(255, 255, 255, 0.75);
  --text-muted: rgba(255, 255, 255, 0.45);
  --text-accent: var(--liquid-primary);

  /* 统一阴影系统 */
  --liquid-shadow-1: 0 8px 32px rgba(100, 149, 237, 0.15);
  --liquid-shadow-2: 0 16px 64px rgba(100, 149, 237, 0.1);
  --liquid-shadow-3: 0 24px 96px rgba(0, 0, 0, 0.15);

  /* 动画时间 */
  --liquid-duration-fast: 0.2s;
  --liquid-duration-normal: 0.4s;
  --liquid-duration-slow: 0.8s;
}

/* ============== 液态玻璃基础样式 ============== */
.liquid-glass-site-selection {
  min-height: 100vh;
  background: radial-gradient(ellipse at top, var(--depth-bg-4) 0%, var(--depth-bg-2) 50%, var(--depth-bg-1) 100%);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* 液态玻璃效果基类 */
.glass-effect {
  background: var(--glass-transparency);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--liquid-shadow-1);
}

.glass-effect-light {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(15px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.glass-effect-accent {
  background: var(--liquid-primary-light);
  backdrop-filter: blur(20px) saturate(200%);
  border: 1px solid var(--liquid-primary-border);
  border-radius: 16px;
  box-shadow: var(--liquid-shadow-1);
}

.glass-effect-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.glass-effect-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

/* GPU加速 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 内容层级文字 */
.content-layer-text {
  color: var(--text-secondary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.content-layer-heading {
  color: var(--text-primary);
  font-weight: 600;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.metric-value-emphasis {
  color: var(--liquid-primary);
  font-weight: 700;
  text-shadow: 0 0 10px rgba(100, 149, 237, 0.3);
}

.metric-value-emphasis.negative {
  color: var(--liquid-error);
  text-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

.metric-value-emphasis.positive {
  color: var(--liquid-success);
  text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.metric-value-emphasis.highlight {
  color: #ffd700;
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
}

/* ============== 液态玻璃按钮系统 ============== */
.liquid-glass-button {
  background: var(--glass-transparency);
  backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.liquid-glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--glass-highlight), transparent);
  transition: left var(--liquid-duration-slow);
}

.liquid-glass-button:hover {
  background: var(--glass-highlight);
  border-color: var(--liquid-primary-border);
  color: var(--text-primary);
  box-shadow: var(--liquid-shadow-1);
  transform: translateY(-2px);
}

.liquid-glass-button:hover::before {
  left: 100%;
}

.liquid-glass-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(100, 149, 237, 0.2);
}

.liquid-glass-button.active {
  background: var(--liquid-gradient-primary);
  border-color: var(--liquid-primary);
  color: white;
  box-shadow: var(--liquid-shadow-2);
}

/* 主要按钮样式 */
.liquid-primary-button {
  background: var(--liquid-gradient-primary);
  border: 2px solid var(--liquid-primary);
  border-radius: 16px;
  color: white;
  font-weight: 700;
  padding: 16px 32px;
  cursor: pointer;
  transition: all var(--liquid-duration-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--liquid-shadow-2);
}

.liquid-primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--liquid-duration-slow);
}

.liquid-primary-button:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    var(--liquid-shadow-3),
    0 0 30px rgba(100, 149, 237, 0.4);
  border-color: rgba(100, 149, 237, 0.8);
}

.liquid-primary-button:hover:not(:disabled)::before {
  left: 100%;
}

.liquid-primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.liquid-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.liquid-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: liquidSpin 1s linear infinite;
}

@keyframes liquidSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ============== 悬浮式液态玻璃导航 ============== */
.liquid-floating-nav {
  position: fixed;
  top: 24px;
  left: 24px;
  right: 24px;
  z-index: 1000;
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 24px;
  align-items: center;
  pointer-events: none;
}

.liquid-nav-section {
  pointer-events: auto;
}

.liquid-nav-section.left {
  justify-self: start;
}

.liquid-nav-section.center {
  justify-self: center;
}

.liquid-nav-section.right {
  justify-self: end;
}

/* 悬浮返回按钮 */
.liquid-floating-back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 14px;
  text-decoration: none;
  transition: all var(--liquid-duration-normal);
}

/* 悬浮标题 */
.liquid-floating-title {
  background: var(--glass-transparency);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 16px 32px;
  text-align: center;
  box-shadow: var(--liquid-shadow-1);
  max-width: 600px;
}

.liquid-title-text {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  background: linear-gradient(90deg, var(--liquid-primary), #00ff88);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(100, 149, 237, 0.3);
}

.liquid-subtitle-text {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin: 0;
  font-weight: 400;
}

/* 悬浮视图切换 */
.liquid-floating-toggle {
  display: flex;
  gap: 8px;
  background: var(--glass-transparency);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 6px;
  box-shadow: var(--liquid-shadow-1);
}

.liquid-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: var(--text-muted);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--liquid-duration-normal);
  white-space: nowrap;
}

.liquid-toggle-btn:hover {
  background: var(--glass-highlight);
  color: var(--text-secondary);
}

.liquid-toggle-btn.active {
  background: var(--liquid-gradient-primary);
  color: white;
  box-shadow: 0 4px 12px rgba(100, 149, 237, 0.3);
}

.liquid-toggle-btn svg {
  transition: transform var(--liquid-duration-fast);
}

.liquid-toggle-btn:hover svg {
  transform: scale(1.1);
}

/* ============== 主工作区域 ============== */
.liquid-main-workspace {
  padding: 120px 24px 24px 24px;
  max-width: 1800px;
  margin: 0 auto;
}

.liquid-dashboard-layout {
  display: grid;
  grid-template-columns: 320px 1fr 360px;
  gap: 24px;
  height: calc(100vh - 180px);
  min-height: 600px;
}

/* ============== 液态玻璃搜索面板 ============== */
.liquid-search-panel {
  padding: 24px;
  overflow-y: auto;
  transition: all var(--liquid-duration-normal);
}

.liquid-search-panel.collapsed {
  width: 60px;
  padding: 24px 12px;
}

.liquid-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--glass-border);
}

.liquid-panel-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.liquid-panel-title {
  font-size: 1.25rem;
  margin: 0;
}

.liquid-results-badge {
  padding: 4px 12px;
  background: var(--liquid-primary);
  color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(100, 149, 237, 0.3);
}

.liquid-collapse-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 16px;
}

/* ============== 液态玻璃表单样式 ============== */
.liquid-search-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.liquid-form-section {
  padding: 20px;
  transition: all var(--liquid-duration-normal);
}

.liquid-form-section:hover {
  border-color: var(--liquid-primary-border);
  box-shadow: var(--liquid-shadow-1);
}

.liquid-section-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  cursor: pointer;
}

.liquid-section-title h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.liquid-section-title p {
  font-size: 0.8rem;
  margin: 0;
  line-height: 1.4;
}

.liquid-expand-indicator {
  margin-left: auto;
  transition: transform var(--liquid-duration-normal);
  color: var(--text-muted);
}

.liquid-expand-indicator.expanded {
  transform: rotate(180deg);
}

.liquid-section-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.liquid-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.liquid-input-label {
  font-size: 0.85rem;
  font-weight: 600;
}

.liquid-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.liquid-input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  font-size: 0.9rem;
  transition: all var(--liquid-duration-normal);
  backdrop-filter: blur(10px);
}

.liquid-input:focus {
  outline: none;
  border-color: var(--liquid-primary);
  box-shadow: 0 0 0 3px rgba(100, 149, 237, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.liquid-input::placeholder {
  color: var(--text-muted);
}

.liquid-input-icon,
.liquid-input-unit {
  position: absolute;
  right: 12px;
  color: var(--text-muted);
  pointer-events: none;
  font-size: 0.8rem;
}

/* 液态玻璃范围控制 */
.liquid-range-display {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.liquid-range-values {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.liquid-range-value {
  text-align: center;
  flex: 1;
}

.liquid-range-value span:first-child {
  display: block;
  font-size: 0.75rem;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.liquid-range-separator {
  color: var(--text-muted);
  font-weight: 300;
  font-size: 1.2rem;
}

.liquid-range-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.liquid-input-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

/* 表单操作区域 */
.liquid-form-actions {
  margin-top: 24px;
}

/* ============== 中央地图区域 ============== */
.liquid-central-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* ============== 悬浮液态玻璃地图样式按钮 ============== */
.floating-map-style-controls {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 1000;
  pointer-events: none;
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 50px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ============== 液态玻璃一键返回宏观视图按钮 ============== */
.liquid-reset-view-button {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 80px;
  height: 40px;
  border-radius: 20px;
  border: none;
  background: none;
  cursor: pointer;
  z-index: 1000;
  overflow: visible;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置按钮基础光晕层 */
.liquid-reset-view-button::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -6px;
  right: -6px;
  bottom: -3px;
  background: radial-gradient(
    ellipse 120% 80% at center,
    rgba(100, 149, 237, 0.12) 0%,
    rgba(100, 149, 237, 0.06) 50%,
    transparent 80%
  );
  border-radius: 26px;
  opacity: 0;
  z-index: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置按钮玻璃层 */
.reset-btn-glass-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 30%,
    rgba(255, 255, 255, 0.06) 70%,
    rgba(255, 255, 255, 0.12) 100%
  );
  backdrop-filter: blur(15px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  z-index: 1;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置按钮内容层 */
.reset-btn-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 100%;
  padding: 0 12px;
  color: rgba(255, 255, 255, 0.85);
  font-size: 0.7rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all var(--liquid-duration-normal);
}

.reset-btn-icon {
  transition: all var(--liquid-duration-normal);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.reset-btn-label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  letter-spacing: 0.02em;
}

/* 重置按钮折射效果层 */
.reset-btn-refraction {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  z-index: 3;
  pointer-events: none;
  opacity: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    transparent 20%,
    transparent 80%,
    rgba(255, 255, 255, 0.2) 100%
  );
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置按钮悬停效果 */
.liquid-reset-view-button:hover::before {
  opacity: 1;
  transform: scale(1.1);
}

.liquid-reset-view-button:hover .reset-btn-glass-layer {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 30%,
    rgba(255, 255, 255, 0.12) 70%,
    rgba(255, 255, 255, 0.2) 100%
  );
  border-color: rgba(100, 149, 237, 0.4);
  box-shadow:
    0 8px 32px rgba(100, 149, 237, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.liquid-reset-view-button:hover .reset-btn-content {
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.liquid-reset-view-button:hover .reset-btn-icon {
  transform: scale(1.1) rotate(-15deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

.liquid-reset-view-button:hover .reset-btn-refraction {
  opacity: 1;
  animation: liquidRefractionPulse 2s ease-in-out infinite;
}

/* 重置按钮点击效果 */
.liquid-reset-view-button:active {
  transform: scale(0.95);
}

.liquid-reset-view-button:active .reset-btn-content {
  transform: translateY(0);
}

.liquid-reset-view-button:active .reset-btn-icon {
  transform: scale(1.05) rotate(-30deg);
}

/* 重置按钮折射动画 */
@keyframes liquidRefractionPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 全屏模式下的重置按钮位置调整 */
.liquid-fullscreen-map-container .liquid-reset-view-button {
  bottom: 30px;
  right: 30px;
}

.floating-map-style-controls.fullscreen {
  top: 30px;
}

.floating-style-btn {
  width: 80px;
  height: 40px;
  border-radius: 20px;
  position: relative;
  cursor: pointer;
  border: none;
  background: none;
  pointer-events: auto;
  overflow: visible;
  transition: all var(--liquid-duration-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重构光影系统 - 基础光晕层 */
.floating-style-btn::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -6px;
  right: -6px;
  bottom: -3px;
  background: radial-gradient(
    ellipse 120% 80% at center,
    rgba(100, 149, 237, 0.08) 0%,
    rgba(100, 149, 237, 0.04) 50%,
    transparent 80%
  );
  border-radius: 26px;
  opacity: 0;
  z-index: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重构玻璃层 - 精确光影控制 */
.btn-glass-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.12) 0%,
    rgba(255, 255, 255, 0.06) 30%,
    rgba(255, 255, 255, 0.04) 70%,
    rgba(255, 255, 255, 0.08) 100%
  );
  backdrop-filter: blur(15px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* 内容层 - 椭圆形水平布局 */
.btn-content {
  position: relative;
  z-index: 3;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 6px;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  transition: all var(--liquid-duration-normal);
  padding: 0 12px;
}

.btn-icon {
  width: 16px;
  height: 16px;
  stroke-width: 2.5;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  flex-shrink: 0;
}

.btn-label {
  font-size: 0.7rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  text-transform: none;
  white-space: nowrap;
}

/* 折射层 - 椭圆形玻璃折射效果 */
.btn-refraction {
  position: absolute;
  top: -30%;
  left: -50%;
  width: 200%;
  height: 160%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.12) 50%,
    transparent 70%
  );
  border-radius: 40px;
  opacity: 0;
  transform: translateX(-100%) rotate(-30deg);
  transition: all var(--liquid-duration-slow) ease-out;
  pointer-events: none;
  z-index: 2;
}

/* 取消悬停效果 - 解决光影错位问题 */
/* 注释掉所有悬停效果，保持按钮静态状态 */
/*
.floating-style-btn:hover::before {
  opacity: 1;
  background: radial-gradient(
    ellipse 130% 90% at center,
    rgba(100, 149, 237, 0.15) 0%,
    rgba(100, 149, 237, 0.08) 40%,
    rgba(100, 149, 237, 0.03) 70%,
    transparent 100%
  );
  transform: scale(1.1);
}

.floating-style-btn:hover .btn-glass-layer {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.18) 0%,
    rgba(255, 255, 255, 0.1) 30%,
    rgba(255, 255, 255, 0.08) 70%,
    rgba(255, 255, 255, 0.12) 100%
  );
  border-color: rgba(100, 149, 237, 0.25);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 16px rgba(100, 149, 237, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.floating-style-btn:hover .btn-content {
  color: rgba(255, 255, 255, 0.95);
  transform: scale(1.02);
  text-shadow: 0 1px 8px rgba(100, 149, 237, 0.3);
}

.floating-style-btn:hover .btn-icon {
  filter: drop-shadow(0 1px 4px rgba(100, 149, 237, 0.3));
}

.floating-style-btn:hover .btn-refraction {
  opacity: 1;
  transform: translateX(100%) rotate(-30deg);
}
*/

/* 重构激活状态动画 - 精确光影控制 */
@keyframes activePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1.05);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.08);
  }
}

@keyframes activeGlow {
  0%, 100% {
    box-shadow:
      0 6px 24px rgba(100, 149, 237, 0.2),
      0 2px 12px rgba(100, 149, 237, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(100, 149, 237, 0.15);
  }
  50% {
    box-shadow:
      0 8px 32px rgba(100, 149, 237, 0.25),
      0 3px 16px rgba(100, 149, 237, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.35),
      inset 0 -1px 0 rgba(100, 149, 237, 0.2);
  }
}

/* 重构激活状态 - 精确视觉反馈 */
.floating-style-btn.active::before {
  opacity: 1;
  background: radial-gradient(
    ellipse 140% 100% at center,
    rgba(100, 149, 237, 0.2) 0%,
    rgba(100, 149, 237, 0.1) 40%,
    rgba(100, 149, 237, 0.05) 70%,
    transparent 100%
  );
  animation: activePulse 2.5s ease-in-out infinite;
}

.floating-style-btn.active .btn-glass-layer {
  background: linear-gradient(145deg,
    rgba(100, 149, 237, 0.25) 0%,
    rgba(100, 149, 237, 0.15) 30%,
    rgba(100, 149, 237, 0.12) 70%,
    rgba(100, 149, 237, 0.18) 100%
  );
  border-color: rgba(100, 149, 237, 0.4);
  transform: translateY(-1px);
  animation: activeGlow 2.5s ease-in-out infinite;
}

.floating-style-btn.active .btn-content {
  color: white;
  text-shadow: 0 1px 10px rgba(100, 149, 237, 0.5);
}

.floating-style-btn.active .btn-icon {
  filter: drop-shadow(0 1px 6px rgba(100, 149, 237, 0.4));
}

.floating-style-btn.active .btn-label {
  font-weight: 700;
}

.floating-style-btn.active .btn-refraction {
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 70%
  );
}

/* 重构点击效果 - 精确反馈控制 */
.floating-style-btn:active {
  transform: scale(0.98);
}

.floating-style-btn:active::before {
  transform: scale(1.02);
  opacity: 0.8;
}

.floating-style-btn:active .btn-glass-layer {
  transform: translateY(1px) scale(0.99);
  box-shadow:
    0 2px 12px rgba(0, 0, 0, 0.15),
    inset 0 2px 4px rgba(0, 0, 0, 0.08),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
}

.floating-style-btn:active .btn-content {
  transform: scale(0.98);
}

.floating-style-btn:active .btn-icon {
  transform: scale(0.95);
}

/* 地图移动时的动态折射效果 - 椭圆形 */
@keyframes mapRefractionEllipse {
  0% {
    transform: translateX(-100%) rotate(-30deg);
    opacity: 0;
  }
  30% {
    opacity: 0.3;
  }
  70% {
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%) rotate(-30deg);
    opacity: 0;
  }
}



/* 地图交互时的折射效果 */
.liquid-map-container:active .btn-refraction,
.liquid-map-container.dragging .btn-refraction {
  animation: mapRefractionEllipse 1.5s ease-in-out;
}

/* 取消地图容器悬停时的整体玻璃效果 */
/*
.liquid-map-container:hover .floating-style-btn .btn-glass-layer {
  background: rgba(255, 255, 255, 0.1);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.15),
    0 0 20px rgba(100, 149, 237, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}
*/

/* 地图缩放指示器效果 */
.liquid-map-container.zooming::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
  border: 3px solid rgba(100, 149, 237, 0.6);
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(100, 149, 237, 0.1) 0%,
    transparent 70%
  );
  z-index: 1001;
  animation: zoomIndicator 1.2s ease-out;
  pointer-events: none;
}

@keyframes zoomIndicator {
  0% {
    transform: scale(0.5);
    opacity: 0;
    border-width: 6px;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
    border-width: 3px;
  }
  70% {
    transform: scale(1.5);
    opacity: 0.8;
    border-width: 2px;
  }
  100% {
    transform: scale(2);
    opacity: 0;
    border-width: 1px;
  }
}

/* 缩放时按钮的反应 */
.liquid-map-container.zooming .floating-style-btn {
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.liquid-map-container.zooming .floating-style-btn .btn-glass-layer {
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(100, 149, 237, 0.2),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

/* 重构玻璃内部光效 - 取消悬停效果 */
.floating-style-btn .btn-glass-layer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    transparent 30%,
    transparent 70%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 取消悬停效果 */
/*
.floating-style-btn:hover .btn-glass-layer::before {
  opacity: 1;
}
*/

.floating-style-btn.active .btn-glass-layer::before {
  opacity: 1;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(100, 149, 237, 0.1) 30%,
    rgba(100, 149, 237, 0.05) 70%,
    rgba(255, 255, 255, 0.12) 100%
  );
}

.liquid-map-workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.liquid-map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
}

.liquid-map-title-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.liquid-map-title {
  font-size: 1.25rem;
  margin: 0;
}

.liquid-selected-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
}



.liquid-map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  background: var(--depth-bg-3);
  border-radius: 0 0 16px 16px;
}

.workspace-map {
  width: 100%;
  height: 100%;
  border-radius: 0 0 16px 16px;
}

/* ============== 液态玻璃分析面板 ============== */
.liquid-analysis-panel {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
}

.liquid-site-badge {
  padding: 4px 12px;
  background: var(--liquid-primary);
  color: white;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(100, 149, 237, 0.3);
}

.liquid-detail-toggle {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.liquid-analysis-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 液态玻璃数据卡片 */
.data-card-glass {
  background: var(--glass-transparency);
  backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 20px;
  transition: all var(--liquid-duration-normal);
  box-shadow: var(--liquid-shadow-1);
}

.data-card-glass:hover {
  border-color: var(--liquid-primary-border);
  box-shadow: var(--liquid-shadow-2);
}

.data-card-glass.primary {
  border-color: var(--liquid-primary-border);
  background: var(--liquid-primary-light);
}

/* 关键指标 */
.liquid-key-metrics {
  margin-bottom: 24px;
}

.liquid-metrics-header {
  margin-bottom: 16px;
}

.liquid-metrics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.liquid-metric-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
}

.liquid-metric-content span:first-child {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 成本分解 */
.liquid-cost-breakdown {
  margin-bottom: 24px;
}

.liquid-chart-container {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.liquid-cost-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.liquid-cost-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  transition: all var(--liquid-duration-normal);
  backdrop-filter: blur(10px);
}

.liquid-cost-item:hover {
  border-color: var(--liquid-primary-border);
  box-shadow: 0 4px 12px rgba(100, 149, 237, 0.1);
}

.liquid-cost-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.liquid-cost-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.cost-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.liquid-cost-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 财务分析结果 */
.liquid-financial-results {
  margin-bottom: 24px;
}

.liquid-financial-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 16px 0;
}

.liquid-metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.liquid-metric-item span:first-child {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 投资建议 */
.liquid-investment-recommendation {
  margin-top: 16px;
}

.recommendation-card {
  padding: 16px;
  border-radius: 12px;
  backdrop-filter: blur(15px);
}

.liquid-recommendation-header {
  margin-bottom: 12px;
}

/* 空状态 */
.liquid-empty-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 24px;
}

.liquid-empty-selection .data-card-glass {
  text-align: center;
  max-width: 300px;
}

/* ============== 全屏地图模式 ============== */
.liquid-map-focus-mode {
  height: calc(100vh - 120px);
  position: relative;
  margin-top: 120px;
}

.liquid-fullscreen-map-container {
  width: 100%;
  height: 100%;
  border-radius: 24px;
  overflow: hidden;
  position: relative;
  margin: 0 24px;
}

.fullscreen-map {
  width: 100%;
  height: 100%;
  border-radius: 24px;
}

.liquid-floating-controls {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 10;
  max-width: 320px;
}

.liquid-floating-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--liquid-duration-normal);
}

.liquid-return-button {
  background: var(--liquid-gradient-primary);
  color: white;
  box-shadow: var(--liquid-shadow-2);
}

.liquid-return-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--liquid-shadow-3);
}

.liquid-overview-panel,
.liquid-site-details-panel {
  padding: 20px;
  backdrop-filter: blur(20px) saturate(180%);
  box-shadow: var(--liquid-shadow-1);
}

.liquid-overview-header,
.liquid-details-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--glass-border);
}

.liquid-overview-stats {
  display: flex;
  gap: 12px;
}

.liquid-stat-item {
  text-align: center;
  flex: 1;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--liquid-primary);
  display: block;
  margin-bottom: 4px;
}

.liquid-details-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.liquid-detail-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--glass-border);
}

.liquid-detail-metric:last-child {
  border-bottom: none;
}

/* ============== 液态玻璃错误通知 ============== */
.liquid-error-toast {
  position: fixed;
  top: 120px;
  right: 24px;
  z-index: 1100;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(20px) saturate(180%);
  box-shadow: var(--liquid-shadow-2);
  animation: liquidSlideInFromRight 0.4s ease;
  max-width: 400px;
}

.liquid-toast-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.liquid-toast-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.liquid-toast-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all var(--liquid-duration-normal);
  flex-shrink: 0;
  font-size: 18px;
  line-height: 1;
}

.liquid-toast-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

@keyframes liquidSlideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ============== 响应式设计 ============== */
@media (max-width: 1400px) {
  .liquid-dashboard-layout {
    grid-template-columns: 280px 1fr 320px;
    gap: 20px;
  }

  .liquid-main-workspace {
    padding: 120px 20px 20px 20px;
  }
}

@media (max-width: 1200px) {
  .liquid-dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 20px;
    height: auto;
    min-height: auto;
  }

  .liquid-main-workspace {
    padding: 120px 16px 16px 16px;
  }

  .liquid-search-panel,
  .liquid-analysis-panel {
    max-height: none;
  }

  .liquid-floating-nav {
    grid-template-columns: 1fr;
    gap: 16px;
    text-align: center;
  }

  .liquid-nav-section.left,
  .liquid-nav-section.right {
    justify-self: center;
  }
}

@media (max-width: 768px) {
  .liquid-floating-nav {
    top: 16px;
    left: 16px;
    right: 16px;
    gap: 12px;
  }

  .liquid-floating-title {
    padding: 12px 20px;
  }

  .liquid-title-text {
    font-size: 1.25rem;
  }

  .liquid-floating-toggle {
    flex-direction: column;
    gap: 4px;
  }

  .liquid-toggle-btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .liquid-main-workspace {
    padding: 140px 12px 12px 12px;
  }

  .liquid-dashboard-layout {
    gap: 16px;
  }

  .liquid-search-panel,
  .liquid-analysis-panel {
    padding: 16px;
  }

  .liquid-floating-controls {
    top: 16px;
    right: 16px;
    left: 16px;
    max-width: none;
  }

  .liquid-overview-stats {
    flex-direction: column;
  }

  .liquid-range-inputs {
    grid-template-columns: 1fr;
  }

  .liquid-error-toast {
    top: 140px;
    right: 16px;
    left: 16px;
    max-width: none;
  }

  /* 悬浮地图样式按钮移动端适配 */
  .floating-map-style-controls {
    gap: 6px;
    top: 16px;
    padding: 4px;
  }

  .floating-style-btn {
    width: 70px;
    height: 36px;
  }

  .btn-content {
    gap: 4px;
    padding: 0 8px;
  }

  .btn-icon {
    width: 14px;
    height: 14px;
  }

  .btn-label {
    font-size: 0.65rem;
  }

  /* 移动端重置按钮适配 */
  .liquid-reset-view-button {
    width: 70px;
    height: 36px;
    bottom: 16px;
    right: 16px;
  }

  .reset-btn-content {
    gap: 3px;
    padding: 0 8px;
    font-size: 0.65rem;
  }

  .reset-btn-icon {
    width: 16px;
    height: 16px;
  }

  .liquid-fullscreen-map-container .liquid-reset-view-button {
    bottom: 20px;
    right: 20px;
  }
}

@media (max-width: 480px) {
  .liquid-floating-nav {
    top: 12px;
    left: 12px;
    right: 12px;
  }

  .liquid-main-workspace {
    padding: 160px 8px 8px 8px;
  }

  .liquid-search-panel,
  .liquid-analysis-panel {
    padding: 12px;
  }

  .liquid-floating-controls {
    top: 12px;
    right: 12px;
    left: 12px;
  }
}

/* ============== 滚动条样式 ============== */
.liquid-search-panel::-webkit-scrollbar,
.liquid-analysis-panel::-webkit-scrollbar {
  width: 6px;
}

.liquid-search-panel::-webkit-scrollbar-track,
.liquid-analysis-panel::-webkit-scrollbar-track {
  background: var(--depth-bg-3);
  border-radius: 3px;
}

.liquid-search-panel::-webkit-scrollbar-thumb,
.liquid-analysis-panel::-webkit-scrollbar-thumb {
  background: var(--liquid-primary-border);
  border-radius: 3px;
}

.liquid-search-panel::-webkit-scrollbar-thumb:hover,
.liquid-analysis-panel::-webkit-scrollbar-thumb:hover {
  background: var(--liquid-primary);
}

/* ============== 可访问性增强 ============== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.liquid-glass-button:focus,
.liquid-toggle-btn:focus,
.liquid-primary-button:focus,
.liquid-input:focus {
  outline: 2px solid var(--liquid-primary);
  outline-offset: 2px;
}

/* ============== 打印样式 ============== */
@media print {
  .liquid-floating-nav,
  .liquid-floating-controls,
  .liquid-error-toast {
    display: none;
  }

  .liquid-glass-site-selection {
    background: white;
    color: black;
  }

  .liquid-dashboard-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* ============== 头部导航 ============== */
.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-secondary);
  padding: var(--spacing-lg) 0;
}

.header-content {
  max-width: 1800px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-left {
  justify-self: start;
}

.header-center {
  justify-self: center;
  text-align: center;
}

.header-right {
  justify-self: end;
}

/* 返回按钮 */
.professional-back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  text-decoration: none;
}

.professional-back-button:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  transform: translateX(-2px);
  box-shadow: var(--shadow-md);
}

/* 项目信息 */
.project-info {
  max-width: 600px;
}

.project-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-xs) 0;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(77, 200, 255, 0.3);
}

.project-subtitle {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin: 0;
  font-weight: 400;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  gap: var(--spacing-sm);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  backdrop-filter: blur(10px);
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-muted);
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  white-space: nowrap;
}

.toggle-button:hover {
  background: var(--bg-panel);
  color: var(--text-secondary);
}

.toggle-button.active {
  background: var(--primary-color);
  color: var(--bg-primary);
  box-shadow: var(--shadow-sm);
}

.toggle-button svg {
  transition: transform var(--transition-fast);
}

.toggle-button:hover svg {
  transform: scale(1.1);
}

/* ============== 主工作区域 ============== */
.main-workspace {
  padding: var(--spacing-xl);
  max-width: 1800px;
  margin: 0 auto;
}

.dashboard-layout {
  display: grid;
  grid-template-columns: 320px 1fr 360px;
  gap: var(--spacing-xl);
  height: calc(100vh - 140px);
  min-height: 600px;
}

/* ============== 搜索面板 ============== */
.search-panel {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
  transition: all var(--transition-normal);
}

.search-panel.collapsed {
  width: 60px;
  padding: var(--spacing-lg) var(--spacing-md);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-muted);
}

.panel-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.panel-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.results-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--primary-color);
  color: var(--bg-primary);
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.collapse-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.collapse-button:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-secondary);
}

/* ============== 表单样式 ============== */
.search-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-section {
  background: var(--bg-panel);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.form-section:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.section-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
}

.section-icon {
  font-size: 1.25rem;
  line-height: 1;
  margin-top: 2px;
}

.section-title h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.section-title p {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
}

.expand-indicator {
  margin-left: auto;
  transition: transform var(--transition-normal);
  color: var(--text-muted);
}

.expand-indicator.expanded {
  transform: rotate(180deg);
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.input-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.1);
  background: var(--bg-secondary);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.input-icon {
  position: absolute;
  right: var(--spacing-md);
  color: var(--text-muted);
  pointer-events: none;
}

.input-unit {
  position: absolute;
  right: var(--spacing-md);
  font-size: 0.8rem;
  color: var(--text-muted);
  pointer-events: none;
}

/* 范围控制 */
.range-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.range-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
}

.range-value {
  text-align: center;
  flex: 1;
}

.range-label {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.range-number {
  display: block;
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
}

.range-separator {
  color: var(--text-muted);
  font-weight: 300;
  font-size: 1.2rem;
}

.range-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.input-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

/* 操作按钮 */
.form-actions {
  margin-top: var(--spacing-lg);
}

.primary-button {
  width: 100%;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-lg);
  color: var(--bg-primary);
  font-weight: 700;
  font-size: 1rem;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.primary-button:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
  box-shadow: 
    0 15px 35px rgba(77, 200, 255, 0.3),
    0 8px 20px rgba(77, 200, 255, 0.2);
}

.primary-button:hover:not(:disabled)::before {
  left: 100%;
}

.primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  position: relative;
  z-index: 2;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ============== 中央地图区域 ============== */
.central-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.map-workspace {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(20px);
}

.map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--bg-panel);
  border-bottom: 1px solid var(--border-muted);
}

.map-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.map-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.selected-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: var(--radius-lg);
  color: var(--secondary-color);
}

.selected-label {
  font-size: 0.85rem;
  font-weight: 500;
}

.selected-value {
  font-size: 0.9rem;
  font-weight: 700;
}

.map-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.control-button:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-secondary);
  transform: scale(1.05);
}

.map-container {
  flex: 1;
  position: relative;
  min-height: 400px;
  background: var(--bg-tertiary);
}

.workspace-map {
  width: 100%;
  height: 100%;
  border-radius: 0;
}

/* ============== 分析面板 ============== */
.analysis-panel {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
  max-height: calc(100vh - 140px);
}

.analysis-panel .panel-header {
  margin-bottom: var(--spacing-xl);
}

.site-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  background: var(--accent-color);
  color: var(--bg-primary);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
}

.detail-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.detail-toggle:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-secondary);
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 关键指标 */
.key-metrics {
  background: var(--bg-panel);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.metrics-header {
  margin-bottom: var(--spacing-lg);
}

.metrics-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.metric-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.metric-card:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.metric-card.primary {
  border-color: var(--border-primary);
  background: rgba(77, 200, 255, 0.05);
}

.metric-icon {
  font-size: 1.5rem;
  line-height: 1;
}

.metric-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* 成本分解 */
.cost-breakdown {
  background: var(--bg-panel);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.breakdown-header {
  margin-bottom: var(--spacing-lg);
}

.breakdown-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.breakdown-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.chart-container {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cost-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.cost-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.cost-item:hover {
  border-color: var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.cost-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.cost-icon {
  font-size: 1.25rem;
  line-height: 1;
}

.cost-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.cost-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.cost-label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.cost-metrics {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cost-value {
  font-size: 0.9rem;
  font-weight: 700;
  color: var(--text-primary);
}

.cost-percentage {
  font-size: 0.8rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* ============== 财务分析 ============== */
.financial-analysis {
  background: var(--bg-panel);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
}

.analysis-header {
  margin-bottom: var(--spacing-lg);
}

.analysis-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.financial-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.metric-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
}

.metric-item .metric-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-item .metric-value {
  font-weight: 700;
  color: var(--text-primary);
}

.metric-item .metric-value.highlight {
  color: var(--primary-color);
}

.metric-item .metric-value.positive {
  color: var(--secondary-color);
}

.metric-item .metric-value.negative {
  color: #ff6b6b;
}

/* 投资建议 */
.investment-recommendation {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
}

.recommendation-card.warning {
  border-color: rgba(255, 193, 7, 0.3);
  background: rgba(255, 193, 7, 0.05);
}

.recommendation-card.success {
  border-color: rgba(0, 255, 136, 0.3);
  background: rgba(0, 255, 136, 0.05);
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.recommendation-icon {
  font-size: 1.25rem;
  line-height: 1;
}

.recommendation-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.recommendation-content {
  font-size: 0.85rem;
  line-height: 1.5;
  color: var(--text-secondary);
}

/* ============== 空状态 ============== */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: var(--spacing-xl);
}

.empty-content {
  text-align: center;
  max-width: 300px;
}

.empty-icon {
  margin-bottom: var(--spacing-lg);
  color: var(--text-muted);
  opacity: 0.5;
}

.empty-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
}

.empty-description {
  font-size: 0.9rem;
  color: var(--text-muted);
  line-height: 1.5;
  margin: 0;
}

/* ============== 全屏地图模式 ============== */
.map-focus-mode {
  height: calc(100vh - 140px);
  position: relative;
}

.fullscreen-map-container {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-xl);
  overflow: hidden;
  position: relative;
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-xl);
}

.fullscreen-map {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-xl);
}

.floating-controls {
  position: absolute;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  z-index: 10;
  max-width: 320px;
}

.floating-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
}

.floating-button:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.return-button {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--bg-primary);
}

.return-button:hover {
  background: var(--primary-light);
  border-color: var(--primary-light);
  color: var(--bg-primary);
}

.overview-panel,
.site-details-panel {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
}

.overview-header,
.details-header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-muted);
}

.overview-header h3,
.details-header h4 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.overview-stats {
  display: flex;
  gap: var(--spacing-md);
}

.stat-item {
  text-align: center;
  flex: 1;
  padding: var(--spacing-md);
  background: var(--bg-panel);
  border: 1px solid var(--border-muted);
  border-radius: var(--radius-md);
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.details-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.detail-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-muted);
}

.detail-metric:last-child {
  border-bottom: none;
}

.detail-metric .metric-label {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.detail-metric .metric-value {
  font-weight: 600;
  color: var(--text-primary);
}

/* ============== 错误通知 ============== */
.error-notification {
  position: fixed;
  top: var(--spacing-xl);
  right: var(--spacing-xl);
  z-index: 1000;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  animation: slideInFromRight 0.3s ease;
  max-width: 400px;
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.notification-icon {
  color: #ff6b6b;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-message {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.notification-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.notification-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-normal);
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ============== 响应式设计 ============== */
@media (max-width: 1400px) {
  .dashboard-layout {
    grid-template-columns: 280px 1fr 320px;
    gap: var(--spacing-lg);
  }

  .header-content {
    padding: 0 var(--spacing-lg);
  }
}

@media (max-width: 1200px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
    gap: var(--spacing-lg);
    height: auto;
    min-height: auto;
  }

  .main-workspace {
    padding: var(--spacing-lg);
  }

  .search-panel,
  .analysis-panel {
    max-height: none;
  }

  .header-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }

  .header-left,
  .header-right {
    justify-self: center;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: var(--spacing-md) 0;
  }

  .header-content {
    padding: 0 var(--spacing-md);
    gap: var(--spacing-sm);
  }

  .project-title {
    font-size: 1.5rem;
  }

  .project-subtitle {
    font-size: 0.8rem;
  }

  .view-toggle {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .toggle-button {
    padding: var(--spacing-sm);
    font-size: 0.8rem;
  }

  .main-workspace {
    padding: var(--spacing-md);
  }

  .dashboard-layout {
    gap: var(--spacing-md);
  }

  .search-panel,
  .analysis-panel {
    padding: var(--spacing-lg);
  }

  .floating-controls {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }

  .overview-stats {
    flex-direction: column;
  }

  .metric-row {
    grid-template-columns: 1fr;
  }

  .range-inputs {
    grid-template-columns: 1fr;
  }

  .error-notification {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-sm);
  }

  .main-workspace {
    padding: var(--spacing-sm);
  }

  .search-panel,
  .analysis-panel {
    padding: var(--spacing-md);
  }

  .project-title {
    font-size: 1.25rem;
  }

  .floating-controls {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
  }
}

/* ============== 滚动条样式 ============== */
.search-panel::-webkit-scrollbar,
.analysis-panel::-webkit-scrollbar {
  width: 6px;
}

.search-panel::-webkit-scrollbar-track,
.analysis-panel::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.search-panel::-webkit-scrollbar-thumb,
.analysis-panel::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: 3px;
}

.search-panel::-webkit-scrollbar-thumb:hover,
.analysis-panel::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* ============== 性能优化 ============== */
.site-selection-page,
.search-panel,
.analysis-panel,
.map-workspace {
  transform: translateZ(0);
  will-change: transform;
}

/* ============== 可访问性增强 ============== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.form-input:focus,
.toggle-button:focus,
.primary-button:focus,
.control-button:focus,
.floating-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ============== 打印样式 ============== */
@media print {
  .page-header,
  .floating-controls,
  .error-notification {
    display: none;
  }

  .site-selection-page {
    background: white;
    color: black;
  }

  .dashboard-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

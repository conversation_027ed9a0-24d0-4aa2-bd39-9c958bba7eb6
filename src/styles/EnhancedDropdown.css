.enhanced-dropdown {
  position: relative;
  overflow: visible !important;
}

.enhanced-dropdown-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--dropdown-backdrop);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--dropdown-border-radius);
  box-shadow: var(--dropdown-shadow);
  padding: var(--dropdown-padding);
  min-width: 160px;
  max-width: 320px;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: var(--z-dropdown);
  animation: dropdownFadeIn var(--dropdown-animation-duration) ease;
  
  /* 防止文本选择和用户交互问题 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  
  /* 优化渲染性能 */
  will-change: transform, opacity;
  contain: layout style paint;
}

.enhanced-dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.enhanced-dropdown-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.enhanced-dropdown-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.enhanced-dropdown-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 暗色模式样式 */
.dark-mode .enhanced-dropdown-content {
  background: rgba(15, 25, 40, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.dark-mode .enhanced-dropdown-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark-mode .enhanced-dropdown-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark-mode .enhanced-dropdown-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-dropdown-content {
    max-width: calc(100vw - 40px);
    max-height: calc(100vh - 120px);
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .enhanced-dropdown-content {
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 80px);
    font-size: 13px;
  }
}

/* 确保在所有设备上都不会被截断 */
@media (max-height: 600px) {
  .enhanced-dropdown-content {
    max-height: calc(100vh - 60px);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .enhanced-dropdown-content {
    border-width: 2px;
    border-color: #000;
  }
  
  .dark-mode .enhanced-dropdown-content {
    border-color: #fff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-dropdown-content {
    animation: none;
  }
} 
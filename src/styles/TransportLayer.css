.toggle-transport-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background: #f0f0f0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 500;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%; /* 确保按钮宽度一致 */
  min-width: 160px; /* 设置最小宽度 */
}

/* 激活状态样式 */
.toggle-transport-button.active {
  background-color: #2196F3;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.toggle-transport-button:hover {
  /* 移除背景色变化效果 */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 距离标签样式 */
.transport-distance-marker {
  background: transparent;
  border: none;
}

/* 使用与RulerTool相同的distance-bubble样式，确保UI一致性 */
.transport-distance-marker .distance-bubble {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 交通图层图例样式 */
.transport-legend {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.85);
  border-radius: 30px;
  padding: 10px 25px;
  display: flex;
  gap: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.transport-legend .legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #333;
}

.transport-legend .legend-item img {
  width: 20px;
  height: 26px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .transport-legend {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
    bottom: 70px;
    max-width: 80%;
  }
}
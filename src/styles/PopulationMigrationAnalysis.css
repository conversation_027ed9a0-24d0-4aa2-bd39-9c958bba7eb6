.population-migration-container {
  padding: 24px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  overflow: hidden;
}

.population-migration-container h2 {
  color: #2c3e50;
  font-size: 28px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #3498db;
  position: relative;
  display: block;
  text-align: center;
  width: 100%;
}

.population-migration-container h2:after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3498db, transparent);
}

.analysis-description {
  color: #5a6a7e;
  margin: 0 auto 24px;
  line-height: 1.6;
  font-size: 16px;
  max-width: 95%;
  padding: 12px 16px;
  background-color: #f8fafc;
  border-radius: 4px;
  text-align: center;
  border-top: 1px solid rgba(52, 152, 219, 0.2);
  border-bottom: 1px solid rgba(52, 152, 219, 0.2);
  box-sizing: border-box;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background-color: #f0f7ff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
  position: relative;
  border: 1px solid rgba(52, 152, 219, 0.2);
}

.filters-container:before {
  content: 'Filters';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #3498db;
  color: white;
  padding: 4px 16px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  position: relative;
}

.filter-group label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.filter-group select {
  padding: 10px 14px;
  border: 1px solid #bdc3c7;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #34495e;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232c3e50' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 35px;
}

.filter-group select:hover {
  border-color: #3498db;
}

.filter-group select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 36px;
  position: relative;
}

@media (max-width: 768px) {
  .charts-container {
    grid-template-columns: 1fr;
  }
}

.chart-wrapper {
  height: 380px;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-wrapper:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
}

.data-summary {
  margin-top: 30px;
  padding: 30px;
  background-color: #f8fafc;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.data-summary h3 {
  font-size: 24px;
  margin-bottom: 20px;
  color: #2c3e50;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
  font-weight: 600;
}

.data-summary h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 3px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 30px;
}

@media (max-width: 992px) {
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .summary-cards {
    grid-template-columns: 1fr;
  }
}

.summary-card {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.card-icon i {
  font-size: 22px;
  color: white;
}

.card-icon.blue {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.card-icon.red {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.card-icon.green {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.card-icon.orange {
  background: linear-gradient(135deg, #f39c12, #d35400);
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 5px;
  font-weight: 500;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}



.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #666;
}

.chart-title-badge {
  position: relative;
  margin: 15px auto 20px;
  color: #2c3e50;
  padding: 0;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  letter-spacing: 0.3px;
  width: 100%;
}

.error-message {
  padding: 15px;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 14px;
}

/* Insights Section Styles */
.insights-section {
  margin-top: 30px;
  padding: 24px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.insights-section h3 {
  font-size: 22px;
  margin-bottom: 20px;
  color: #2c3e50;
  text-align: center;
  position: relative;
  padding-bottom: 12px;
}

.insights-section h3:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  border-radius: 3px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 20px;
}

@media (max-width: 992px) {
  .insights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .insights-grid {
    grid-template-columns: 1fr;
  }
}

.insight-card {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid #3498db;
}

.insight-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.insight-card:nth-child(1) {
  border-left-color: #3498db;
}

.insight-card:nth-child(2) {
  border-left-color: #e74c3c;
}

.insight-card:nth-child(3) {
  border-left-color: #2ecc71;
}

.insight-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 15px;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.insight-card:nth-child(1) .insight-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.insight-card:nth-child(2) .insight-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.insight-card:nth-child(3) .insight-icon {
  background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.insight-card h4 {
  font-size: 16px;
  color: #34495e;
  margin: 15px 0 10px;
  text-align: center;
  font-weight: 600;
}

.insight-card p {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}
/* 复用 MoneyPurposeAnalysis.css 的样式 */
.money-purpose-container {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.money-analysis-wrapper {
  margin-top: 20px;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  min-width: 150px;
}

.data-dashboard {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-tiles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-tile {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: transform 0.2s;
}

.stat-tile:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-content {
  width: 100%;
}

.stat-tile h3 {
  font-size: 14px;
  margin-bottom: 10px;
  color: #555;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.stat-percentage {
  font-size: 18px;
  font-weight: 600;
  color: #3498db;
}

.stat-note {
  font-size: 12px;
  color: #777;
  margin-top: 5px;
}

.total-money {
  border-left: 4px solid #3498db;
}

.daily-living {
  border-left: 4px solid #2ecc71;
}

.education {
  border-left: 4px solid #f39c12;
}

.debt-payment {
  border-left: 4px solid #e74c3c;
}

.chart-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.chart-box {
  flex: 1;
  min-width: 300px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chart-box.full-width {
  flex-basis: 100%;
}

.chart-heading {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
  text-align: center;
}

.chart-container {
  height: 300px;
  position: relative;
}

.insights-panel {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-top: 20px;
}

.insights-heading {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
}

.insights-heading i {
  color: #f39c12;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.insight-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.insight-header i {
  color: #3498db;
}

.insight-header h4 {
  font-size: 16px;
  color: #333;
  margin: 0;
}

.insight-card p {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
  margin: 0;
}

.analysis-description {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 15px;
}

@media (max-width: 768px) {
  .stat-tiles {
    grid-template-columns: 1fr 1fr;
  }
  
  .chart-row {
    flex-direction: column;
  }
  
  .chart-box {
    min-width: 100%;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stat-tiles {
    grid-template-columns: 1fr;
  }
  
  .filters-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    width: 100%;
  }
}

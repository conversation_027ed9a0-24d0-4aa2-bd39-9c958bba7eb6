.cost-breakdown-chart-container {
  position: relative;
  width: 100%;
  height: 260px;
  margin-bottom: var(--space-6);
}

.chart-center-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  pointer-events: none;
}

.chart-center-text .center-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  display: block;
}

.chart-center-text .center-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-transform: uppercase;
} 
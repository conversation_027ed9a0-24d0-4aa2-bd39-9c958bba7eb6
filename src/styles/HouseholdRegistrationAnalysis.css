/* Main container styles */
.household-registration-container {
  padding: 24px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  overflow: hidden;
}

.household-registration-container h2 {
  color: #2c3e50;
  font-size: 28px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #3498db;
  position: relative;
  display: block;
  text-align: center;
  width: 100%;
}

.household-registration-container h2:after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3498db, transparent);
}

/* Registration analysis wrapper */
.registration-analysis-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Info banner */
.info-banner {
  display: flex;
  align-items: center;
  background-color: #f0f7ff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 8px;
  border-left: 4px solid #3498db;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 16px;
  color: #3498db;
  font-size: 20px;
}

.info-banner p {
  color: #4a5568;
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
}

/* Control panel */
.control-panel {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.control-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: space-between;
}

.filter-control {
  flex: 1;
  min-width: 200px;
}

.filter-control label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.select-wrapper {
  position: relative;
}

.select-wrapper:after {
  content: '';
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #718096;
  pointer-events: none;
}

.filter-control select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #4a5568;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-control select:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

/* Data dashboard */
.data-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Stat tiles */
.stat-tiles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-tile {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.stat-tile:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-tile.population:before { background: linear-gradient(to right, #3498db, #2980b9); }
.stat-tile.residence:before { background: linear-gradient(to right, #2ecc71, #27ae60); }
.stat-tile.elsewhere:before { background: linear-gradient(to right, #f39c12, #f1c40f); }
.stat-tile.rate:before { background: linear-gradient(to right, #9b59b6, #8e44ad); }

.stat-tile:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-content {
  padding: 20px;
  text-align: center;
}

.stat-tile h3 {
  font-size: 16px;
  color: #4a5568;
  margin: 0 0 16px 0;
  font-weight: 600;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.stat-note {
  font-size: 14px;
  color: #718096;
}

/* Chart section */
.chart-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.chart-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 24px;
}

.chart-box {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
  transition: all 0.3s ease;
}

.chart-box.full-width {
  grid-column: 1 / -1;
}

.chart-box:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.chart-heading {
  font-size: 18px;
  color: #2d3748;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
  text-align: center;
}

.chart-container {
  height: 350px;
  position: relative;
}

/* Insights panel */
.insights-panel {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.insights-heading {
  font-size: 20px;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.insights-heading i {
  color: #3498db;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insight-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.insight-header i {
  color: #3498db;
  font-size: 18px;
}

.insight-header h4 {
  font-size: 16px;
  color: #2d3748;
  margin: 0;
}

.insight-card p {
  color: #4a5568;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* Loading and error states */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  font-size: 18px;
  color: #3498db;
}

.loading-container:before {
  content: '';
  width: 30px;
  height: 30px;
  margin-right: 15px;
  border: 3px solid #e2e8f0;
  border-top-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-container {
  color: #e53e3e;
  border-left: 4px solid #e53e3e;
  padding-left: 20px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .household-registration-container h2 {
    font-size: 24px;
  }
  
  .control-filters {
    flex-direction: column;
  }
  
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-value {
    font-size: 28px;
  }
}

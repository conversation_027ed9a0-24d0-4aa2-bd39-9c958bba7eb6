/* ===== CSS VARIABLES - DESIGN SYSTEM ===== */
/* 
  This section defines the core design tokens used throughout the application.
  These variables ensure consistent colors, spacing, and styling across all components.
  Colors follow a dark theme with blue/cyan primary colors and green accent colors.
  Spacing uses an 8px base unit system for consistent layout spacing.
  Border radius values create a modern, rounded appearance.
  Transition timings provide smooth animations and interactions.
*/
:root {
  /* Primary Colors - Main brand colors for buttons, links, and highlights */
  --primary-color: #4dc8ff;
  --primary-dark: #2196f3;
  --primary-light: #77d5ff;
  --secondary-color: #00ff88;
  --accent-color: #64b5f6;

  /* Background Colors - Layered dark theme backgrounds for depth */
  --bg-primary: #0f1419;
  --bg-secondary: #1a1f2e;
  --bg-tertiary: #2a2f3e;
  --bg-surface: rgba(26, 31, 46, 0.95);
  --bg-glass: rgba(26, 31, 46, 0.8);
  --bg-panel: rgba(42, 47, 62, 0.9);

  /* Text Colors - Hierarchy of text colors for different importance levels */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-muted: rgba(255, 255, 255, 0.7);
  --text-accent: var(--primary-color);

  /* Border & Shadow - Subtle borders and shadows for component separation */
  --border-primary: rgba(77, 200, 255, 0.2);
  --border-secondary: rgba(255, 255, 255, 0.1);
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);

  /* Spacing - 8px base unit system for consistent layout spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;

  /* Border Radius - Modern rounded corners for components */
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;

  /* Transitions - Smooth animation timings for interactions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Global overscroll protection - Prevents bounce effects on mobile */
html.ai-project-active,
body.ai-project-active {
  background-color: var(--bg-primary) !important;
  overscroll-behavior: none;
}

/* ===== PAGE FOUNDATION ===== */
/* 
  Main page container with gradient background and tech-inspired styling.
  Uses a dark gradient background with subtle radial overlays for visual interest.
  Sets up the base typography and prevents horizontal scrolling.
*/
.ai-project-page {
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(77, 200, 255, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.04) 0%, transparent 50%),
    linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  position: relative;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--text-primary);
  overscroll-behavior: none;
  line-height: 1.6;
}

/* ===== BACKGROUND ELEMENTS ===== */
/* 
  Fixed background layer that sits behind all content.
  Provides the base dark background for the entire page.
*/
.ai-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
  background: var(--bg-primary);
}

/* 
  Animated grid pattern overlay for tech aesthetic.
  Creates a subtle grid effect that pulses slowly for visual interest.
*/
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(90deg, transparent 98%, rgba(77, 200, 255, 0.1) 98%),
    linear-gradient(0deg, transparent 98%, rgba(77, 200, 255, 0.1) 98%);
  background-size: 80px 80px;
  opacity: 0.3;
  animation: gridPulse 8s ease-in-out infinite;
}

/* Grid pulse animation for subtle breathing effect */
@keyframes gridPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

/* ===== HEADER DESIGN ===== */
/* 
  Fixed header with glass morphism effect and project navigation.
  Contains back button, project title, status indicator, and language selector.
  Uses backdrop blur for modern glass-like appearance.
*/
.ai-header {
  position: relative;
  z-index: 100;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-md);
}

/* 
  Header container with three-column grid layout.
  Left: Back button, Center: Project title and status, Right: Language selector.
*/
.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-lg *0.8) var(--spacing-xl *0.8);
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  align-items: center;
  gap: var(--spacing-lg);
}

/* Header left section - Back navigation */
.header-left {
  display: flex;
  justify-content: flex-start;
}

/* Header center section - Project title and status */
.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Header right section - Language selector */
.header-right {
  display: flex;
  justify-content: flex-end;
}

/* Back Button - Navigation button with hover effects */
.back-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  transform: translateX(-2px);
  box-shadow: var(--shadow-md);
}

.back-button svg {
  transition: transform var(--transition-normal);
}

.back-button:hover svg {
  transform: translateX(-2px);
}

/* Project Title - Main heading with gradient text effect */
.project-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Project Status - Status indicator with animated pulse */
.project-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 13px;
  color: var(--text-muted);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--secondary-color);
  box-shadow: 0 0 8px rgba(0, 255, 136, 0.4);
  animation: statusPulse 2s ease-in-out infinite;
}

/* Status pulse animation for active indicator */
@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* Language Selector - Dropdown for language switching */
.language-selector {
  position: relative;
}

/* Language trigger button - Shows current language with flag */
.language-trigger {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.language-trigger:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

/* Language flag emoji */
.language-flag {
  font-size: 16px;
}

/* Language name text */
.language-name {
  flex: 1;
}

/* Dropdown arrow icon with rotation animation */
.dropdown-icon {
  transition: transform var(--transition-normal);
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

/* Language dropdown menu - Appears below trigger */
.language-menu {
  position: absolute;
  top: calc(100% + var(--spacing-sm));
  right: 0;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  min-width: 180px;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease;
}

/* Dropdown fade-in animation */
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Individual language option in dropdown */
.language-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 14px;
  font-weight: 500;
  text-align: left;
}

.language-option:hover {
  background: var(--bg-panel);
  color: var(--text-primary);
}

.language-option.active {
  background: rgba(77, 200, 255, 0.1);
  color: var(--primary-color);
}

/* Check icon for active language */
.check-icon {
  color: var(--primary-color);
}

/* ===== MAIN CONTENT LAYOUT ===== */
/* 
  Main content area with flexible layout system.
  Supports single panel, dual panel, and results-focused layouts.
  Uses CSS Grid for responsive design and smooth transitions.
*/
.ai-main {
  position: relative;
  z-index: 10;
  padding: var(--spacing-2xl) 0;
  min-height: calc(100vh - 120px);
}

/* Main container with max width and padding */
.ai-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
}

/* 
  Content layout system with three modes:
  - Default: Single column layout
  - dual-panel: Two-column grid for side-by-side panels
  - results-focused: Single column optimized for results display
*/
.content-layout {
  display: flex;
  flex-direction: column;
  gap: 0;
  align-items: stretch;
  transition: all var(--transition-slow);
  max-width: 900px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.content-layout.dual-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.content-layout.results-focused {
  grid-template-columns: 1fr;
  max-width: 900px;
  margin: 0 auto;
}

.content-layout.results-focused .input-panel.hidden {
  display: none;
}

/* ===== PANEL STYLES ===== */
/* 
  Base panel styling for input and results panels.
  Uses glass morphism effect with backdrop blur and subtle borders.
  Includes hover effects and smooth transitions.
*/
.input-panel,
.results-panel {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  overflow: hidden;
  width: 90%;
  margin: 0 auto;
}

.input-panel:hover,
.results-panel:hover {
  border-color: var(--border-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-1px);
}

/* Panel header with title, description, and metadata */
.panel-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border-secondary);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.header-info h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-info p {
  font-size: 14px;
  color: var(--text-muted);
  margin: 0;
  line-height: 1.4;
}

/* Input Panel Meta */
.input-meta,
.results-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 12px;
  color: var(--text-muted);
}

.char-count,
.results-count {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(77, 200, 255, 0.1);
  border-radius: var(--radius-sm);
  font-weight: 600;
  color: var(--primary-color);
  min-width: 24px;
  text-align: center;
}

.view-toggle-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-toggle-button:hover {
  background: var(--bg-surface);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

/* ===== INPUT CONTENT ===== */
/* 
  Input content area with form fields and textarea.
  Provides structured input for AI analysis with validation and feedback.
*/
.input-content {
  padding: var(--spacing-xl);
}

/* 
  Main textarea for user input with enhanced styling.
  Features focus states, placeholder text, and responsive sizing.
  Supports both structured form input and free-form text entry.
*/
.input-textarea {
  width: 100%;
  min-height: 360px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  transition: all var(--transition-normal);
  font-family: inherit;
  font-weight: 400;
}

.input-textarea:focus {
  outline: none;
  border-color: var(--border-primary);
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.1);
  background: rgba(255, 255, 255, 0.04);
}

.input-textarea::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

/* Quick input chips for common phrases or templates */
.quick-inputs {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
  margin-top: var(--spacing-md);
}

.quick-input-chip {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-muted);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.quick-input-chip:hover {
  background: rgba(77, 200, 255, 0.1);
  border-color: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* ===== INPUT ACTIONS ===== */
/* 
  Action buttons area with primary analyze button and secondary clear button.
  Provides clear call-to-action for users to process their input.
*/
.input-actions {
  padding: var(--spacing-xl);
  border-top: 1px solid var(--border-secondary);
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* Primary analyze button with gradient background and loading states */
.analyze-button.primary {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.8rem 1.6rem;
  color: var(--bg-primary);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.analyze-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left 0.5s ease;
}

.analyze-button.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.analyze-button.primary:hover:not(:disabled)::before {
  left: 100%;
}

.analyze-button.primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Secondary clear button for resetting form */
.clear-button.secondary {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: 0.6rem 1.2rem;
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clear-button.secondary:hover:not(:disabled) {
  background: var(--bg-surface);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.clear-button.secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Loading spinner for analyze button during processing */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(15, 25, 40, 0.2);
  border-top: 2px solid var(--bg-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESULTS CONTENT ===== */
/* 
  Results display area with analysis outcomes and interactive elements.
  Shows extracted information in organized categories with edit capabilities.
*/
.results-content {
  padding: var(--spacing-xl);
}

/* Empty and error states for results display */
.empty-state,
.error-state {
  text-align: center;
  padding: var(--spacing-2xl) var(--spacing-xl);
  color: var(--text-muted);
}

.empty-icon,
.error-icon {
  margin-bottom: var(--spacing-lg);
  color: var(--border-primary);
  opacity: 0.6;
  animation: iconPulse 3s ease-in-out infinite;
}

/* Icon pulse animation for empty/error states */
@keyframes iconPulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

.empty-state h3,
.error-state h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state p,
.error-state p {
  font-size: 14px;
  color: var(--text-muted);
  margin: 0 0 var(--spacing-lg) 0;
  line-height: 1.5;
}

/* Error state with special styling and retry functionality */
.error-state {
  background: rgba(255, 71, 87, 0.05);
  border: 1px solid rgba(255, 71, 87, 0.1);
  border-radius: var(--radius-md);
  margin: var(--spacing-lg);
}

.error-state .error-icon {
  color: #ff4757;
}

.error-state h4 {
  color: #ff4757;
}

/* Retry button for error recovery */
.retry-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 71, 87, 0.1);
  border: 1px solid rgba(255, 71, 87, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: #ff4757;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
  margin: 0 auto;
}

.retry-button:hover {
  background: rgba(255, 71, 87, 0.15);
  border-color: rgba(255, 71, 87, 0.3);
  transform: translateY(-1px);
}

/* Analysis Results - Main results display container */
.analysis-results {
  background: transparent;
}

/* Summary section with AI-generated analysis overview */
.summary-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: rgba(77, 200, 255, 0.05);
  border: 1px solid rgba(77, 200, 255, 0.1);
  border-radius: var(--radius-md);
}

.summary-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 var(--spacing-sm) 0;
}

.summary-text {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Results header with title and action buttons */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.results-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.results-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Export button for downloading results */
.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-normal);
  width: 36px;
  height: 36px;
}

.export-button:hover {
  background: var(--bg-surface);
  border-color: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* Edit hint text for user guidance */
.edit-hint {
  font-size: 11px;
  color: var(--text-muted);
  font-style: italic;
}

/* ===== RESULTS GRID ===== */
/* 
  Grid layout for displaying analysis results in organized categories.
  Each category contains extracted information with edit and delete capabilities.
*/
.results-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* Individual result category container */
.result-category {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.result-category:hover {
  border-color: var(--border-primary);
  background: rgba(255, 255, 255, 0.03);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Category header with title, count, and add button */
.category-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-secondary);
  background: rgba(77, 200, 255, 0.02);
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.category-info h4 {
  color: var(--primary-color);
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
}

/* Item count badge showing number of results */
.item-count {
  background: rgba(77, 200, 255, 0.1);
  color: var(--primary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  font-size: 11px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

/* Add item button for creating new entries */
.add-item-button {
  background: rgba(77, 200, 255, 0.08);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs);
  color: var(--primary-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  opacity: 0.7;
}

.add-item-button:hover {
  background: rgba(77, 200, 255, 0.12);
  border-color: rgba(77, 200, 255, 0.25);
  opacity: 1;
  transform: scale(1.02);
}

/* Container for result items with flex layout */
.result-items {
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  min-height: 60px;
  align-items: flex-start;
}

/* Individual result item with edit and delete functionality */
.result-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(77, 200, 255, 0.06);
  border: 1px solid rgba(77, 200, 255, 0.15);
  border-radius: var(--radius-xl);
  padding: 2px;
  transition: all var(--transition-fast);
  animation: itemFadeIn 0.3s ease-out;
}

/* Fade-in animation for new result items */
@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.result-item:hover {
  background: rgba(77, 200, 255, 0.08);
  border-color: rgba(77, 200, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 1px 4px rgba(77, 200, 255, 0.08);
}

/* Editable result tag with focus states */
.result-tag {
  background: transparent;
  border: none;
  color: var(--primary-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 12px;
  font-weight: 500;
  cursor: text;
  outline: none;
  min-width: 20px;
  text-align: center;
  transition: all var(--transition-fast);
}

.result-tag:hover {
  background: rgba(77, 200, 255, 0.08);
}

.result-tag:focus {
  background: rgba(77, 200, 255, 0.12);
  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.2);
}

/* Delete button for removing result items */
.delete-item-button {
  background: rgba(255, 71, 87, 0.1);
  border: 1px solid rgba(255, 71, 87, 0.2);
  border-radius: 50%;
  padding: var(--spacing-xs);
  color: rgba(255, 71, 87, 0.8);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  width: 20px;
  height: 20px;
}

.result-item:hover .delete-item-button {
  opacity: 1;
  transform: scale(1);
}

.delete-item-button:hover {
  background: rgba(255, 71, 87, 0.15);
  border-color: rgba(255, 71, 87, 0.3);
  transform: scale(1.1);
}

/* ===== SITE SELECTION BUTTON ===== */
/* 
  Prominent call-to-action button for site selection feature.
  Features gradient background, hover effects, and animated shine effect.
  Appears after successful AI analysis to guide users to next step.
*/
.site-selection-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-2xl);
  padding: 0 var(--spacing-xl) var(--spacing-2xl) var(--spacing-xl);
  animation: fadeInUp 0.8s ease-out 0.3s both;
}

/* Fade-in animation for site selection section */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Main site selection button with gradient and effects */
.site-selection-button {
  position: relative;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: 2px solid var(--primary-color);
  border-radius: var(--radius-lg);
  padding: 0;
  cursor: pointer;
  transition: all var(--transition-normal);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  min-width: 280px;
  height: 70px;
}

/* Animated shine effect overlay */
.site-selection-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.site-selection-button:hover {
  transform: translateY(-2px) scale(1.01);
  border-color: var(--primary-light);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  box-shadow:
    0 8px 20px rgba(77, 200, 255, 0.2),
    0 4px 12px rgba(77, 200, 255, 0.15);
}

.site-selection-button:hover::before {
  left: 100%;
}

.site-selection-button:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--shadow-md);
}

/* Button content with icon and text */
.button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  height: 100%;
  color: var(--bg-primary);
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: all var(--transition-normal);
}

.site-selection-button:hover .button-content {
  color: var(--bg-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.button-content svg {
  transition: all var(--transition-normal);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.site-selection-button:hover .button-content svg {
  transform: scale(1.05) rotate(2deg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* ===== RESPONSIVE DESIGN ===== */
/* 
  Responsive breakpoints for different screen sizes.
  Ensures optimal layout and usability across desktop, tablet, and mobile devices.
  Uses mobile-first approach with progressive enhancement.
*/

/* Large screens (1200px and below) */
@media (max-width: 1200px) {
  .content-layout.dual-panel {
    gap: var(--spacing-lg);
  }

  .site-selection-section {
    margin-top: var(--spacing-xl);
    padding: 0 var(--spacing-lg) var(--spacing-xl) var(--spacing-lg);
  }

  .site-selection-button {
    min-width: 260px;
    height: 65px;
  }

  .button-content {
    font-size: 15px;
  }
}

/* Medium screens (1024px and below) - Tablet layout */
@media (max-width: 1024px) {
  .content-layout.dual-panel {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .content-layout.results-focused {
    max-width: 100%;
  }

  .header-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    text-align: center;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }

  .project-title {
    font-size: 1.6rem;
  }

  .language-menu {
    right: auto;
    left: 50%;
    transform: translateX(-50%);
  }

  .edit-hint {
    display: none;
  }
}

/* Small screens (768px and below) - Mobile layout */
@media (max-width: 768px) {
  .ai-container {
    padding: 0 var(--spacing-md);
  }

  .header-container {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .back-button,
  .language-trigger {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
  }

  .project-title {
    font-size: 1.4rem;
  }

  .language-menu {
    min-width: 200px;
  }

  .input-panel,
  .results-panel {
    border-radius: var(--radius-md);
  }

  .panel-header {
    padding: var(--spacing-lg) var(--spacing-lg);
  }

  .header-info h2 {
    font-size: 1.2rem;
  }

  .input-content {
    padding: var(--spacing-lg);
  }

  .input-textarea {
    min-height: 280px;
    padding: var(--spacing-md);
  }

  .quick-inputs {
    gap: var(--spacing-xs);
  }

  .quick-input-chip {
    font-size: 10px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .input-actions {
    flex-direction: column;
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }

  .analyze-button.primary,
  .clear-button.secondary {
    width: 100%;
    justify-content: center;
  }

  .results-content {
    padding: var(--spacing-lg);
  }

  .results-grid {
    gap: var(--spacing-md);
  }

  .category-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .result-items {
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-xs);
  }

  .add-item-button {
    width: 24px;
    height: 24px;
  }

  .result-item {
    gap: 2px;
  }

  .result-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 11px;
  }

  .delete-item-button {
    width: 18px;
    height: 18px;
  }

  .site-selection-section {
    margin-top: var(--spacing-lg);
    padding: 0 var(--spacing-md) var(--spacing-lg) var(--spacing-md);
  }

  .site-selection-button {
    min-width: 240px;
    height: 60px;
  }

  .button-content {
    font-size: 14px;
    gap: var(--spacing-sm);
  }
}

/* Extra small screens (480px and below) - Compact mobile layout */
@media (max-width: 480px) {
  .ai-container {
    padding: 0 var(--spacing-sm);
  }

  .header-container {
    padding: var(--spacing-md) var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .project-title {
    font-size: 1.2rem;
  }

  .content-layout.dual-panel {
    gap: var(--spacing-md);
  }

  .panel-header {
    padding: var(--spacing-md);
  }

  .header-info h2 {
    font-size: 1.1rem;
  }

  .input-content {
    padding: var(--spacing-md);
  }

  .input-textarea {
    min-height: 240px;
    padding: var(--spacing-sm);
    font-size: 13px;
  }

  .input-actions {
    padding: var(--spacing-md);
  }

  .results-content {
    padding: var(--spacing-md);
  }

  .content-layout.results-focused {
    max-width: 100%;
  }

  .result-item {
    padding: 1px;
  }

  .result-tag {
    padding: 3px 6px;
    font-size: 10px;
  }

  .delete-item-button {
    width: 16px;
    height: 16px;
  }

  .add-item-button {
    width: 20px;
    height: 20px;
    padding: 3px;
  }

  .results-actions {
    gap: var(--spacing-sm);
  }

  .export-button {
    width: 32px;
    height: 32px;
    padding: var(--spacing-xs);
  }

  .site-selection-section {
    margin-top: var(--spacing-md);
    padding: 0 var(--spacing-sm) var(--spacing-md) var(--spacing-sm);
  }

  .site-selection-button {
    min-width: 220px;
    height: 55px;
  }

  .button-content {
    font-size: 13px;
    gap: var(--spacing-sm);
    letter-spacing: 0.3px;
  }

  .button-content svg {
    width: 20px;
    height: 20px;
  }
}

/* 站点选择模态框样式 */
.site-selection-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
}

.site-selection-modal {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  max-width: 95vw;
  max-height: 90vh;
  width: 1200px;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.modal-header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

.view-toggle-controls {
  display: flex;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 0.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.view-toggle-controls .view-toggle-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle-controls .view-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #4dc8ff;
}

.view-toggle-controls .view-toggle-btn.active {
  background: rgba(77, 200, 255, 0.2);
  color: #4dc8ff;
  border: 1px solid rgba(77, 200, 255, 0.3);
}

.modal-header h2 {
  color: #ffffff;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modal-close-btn {
  background: none;
  border: none;
  color: #ffffff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.modal-content {
  padding: 2rem;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.prospecting-results h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.results-subtitle {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.parcels-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.parcel-card {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.parcel-card:hover {
  transform: translateY(-2px);
  border-color: rgba(77, 200, 255, 0.3);
  box-shadow: 0 8px 32px rgba(77, 200, 255, 0.1);
}

.parcel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}

.parcel-header h4 {
  color: #4dc8ff;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.parcel-rank {
  background: linear-gradient(135deg, #4dc8ff 0%, #00d4ff 100%);
  color: #0a0f1c;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 700;
}

.parcel-info {
  margin-bottom: 1rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.info-row .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-row .value {
  color: #ffffff;
  font-weight: 600;
}

.cost-breakdown {
  margin-bottom: 1.5rem;
}

.cost-breakdown h5 {
  color: #4dc8ff;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.breakdown-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  padding: 0.25rem 0;
}

.breakdown-item span:first-child {
  color: rgba(255, 255, 255, 0.6);
}

.breakdown-item span:last-child {
  color: #ffffff;
  font-weight: 500;
}

.select-parcel-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.25) 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.select-parcel-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.25) 0%, rgba(77, 200, 255, 0.35) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(77, 200, 255, 0.2);
}

.select-parcel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.financial-analysis-results {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.financial-analysis-results h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #fff 0%, #00ff88 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.financial-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  backdrop-filter: blur(10px);
}

.summary-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.summary-item .value {
  color: #00ff88;
  font-size: 1.2rem;
  font-weight: 700;
}

/* 地图视图容器样式 */
.map-view-container {
  padding: 0;
}

.map-view-container h3 {
  color: #ffffff;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  padding: 0 2rem;
  background: linear-gradient(135deg, #fff 0%, #4dc8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.map-view-container .results-subtitle {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2rem;
  font-size: 0.9rem;
  padding: 0 2rem;
}

.modal-map {
  border-radius: 0;
  border: none;
  box-shadow: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  padding: 1rem;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  align-items: center;
}

.form-section label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  width: calc(95% + 4px);
  margin-right: -4px;
}

.form-section label::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
  opacity: 0.2;
}

.form-section .input-field {
  width: 95%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.03);
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.4;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 22px;
  max-height: 40px;
  resize: vertical;
}

.form-section .input-field:hover {
  border-color: var(--border-primary);
  background: rgba(255, 255, 255, 0.04);
}

.form-section .input-field:focus {
  outline: none;
  border-color: var(--primary-color);
  background: rgba(255, 255, 255, 0.05);
  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.1);
}

.form-section .input-field::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-style: italic;
}

/* File Input Styles */
.file-input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 1rem 0;
  width: 95%;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  justify-content: center;
}

.file-input-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
  border-radius: var(--radius-sm);
  padding: 0.75rem 1.5rem;
  color: var(--bg-primary);
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-input-label:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.file-input-label svg {
  width: 18px;
  height: 18px;
  color: var(--bg-primary);
}

.file-input {
  display: none;
}

.file-name {
  color: var(--text-muted);
  font-size: 0.85rem;
  font-style: italic;
  margin-left: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-secondary);
}

.file-name.has-file {
  color: var(--primary-color);
  background: rgba(77, 200, 255, 0.1);
  border-color: var(--primary-color);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  justify-content: center;
}

.analyze-button.primary {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
  border-radius: var(--radius-md);
  padding: 0.8rem 1.6rem;
  color: var(--bg-primary);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.analyze-button.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left 0.5s ease;
}

.analyze-button.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.18);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.analyze-button.primary:hover:not(:disabled)::before {
  left: 100%;
}

.analyze-button.primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Secondary clear button for resetting form */
.clear-button.secondary {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--bg-panel);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: 0.6rem 1.2rem;
  color: var(--text-muted);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  max-width: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clear-button.secondary:hover:not(:disabled) {
  background: var(--bg-surface);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

.clear-button.secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .analyze-button.primary,
  .clear-button.secondary {
    width: 100%;
    max-width: none;
  }
}

/* Form Styles */
.analysis-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 2rem;
  max-width: 720px;
  margin: 0 auto;
  width: 90%;
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

.form-sections-container {
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 0;
  transition: all 0.3s ease;
  align-items: center;
}

.form-section:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  margin-bottom: 0.5rem;
  padding-bottom: 1rem;
}

.form-section label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  width: calc(95% + 4px);
  margin-right: -4px;
}

.form-section label::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
  opacity: 0.2;
}

.form-section .input-field {
  width: 90%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.03);
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.4;
  transition: all 0.3s ease;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 22px;
  max-height: 40px;
  resize: vertical;
}

/* File Input Styles */
.file-input-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 1rem 0;
  width: 95%;
  padding: 0.5rem;
  transition: all 0.3s ease;
  justify-content: center;
}

/* Results Popup Styles */
.results-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.results-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  overflow: hidden;
  transition: none;
  transform: translate(-50%, -50%) !important;
}

.results-panel .results-content {
  flex: 1;
  overflow-y: auto;
  max-height: 70vh;
  padding: var(--spacing-xl);
}

/* ===== BROWSER-LIKE TAB STYLING ===== */
.tab-container {
  display: flex;
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-bottom: none;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  overflow: hidden;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-sm);
  width: 90%;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  background: var(--bg-panel);
  border: none;
  border-right: 1px solid var(--border-secondary);
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 500;
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  min-height: 48px;
}

.tab-item:last-child {
  border-right: none;
}

.tab-item:hover {
  background: var(--bg-surface);
  color: var(--text-secondary);
}

.tab-item.active {
  background: var(--bg-surface);
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  box-shadow: 0 2px 8px rgba(77, 200, 255, 0.1);
}

.tab-item.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.05) 0%, transparent 100%);
  pointer-events: none;
}

.tab-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  transition: all var(--transition-normal);
}

.tab-item.active .tab-icon {
  color: var(--primary-color);
  transform: scale(1.1);
}

.tab-label {
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all var(--transition-normal);
}

.tab-item.active .tab-label {
  color: var(--primary-color);
  text-shadow: 0 0 8px rgba(77, 200, 255, 0.3);
}

/* Tab indicator animation */
.tab-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.tab-item.active::after {
  width: 100%;
}

/* Enhanced hover effects */
.tab-item:hover .tab-icon {
  transform: scale(1.05);
}

.tab-item:hover .tab-label {
  color: var(--text-primary);
}

/* Focus state for accessibility */
.tab-item:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: -2px;
  box-shadow: 0 0 0 4px rgba(77, 200, 255, 0.1);
}

/* Tab ripple effect */
.tab-item::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(77, 200, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.tab-item:active::before {
  width: 100px;
  height: 100px;
}

/* Responsive tab styles */
@media (max-width: 768px) {
  .tab-container {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
  }
  
  .tab-item {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 13px;
    min-height: 44px;
  }
  
  .tab-icon {
    width: 16px;
    height: 16px;
  }
  
  .tab-label {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .tab-item {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 12px;
    min-height: 40px;
    gap: var(--spacing-xs);
  }
  
  .tab-icon {
    width: 14px;
    height: 14px;
  }
  
  .tab-label {
    font-size: 11px;
    letter-spacing: 0.3px;
  }
}

/* Update input panel to work with tabs - ATTACHED STYLE */
.input-panel {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  margin-top: 0;
  border-top: none;
  box-shadow: var(--shadow-lg);
}

.input-panel .panel-header {
  border-top: none;
}

/* Remove old toggle switch styles */
.toggle-switch {
  display: none;
}

/* ===== EMAIL INPUT STYLING ===== */
.email-input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-lg);
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
  width: 100%;
  box-sizing: border-box;
  max-width: 720px;
  margin: 0 auto;
}

.email-input-container:hover {
  border-color: var(--border-primary);
  box-shadow: var(--shadow-md);
}

.email-input-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
  width: 100%;
  justify-content: center;
}

.email-input-header svg {
  color: var(--primary-color);
  flex-shrink: 0;
}

.email-input-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.email-textarea {
  min-height: 200px !important;
  width: 100% !important;
  box-sizing: border-box;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-size: 14px;
  line-height: 1.6;
  resize: vertical;
  transition: all var(--transition-normal);
  font-family: inherit;
  margin: 0 auto;
  display: block;
}

.email-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.1);
  background: var(--bg-panel);
  outline: none;
}

.email-textarea::placeholder {
  color: var(--text-muted);
  font-style: italic;
  line-height: 1.5;
}

.email-input-tips {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-secondary);
  width: 100%;
  align-items: center;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
  transition: all var(--transition-normal);
}

.tip-item:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  transform: translateX(2px);
}

.tip-item svg {
  color: var(--secondary-color);
  flex-shrink: 0;
}

.tip-item span {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Email-specific responsive styles */
@media (max-width: 768px) {
  .email-input-container {
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
  }
  
  .email-input-header {
    padding-bottom: var(--spacing-sm);
  }
  
  .email-input-title {
    font-size: 14px;
  }
  
  .email-textarea {
    min-height: 150px !important;
    font-size: 13px;
  }
  
  .email-input-tips {
    padding-top: var(--spacing-sm);
  }
  
  .tip-item {
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .tip-item span {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .email-input-container {
    padding: var(--spacing-sm);
  }
  
  .email-input-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
  
  .email-textarea {
    min-height: 120px !important;
    font-size: 12px;
  }
  
  .tip-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
    text-align: left;
  }
}

/* ===== RESULTS PANEL CLOSE BUTTON ===== */
/* 
  Close button for the results popup panel.
  Matches the theme with glass morphism effect and smooth hover animations.
  Uses the primary blue color scheme and consistent spacing.
*/
.close-results {
  background: var(--bg-surface);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  position: relative;
  overflow: hidden;
}

.close-results::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  border-radius: var(--radius-md);
}

.close-results:hover {
  background: var(--bg-panel);
  border-color: var(--border-primary);
  color: var(--text-primary);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.close-results:hover::before {
  opacity: 0.1;
}

.close-results svg {
  position: relative;
  z-index: 1;
  transition: transform var(--transition-normal);
}

.close-results:hover svg {
  transform: rotate(90deg);
}

.close-results:active {
  transform: scale(0.95);
}

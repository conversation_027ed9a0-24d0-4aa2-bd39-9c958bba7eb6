/* 基本样式 - 稳定版本，无悬停效果 */
.migration-marker {
  cursor: pointer !important;
  z-index: 1000 !important;
  pointer-events: auto !important;
}

/* 简洁模式提示框 */
.migration-tooltip-compact {
  background-color: white;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  min-width: 150px;
  font-size: 12px;
}

.migration-tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 0;
  margin-bottom: 10px;
  position: relative;
}

.migration-tooltip-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.migration-total {
  font-size: 13px;
  font-weight: 500;
  color: #1890ff;
  margin-top: 4px;
}

/* 展开模式提示框 */
.migration-tooltip-expanded {
  background-color: white;
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  padding: 0;
  min-width: 260px;
  max-width: 340px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.08);
  pointer-events: auto !important; /* 确保可以点击 */
}

/* 覆盖Leaflet默认的tooltip样式以确保交互性 */
.leaflet-tooltip {
  pointer-events: auto !important;
}

.migration-tooltip-content {
  padding: 15px;
  position: relative;
}

.migration-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s;
  background-color: #f0f0f0;
  z-index: 1020;
}

.migration-close-btn:hover {
  color: #f5222d;
  transform: rotate(90deg);
  background-color: #e0e0e0;
}

/* 数据块样式 */
.migration-data-blocks {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 15px 0;
}

.migration-data-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f9f9f9;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
}

.migration-data-label {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #333;
}

.color-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
  display: inline-block;
}

.migration-data-value {
  font-size: 15px;
  font-weight: 600;
  color: #1890ff;
}

/* 图表切换按钮 */
.migration-chart-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  font-size: 14px;
  color: white;
  cursor: pointer;
  background-color: #1890ff;
  border-radius: 4px;
  margin: 15px 0;
  transition: all 0.2s;
  font-weight: 500;
  border: none;
  width: 100%;
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1010;
}

.migration-chart-toggle:hover {
  background-color: #40a9ff;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

.migration-chart-toggle:active {
  background-color: #096dd9;
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(24, 144, 255, 0.4);
}

.toggle-icon {
  margin-left: 5px;
  font-size: 10px;
}

/* 图表容器 */
.migration-chart-container {
  margin-top: 12px;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.migration-pie-container {
  width: 140px;
  height: 140px;
  margin: 0 auto 10px;
}

.migration-legend {
  font-size: 12px;
  margin-top: 12px;
}

.migration-legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.migration-legend-item .color-box {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  display: inline-block;
}

.migration-legend-item strong {
  margin: 0 4px;
}

.percentage {
  color: #999;
  font-size: 11px;
  margin-left: 4px;
}

/* 全国数据切换按钮 */
.nationwide-toggle-btn {
  position: absolute;
  top: 80px;
  left: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
  user-select: none;
  display: flex;
  align-items: center;
  gap: 5px;
}

.nationwide-toggle-btn:hover {
  background-color: rgba(240, 240, 240, 0.95);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

.toggle-icon {
  font-size: 10px;
  display: inline-block;
  margin-right: 3px;
  color: #666;
}

/* 图例样式 */
.migration-type-legend {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(255, 255, 255, 0.95); /* Match popup background */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 10px; /* Consistent radius */
  padding: 10px 15px; /* Adjust padding */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15); /* Adjust shadow */
  z-index: 1000;
  /* max-width: 80%; */ /* Remove max-width */
  width: auto; /* Let content determine width */
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  animation: slideUp 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
  from { transform: translate(-50%, 100px); opacity: 0; }
  to { transform: translate(-50%, 0); opacity: 1; }
}

.migration-type-legend h4 {
  margin: 0 0 8px 0; /* Adjust margin */
  font-size: 14px; /* Slightly smaller */
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08); /* Match popup border */
  padding-bottom: 6px; /* Adjust padding */
  width: 100%;
}

.legend-items {
  display: flex;
  flex-direction: row; /* Align items horizontally */
  flex-wrap: wrap; /* Allow wrapping */
  justify-content: center; /* Center items */
  gap: 8px 15px; /* Row and column gap */
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 12px; /* Match popup legend */
  color: #555;
}

.legend-item .color-box {
  width: 10px; /* Match popup legend */
  height: 10px;
  border-radius: 3px; /* Match popup legend */
  margin-right: 6px; /* Match popup legend */
  display: inline-block;
  flex-shrink: 0;
}

/* Remove unused hint style */
/*
.legend-hint {
  margin-top: 8px;
  font-size: 11px;
  color: #888;
  text-align: center;
}
*/

/* 迁移控制面板样式已移至 MigrationControls.css */

.toggle-migration-button {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  width: 100%;
  margin-bottom: 8px;
  transition: background-color 0.3s;
}

.toggle-migration-button:hover {
  background-color: #40a9ff;
}

.toggle-migration-button.active {
  background-color: #f5222d;
}

.toggle-migration-button.active:hover {
  background-color: #ff4d4f;
}

/* 无数据消息样式 */
.migration-no-data-alert {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  pointer-events: none;
}

.migration-no-data-message {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  text-align: center;
  max-width: 300px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.migration-no-data-icon {
  font-size: 32px;
  margin-bottom: 15px;
  color: #1890ff;
}

.migration-no-data-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.migration-no-data-hint {
  font-size: 14px;
  color: #666;
}

.migration-controls-container {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2000;
  /* 阻止所有事件传播到地图 */
  touch-action: none;
  user-select: none;
  pointer-events: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .migration-tooltip {
    min-width: 180px;
  }

  .migration-pie-container {
    width: 100px;
    height: 100px;
  }

  .migration-type-legend {
    max-width: 200px;
    bottom: 10px;
    right: 10px;
  }
}

.marker-tooltip {
  pointer-events: none !important; /* Ensure it doesn't block mouse events */
  z-index: 1500;
  animation: tooltipFadeIn 0.2s ease-out;
  transition: transform 0.1s ease-out, opacity 0.1s ease-out;
  opacity: 0.98;
  user-select: none;
}

@keyframes tooltipFadeIn {
  from { opacity: 0; transform: translate(-50%, -140%); }
  to { opacity: 0.98; transform: translate(-50%, -150%); }
}

.marker-tooltip-content {
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
  padding: 12px 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  min-width: 200px;
  max-width: 260px;
  position: relative;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.95);
}

.marker-tooltip-content:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  margin-left: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgba(255, 255, 255, 0.95);
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.15));
  z-index: -1;
}

.marker-tooltip-header {
  margin-bottom: 8px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding-bottom: 6px;
}

.marker-tooltip-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.marker-tooltip-total {
  text-align: center;
  font-size: 16px;
  color: #1890ff;
  font-weight: 600;
  margin-bottom: 8px;
}

.marker-tooltip-hint {
  text-align: center;
  font-size: 13px;
  color: #666;
  font-style: italic;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 4px 8px;
  margin-top: 6px;
}

.time-series-chart-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50vh;
  background: white;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 20px 20px 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.time-series-chart-panel.show {
  transform: translateY(0);
}

.panel-header {
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 时间轴滑块样式 */
.time-slider {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(240, 249, 255, 0.95));
  padding: 16px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
  position: relative;
}

.time-slider::before {
  content: "SELECT TIME RANGE";
  position: absolute;
  top: -8px;
  left: 16px;
  background: white;
  padding: 0 8px;
  font-size: 11px;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.slider-container {
  position: relative;
  height: 40px;
  padding: 0 20px;
  margin-top: 8px;
}

.slider-track {
  position: relative;
  height: 6px;
  background: linear-gradient(to right, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
  border-radius: 3px;
  cursor: pointer;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.slider-handle {
  position: absolute;
  top: 50%;
  width: 22px;
  height: 22px;
  background: #1890ff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
  border: 2px solid white;
}

.slider-handle:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 3px 8px rgba(24, 144, 255, 0.4);
}

.slider-tooltip {
  position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(24, 144, 255, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 图表类型选择器样式 */
.chart-type-selector {
  display: flex;
  gap: 12px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(240, 249, 255, 0.95));
  padding: 16px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
  position: relative;
}

.chart-type-selector::before {
  content: "CHART TYPE";
  position: absolute;
  top: -8px;
  left: 16px;
  background: white;
  padding: 0 8px;
  font-size: 11px;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.type-button {
  padding: 10px 18px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: white;
  color: #555;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.type-button:hover {
  background: rgba(24, 144, 255, 0.03);
  border-color: rgba(24, 144, 255, 0.4);
  color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(24, 144, 255, 0.15);
}

.type-button.active {
  background: rgba(24, 144, 255, 0.12);
  border-color: rgba(24, 144, 255, 0.6);
  color: #1890ff;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

/* 数据类别选择器样式 */
.category-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(240, 249, 255, 0.95));
  padding: 16px;
  border-radius: 10px;
  width: 100%;
  box-sizing: border-box;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  margin-bottom: 16px;
}

.category-selector::before {
  content: "DATA CATEGORY";
  position: absolute;
  top: -8px;
  left: 16px;
  background: white;
  padding: 0 8px;
  font-size: 11px;
  color: #666;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.category-button {
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: white;
  color: #555;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 100px;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.category-button:hover {
  background: rgba(24, 144, 255, 0.03);
  border-color: rgba(24, 144, 255, 0.4);
  color: #1890ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(24, 144, 255, 0.15);
}

.category-button.active {
  background: rgba(24, 144, 255, 0.12);
  border-color: rgba(24, 144, 255, 0.6);
  color: #1890ff;
  font-weight: 600;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
}

/* 图表容器样式 */
.chart-container {
  flex: 1;
  min-height: 300px;
  background: white;
  border-radius: 10px;
  padding: 20px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-top: 8px;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .time-series-chart-panel {
    height: 75vh;
  }

  .category-selector {
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 12px;
  }

  .chart-type-selector {
    padding: 12px;
  }
  
  .category-selector::before,
  .chart-type-selector::before {
    font-size: 10px;
  }

  .category-button, .type-button {
    min-width: 80px;
    max-width: 120px;
    font-size: 12px;
    padding: 6px 10px;
  }

  .chart-type-selector, .category-selector, .time-slider {
    padding: 10px;
    gap: 8px;
  }
  
  .chart-type-selector::before,
  .category-selector::before,
  .time-slider::before {
    font-size: 9px;
    top: -7px;
    left: 12px;
    padding: 0 6px;
  }
  
  .chart-container {
    padding: 15px;
    min-height: 250px;
  }
  
  .slider-container {
    padding: 0 10px;
    margin-top: 6px;
  }
  
  .slider-handle {
    width: 18px;
    height: 18px;
  }
  
  .slider-tooltip {
    font-size: 11px;
    padding: 4px 8px;
  }
}
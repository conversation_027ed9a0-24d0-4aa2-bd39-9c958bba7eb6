/* 经济热点图层基础弹出窗口样式 */
.economic-popup {
  padding: 15px;
  max-width: 320px;
  border-radius: 8px;
  box-shadow: 0 3px 20px rgba(0, 0, 0, 0.25);
  font-size: 14px;
  background-color: white;
  animation: popup-fade-in 0.3s ease;
}

@keyframes popup-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹窗标题样式 */
.economic-popup h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

/* 弹出窗口文本样式 */
.economic-popup p {
  margin: 5px 0;
  color: #555;
  line-height: 1.4;
}

/* 弹出窗口强调文本 */
.economic-popup strong {
  color: #222;
  font-weight: 600;
}

/* 核心市场弹窗样式 */
.economic-popup.core-market {
  border-left: 4px solid #ff3b30;
  z-index: 2000 !important;
}

.economic-popup.core-market h3 {
  color: #d32f2f;
}

/* 补贴区域弹窗样式 */
.economic-popup.subsidy-area {
  border-left: 4px solid #007aff;
  z-index: 2000 !important;
}

.economic-popup.subsidy-area h3 {
  color: #0062cc;
}

/* 详细数据表格 */
.economic-popup-table {
  width: 100%;
  margin: 8px 0;
  border-collapse: collapse;
  font-size: 12px;
}

.economic-popup-table th,
.economic-popup-table td {
  padding: 6px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.economic-popup-table th {
  font-weight: 600;
  color: #555;
  font-size: 11px;
  text-transform: uppercase;
}

/* 数据可视化条 */
.data-bar-container {
  height: 8px;
  background: #eee;
  border-radius: 4px;
  margin: 8px 0;
  overflow: hidden;
}

.data-bar {
  height: 100%;
  border-radius: 4px;
}

.data-bar.high {
  background: linear-gradient(to right, #ff9500, #ff3b30);
}

.data-bar.medium {
  background: linear-gradient(to right, #ffcc00, #ff9500);
}

.data-bar.low {
  background: linear-gradient(to right, #5ac8fa, #4cd964);
}

/* 经济热点图层大头钉标记样式 - 增强可视性 */
.economic-pin-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-origin: bottom center;
  z-index: 1000 !important; /* 确保标记显示在其他元素之上 */
  pointer-events: auto !important;
}

.economic-pin-marker .pin-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: markerPulse 2s infinite;
}

/* 核心市场大头钉 - 红色 */
.economic-pin-marker .pin {
  width: 36px; /* 增大尺寸 */
  height: 36px;
  border-radius: 50% 50% 50% 0;
  background: #ff3b30; /* 鲜红色 */
  position: relative;
  transform: rotate(-45deg);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 4px rgba(255, 255, 255, 0.4); /* 增强阴影和光晕 */
  border: 2px solid rgba(255, 255, 255, 0.9); /* 白色边框增加对比度 */
  transition: all 0.3s ease;
}

.economic-pin-marker .pin::after {
  content: '';
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: white;
  position: absolute;
  top: 9px;
  left: 9px;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.2);
}

/* 补贴区域大头钉 - 蓝色 */
.economic-pin-marker.subsidy-area .pin {
  background: #007aff; /* 更鲜明的蓝色 */
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 4px rgba(255, 255, 255, 0.4);
}

/* 标签样式增强 */
.economic-pin-marker .pin-label {
  background: rgba(255, 255, 255, 0.95);
  color: #222;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: bold;
  margin-top: 2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 1100;
  white-space: nowrap;
  border: 1px solid #ddd;
  max-width: 140px;
  text-overflow: ellipsis;
  overflow: hidden;
  pointer-events: none;
}

/* 鼠标悬停效果 */
.economic-pin-marker:hover {
  z-index: 2000 !important;
}

.economic-pin-marker:hover .pin {
  transform: rotate(-45deg) scale(1.1);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.5);
}

.economic-pin-marker:hover .pin-label {
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

/* 脉动动画 */
@keyframes markerPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 4px rgba(255, 255, 255, 0.4);
  }
  50% {
    transform: scale(1.08);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.5), 0 0 0 6px rgba(255, 255, 255, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 4px rgba(255, 255, 255, 0.4);
  }
}

/* 服务半径圈样式增强 */
.economic-service-circle {
  animation: fadeIn 1s ease-in-out;
  z-index: 800 !important; /* 确保圆圈显示在其他元素之下，但在标记之上 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* 脉动边框效果 */
.economic-service-circle-pulse {
  animation: circlePulse 3s infinite ease-in-out;
}

@keyframes circlePulse {
  0% { stroke-width: 1; stroke-opacity: 0.5; }
  50% { stroke-width: 2; stroke-opacity: 0.7; }
  100% { stroke-width: 1; stroke-opacity: 0.5; }
}

/* 经济热点UI容器 - 控制所有相关UI元素 */
.economic-hotspot-ui-container {
  position: relative;
  z-index: 1000;
}

/* 模型切换按钮组 */
.model-toggle-container {
  margin-top: 15px;
  padding-top: 12px;
  border-top: 1px solid #eee;
}

.dark-mode .model-toggle-container {
  border-top-color: #444;
}

.model-toggle-label {
  font-size: 13px;
  color: #444;
  margin-bottom: 8px;
  font-weight: 500;
}

.dark-mode .model-toggle-label {
  color: #ddd;
}

.model-toggle-buttons {
  display: flex;
  gap: 8px;
}

.model-toggle-btn {
  flex: 1;
  padding: 8px 12px;
  border-radius: 6px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  color: #444;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode .model-toggle-btn {
  background: #333;
  border-color: #444;
  color: #ddd;
}

.model-toggle-btn:hover {
  background: #eaeaea;
  border-color: #ccc;
}

.dark-mode .model-toggle-btn:hover {
  background: #3a3a3a;
  border-color: #555;
}

.model-toggle-btn.active {
  background: #007aff;
  border-color: #0062cc;
  color: white;
}

.dark-mode .model-toggle-btn.active {
  background: #0062cc;
  border-color: #004999;
}

.model-toggle-btn.active:hover {
  background: #0062cc;
}

.dark-mode .model-toggle-btn.active:hover {
  background: #004999;
}

/* 设置按钮样式 */
.settings-toggle-button {
  position: absolute;
  bottom: 180px;
  right: 20px;
  width: 36px;
  height: 36px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #ddd;
  color: #444;
  z-index: 1010;
}

.settings-toggle-button:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.settings-toggle-button.active {
  background: #007aff;
  color: white;
  border-color: #0062cc;
}

.dark-mode .settings-toggle-button {
  background: #333;
  border-color: #555;
  color: #eee;
}

.dark-mode .settings-toggle-button.active {
  background: #0062cc;
  color: white;
  border-color: #007aff;
}

/* 经济热点图例容器 */
.economic-hotspot-legend {
  position: absolute;
  bottom: 10px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px;
  border-radius: 10px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.25);
  z-index: 1500;
  font-size: 12px;
  max-width: 300px;
  border: 1px solid #ddd;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.economic-hotspot-legend:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.dark-mode .economic-hotspot-legend {
  background: rgba(40, 40, 40, 0.95);
  color: #eee;
  border: 1px solid #555;
}

.economic-hotspot-legend h3 {
  margin: 0 0 10px 0;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  color: #333;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.dark-mode .economic-hotspot-legend h3 {
  color: #fff;
  border-bottom-color: #555;
}

.economic-hotspot-legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 5px 0;
}

.legend-marker-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.legend-marker {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.legend-marker .legend-pin {
  width: 18px;
  height: 18px;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg);
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.legend-marker.pin-core .legend-pin {
  background: #FF3B30;
}

.legend-marker.pin-subsidy .legend-pin {
  background: #5AC8FA;
}

.legend-marker.pin-minor .legend-pin {
  background: #FF9500;
}

.legend-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 经济热点帮助模态框样式 */
.economic-help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.help-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 700px;
  max-height: 85vh;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.4s ease;
}

.dark-mode .help-modal-content {
  background: #222;
  color: #eee;
  border: 1px solid #444;
}

.help-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.dark-mode .help-header {
  border-bottom-color: #444;
}

.help-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.dark-mode .help-header h2 {
  color: #fff;
}

.help-content {
  padding: 20px;
  overflow-y: auto;
  flex-grow: 1;
}

.help-section {
  margin-bottom: 24px;
}

.help-section h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #333;
}

.dark-mode .help-section h3 {
  color: #fff;
}

.help-section h4 {
  margin: 12px 0 5px 0;
  font-size: 16px;
  color: #444;
}

.dark-mode .help-section h4 {
  color: #ddd;
}

.help-section p, .help-section li {
  margin: 8px 0;
  line-height: 1.5;
  color: #555;
}

.dark-mode .help-section p, .dark-mode .help-section li {
  color: #bbb;
}

.help-section ul {
  padding-left: 25px;
}

.model-explanation {
  background: rgba(0, 0, 0, 0.03);
  padding: 15px;
  border-radius: 8px;
  margin: 12px 0;
}

.dark-mode .model-explanation {
  background: rgba(255, 255, 255, 0.05);
}

.help-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
}

.dark-mode .help-footer {
  border-top-color: #444;
}

.help-ok-button {
  padding: 8px 20px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.help-ok-button:hover {
  background: #0062cc;
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(0, 98, 204, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.legend-description {
  flex: 1;
  line-height: 1.4;
}

.legend-note {
  margin-top: 10px;
  font-style: italic;
  color: #666;
  font-size: 11px;
  text-align: center;
  padding-top: 5px;
  border-top: 1px dashed #eee;
}

.dark-mode .legend-note {
  color: #aaa;
  border-top-color: #444;
}

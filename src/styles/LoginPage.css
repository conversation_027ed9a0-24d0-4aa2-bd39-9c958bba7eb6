/* LoginPage.css - 按照设计图重构的现代化登录界面 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS变量定义 */
:root {
  --primary-blue: #4A90E2;
  --secondary-purple: #7B68EE;
  --accent-light: #9CA3AF;
  --background-dark: #0F0F23;
  --background-gradient-start: #1E293B;
  --background-gradient-middle: #334155;
  --background-gradient-end: #475569;
  --card-bg: rgba(255, 255, 255, 0.1);
  --card-border: rgba(255, 255, 255, 0.2);
  --input-bg: rgba(255, 255, 255, 0.05);
  --input-border: rgba(255, 255, 255, 0.15);
  --input-border-focus: rgba(255, 255, 255, 0.3);
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);
  --button-primary: #4A90E2;
  --button-secondary: rgba(255, 255, 255, 0.1);
  --error-color: #EF4444;
  --success-color: #10B981;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --shadow-card: 0 20px 40px rgba(0, 0, 0, 0.3);
  --shadow-input: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主容器 - 深蓝紫色渐变背景 */
.login-page {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, 
    #0F0F23 0%, 
    #1E293B 25%, 
    #334155 50%, 
    #475569 75%, 
    #64748B 100%
  );
  font-family: 'Inter', sans-serif;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 移除3D背景以简化设计 */
.login-background {
  display: none;
}

/* 浮动导航按钮 - 简化设计 */
.floating-nav-buttons {
  position: fixed;
  top: 30px;
  left: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
}

.floating-back-button,
.floating-language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  backdrop-filter: blur(20px);
  pointer-events: auto;
  box-shadow: var(--shadow-card);
}

.floating-back-button:hover,
.floating-language-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--input-border-focus);
  transform: translateY(-2px);
}

/* 语言下拉菜单 */
.floating-language-dropdown {
  position: relative;
  pointer-events: auto;
}

.dropdown-arrow {
  font-size: 10px;
  transition: transform var(--transition);
  margin-left: 6px;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.language-dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 140px;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
  z-index: 1150;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.language-option {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.language-option:hover {
  background: rgba(255, 255, 255, 0.1);
}

.language-option.active {
  background: rgba(255, 255, 255, 0.15);
  color: var(--button-primary);
}

.flag-icon {
  font-size: 14px;
}

/* 主要内容区域 */
.login-container {
  position: relative;
  z-index: 5;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.login-content {
  width: 100%;
}

/* 表单容器 - 半透明卡片设计 */
.form-section {
  width: 100%;
}

.form-container {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 24px;
  padding: 40px 32px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-card);
  position: relative;
  overflow: hidden;
}

/* 移除进度条以简化设计 */
.form-progress {
  display: none;
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;
}

/* 顶部图标 */
.form-header::before {
  content: '';
  width: 60px;
  height: 60px;
  background: var(--button-primary);
  border-radius: 16px;
  display: block;
  margin: 0 auto 20px;
  position: relative;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 32px;
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  letter-spacing: -0.02em;
}

.form-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 400;
}

/* 表单样式 */
.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.input-wrapper {
  position: relative;
  width: 100%;
}

.form-group input,
.language-select {
  width: 100%;
  height: 52px;
  padding: 0 16px 0 48px;
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 400;
  transition: var(--transition);
  box-shadow: var(--shadow-input);
}

.form-group input:focus,
.language-select:focus {
  outline: none;
  border-color: var(--input-border-focus);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

.language-select {
  padding-left: 48px;
  appearance: none;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="rgba(255,255,255,0.5)" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
  background-repeat: no-repeat;
  background-position: right 16px center;
}

.language-select option {
  background: var(--background-dark);
  color: var(--text-primary);
}

/* 输入框图标 */
.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  pointer-events: none;
  transition: var(--transition);
}

.form-group input:focus + .input-icon {
  color: var(--button-primary);
}

/* 密码切换按钮 */
.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: var(--transition);
}

.password-toggle:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.password-strength-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.password-strength-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 2px;
  transition: var(--transition);
}

.password-strength.weak .password-strength-bar::after {
  width: 33%;
  background: var(--error-color);
}

.password-strength.medium .password-strength-bar::after {
  width: 66%;
  background: #F59E0B;
}

.password-strength.strong .password-strength-bar::after {
  width: 100%;
  background: var(--success-color);
}

.password-strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 60px;
}

.password-strength.weak .password-strength-text {
  color: var(--error-color);
}

.password-strength.medium .password-strength-text {
  color: #F59E0B;
}

.password-strength.strong .password-strength-text {
  color: var(--success-color);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  font-size: 14px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--text-secondary);
  font-weight: 400;
}

.checkbox-label:hover {
  color: var(--text-primary);
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  margin: 0;
  padding: 0;
  accent-color: var(--button-primary);
}

.forgot-password {
  background: none;
  border: none;
  color: var(--button-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.forgot-password:hover {
  color: var(--text-primary);
  text-decoration: underline;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 52px;
  background: var(--button-primary);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.submit-button:hover:not(:disabled) {
  background: #3A7BD5;
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.submit-button:active:not(:disabled) {
  transform: translateY(0);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分隔线 */
.form-divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
  gap: 16px;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
}

.divider-text {
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 500;
}

/* 社交登录按钮 */
.social-login {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.social-button {
  flex: 1;
  height: 52px;
  background: var(--button-secondary);
  border: 1px solid var(--input-border);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  backdrop-filter: blur(10px);
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--input-border-focus);
  transform: translateY(-1px);
}

/* 切换按钮 */
.form-switch {
  text-align: center;
}

.switch-button {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: underline;
}

.switch-button:hover {
  color: var(--text-primary);
}

/* 错误信息 */
.error-message {
  color: var(--error-color);
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
  animation: slideInError 0.3s ease-out;
}

.error-message.field-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin: 16px 0;
}

.form-group input.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-group input.success {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单动画 */
.form-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 中文字体优化 */
.login-page[data-lang="zh"] {
  font-family: 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.login-page[data-lang="zh"] .form-group label,
.login-page[data-lang="zh"] .form-title,
.login-page[data-lang="zh"] .form-subtitle {
  letter-spacing: 0.02em;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .login-container {
    max-width: 100%;
    padding: 16px;
  }

  .floating-nav-buttons {
    top: 20px;
    left: 16px;
    right: 16px;
  }

  .floating-back-button,
  .floating-language-toggle {
    padding: 10px 14px;
    font-size: 13px;
  }

  .form-container {
    padding: 32px 24px;
    border-radius: 20px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group input,
  .language-select,
  .submit-button,
  .social-button {
    height: 48px;
  }

  .social-button {
    font-size: 13px;
  }
}

@media screen and (max-width: 480px) {
  .login-container {
    padding: 12px;
  }

  .floating-nav-buttons {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .floating-back-button,
  .floating-language-toggle {
    padding: 8px 12px;
    font-size: 12px;
  }

  .form-container {
    padding: 24px 20px;
    border-radius: 16px;
  }

  .form-title {
    font-size: 22px;
  }

  .form-group input,
  .language-select,
  .submit-button {
    height: 44px;
    font-size: 15px;
  }

  .social-login {
    flex-direction: column;
  }

  .form-options {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}

/* 滚动条样式 */
.form-container::-webkit-scrollbar {
  width: 4px;
}

.form-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.form-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}



.statistics-section {
  margin: 20px 0;
  padding: 15px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.chart-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  width: 100%;
  justify-content: space-between;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.chart-card {
  flex: 1;
  min-width: 300px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  max-width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.chart-card.full-width {
  flex-basis: 100%;
  width: 100%;
  max-width: 100%;
}

.chart-description {
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.chart-error, .no-chart-data {
  padding: 20px;
  text-align: center;
  color: #d32f2f;
  background-color: #ffebee;
  border-radius: 4px;
  margin: 20px 0;
}

.no-chart-data {
  color: #ff9800;
  background-color: #fff3e0;
}

.custom-tooltip {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.section-icon {
  margin-right: 8px;
  font-style: normal;
}

/* 修改 Recharts 容器样式 */
.recharts-responsive-container {
  width: 100% !important;
  max-width: 100% !important;
  height: auto !important;
  min-height: 300px !important;
  overflow: hidden !important;
}

/* 确保图表内部元素不会溢出 */
.recharts-wrapper,
.recharts-surface {
  max-width: 100% !important;
  width: auto !important;
  overflow: hidden !important;
}

/* 针对特定图表的样式 */
.investment-chart,
.production-capacity-chart {
  max-width: 100% !important;
  overflow: hidden !important;
  width: 100% !important;
}
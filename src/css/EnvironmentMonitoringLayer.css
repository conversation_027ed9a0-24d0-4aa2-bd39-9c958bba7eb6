.environment-layer-button {
  position: fixed;
  bottom: 80px;
  right: 10px;
  padding: 10px 15px;
  background-color: white;
  color: #4CAF50;
  border: 2px solid #4CAF50;
  border-radius: 4px;
  cursor: pointer;
  z-index: 1000;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.environment-layer-button:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: translateY(-2px);
}

.environment-layer-button.active {
  background-color: #4CAF50;
  color: white;
}

.environment-controls {
  position: fixed;
  bottom: 125px;
  right: 10px;
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  padding: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transform-origin: bottom right;
  transform: scale(0);
  opacity: 0;
  transition: all 0.3s ease;
}

.environment-controls.show {
  transform: scale(1);
  opacity: 1;
}

.environment-type-selector {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  padding: 8px;
  z-index: 1000;
  justify-content: center;
  max-width: 90%;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  gap: 8px;
}

.environment-type-selector.active {
  opacity: 1;
  visibility: visible;
}

.env-type-btn {
  background-color: white;
  color: #333;
  border: 2px solid #2196F3;
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.env-type-btn:hover {
  background-color: rgba(33, 150, 243, 0.1);
  transform: translateY(-2px);
}

.env-type-btn.active {
  background-color: #2196F3;
  color: white;
}

.environment-legend {
  position: fixed;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  padding: 10px;
  z-index: 1000;
  max-width: 90%;
  margin: 0 auto;
}

.environment-legend h4 {
  margin: 0 0 8px 0;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  margin: 0 5px 5px 0;
  font-size: 12px;
}

.color-box {
  display: inline-block;
  width: 15px;
  height: 15px;
  margin-right: 5px;
  border-radius: 3px;
}

@media (max-width: 768px) {
  .environment-legend {
    max-width: 95%;
  }
  
  .legend-items {
    justify-content: space-around;
  }
  
  .legend-item {
    font-size: 10px;
    margin: 2px;
  }
  
  .color-box {
    width: 12px;
    height: 12px;
  }
}

/* 将environment-layer-button样式修改为与其他地图按钮一致 */
.map-side-controls .toggle-environment-button {
  position: relative;
  bottom: auto;
  right: auto;
  padding: 8px 16px;
  background-color: white;
  color: #333;
  border: 2px solid #4CAF50;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 10px;
  display: block;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.map-side-controls .toggle-environment-button:hover {
  background-color: rgba(76, 175, 80, 0.1);
  transform: translateY(-2px);
}

.map-side-controls .toggle-environment-button.active {
  background-color: #4CAF50;
  color: white;
} 
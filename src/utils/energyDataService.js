/**
 * Energy Data Service for EIA API Integration
 * Handles fetching and processing energy data from the U.S. Energy Information Administration
 */

const EIA_API_KEY = 'YzyXapQ0gZtmjVCaQDBcBa9ZQzcCWJxa2xU2nxkB';
const EIA_BASE_URL = 'https://api.eia.gov/v2/electricity/retail-sales/data/';

// Energy data types configuration
export const ENERGY_DATA_TYPES = {
  customers: {
    id: 'customers',
    name: 'Number of Ultimate Customers',
    description: 'Total number of electricity customers',
    unit: 'customers',
    color: '#3B82F6' // Blue
  },
  price: {
    id: 'price',
    name: 'Average Price of Electricity',
    description: 'Average price per kWh to ultimate customers',
    unit: 'cents/kWh',
    color: '#10B981' // Green
  },
  revenue: {
    id: 'revenue',
    name: 'Revenue from Sales',
    description: 'Total revenue from electricity sales',
    unit: 'thousand dollars',
    color: '#F59E0B' // Yellow
  },
  sales: {
    id: 'sales',
    name: 'Megawatthours Sold',
    description: 'Total electricity sold to ultimate customers',
    unit: 'MWh',
    color: '#EF4444' // Red
  }
};

// State codes for filtering
export const US_STATE_CODES = {
  'AL': 'Alabama', 'AK': 'Alaska', 'AZ': 'Arizona', 'AR': 'Arkansas', 'CA': 'California',
  'CO': 'Colorado', 'CT': 'Connecticut', 'DE': 'Delaware', 'FL': 'Florida', 'GA': 'Georgia',
  'HI': 'Hawaii', 'ID': 'Idaho', 'IL': 'Illinois', 'IN': 'Indiana', 'IA': 'Iowa',
  'KS': 'Kansas', 'KY': 'Kentucky', 'LA': 'Louisiana', 'ME': 'Maine', 'MD': 'Maryland',
  'MA': 'Massachusetts', 'MI': 'Michigan', 'MN': 'Minnesota', 'MS': 'Mississippi', 'MO': 'Missouri',
  'MT': 'Montana', 'NE': 'Nebraska', 'NV': 'Nevada', 'NH': 'New Hampshire', 'NJ': 'New Jersey',
  'NM': 'New Mexico', 'NY': 'New York', 'NC': 'North Carolina', 'ND': 'North Dakota', 'OH': 'Ohio',
  'OK': 'Oklahoma', 'OR': 'Oregon', 'PA': 'Pennsylvania', 'RI': 'Rhode Island', 'SC': 'South Carolina',
  'SD': 'South Dakota', 'TN': 'Tennessee', 'TX': 'Texas', 'UT': 'Utah', 'VT': 'Vermont',
  'VA': 'Virginia', 'WA': 'Washington', 'WV': 'West Virginia', 'WI': 'Wisconsin', 'WY': 'Wyoming'
};

// Sector codes for filtering
export const SECTOR_CODES = {
  'RES': 'Residential',
  'COM': 'Commercial',
  'IND': 'Industrial',
  'TRA': 'Transportation',
  'ALL': 'All Sectors'
};

/**
 * Fetch energy data from EIA API
 * @param {Object} options - Configuration options
 * @param {Array} options.dataTypes - Array of data types to fetch ['customers', 'price', 'revenue', 'sales']
 * @param {string} options.stateId - State code filter (optional)
 * @param {string} options.sectorId - Sector code filter (optional)
 * @param {number} options.offset - Data offset for pagination (default: 0)
 * @param {number} options.length - Number of records to fetch (default: 5000)
 * @returns {Promise<Object>} Processed energy data
 */
export const fetchEnergyData = async (options = {}) => {
  const {
    dataTypes = ['customers', 'price', 'revenue', 'sales'],
    stateId = '',
    sectorId = '',
    offset = 0,
    length = 5000
  } = options;

  try {
    // Build API URL - Let's test with a simpler approach first
    const url = new URL(EIA_BASE_URL);
    url.searchParams.append('api_key', EIA_API_KEY);
    url.searchParams.append('frequency', 'monthly');

    // Add data types - use the correct format based on EIA documentation
    dataTypes.forEach((dataType) => {
      url.searchParams.append('data[]', dataType);
    });

    // Add sorting
    url.searchParams.append('sort[0][column]', 'period');
    url.searchParams.append('sort[0][direction]', 'desc');

    // Add pagination
    url.searchParams.append('offset', offset.toString());
    url.searchParams.append('length', length.toString());

    // Add filters if provided - use the correct format
    if (stateId) {
      url.searchParams.append('facets[stateid][]', stateId);
    }
    if (sectorId) {
      url.searchParams.append('facets[sectorid][]', sectorId);
    }

    // If no filters are provided, let's get data for all states and sectors
    // This should give us a broader dataset to work with
    if (!stateId && !sectorId) {
      console.log('No filters provided, fetching data for all states and sectors');
    }

    console.log('Fetching energy data from:', url.toString());

    const response = await fetch(url.toString());

    if (!response.ok) {
      console.error(`EIA API request failed: ${response.status} ${response.statusText}`);
      throw new Error(`EIA API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Raw EIA API response:', data);

    if (!data.response || !data.response.data) {
      console.error('Invalid response format from EIA API:', data);
      throw new Error('Invalid response format from EIA API');
    }

    console.log('Raw data records count:', data.response.data.length);
    console.log('Sample raw data records:', data.response.data.slice(0, 3));

    const processedData = processEnergyData(data.response.data, dataTypes);
    console.log('Processed energy data:', processedData);

    return processedData;
  } catch (error) {
    console.error('Error fetching energy data:', error);
    throw error;
  }
};

/**
 * Process raw EIA API data into a more usable format
 * @param {Array} rawData - Raw data from EIA API
 * @param {Array} dataTypes - Data types that were requested
 * @returns {Object} Processed data grouped by state and period
 */
const processEnergyData = (rawData, dataTypes) => {
  const processedData = {
    byState: {},
    byPeriod: {},
    summary: {},
    metadata: {
      totalRecords: rawData.length,
      dataTypes: dataTypes,
      lastUpdated: new Date().toISOString()
    }
  };

  console.log('Processing', rawData.length, 'raw data records');

  // Process each record
  rawData.forEach((record, index) => {
    const stateId = record.stateid;
    const stateName = record.stateDescription || record.statename || US_STATE_CODES[stateId] || stateId;
    const period = record.period;
    const sectorId = record.sectorid;
    const sectorName = record.sectorName || record.sectorname || SECTOR_CODES[sectorId] || sectorId;

    if (index < 5) {
      console.log(`Processing record ${index}:`, {
        stateId, stateName, period, sectorId, sectorName,
        fullRecord: record,
        dataValues: dataTypes.map(dt => ({ [dt]: record[dt] }))
      });
    }

    // Skip records with missing essential data
    if (!stateId || !period || !sectorId) {
      console.log(`Skipping record ${index} due to missing essential data:`, { stateId, period, sectorId });
      return;
    }

    // Initialize state data if not exists
    if (!processedData.byState[stateId]) {
      processedData.byState[stateId] = {
        stateId,
        stateName,
        sectors: {},
        totals: {}
      };
    }

    // Initialize sector data if not exists
    if (!processedData.byState[stateId].sectors[sectorId]) {
      processedData.byState[stateId].sectors[sectorId] = {
        sectorId,
        sectorName,
        periods: {}
      };
    }

    // Initialize period data if not exists
    if (!processedData.byState[stateId].sectors[sectorId].periods[period]) {
      processedData.byState[stateId].sectors[sectorId].periods[period] = {
        period,
        data: {}
      };
    }

    // Add data values
    dataTypes.forEach(dataType => {
      const value = record[dataType];
      if (value !== null && value !== undefined && value !== '') {
        const numericValue = parseFloat(value);
        if (!isNaN(numericValue)) {
          // Get unit from the API response (e.g., 'sales-units') or fallback to our config
          const unitField = `${dataType}-units`;
          const unit = record[unitField] || ENERGY_DATA_TYPES[dataType]?.unit || '';

          processedData.byState[stateId].sectors[sectorId].periods[period].data[dataType] = {
            value: numericValue,
            unit: unit,
            type: dataType
          };

          if (index < 5) {
            console.log(`  Added ${dataType} value: ${numericValue} ${unit} for ${stateId}-${sectorId}-${period}`);
          }
        } else {
          if (index < 5) {
            console.log(`  Skipped ${dataType} - invalid numeric value: ${value}`);
          }
        }
      } else {
        if (index < 5) {
          console.log(`  Skipped ${dataType} - null/undefined/empty value: ${value}`);
        }
      }
    });

    // Initialize period grouping if not exists
    if (!processedData.byPeriod[period]) {
      processedData.byPeriod[period] = {
        period,
        states: {}
      };
    }

    if (!processedData.byPeriod[period].states[stateId]) {
      processedData.byPeriod[period].states[stateId] = {
        stateId,
        stateName,
        sectors: {}
      };
    }

    processedData.byPeriod[period].states[stateId].sectors[sectorId] = 
      processedData.byState[stateId].sectors[sectorId].periods[period];
  });

  // Calculate summary statistics
  calculateSummaryStatistics(processedData, dataTypes);

  return processedData;
};

/**
 * Calculate summary statistics for the processed data
 * @param {Object} processedData - Processed data object
 * @param {Array} dataTypes - Data types to calculate summaries for
 */
const calculateSummaryStatistics = (processedData, dataTypes) => {
  const summary = processedData.summary;

  dataTypes.forEach(dataType => {
    summary[dataType] = {
      total: 0,
      average: 0,
      min: Infinity,
      max: -Infinity,
      count: 0,
      unit: ENERGY_DATA_TYPES[dataType]?.unit || ''
    };

    let sum = 0;
    let count = 0;
    let unitFromData = '';

    // Iterate through all states and sectors to calculate totals
    Object.values(processedData.byState).forEach(stateData => {
      Object.values(stateData.sectors).forEach(sectorData => {
        Object.values(sectorData.periods).forEach(periodData => {
          const dataPoint = periodData.data[dataType];
          if (dataPoint && typeof dataPoint.value === 'number') {
            const value = dataPoint.value;
            sum += value;
            count++;
            summary[dataType].min = Math.min(summary[dataType].min, value);
            summary[dataType].max = Math.max(summary[dataType].max, value);

            // Use unit from actual data if available
            if (dataPoint.unit && !unitFromData) {
              unitFromData = dataPoint.unit;
            }
          }
        });
      });
    });

    summary[dataType].total = sum;
    summary[dataType].average = count > 0 ? sum / count : 0;
    summary[dataType].count = count;

    // Use unit from data if available, otherwise use default
    if (unitFromData) {
      summary[dataType].unit = unitFromData;
    }

    // Handle case where no data was found
    if (summary[dataType].min === Infinity) {
      summary[dataType].min = 0;
    }
    if (summary[dataType].max === -Infinity) {
      summary[dataType].max = 0;
    }

    console.log(`Summary for ${dataType}:`, summary[dataType]);
  });
};

/**
 * Get the latest data for a specific state and data type
 * @param {Object} processedData - Processed energy data
 * @param {string} stateId - State code
 * @param {string} dataType - Data type
 * @param {string} sectorId - Sector code (optional, defaults to 'ALL')
 * @returns {Object|null} Latest data point or null if not found
 */
export const getLatestStateData = (processedData, stateId, dataType, sectorId = 'ALL') => {
  const stateData = processedData.byState[stateId];
  if (!stateData || !stateData.sectors[sectorId]) {
    return null;
  }

  const periods = Object.keys(stateData.sectors[sectorId].periods).sort().reverse();
  
  for (const period of periods) {
    const periodData = stateData.sectors[sectorId].periods[period];
    if (periodData.data[dataType]) {
      return {
        ...periodData.data[dataType],
        period,
        stateId,
        stateName: stateData.stateName,
        sectorId,
        sectorName: stateData.sectors[sectorId].sectorName
      };
    }
  }

  return null;
};

// Test function to check API connectivity
export const testEIAAPI = async () => {
  try {
    // Test with a simple request first
    const testUrl = `https://api.eia.gov/v2/electricity/retail-sales/data/?api_key=${EIA_API_KEY}&data[]=sales&facets[stateid][]=CA&facets[sectorid][]=RES&frequency=monthly&length=5`;
    console.log('Testing EIA API with URL:', testUrl);

    const response = await fetch(testUrl);
    console.log('API Response status:', response.status);
    console.log('API Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error:', response.status, response.statusText, errorText);
      return null;
    }

    const data = await response.json();
    console.log('Test API Response:', data);

    // Check if we have actual data
    if (data.response && data.response.data && data.response.data.length > 0) {
      console.log('✅ API test successful - received', data.response.data.length, 'records');
      console.log('Sample record:', data.response.data[0]);
    } else {
      console.log('⚠️ API responded but no data found');
    }

    return data;
  } catch (error) {
    console.error('Test API Error:', error);
    return null;
  }
};

export default {
  fetchEnergyData,
  getLatestStateData,
  testEIAAPI,
  ENERGY_DATA_TYPES,
  US_STATE_CODES,
  SECTOR_CODES
};

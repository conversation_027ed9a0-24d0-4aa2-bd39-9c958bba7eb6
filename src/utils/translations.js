// 多语言文本对象
const translations = {
  en: {
    high: 'High',
    low: 'Low',
    hideLaborTrend: 'Hide Labor Trend Layer',
    showLaborTrend: 'Show Labor Trend Layer',
    hideTransport: 'Hide Transport Layer',
    showTransport: 'Show Transport Layer',
    backToMap: 'Back to Map',
    parkLayout: 'Park Layout',
    loading: 'Loading park information...',
    notFound: 'Park information not found',
    cannotLoad: 'Cannot load ID',
    map: 'Map',
    overview: 'Park Overview',
    laborTrends: 'Labor Trends', // 修正拼写错误 "Labo Trends"
    laborEconomics: 'Labor Economics',
    majorCustomers: 'Major Customers',
    basicInfo: 'Basic Information',
    province: 'Province',
    cityZoning: 'City Zoning',
    saleMethod: 'Sale Method',
    managementStatus: 'Management Status',
    managementFee: 'Management Fee',
    settings: 'Settings',
    appearance: 'Appearance',
    language: 'Language',
    accountManagement: 'Account Management',
    units: 'Units',
    metric: 'Metric (m, m²)',
    imperial: 'Imperial (ft, ft²)',
    currency: 'Currency',
    myData: 'My Data',
    aboutUs: 'About Us',
    statusDescription: 'Status Description',
    currencyRates: 'Currency Rates',
    distanceInfo: 'Distance Information',
    nearestPort: 'Nearest Port',
    nearestAirport: 'Nearest Airport',
    nearestCity: 'Nearest City',
    infrastructure: 'Infrastructure',
    electricity: 'Electricity',
    waterSupply: 'Water Supply',
    reservoirCapacity: 'Reservoir Capacity',
    productionCapacity: 'Production Capacity',
    wasteWaterTreatment: 'Waste Water Treatment',
    treatmentCapacity: 'Treatment Capacity',
    road: 'Road',
    telephone: 'Telephone',
    internet: 'Internet',
    type: 'Type',
    security: 'Security',
    fireProtection: 'Fire Protection',
    nearbyFacilities: 'Nearby Facilities',
    banks: 'Banks',
    hospitals: 'Hospitals',
    gasStations: 'Gas Stations',
    restaurants: 'Restaurants',
    convenienceStores: 'Convenience Stores',
    shoppingCenters: 'Shopping Centers',
    otherFacilities: 'Other Facilities',
    contactInfo: 'Contact Information',
    website: 'Website',
    callCenter: 'Call Center',
    phoneNumbers: 'Phone Numbers',
    mobileNumbers: 'Mobile Numbers', // 添加缺失的翻译
    location: 'Location',
    coordinates: 'Coordinates',
    latitude: 'Latitude',
    longitude: 'Longitude',
    transportConnections: 'Transport Connections',
    parkHighlights: 'Park Highlights',
    richWaterResources: 'Rich Water Resources',
    stablePower: 'Stable Power Supply',
    convenientFacilities: 'Convenient Facilities',
    strategicLocation: 'Strategic Location',
    waterResourcesDesc: 'Has its own water supply system with a reservoir capacity of 36 million cubic meters, particularly suitable for industries that use a large amount of water.',
    stablePowerDesc: 'On-site power plant provides up to 670MW of electricity, with backup power from the provincial electricity authority.',
    convenientFacilitiesDesc: 'Surrounded by facilities like hospitals, shopping centers, restaurants, hotels, and apartments, very convenient for settling down.',
    strategicLocationDesc: 'Located near the future East-West Corridor project, just about 1 hour travel to the Cambodian border, ideal as a base for trading with Cambodia.',
    infoIncomplete: 'Park information is being updated',
    infoIncompleteDesc: 'The detailed information for this park has not yet been fully entered into the system. Please check back later or contact us for more information.',
    switchToEnglish: 'Switch to English',
    switchToChinese: 'Switch to Chinese',
    languageOptions: 'Language Options',
    english: 'English',
    chinese: 'Chinese',
    unknown: 'Unknown',
    transportRoutes: 'Transport Routes',
    industrialPark: 'Industrial Park',
    toPort: 'To Port',
    toAirport: 'To Airport',
    toCity: 'To City',
    resetMapView: 'Reset Map View',
    distance: 'Distance', // 添加缺失的翻译

    // 地图控件相关
    mapStyle: 'Map Style',
    displayMode: 'Display Mode',
    dayStyle: 'Day',
    nightStyle: 'Night',
    satelliteStyle: 'Satellite',
    dayStyleTooltip: 'Switch to day map',
    nightStyleTooltip: 'Switch to night map',
    satelliteStyleTooltip: 'Switch to satellite map',
    allLocations: 'All',
    portsOnly: 'Ports',
    citiesOnly: 'Cities',
    allLocationsTooltip: 'Show all locations',
    portsOnlyTooltip: 'Show ports only',
    citiesOnlyTooltip: 'Show cities only',
    placeMarkers: 'Place Markers',
    stopPlacingMarkers: 'Stop Placing Markers',
    clearAllMarkers: 'Clear All Markers',
    results: 'Results',
    sortAsc: 'Sort (asc)',
    sortDesc: 'Sort (desc)',

    // Custom marker related
    customMarker: 'Custom Marker',
    markerLabel: 'Marker Label',
    markerColor: 'Marker Color',
    saveMarker: 'Save',
    removeMarker: 'Remove Marker',
    copyToClipboard: 'Copy to Clipboard',

    // 劳动力趋势分析相关
    laborTrendAnalysis: 'Labor Trend Analysis',
    switchToCompareMode: 'Switch to Compare Mode',
    switchToTrendMode: 'Switch to Trend Mode',
    selectCompareYears: 'Select Compare Years',
    selectAll: 'Select All',
    clearAll: 'Clear All',
    pleaseSelectYears: 'Please select at least one year for comparison',
    chartType: 'Chart Type',
    lineChart: 'Line Chart',
    barChart: 'Bar Chart',
    dataCategory: 'Data Category',
    education: 'Education Level',
    employment: 'Employment Status',
    occupation: 'Occupation Distribution',
    industry: 'Industry Distribution',
    workingHours: 'Working Hours',
    selectMetrics: 'Select Metrics',
    pleaseSelectMetrics: 'Please select at least one metric to display chart',
    data: 'Data',
    laborDataComparison: 'Labor Data Comparison',
    dataLoadError: 'No labor trend data available.',
    laborMarketData: 'Labor Market Data', // 添加缺失的翻译

    // 劳动力数据图表相关
    laborMarketAnalysis: 'Labor Market Analysis',
    selectRegion: 'Select Region',
    regional_total: 'Regional Total',
    totalPopulation: 'Total Population (15+ years)',
    employedPersons: 'Employed Persons',
    unemploymentRate: 'Unemployment Rate',
    laborForceParticipationRate: 'Labor Force Participation Rate',
    employmentByIndustry: 'Employment by Industry',
    employmentByOccupation: 'Employment by Occupation',
    laborForceParticipation: 'Labor Force Participation',
    educationLevel: 'Education Level of Employed Persons',
    employmentStatus: 'Employment Status Distribution',
    marketInsights: 'Market Insights',
    genderComparison: 'Gender Comparison',
    male: 'Male',
    female: 'Female',
    dataSource: 'Data Source',
    laborDataSource: 'National Statistical Office of Thailand, Q4 2024',

    // 行业类别
    manufacturing: 'Manufacturing',
    wholesale_and_retail_trade: 'Wholesale and Retail Trade',
    accommodation_and_food_services: 'Accommodation and Food Services',
    transportation_and_warehousing: 'Transportation and Warehousing',
    construction: 'Construction',
    agriculture_forestry_and_fishing: 'Agriculture, Forestry, and Fishing',

    // 职业类别
    managers_senior_officials_legislators: 'Managers and Senior Officials',
    professionals_: 'Professionals',
    technicians_and_associate_professionals: 'Technicians and Associates',
    clerks: 'Clerks',
    service_workers_and_salespersons: 'Service and Sales Workers',
    craftsmen_and_related_workers: 'Craftsmen',
    plant_and_machine_operators_and_assemblers: 'Machine Operators',
    general_labor: 'General Labor',

    // 劳动力参与
    inLaborForce: 'In Labor Force',
    notInLaborForce: 'Not in Labor Force',

    // 教育水平
    no_education: 'No Education',
    primary_education: 'Primary Education',
    lower_secondary_education: 'Lower Secondary',
    upper_secondary_education_academic_track: 'Upper Secondary (Academic)',
    upper_secondary_education_vocational_track: 'Upper Secondary (Vocational)',
    higher_education_professional_track: 'Higher Education (Professional)',
    higher_education_education_track: 'Higher Education (Education)',

    // 就业状态
    employer: 'Employer',
    government_employee: 'Government Employee',
    private_employee: 'Private Employee',
    self_employed: 'Self-Employed',
    family_business_worker: 'Family Business Worker',

    currentLocation: 'Current Location',
    nearbyArea: 'Nearby Area',
    regionalTotalDescription: 'Showing overall labor data for Thailand Central Region',
    prachinBuriDescription: 'Labor data for 304 Industrial Park location',
    nearbyAreaDescription: 'Labor data for nearby areas, for reference',

    // 洞察文本
    laborMarketInsight1: 'The manufacturing sector employs the largest number of workers in this region, accounting for a significant portion of total employment.',
    laborMarketInsight2: 'The region has a moderate labor force participation rate with a relatively low unemployment rate.',
    laborMarketInsight3: 'A substantial proportion of the workforce has secondary education or higher, indicating a relatively skilled labor pool.',

    // 劳动力数据图表相关新增翻译
    manufacturingRatio: 'Manufacturing Employment Ratio',
    higherEducationRatio: 'Higher Education Ratio',
    otherLaborCategories: 'Other Labor Force Categories',
    agriNonAgriRatio: 'Agricultural vs Non-Agricultural Employment',
    agricultural_sector: 'Agricultural Sector',
    non_agricultural_sector: 'Non-Agricultural Sector',
    employment_by_sector: 'Employment by Sector',
    employmentDistribution: 'Employment Distribution',
    laborForceDetailedDistribution: 'Detailed Labor Force Distribution',
    unemployedPersons: 'Unemployed Persons',
    seasonallyUnemployed: 'Seasonally Unemployed',
    housework: 'Housework',
    studying: 'Studying',
    unableToWork: 'Unable to Work',
    sickOrDisabled: 'Sick/Disabled Persons',
    others: 'Others',

    // 地图样式切换器
    dayStyle: 'Day',
    nightStyle: 'Night',
    satelliteStyle: 'Satellite',

    // 设施搜索功能
    searchFacilities: 'Search Nearby Facilities',
    customSearchPlaceholder: 'Custom search...',
    searchResults: 'Search Results',
    noResultsFound: 'No results found',
    facilityType: 'Type',
    searchRadius: 'Search Radius',
    phone: 'Phone',
    website: 'Website',
    expand: 'Expand',
    collapse: 'Collapse',
    clearResults: 'Clear Results',

    // 设施类别
    amenity: 'Amenities',
    shop: 'Shops',
    tourism: 'Tourism',
    leisure: 'Leisure',
    public_transport: 'Transportation',

    // 具体设施类型
    restaurant: 'Restaurant',
    cafe: 'Cafe',
    bank: 'Bank',
    hospital: 'Hospital',
    school: 'School',
    pharmacy: 'Pharmacy',
    fuel: 'Gas Station',
    police: 'Police Station',
    supermarket: 'Supermarket',
    convenience: 'Convenience Store',
    mall: 'Shopping Mall',
    clothes: 'Clothing Store',
    hardware: 'Hardware Store',
    hotel: 'Hotel',
    attraction: 'Attraction',
    museum: 'Museum',
    park: 'Park',
    sports_centre: 'Sports Center',
    sportsCenter: 'Sports Center',
    stadium: 'Stadium',
    station: 'Train Station',
    bus_stop: 'Bus Stop',
    busStop: 'Bus Stop',
    gasStation: 'Gas Station',

    // 交通图层图例
    port: 'Port',
    airport: 'Airport',
    city: 'City',

    // 园区统计图表相关
    parkStatistics: 'Park Statistics',
    cannotLoadChartData: 'Cannot load chart data: data is empty',
    dataProcessingFailed: 'Cannot load chart data: data processing failed',
    chartRenderingFailed: 'Chart rendering failed',
    landUsage: 'Land Usage',
    totalArea: 'Total Area',
    developedArea: 'Developed Area',
    undevelopedArea: 'Undeveloped Area',
    electricityDistribution: 'Electricity Distribution',
    totalCapacity: 'Total Capacity',
    factoryUsage: 'Factory Usage',
    doubleAUsage: 'Double A Usage',
    toEGAT: 'To EGAT',
    landSales: 'Land Sales',
    expectedSales2012: 'Expected Sales 2012',
    annualAverageSales: 'Annual Average Sales',
    soldToDate: 'Sold To Date',
    expectedTotal: 'Expected Total',
    annualAverage: 'Annual Average',
    landArea: 'Land Area',
    powerGeneration: 'Power Generation',
    totalPowerPlants: 'Total Power Plants',
    biofuelCapacity: 'Biofuel Capacity',
    biofuel: 'Biofuel',
    otherEnergy: 'Other Energy',
    doubleAInvestment: 'Double A Investment',
    thirdPaperPlantInvestment: 'Third Paper Plant Investment',
    thirdPaperPlant: 'Third Paper Plant',
    lastYearSales: 'Last Year Sales',
    salesGrowthProjection: 'Sales Growth Projection',
    investment: 'Investment',
    millionBaht: 'Million Baht',
    productionCapacity: 'Production Capacity',
    initialCapacity: 'Initial Annual Capacity',
    fullCapacity: 'Full Capacity (Three Plants)',
    paperProduction: 'Paper Production',
    tonnes: 'Tonnes',
    soldSince2012: 'Sold Since 2012',
    expectedRemaining2012: 'Expected Remaining 2012',

    // 修正ParkDetailPage.jsx中使用的键
    hideLaborTrend: 'Hide Labor Trend',
    showLaborTrend: 'Show Labor Trend',

    // 地图操作
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    showRuler: 'Show Ruler',
    hideRuler: 'Hide Ruler',
    stateMatch: 'State Match',
    nameMatch: 'Name Match',

    // Map filter related
    filters: 'Filters',
    hideFilters: 'Hide Filters',
    showFilters: 'Show Filters',
    toggleFilters: 'Toggle Filters',
    leaseCostRange: 'Lease Cost Range',
    min: 'Min',
    max: 'Max',
    states: 'States',
    cities: 'Cities',
    all: 'All',
    clear: 'Clear',
    showMore: 'Show More',
    showLess: 'Show Less',
    searchCities: 'Search cities...',
    noCitiesFound: 'No cities found matching your search.',
    thaiProvinces: 'Thai Provinces',
    zoomTo: 'Zoom to',

    // 环境监测相关
    showEnvironment: 'Show Environment',
    hideEnvironment: 'Hide Environment',
    airQuality: 'Air Quality',
    waterQuality: 'Water Quality',
    noiseLevel: 'Noise Level',
    windDirection: 'Wind Direction',
    environmentMonitoring: 'Environment Monitoring',
    excellent: 'Excellent',
    good: 'Good',
    lightPollution: 'Light Pollution',
    moderatePollution: 'Moderate Pollution',

    // 迁移类型相关
    hideMigration: 'Hide Migration Data',
    showMigration: 'Show Migration Data',
    gender: 'Gender',
    total: 'Total',
    male: 'Male',
    female: 'Female',
    migrationType: 'Migration Type',
    all: 'All',
    wholeHouseholdMigrated: 'Whole Household Migrated',
    partialHouseholdMigrated: 'Partial Household Migrated',
    individualMigrated: 'Individual Migrated',
    migrationTypeAnalysis: 'Migration Type Analysis',
    migrationTypeDesc: 'This analysis shows migration population by migration type, region, and administrative area, helping you understand regional migration patterns.',
    showChart: 'Show Chart',
    hideChart: 'Hide Chart',
    clickForDetails: 'Click on a region for details',
    showNationwideData: 'Show Nationwide Data',
    hideNationwideData: 'Hide Nationwide Data',
    totalMigration: 'Total Migration',
    migrationDistribution: 'Migration Distribution',
    noDataAvailable: 'No data available',
    showThaiName: 'Show Thai Name',
    showEnglishName: 'Show English Name',
    withinRegion: 'Within Region',
    betweenRegions: 'Between Regions',
    fromAbroad: 'From Abroad',
    areaType: 'Area Type',
    municipality: 'Municipality',
    nonMunicipality: 'Non-Municipality',
    migrationMap: 'Migration Map',
    loadingMap: 'Loading map data...',
    mapNote: 'Map shows migration patterns across regions. Colors indicate migration intensity.',
    regionComparison: 'Region Comparison',
    regionalMigration: 'Regional Migration',
    policyImplications: 'Policy Implications',
    heavyPollution: 'Heavy Pollution',
    severePollution: 'Severe Pollution',
    waterClassI: 'Class I',
    waterClassII: 'Class II',
    waterClassIII: 'Class III',
    waterClassIV: 'Class IV',
    waterClassV: 'Class V',
    waterClassBelowV: 'Below Class V',
    quiet: 'Quiet',
    lowNoise: 'Low Noise',
    moderateNoise: 'Moderate Noise',
    highNoise: 'High Noise',
    severeNoise: 'Severe Noise',
    breeze: 'Breeze',
    lightWind: 'Light Wind',
    moderateWind: 'Moderate Wind',
    strongWind: 'Strong Wind',
    galeWind: 'Gale Wind',

    // 天气工具相关
    checkWeather: 'Check Weather',
    weatherInfo: 'Weather Information',
    currentWeather: 'Current Weather',
    forecast: 'Weather Forecast',
    nearbyWeather: 'Nearby Weather',
    hourly: 'Hourly',
    daily: 'Daily',
    feelsLike: 'Feels Like',
    humidity: 'Humidity',
    wind: 'Wind Speed',
    pressure: 'Pressure',
    uvIndex: 'UV Index',
    sunrise: 'Sunrise',
    sunset: 'Sunset',
    today: 'Today',
    refresh: 'Refresh',
    retry: 'Retry',
    close: 'Close',
    errorLoadingWeather: 'Error loading weather data',
    nearbyWeatherError: 'Error loading nearby weather',
    apiLimitNote: 'OpenWeather API limited to 1000 calls per day. Please use sparingly.',

    // Time Series Panel translations
    showTimeSeries: 'Show Time Series',
    hideTimeSeries: 'Hide Time Series',
    timeSeriesAnalysis: 'Time Series Analysis',
    selectTimeRange: 'Select Time Range',
    selectDataCategories: 'Select Data Categories',
    updateChart: 'Update Chart',
    noDataAvailable: 'No data available for the selected time range',
    error: 'Error loading data',

    // Income Source Analysis (formerly Money & Goods Receipt by Sender Analysis)
    moneyGoodsSenderAnalysis: 'Income Source Analysis',
    moneyGoodsSenderDesc: 'This analysis shows migration numbers by received money and goods by sender type, gender, and region, helping identify key financial contributors to migrants.',
    senderType: 'Sender Type',
    receivedFromParents: 'Received from Parents',
    receivedFromSpouse: 'Received from Spouse',
    receivedFromChildren: 'Received from Children',
    receivedFromRelatives: 'Received from Relatives',
    receivedFromOthers: 'Received from Others',
    senderPatterns: 'Sender Patterns',
    familySupport: 'Family Support',
    senderDistribution: 'Sender Distribution',
    senderFlowAnalysis: 'Sender Flow Analysis',

    // Migrant Remittance Analysis (formerly Money & Goods Sending Analysis)
    moneyGoodsSendingAnalysis: 'Migrant Remittance Analysis',
    moneyGoodsSendingDesc: 'This analysis shows migration numbers by sending money and goods, gender, and region, helping analyze reverse fund flow and support trade planning.',
    onlyMoney: 'Only Money',
    onlyGoods: 'Only Goods',
    bothMoneyAndGoods: 'Both Money and Goods',
    didNotSend: 'Did Not Send',
    sendingDistribution: 'Sending Distribution',
    genderDifferences: 'Gender Differences',
    regionalVariations: 'Regional Variations',
    regionalBreakdown: 'Regional Breakdown',

    // Remittance Destination Analysis (formerly Money & Goods Sending by Recipient Analysis)
    moneyGoodsRecipientAnalysis: 'Remittance Destination Analysis',
    moneyGoodsRecipientDesc: 'This analysis shows migration numbers by recipient of sent money and goods, gender, and region, helping explore how migrants send funds and goods.',
    recipientType: 'Recipient Type',
    sentToParents: 'Sent to Parents',
    sentToSpouse: 'Sent to Spouse',
    sentToChildren: 'Sent to Children',
    sentToRelatives: 'Sent to Relatives',
    sentToOthers: 'Sent to Others',
    recipientPatterns: 'Recipient Patterns',
    recipientDistribution: 'Recipient Distribution',
    recipientFlowAnalysis: 'Recipient Flow Analysis',

    // Money Sending Purpose Analysis (Table 20)
    moneySendingPurposeAnalysis: 'Remittance Purpose Analysis',
    moneySendingPurposeDesc: 'This analysis shows migration numbers by money sending purpose, gender, and region, helping with economic modeling and forecasting.',
    forDailyLiving: 'For daily living expenses',
    forEducation: 'For education',
    toPayOffDebt: 'To pay off debt',
    purposePatterns: 'Purpose Patterns',
    economicImpact: 'Economic Impact',

    // Money Usage Analysis
    moneyPurposeAnalysis: 'Received Money Usage Analysis',
    moneyPurposeDesc: 'This analysis shows how migrants use the money they receive, helping understand consumption patterns and economic impacts.',

    // Population Migration Analysis
    populationMigrationAnalysis: 'Population Migration Analysis',

    // Employment Migration Analysis
    employmentMigrationAnalysis: 'Employment Migration Analysis',

    // 人口迁移分析相关
    migrationPopulationAnalysis: 'Migration Population Analysis',
    region: 'Region',
    ageGroup: 'Age Group',
    gender: 'Gender',
    migrationStatus: 'Migration Status',
    all: 'All',
    keyInsights: 'Key Insights',
    insightMigration: 'Analyze the distribution of migrant population by age groups and regions',
    insightComparison: 'Compare the structural differences between local and migrant populations',
    insightTrends: 'Identify migration trends in specific regions',
    migrationTypeDistribution: 'Migration Type Distribution',

    // Household Registration Analysis
    householdRegistrationAnalysis: 'Household Registration Analysis',
    householdRegistrationDesc: 'This analysis shows household registration status by region, gender, and migration status, helping you understand population mobility and settlement patterns.',
    registeredInResidence: 'Registered in Residence',
    registeredElsewhere: 'Registered Elsewhere',
    notRegistered: 'Not Registered Anywhere',
    registrationRate: 'Local Registration Rate',
    registrationPatterns: 'Registration Patterns',
    migrantRegistration: 'Migrant Registration Status',
    nonMigrantRegistration: 'Non-Migrant Registration Status',
    registrationDistribution: 'Registration Distribution',
    registrationLocation: 'Registration Location',
    detailedBreakdown: 'Detailed Breakdown',

    // Education Level Analysis
    educationLevelAnalysis: 'Education Level Analysis',
    educationLevelDesc: 'This analysis shows education levels by migration status, gender, and region, helping you understand population education patterns.',
    migrantPopulation: 'Migrant Population',
    nonMigrantPopulation: 'Non-Migrant Population',
    educationRate: 'Higher Education Rate',
    educationPatterns: 'Education Patterns',
    migrantEducation: 'Migrant Education Levels',
    nonMigrantEducation: 'Non-Migrant Education Levels',
    educationDistribution: 'Education Distribution',
    educationComparison: 'Education Comparison',
    noSchooling: 'No Schooling',
    lessThanPrimary: 'Less Than Primary',
    primaryEducation: 'Primary Education',
    lowerSecondaryEducation: 'Lower Secondary Education',
    upperSecondaryEducation: 'Upper Secondary Education',
    tertiaryEducation: 'Tertiary Education',
    otherEducation: 'Other Education',
    unknown: 'Unknown',

    // Marital Status Analysis
    maritalStatusAnalysis: 'Marital Status Analysis',
    maritalStatusDesc: 'This analysis shows marital status by migration status, gender, and region, helping you understand population marital patterns.',
    marriageRate: 'Marriage Rate',
    maritalPatterns: 'Marital Status Patterns',
    migrantMaritalStatus: 'Migrant Marital Status',
    nonMigrantMaritalStatus: 'Non-Migrant Marital Status',
    maritalDistribution: 'Marital Status Distribution',
    maritalComparison: 'Marital Status Comparison',
    single: 'Single',
    married: 'Married',
    widowed: 'Widowed',
    divorced: 'Divorced',
    separated: 'Separated',

    // Migration Frequency Analysis
    migrationFrequencyAnalysis: 'Migration Frequency Analysis',
    migrationFrequencyDesc: 'This analysis shows migration population by migration frequency, gender, and region, helping you understand migration patterns and predict trends.',
    didNotMigrate: 'Did Not Migrate',
    migratedOnce: 'Migrated Once',
    migratedTwice: 'Migrated Twice',
    migratedMoreThanThree: 'Migrated More Than 3 Times',
    frequencyDistribution: 'Frequency Distribution',
    regionalTrends: 'Regional Trends',

    // Expected Stay Duration Analysis
    expectedStayAnalysis: 'Expected Stay Duration Analysis',
    expectedStayDesc: 'This analysis shows migrant population by expected stay duration, gender, and region, helping you predict infrastructure needs.',
    totalMigrants: 'Total Migrants',
    shortTermMigrants: 'Short-term Migrants (<12 months)',
    longTermMigrants: 'Long-term Migrants (≥12 months)',
    permanentMigrants: 'Permanent Migrants',
    stayPatterns: 'Stay Duration Patterns',
    stayDistribution: 'Stay Duration Distribution',
    stayComparison: 'Gender Comparison by Duration',

    // Future Migration Reason Analysis
    futureMigrationReasonAnalysis: 'Future Migration Reason Analysis',
    futureMigrationReasonDesc: 'This analysis shows population by expected future migration reason, gender, and region, helping you predict future migration drivers and plan ahead.',
    futureMigrants: 'Future Migrants',

    // Migrant Income Analysis (formerly Money & Goods Receipt Analysis)
    moneyGoodsReceiptAnalysis: 'Migrant Income Analysis',
    moneyGoodsReceiptDesc: 'This analysis shows migration numbers by received money and goods, gender, and region, helping you understand financial impacts of migration.',
    receivedMoney: 'Received Only Money',
    receivedGoods: 'Received Only Goods',
    receivedBoth: 'Received Both Money and Goods',
    didNotReceive: 'Did Not Receive',
    financialImpact: 'Financial Impact',
    remittancePatterns: 'Remittance Patterns',
    fundFlowAnalysis: 'Fund Flow Analysis',
    receiptDistribution: 'Receipt Distribution',
    lookingForJob: 'Looking for a job',
    wantToChangeJob: 'Want to change job',
    wantToIncreaseIncome: 'Want to increase income',
    jobAssignment: 'Job assignment',
    furtherEducation: 'Further education',
    relocation: 'Relocation',
    returnToHometown: 'Return to hometown',
    followFamilyMember: 'Follow family member',
    others: 'Others',
    reasonDistribution: 'Reason Distribution',
    topReasons: 'Top Migration Reasons',
    regionalComparison: 'Regional Comparison',
    economicFactors: 'Economic Factors',
    familyFactors: 'Family Factors',
    educationFactors: 'Education Factors',

    // Layer explanations
    layerInfo: 'Layer Information',
    gridLayer: 'Grid Layer',
    trafficLayer: 'Traffic Layer',
    poiLayer: 'Points of Interest Layer',
    terrainLayer: 'Terrain Layer',
    analyticsLayer: 'Analytics Layer',
    close: 'Close',

    // Grid layer
    gridLayerDesc: 'The grid layer displays latitude and longitude grid lines on the map, helping you understand geographic locations and distances.',
    latLongGridLines: 'Latitude/Longitude Grid Lines (0.5 degree intervals)',

    // Traffic layer
    trafficLayerDesc: 'The traffic layer displays road networks and transportation infrastructure, including different types of roads and transportation facilities.',
    highwayMainRoad: 'Highway/Main Road',
    secondaryRoad: 'Secondary Road',
    normalRoad: 'Normal Road',
    pathUnpavedRoad: 'Path/Unpaved Road',
    transportHub: 'Transport Hub (Airport, Train Station, etc.)',

    // POI layer
    poiLayerDesc: 'The Points of Interest layer displays important locations and facilities on the map, such as cities, attractions, and service facilities.',
    majorCity: 'Major City',

    // Terrain layer
    terrainLayerDesc: 'The terrain layer displays elevation, mountains, rivers, and other natural geographic features, helping you understand the topography.',
    plainLowland: 'Plain/Lowland',
    hills: 'Hills',
    mountains: 'Mountains',
    highMountains: 'High Mountains',
    riverWaterSystem: 'River/Water System',

    // Analytics layer
    analyticsLayerDesc: 'The analytics layer displays data heat maps, showing data density and distribution across different areas.',
    lowDensityArea: 'Low Density Area',
    mediumLowDensityArea: 'Medium-Low Density Area',
    mediumHighDensityArea: 'Medium-High Density Area',
    highDensityArea: 'High Density Area',
    veryHighDensityArea: 'Very High Density Area',

    // Economic Hotspot related
    economicHotspot: 'Economic Geography Hotspot',
    economicHotspotSettings: 'Economic Hotspot Settings',
    modelType: 'Model Type',
    locationQuotient: 'Location Quotient (LQ)',
    gravityModel: 'Gravity Model',
    industry: 'Industry',
    manufacturing: 'Manufacturing',
    government: 'Government',
    privateEnterprise: 'Private Enterprise',
    selfEmployed: 'Self-Employed',
    intensity: 'Intensity',
    serviceRadius: 'Service Radius',
    locationQuotientLegend: 'Location Quotient (LQ) Legend',
    gravityModelLegend: 'Gravity Model Legend',
    highSpecialization: 'High Specialization (LQ > 1.5)',
    mediumHighSpecialization: 'Medium-High Specialization (LQ 1.25-1.5)',
    averageSpecialization: 'Average Specialization (LQ 0.75-1.25)',
    lowSpecialization: 'Low Specialization (LQ 0.5-0.75)',
    veryLowSpecialization: 'Very Low Specialization (LQ < 0.5)',
    coreEconomicCenter: 'Core Economic Center',
    majorInfluenceZone: 'Major Influence Zone',
    moderateInfluenceZone: 'Moderate Influence Zone',
    minorInfluenceZone: 'Minor Influence Zone',
    peripheralZone: 'Peripheral Zone',
  },
  zh: {
    high: '高',
    low: '低',
    hideLaborTrendLayer: '隐藏劳动力趋势',
    showLaborTrendLayer: '显示劳动力趋势',
    backToMap: '返回地图',
    parkLayout: '园区布局',
    loading: '加载园区信息中...',
    notFound: '未找到园区信息',
    cannotLoad: '无法加载ID为',
    map: '地图',
    overview: '园区概览',
    laborTrends: '劳动力趋势',
    LaborTrends: '劳动力趋势', // 添加首字母大写的版本，匹配ParkDetailPage.jsx中的用法
    laborEconomics: '劳动力经济分析',
    majorCustomers: '主要客户',
    basicInfo: '基本信息',
    province: '省份',
    cityZoning: '城市区域',
    saleMethod: '销售方式',
    managementStatus: '管理状态',
    managementFee: '管理费',
    settings: '设置',
    appearance: '外观',
    language: '语言',
    accountManagement: '账户管理',
    units: '单位设置',
    metric: '公制 (米, 平方米)',
    imperial: '英制 (英尺, 平方英尺)',
    currency: '货币设置',
    myData: '我的数据',
    aboutUs: '关于我们',
    statusDescription: '管理状态说明',
    currencyRates: '货币汇率',
    distanceInfo: '距离信息',
    nearestPort: '最近港口',
    nearestAirport: '最近机场',
    nearestCity: '最近城市',
    infrastructure: '基础设施',
    electricity: '电力供应',
    waterSupply: '水源供应',
    reservoirCapacity: '蓄水池容量',
    productionCapacity: '生产能力',
    wasteWaterTreatment: '废水处理',
    treatmentCapacity: '处理能力',
    road: '道路',
    telephone: '电话',
    internet: '互联网',
    type: '类型',
    security: '安保设施',
    fireProtection: '消防设施',
    nearbyFacilities: '周边设施',
    banks: '银行',
    hospitals: '医院',
    gasStations: '加油站',
    restaurants: '餐厅',
    convenienceStores: '便利店',
    shoppingCenters: '购物中心',
    otherFacilities: '其他设施',
    contactInfo: '联系方式',
    website: '网站',
    callCenter: '客服中心',
    phoneNumbers: '联系电话',
    mobileNumbers: '手机号码', // 添加缺失的翻译
    location: '地理位置',
    coordinates: '坐标位置',
    latitude: '纬度',
    longitude: '经度',
    transportConnections: '交通连接',
    parkHighlights: '园区亮点',
    richWaterResources: '丰富水资源',
    stablePower: '稳定电力',
    convenientFacilities: '便利生活设施',
    strategicLocation: '战略位置',
    waterResourcesDesc: '拥有自己的水源供应系统，蓄水池容量达3600万立方米，特别适合用水量大的产业。',
    stablePowerDesc: '现场发电厂提供最大670MW电力，并有省级电力局提供的应急电力储备。',
    convenientFacilitiesDesc: '周边配套完善，包括医院、购物中心、餐厅、酒店和公寓，便于安居。',
    strategicLocationDesc: '位于东西走廊未来项目附近，距离柬埔寨边境仅约1小时车程，是与柬埔寨贸易的理想基地。',
    infoIncomplete: '园区信息正在完善中',
    infoIncompleteDesc: '该园区的详细信息尚未完全录入系统，请稍后再查看或联系我们获取更多信息。',
    switchToEnglish: '切换到英文',
    switchToChinese: '切换到中文',
    languageOptions: '语言选项',
    english: '英文',
    chinese: '中文',
    unknown: '未知',
    transportRoutes: '交通路线',
    industrialPark: '工域探索',
    toPort: '到港口',
    toAirport: '到机场',
    toCity: '到城市',
    resetMapView: '重置地图视图',
    distance: '距离',

    // 劳动力趋势分析相关
    laborTrendAnalysis: '劳动力趋势分析',
    switchToCompareMode: '切换到比较模式',
    switchToTrendMode: '切换到趋势模式',
    selectCompareYears: '选择比较年份',
    selectAll: '全选',
    clearAll: '清除全部',
    pleaseSelectYears: '请至少选择一个年份进行比较',
    chartType: '图表类型',
    lineChart: '折线图',
    barChart: '柱状图',
    dataCategory: '数据类别',
    education: '教育水平',
    employment: '就业状态',
    occupation: '职业分布',
    industry: '行业分布',
    workingHours: '工作时间',
    selectMetrics: '选择指标',
    pleaseSelectMetrics: '请至少选择一个指标以显示图表',
    data: '数据',
    laborDataComparison: '劳动力数据比较',
    dataLoadError: '没有可用的劳动力趋势数据。',
    laborMarketData: '劳动力市场数据',

    // 劳动力数据图表相关
    laborMarketAnalysis: '劳动力市场分析',
    selectRegion: '选择地区',
    regional_total: '区域总计',
    totalPopulation: '总人口 (15岁以上)',
    employedPersons: '就业人数',
    unemploymentRate: '失业率',
    laborForceParticipationRate: '劳动参与率',
    employmentByIndustry: '按行业划分的就业情况',
    employmentByOccupation: '按职业分类的就业情况',
    laborForceParticipation: '劳动力参与情况',
    educationLevel: '就业人口教育水平',
    employmentStatus: '就业状态分布',
    marketInsights: '市场洞察',
    genderComparison: '性别对比分析',
    male: '男性',
    female: '女性',
    dataSource: '数据来源',
    laborDataSource: '泰国国家统计局，2024年第四季度',

    // 行业类别
    manufacturing: '制造业',
    wholesale_and_retail_trade: '批发和零售业',
    accommodation_and_food_services: '住宿和餐饮业',
    transportation_and_warehousing: '运输和仓储业',
    construction: '建筑业',
    agriculture_forestry_and_fishing: '农林渔业',

    // 职业类别
    managers_senior_officials_legislators: '管理人员和高级官员',
    professionals_: '专业人员',
    technicians_and_associate_professionals: '技术人员和助理专业人员',
    clerks: '文员',
    service_workers_and_salespersons: '服务和销售人员',
    craftsmen_and_related_workers: '工匠和相关工人',
    plant_and_machine_operators_and_assemblers: '机械操作员',
    general_labor: '普通劳工',

    // 劳动力参与
    inLaborForce: '劳动力人口',
    notInLaborForce: '非劳动力人口',

    // 教育水平
    no_education: '无教育程度',
    primary_education: '小学教育',
    lower_secondary_education: '初中教育',
    upper_secondary_education_academic_track: '高中教育(学术)',
    upper_secondary_education_vocational_track: '高中教育(职业)',
    higher_education_professional_track: '高等教育(专业)',
    higher_education_education_track: '高等教育(教育)',

    // 就业状态
    employer: '雇主',
    government_employee: '政府雇员',
    private_employee: '私企雇员',
    self_employed: '个体经营者',
    family_business_worker: '家族企业工作者',

    currentLocation: '当前位置',
    nearbyArea: '周边地区',
    regionalTotalDescription: '显示泰国中部地区的总体劳动力数据',
    prachinBuriDescription: '304工业园所在地区的劳动力数据',
    nearbyAreaDescription: '周边地区的劳动力数据，可作为参考',

    // 洞察文本
    laborMarketInsight1: '制造业是该地区雇佣人数最多的行业，占总就业人口的很大一部分。',
    laborMarketInsight2: '该地区有适中的劳动参与率，失业率相对较低。',
    laborMarketInsight3: '相当一部分劳动力拥有中等或更高教育水平，表明该地区劳动力素质较高。',

    // 劳动力数据图表相关新增翻译
    manufacturingRatio: '制造业就业占比',
    higherEducationRatio: '高等教育比例',
    otherLaborCategories: '其他劳动力类别',
    agriNonAgriRatio: '农业与非农业就业比例',
    agricultural_sector: '农业部门',
    non_agricultural_sector: '非农业部门',
    employment_by_sector: '按部门划分的就业情况',
    employmentDistribution: '就业分布',
    laborForceDetailedDistribution: '劳动力详细分布',
    unemployedPersons: '失业人员',
    seasonallyUnemployed: '季节性失业人员',
    housework: '家务劳动',
    studying: '在校学习',
    unableToWork: '无法工作',
    sickOrDisabled: '病残人员',
    others: '其他',

    // 园区统计图表相关
    parkStatistics: '园区统计',
    cannotLoadChartData: '无法加载图表数据：数据为空',
    dataProcessingFailed: '无法加载图表数据：数据处理失败',
    chartRenderingFailed: '图表渲染失败',
    landUsage: '土地使用情况',
    totalArea: '总面积',
    developedArea: '已开发面积',
    undevelopedArea: '未开发面积',
    electricityDistribution: '电力分配',
    totalCapacity: '总容量',
    factoryUsage: '工厂使用',
    doubleAUsage: 'Double A 使用',
    toEGAT: '输送至 EGAT',
    landSales: '土地销售',
    expectedSales2012: '2012年预期销售',
    annualAverageSales: '年平均销售',
    soldToDate: '迄今已售',
    expectedTotal: '预期总计',
    annualAverage: '年平均',
    landArea: '土地面积',
    powerGeneration: '发电量',
    totalPowerPlants: '总发电厂',
    biofuelCapacity: '生物燃料容量',
    biofuel: '生物燃料',
    otherEnergy: '其他能源',
    doubleAInvestment: 'Double A 投资',
    thirdPaperPlantInvestment: '第三造纸厂投资',
    thirdPaperPlant: '第三造纸厂',
    lastYearSales: '去年销售',
    salesGrowthProjection: '销售增长预测',
    investment: '投资',
    millionBaht: '百万泰铢',
    productionCapacity: '生产能力',
    initialCapacity: '初始年产能',
    fullCapacity: '满负荷产能（三个工厂）',
    paperProduction: '纸张生产',
    tonnes: '吨',
    soldSince2012: '自2012年起已售',
    expectedRemaining2012: '2012年预期剩余',

    // 修正ParkDetailPage.jsx中使用的键
    hideLaborTrend: '隐藏劳动力趋势',
    showLaborTrend: '显示劳动力趋势',

    // 设施搜索功能
    searchFacilities: '搜索周边设施',
    customSearchPlaceholder: '自定义搜索...',
    searchResults: '搜索结果',
    noResultsFound: '未找到结果',
    facilityType: '类型',
    searchRadius: '搜索半径',
    phone: '电话',
    website: '网站',
    expand: '展开',
    collapse: '收起',
    clearResults: '清除结果',

    // 设施类别
    amenity: '便民设施',
    shop: '商店',
    tourism: '旅游',
    leisure: '休闲',
    public_transport: '交通',

    // 具体设施类型
    restaurant: '餐厅',
    cafe: '咖啡厅',
    bank: '银行',
    hospital: '医院',
    school: '学校',
    pharmacy: '药店',
    fuel: '加油站',
    police: '警察局',
    supermarket: '超市',
    convenience: '便利店',
    mall: '购物中心',
    clothes: '服装店',
    hardware: '五金店',
    hotel: '酒店',
    attraction: '景点',
    museum: '博物馆',
    park: '公园',
    sports_centre: '体育中心',
    sportsCenter: '体育中心',
    stadium: '体育场',
    station: '火车站',
    bus_stop: '公交车站',
    busStop: '公交车站',
    gasStation: '加油站',

    // 地图操作
    zoomIn: '放大',
    zoomOut: '缩小',
    showRuler: '显示测量工具',
    hideRuler: '隐藏测量工具',
    stateMatch: '州匹配',
    nameMatch: '名称匹配',
    placeMarkers: '放置标记',
    stopPlacingMarkers: '停止放置标记',
    clearAllMarkers: '清除所有标记',
    results: '结果',
    sortAsc: '排序 (升序)',
    sortDesc: '排序 (降序)',

    // 自定义标记相关
    customMarker: '自定义标记',
    markerLabel: '标记名称',
    markerColor: '标记颜色',
    saveMarker: '保存',
    removeMarker: '删除标记',
    copyToClipboard: '复制到剪贴板',

    // 地图过滤器相关
    filters: '过滤器',
    hideFilters: '隐藏过滤器',
    showFilters: '显示过滤器',
    toggleFilters: '切换过滤器',
    search: '搜索',
    searchLocations: '搜索位置',
    enterLocationName: '输入位置名称...',
    enterSearchTerm: '输入搜索词查找位置',
    results: '搜索结果',
    searching: '搜索中...',
    noResultsFound: '没有找到匹配"{searchTerm}"的园区',
    leaseCostRange: '租赁成本范围',
    min: '最小值',
    max: '最大值',
    states: '州',
    cities: '城市',
    all: '全部',
    clear: '清除',
    showMore: '显示更多',
    showLess: '显示更少',
    searchCities: '搜索城市...',
    noCitiesFound: '没有找到匹配的城市。',
    thaiProvinces: '泰国省份',
    zoomTo: '缩放到',

    // 环境监测相关
    showEnvironment: '显示环境',
    hideEnvironment: '隐藏环境',
    airQuality: '空气质量',
    waterQuality: '水质',
    noiseLevel: '噪声',
    windDirection: '风向风速',
    environmentMonitoring: '环境监测',
    excellent: '优',
    good: '良',
    lightPollution: '轻度污染',
    moderatePollution: '中度污染',
    heavyPollution: '重度污染',
    severePollution: '严重污染',
    waterClassI: 'I类',
    waterClassII: 'II类',
    waterClassIII: 'III类',
    waterClassIV: 'IV类',
    waterClassV: 'V类',
    waterClassBelowV: '劣V类',
    quiet: '安静',
    lowNoise: '低噪声',
    moderateNoise: '中等噪声',
    highNoise: '高噪声',
    severeNoise: '严重噪声',
    breeze: '微风',
    lightWind: '小风',
    moderateWind: '中风',
    strongWind: '大风',
    galeWind: '强风',

    // 迁移类型相关
    hideMigration: '隐藏迁移数据',
    showMigration: '显示迁移数据',
    gender: '性别',
    total: '总计',
    male: '男性',
    female: '女性',
    migrationType: '迁移类型',

    // 移民收入分析 (原金钱和物品接收分析)
    moneyGoodsReceiptAnalysis: '移民收入分析',
    moneyGoodsReceiptDesc: '此分析按接收金钱和物品、性别和地区显示迁移人数，帮助您了解迁移的财务影响。',
    receivedMoney: '仅接收金钱',
    receivedGoods: '仅接收物品',
    receivedBoth: '同时接收金钱和物品',
    didNotReceive: '未接收任何物品',
    financialImpact: '财务影响',
    remittancePatterns: '汇款模式',
    fundFlowAnalysis: '资金流动分析',
    receiptDistribution: '接收分布',

    // 收入来源分析 (原金钱和物品发送者分析)
    moneyGoodsSenderAnalysis: '收入来源分析',
    moneyGoodsSenderDesc: '此分析按发送者类型、性别和地区显示接收金钱和物品的迁移人数，帮助识别迁移财务的主要贡献者。',
    senderType: '发送者类型',
    receivedFromParents: '从父母接收',
    receivedFromSpouse: '从配偶接收',
    receivedFromChildren: '从子女接收',
    receivedFromRelatives: '从亲戚接收',
    receivedFromOthers: '从其他人接收',
    senderPatterns: '发送者模式',
    familySupport: '家庭支持',
    senderDistribution: '发送者分布',
    senderFlowAnalysis: '发送者流动分析',

    // 移民汇款分析 (原金钱和物品发送分析)
    moneyGoodsSendingAnalysis: '移民汇款分析',
    moneyGoodsSendingDesc: '此分析按发送金钱和物品、性别和地区显示迁移人数，帮助分析资金和物品的反向流动，辅助贸易和经济规划。',
    onlyMoney: '仅发送金钱',
    onlyGoods: '仅发送物品',
    bothMoneyAndGoods: '同时发送金钱和物品',
    didNotSend: '未发送任何物品',
    sendingDistribution: '发送分布',
    genderDifferences: '性别差异',
    regionalVariations: '地区差异',
    regionalBreakdown: '地区细分',

    // 汇款目的地分析 (原按接收者划分的金钱和物品发送分析)
    moneyGoodsRecipientAnalysis: '汇款目的地分析',
    moneyGoodsRecipientDesc: '此分析按接收者、性别和地区显示发送金钱和物品的迁移人数，帮助探索移民如何及向何处发送资金和物品。',
    recipientType: '接收者类型',
    sentToParents: '发送给父母',
    sentToSpouse: '发送给配偶',
    sentToChildren: '发送给子女',
    sentToRelatives: '发送给亲戚',
    sentToOthers: '发送给其他人',
    recipientPatterns: '接收者模式',
    recipientDistribution: '接收者分布',
    recipientFlowAnalysis: '接收者流动分析',

    // 汇款用途分析 (表20)
    moneySendingPurposeAnalysis: '汇款目的分析',
    moneySendingPurposeDesc: '此分析按发送金钱用途、性别和地区划分的发送金钱的迁移人数，协助经济建模和预测。',
    forDailyLiving: '日常生活开支',
    forEducation: '教育开支',
    toPayOffDebt: '偿还债务',
    purposePatterns: '用途模式',
    economicImpact: '经济影响',

    // 金钱使用目的分析
    moneyPurposeAnalysis: '收到资金使用分析',
    moneyPurposeDesc: '此分析显示移民如何按用途、性别和地区使用收到的资金，帮助理解财务模式。',
    purpose: '用途',
    totalMoney: '收到的总资金',
    dailyLiving: '日常生活开支',
    education: '教育开支',
    investment: '商业投资',
    others: '其他',
    purposeDistribution: '用途分布',
    purposeComparison: '各地区用途比较',
    moneyUsagePatterns: '资金使用模式',
    regionalDistribution: '地区分布',
    all: '全部',
    wholeHouseholdMigrated: '全家迁移',
    partialHouseholdMigrated: '部分家庭迁移',
    individualMigrated: '个人迁移',
    migrationTypeAnalysis: '迁移类型分析',
    migrationTypeDesc: '此分析按迁移类型、地区和行政区域显示迁移人口，帮助您了解区域迁移模式。',
    migrationTypeDistribution: '迁移类型分布',
    showChart: '显示图表',
    hideChart: '隐藏图表',
    clickForDetails: '点击区域查看详情',
    withinRegion: '区域内迁移',
    betweenRegions: '区域间迁移',
    fromAbroad: '来自国外',
    areaType: '区域类型',
    municipality: '市政区',
    nonMunicipality: '非市政区',
    migrationMap: '迁移地图',
    loadingMap: '加载地图数据...',
    mapNote: '地图显示各地区的迁移模式。颜色表示迁移强度。',
    regionComparison: '区域比较',
    regionalMigration: '区域迁移',
    policyImplications: '政策影响',

    // 天气工具相关
    checkWeather: '查看天气',
    weatherInfo: '天气信息',
    currentWeather: '当前天气',
    forecast: '天气预报',
    nearbyWeather: '周边天气',
    hourly: '每小时',
    daily: '每天',
    feelsLike: '体感温度',
    humidity: '湿度',
    wind: '风速',
    pressure: '气压',
    uvIndex: '紫外线指数',
    sunrise: '日出',
    sunset: '日落',
    today: '今天',
    refresh: '刷新',
    retry: '重试',
    close: '关闭',
    errorLoadingWeather: '加载天气数据失败',
    nearbyWeatherError: '加载周边天气失败',
    apiLimitNote: 'OpenWeather API 每日限制1000次调用，请谨慎使用',

    // Time Series Panel translations
    showTimeSeries: '显示时间序列',
    hideTimeSeries: '隐藏时间序列',
    timeSeriesAnalysis: '时间序列分析',
    selectTimeRange: '选择时间范围',
    selectDataCategories: '选择数据类别',
    updateChart: '更新图表',
    noDataAvailable: '所选时间范围内无可用数据',
    error: '加载数据出错',

    // 人口迁移分析
    populationMigrationAnalysis: '人口迁移分析',

    // 就业迁移分析
    employmentMigrationAnalysis: '就业迁移分析',

    // 人口迁移分析相关
    migrationPopulationAnalysis: '人口迁移分析',
    region: '地区',
    ageGroup: '年龄组',
    gender: '性别',
    migrationStatus: '迁移状态',
    all: '全部',
    keyInsights: '关键洞察',
    insightMigration: '分析迁移人口在不同年龄组和地区的分布情况',
    insightComparison: '比较当地人口和迁移人口的结构差异',
    insightTrends: '识别特定地区的人口迁移趋势',

    // 户籍登记分析相关
    householdRegistrationAnalysis: '户籍登记分析',
    householdRegistrationDesc: '此分析按地区、性别和迁移状态显示户籍登记状况，帮助您了解人口流动和定居模式。',
    registeredInResidence: '在居住地登记',
    registeredElsewhere: '在其他地方登记',
    notRegistered: '未在任何地方登记',
    registrationRate: '本地登记率',
    registrationPatterns: '登记模式',
    migrantRegistration: '迁移人口登记状态',
    nonMigrantRegistration: '非迁移人口登记状态',
    registrationDistribution: '登记分布',
    registrationLocation: '登记位置',
    detailedBreakdown: '详细分类',

    // 教育水平分析相关
    educationLevelAnalysis: '教育水平分析',
    educationLevelDesc: '此分析按迁移状态、性别和地区显示教育水平，帮助您了解人口教育模式。',
    migrantPopulation: '迁移人口',
    nonMigrantPopulation: '非迁移人口',
    educationRate: '高等教育率',
    educationPatterns: '教育模式',
    migrantEducation: '迁移人口教育水平',
    nonMigrantEducation: '非迁移人口教育水平',
    educationDistribution: '教育分布',
    educationComparison: '教育比较',
    noSchooling: '无学历',
    lessThanPrimary: '小学以下',
    primaryEducation: '小学教育',
    lowerSecondaryEducation: '初中教育',
    upperSecondaryEducation: '高中教育',
    tertiaryEducation: '高等教育',
    otherEducation: '其他教育',
    unknown: '未知',

    // 婚姻状况分析相关
    maritalStatusAnalysis: '婚姻状况分析',
    maritalStatusDesc: '此分析按迁移状态、性别和地区显示婚姻状况，帮助您了解人口婚姻模式。',
    marriageRate: '结婚率',
    maritalPatterns: '婚姻状况模式',
    migrantMaritalStatus: '迁移人口婚姻状况',
    nonMigrantMaritalStatus: '非迁移人口婚姻状况',
    maritalDistribution: '婚姻状况分布',
    maritalComparison: '婚姻状况比较',
    single: '未婚',
    married: '已婚',
    widowed: '丧偶',
    divorced: '离婚',
    separated: '分居',

    // 迁移频率分析相关
    migrationFrequencyAnalysis: '迁移频率分析',
    migrationFrequencyDesc: '此分析按迁移频率、性别和地区显示迁移人口，帮助您了解迁移模式并预测趋势。',
    didNotMigrate: '未迁移',
    migratedOnce: '迁移1次',
    migratedTwice: '迁移2次',
    migratedMoreThanThree: '迁移3次以上',
    frequencyDistribution: '频率分布',
    regionalTrends: '区域趋势',

    // 预期停留时间分析相关
    expectedStayAnalysis: '预期停留时间分析',
    expectedStayDesc: '此分析按预期停留时间、性别和地区显示迁移人口，帮助您预测基础设施需求。',
    totalMigrants: '迁移人口总数',
    shortTermMigrants: '短期迁移人口（<12个月）',
    longTermMigrants: '长期迁移人口（≥12个月）',
    permanentMigrants: '永久迁移人口',
    stayPatterns: '停留时间模式',
    stayDistribution: '停留时间分布',
    stayComparison: '按性别的停留时间比较',

    // 未来迁移原因分析相关
    futureMigrationReasonAnalysis: '未来迁移原因分析',
    futureMigrationReasonDesc: '此分析按预期未来迁移原因、性别和地区显示迁移人口，帮助您预测未来迁移驱动因素并提前规划。',
    futureMigrants: '未来迁移人口',
    lookingForJob: '寻找工作',
    wantToChangeJob: '想要换工作',
    wantToIncreaseIncome: '想要增加收入',
    jobAssignment: '工作调动',
    furtherEducation: '继续教育',
    relocation: '搬迁',
    returnToHometown: '返回家乡',
    followFamilyMember: '跟随家庭成员',
    others: '其他',
    reasonDistribution: '原因分布',
    topReasons: '主要迁移原因',
    regionalComparison: '区域比较',
    economicFactors: '经济因素',
    familyFactors: '家庭因素',
    educationFactors: '教育因素',

    // 图层说明
    layerInfo: '图层信息',
    gridLayer: '网格图层',
    trafficLayer: '交通图层',
    poiLayer: '兴趣点图层',
    terrainLayer: '地形图层',
    analyticsLayer: '分析图层',
    close: '关闭',

    // 网格图层
    gridLayerDesc: '网格图层在地图上显示经纬度网格线，帮助您了解地理位置和距离。',
    latLongGridLines: '经纬度网格线（间隔0.5度）',

    // 交通图层
    trafficLayerDesc: '交通图层显示道路网络和交通基础设施，包括不同类型的道路和交通设施。',
    highwayMainRoad: '高速公路/主干道',
    secondaryRoad: '次干道',
    normalRoad: '普通道路',
    pathUnpavedRoad: '小路/未铺装道路',
    transportHub: '交通枢纽（机场、火车站等）',

    // 兴趣点图层
    poiLayerDesc: '兴趣点图层显示地图上的重要地点和设施，如城市、景点和服务设施。',
    majorCity: '主要城市',

    // 地形图层
    terrainLayerDesc: '地形图层显示地形高度、山脉、河流等自然地理特征，帮助您了解地形起伏。',
    plainLowland: '平原/低地',
    hills: '丘陵',
    mountains: '山地',
    highMountains: '高山',
    riverWaterSystem: '河流/水系',

    // 分析图层
    analyticsLayerDesc: '分析图层显示数据热力图，展示不同区域的数据密度和分布情况。',
    lowDensityArea: '低密度区域',
    mediumLowDensityArea: '中低密度区域',
    mediumHighDensityArea: '中高密度区域',
    highDensityArea: '高密度区域',
    veryHighDensityArea: '极高密度区域',

    // 经济热点相关
    economicHotspot: '经济地理热点',
    economicHotspotSettings: '经济热点设置',
    modelType: '模型类型',
    locationQuotient: '区位商 (LQ)',
    gravityModel: '重力模型',
    industry: '行业',
    manufacturing: '制造业',
    government: '政府部门',
    privateEnterprise: '私营企业',
    selfEmployed: '个体经营',
    intensity: '强度',
    serviceRadius: '服务半径',
    locationQuotientLegend: '区位商 (LQ) 图例',
    gravityModelLegend: '重力模型图例',
    highSpecialization: '高度专业化 (LQ > 1.5)',
    mediumHighSpecialization: '中高度专业化 (LQ 1.25-1.5)',
    averageSpecialization: '平均专业化 (LQ 0.75-1.25)',
    lowSpecialization: '低专业化 (LQ 0.5-0.75)',
    veryLowSpecialization: '极低专业化 (LQ < 0.5)',
    coreEconomicCenter: '核心经济中心',
    majorInfluenceZone: '主要影响区',
    moderateInfluenceZone: '中等影响区',
    minorInfluenceZone: '次要影响区',
    peripheralZone: '外围区域',
  }
};

export default translations;




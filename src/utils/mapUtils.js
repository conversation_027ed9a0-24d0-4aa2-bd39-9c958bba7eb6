import L from 'leaflet';
// 确保已安装 leaflet-geometryutil: npm install leaflet-geometryutil
import 'leaflet-geometryutil';

// 创建发光图标
export const createGlowingIcon = (color) => {
  // 假设您的标记图像位于公共文件夹或配置正确
  const iconPath = `/markers/${color}-marker.png`; // 根据需要调整路径
  const shadowPath = '/markers/marker-shadow.png'; // 根据需要调整路径
  return L.icon({
    iconUrl: iconPath,
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowUrl: shadowPath,
    shadowSize: [41, 41],
    shadowAnchor: [12, 41]
  });
};

// 解析运输键
export const parseTransportKey = (key) => {
  if (!key || key === 'Not Found') return 'Not Found';
  return key;
};

// 去除港口前缀
export const stripPortPrefix = (portName) => {
  if (!portName || portName === 'Not Found') return 'Not Found';
  return portName.replace(/^Port of /i, '');
};

/**
 * 计算两个L.LatLng点之间的测地线距离（以米为单位）。
 * 稳健地处理不同的输入类型。
 * @param {L.LatLng|{lat: number, lng: number}} latlng1 - 第一个点。
 * @param {L.LatLng|{lat: number, lng: number}} latlng2 - 第二个点。
 * @returns {number} 距离（米）。
 */
export const calculateDistance = (latlng1, latlng2) => {
   if (!latlng1 || !latlng2) return 0;
   // 确保输入是L.LatLng对象，以便使用distanceTo方法
   const p1 = (latlng1 instanceof L.LatLng) ? latlng1 : L.latLng(latlng1.lat, latlng1.lng);
   const p2 = (latlng2 instanceof L.LatLng) ? latlng2 : L.latLng(latlng2.lat, latlng2.lng);
   try {
       return p1.distanceTo(p2);
   } catch (e) {
       console.error("计算距离时出错:", e, latlng1, latlng2);
       return 0;
   }
};

// 将米转换为公里
export const metersToKilometers = (meters) => {
  return meters / 1000;
};

// 将米转换为英里
export const metersToMiles = (meters) => {
  return meters / 1609.344;
};

// 将米转换为英尺
export const metersToFeet = (meters) => {
    return meters * 3.28084;
}

/**
 * 将米为单位的距离格式化为可读字符串（公制或英制）。
 * 在较小（m、ft）和较大（km、mi）单位之间切换。
 * 包括千位分隔符。
 * @param {number} meters - 距离值。
 * @param {'metric'|'imperial'} [unit='metric'] - 所需的单位系统。
 * @returns {string} 格式化的距离字符串（例如，"1,234.56 m"、"1.23 km"、"4,050.39 ft"、"0.77 mi"）。
 */
export const formatDistance = (meters, unit = 'metric') => {
   if (isNaN(meters)) return 'N/A';
   try {
       const options = { minimumFractionDigits: 2, maximumFractionDigits: 2 };
       if (unit === 'imperial') {
         const feet = metersToFeet(meters);
         if (feet >= 5280) { // 如果 >= 1英里则使用英里
             const miles = metersToMiles(meters);
             return miles.toLocaleString(undefined, options) + ' mi';
         }
         // 否则使用英尺
         return feet.toLocaleString(undefined, options) + ' ft';
       } else { // 公制
         if (meters >= 1000) { // 如果 >= 1公里则使用公里
           const km = metersToKilometers(meters);
           return km.toLocaleString(undefined, options) + ' km';
         }
         // 否则使用米
         return meters.toLocaleString(undefined, options) + ' m';
       }
     } catch(e) {
         console.error("格式化距离时出错:", e);
         return '错误';
     }
};

/**
 * 计算由LatLng点定义的多边形的测地线面积（平方米）。
 * 需要'leaflet-geometryutil'库。稳健地处理输入。
 * @param {Array<L.LatLng|{lat: number, lng: number}>} latlngs - 定义多边形的点数组。
 * @returns {number} 面积（平方米）。
 */
export const calculatePolygonArea = (latlngs) => {
  if (!latlngs || latlngs.length < 3) return 0;
  
  // 确保所有点都是L.LatLng对象
  const points = latlngs.map(p => (p instanceof L.LatLng) ? p : L.latLng(p.lat, p.lng));
  
  try {
    // 确保L.GeometryUtil已正确加载
    if (!L.GeometryUtil || typeof L.GeometryUtil.geodesicArea !== 'function') {
      console.error("L.GeometryUtil.geodesicArea不可用，请确保leaflet-geometryutil库已正确加载");
      
      // 使用备用方法计算面积（平面近似）
      return Math.abs(calculateFallbackArea(points));
    }
    
    // 使用L.GeometryUtil.geodesicArea计算测地线面积
    const area = L.GeometryUtil.geodesicArea(points);
    console.log("计算的多边形面积:", area, "平方米", "点数量:", points.length);
    return Math.abs(area);
  } catch (error) {
    console.error("使用leaflet-geometryutil计算测地线面积时出错:", error);
    
    // 使用备用方法计算面积
    return Math.abs(calculateFallbackArea(points));
  }
};

/**
 * 备用面积计算方法（平面近似）
 * 使用叉积计算多边形面积
 * @param {Array<L.LatLng>} points - 多边形顶点数组
 * @returns {number} 面积（平方米）
 */
const calculateFallbackArea = (points) => {
  if (!points || points.length < 3) return 0;
  
  // 使用地球半径（米）
  const earthRadius = 6371000;
  
  // 将经纬度转换为弧度
  const pointsRadians = points.map(p => ({
    lat: p.lat * Math.PI / 180,
    lng: p.lng * Math.PI / 180
  }));
  
  // 计算面积（使用球面多边形面积公式）
  let area = 0;
  for (let i = 0; i < pointsRadians.length; i++) {
    const j = (i + 1) % pointsRadians.length;
    area += pointsRadians[i].lng * pointsRadians[j].lat - pointsRadians[j].lng * pointsRadians[i].lat;
  }
  
  // 计算最终面积（平方米）
  area = Math.abs(area) * 0.5 * earthRadius * earthRadius;
  console.log("备用方法计算的多边形面积:", area, "平方米");
  return area;
};

/**
 * 计算由LatLng点定义的多边形的测地线周长（米）。
 * 稳健地处理输入。
 * @param {Array<L.LatLng|{lat: number, lng: number}>} latlngs - 定义多边形的点数组。
 * @returns {number} 周长（米）。
 */
export const calculatePolygonPerimeter = (latLngs) => {
  if (!latLngs || latLngs.length < 2) return 0;
  
  let perimeter = 0;
  for (let i = 0; i < latLngs.length; i++) {
    const j = (i + 1) % latLngs.length; // 循环到第一个点
    perimeter += calculateDistance(latLngs[i], latLngs[j]);
  }
  
  return perimeter;
};

// 将平方米转换为平方公里
export const sqMetersToSqKilometers = (sqMeters) => {
  return sqMeters / 1000000;
};

// 将平方米转换为平方英里
export const sqMetersToSqMiles = (sqMeters) => {
  return sqMeters / 2589988.11;
};

// 将平方米转换为公顷
export const sqMetersToHectares = (sqMeters) => {
    return sqMeters / 10000;
}

// 将平方米转换为平方英尺
export const sqMetersToSqFeet = (sqMeters) => {
    return sqMeters * 10.7639;
}

// 将平方米转换为英亩
export const sqMetersToAcres = (sqMeters) => {
    return sqMeters / 4046.86; // 更标准的转换因子
}

/**
 * 将平方米为单位的面积格式化为可读字符串，根据指定的单位进行格式化。
 * @param {number} sqMeters - 面积值（平方米）。
 * @param {'metric'|'imperial'|'square_meters'|'hectares'|'square_kilometers'|'square_feet'|'acres'|'square_miles'} [unit='square_meters'] - 所需的单位。
 * @returns {string} 格式化的面积字符串。
 */
export const formatArea = (sqMeters, unit = 'square_meters') => {
  if (isNaN(sqMeters) || sqMeters === 0) return '0.00 m²';
  try {
    const options = { minimumFractionDigits: 2, maximumFractionDigits: 2 };
    
    // 确保面积值为正数
    const area = Math.abs(sqMeters);
    
    // 根据指定的单位格式化
    switch(unit) {
      case 'square_meters':
        return area.toLocaleString(undefined, options) + ' m²';
      case 'hectares':
        return sqMetersToHectares(area).toLocaleString(undefined, options) + ' ha';
      case 'square_kilometers':
        return sqMetersToSqKilometers(area).toLocaleString(undefined, options) + ' km²';
      case 'square_feet':
        return sqMetersToSqFeet(area).toLocaleString(undefined, options) + ' ft²';
      case 'acres':
        return sqMetersToAcres(area).toLocaleString(undefined, options) + ' acres';
      case 'square_miles':
        return sqMetersToSqMiles(area).toLocaleString(undefined, options) + ' mi²';
      case 'imperial':
        // 英制单位 - 根据大小自动选择最合适的单位
        const sqFeet = sqMetersToSqFeet(area);
        if (sqFeet < 43560) { // 小于1英亩
          return sqFeet.toLocaleString(undefined, options) + ' ft²';
        } else if (area < 2589988.11) { // 小于1平方英里
          const acres = sqMetersToAcres(area);
          return acres.toLocaleString(undefined, options) + ' acres';
        } else {
          const sqMiles = sqMetersToSqMiles(area);
          return sqMiles.toLocaleString(undefined, options) + ' mi²';
        }
      case 'metric':
      default:
        // 公制单位 - 根据大小自动选择最合适的单位
        if (area < 10000) { // 小于1公顷
          return area.toLocaleString(undefined, options) + ' m²';
        } else if (area < 1000000) { // 小于1平方公里
          const hectares = sqMetersToHectares(area);
          return hectares.toLocaleString(undefined, options) + ' ha';
        } else {
          const sqKm = sqMetersToSqKilometers(area);
          return sqKm.toLocaleString(undefined, options) + ' km²';
        }
    }
  } catch(e) {
    console.error("格式化面积时出错:", e);
    return '错误';
  }
};
import translations from './translations';

/**
 * 获取翻译文本，如果翻译不存在则返回键名
 * @param {string} key - 翻译键
 * @param {string} lang - 语言代码 (en/zh)
 * @returns {string} 翻译文本或键名
 */
export const getTranslation = (key, lang = 'en') => {
  if (!translations[lang] || !translations[lang][key]) {
    console.warn(`翻译缺失: ${lang}.${key}`);
    return key;
  }
  return translations[lang][key];
};

/**
 * 创建翻译函数
 * @param {string} lang - 语言代码 (en/zh)
 * @returns {Function} 翻译函数
 */
export const createTranslator = (lang) => {
  return (key) => getTranslation(key, lang);
};
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import '../../styles/MarkerTooltip.css';

const MarkerTooltip = ({
  isVisible,
  position,
  regionName,
  regionNameTH,
  totalMigration,
  t
}) => {
  const [tooltipPosition, setTooltipPosition] = useState({ x: position.x, y: position.y });

  // Update position with a slight delay to ensure stability
  useEffect(() => {
    if (isVisible) {
      // Use requestAnimationFrame for smoother updates
      const animationId = requestAnimationFrame(() => {
        setTooltipPosition({
          x: position.x,
          y: position.y
        });
      });

      return () => cancelAnimationFrame(animationId);
    }
  }, [isVisible, position.x, position.y]);

  if (!isVisible) return null;

  // Calculate position to ensure tooltip appears above the marker
  const style = {
    position: 'absolute',
    left: `${tooltipPosition.x}px`,
    top: `${tooltipPosition.y}px`,
    transform: 'translate(-50%, -150%)', // Move tooltip higher above the marker
    marginTop: '-20px', // Increase top margin for better spacing
    zIndex: 1500,
    pointerEvents: 'none', // Prevent tooltip from blocking mouse events
    willChange: 'transform', // Optimize for animations
    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))' // Add shadow for better visibility
  };

  return ReactDOM.createPortal(
    <div
      className="marker-tooltip"
      style={style}
      // Remove event handlers since we're using pointerEvents: 'none'
      // This ensures the tooltip doesn't interfere with map interactions
    >
      <div className="marker-tooltip-content">
        <div className="marker-tooltip-header">
          <h4>{regionNameTH || regionName}</h4>
        </div>
        <div className="marker-tooltip-total">
          {t.total || 'Total'}: {totalMigration.toLocaleString()}
        </div>
        <div className="marker-tooltip-hint">
          {t.clickToViewData || '👆 Click for details'}
        </div>
      </div>
    </div>,
    document.body
  );
};

export default MarkerTooltip;

/**
 * Simple Leaflet Grid Layer implementation
 */
import L from 'leaflet';

// Only define the plugin if it's not already defined
if (!L.GridLayer.Simple) {
  L.GridLayer.Simple = L.GridLayer.extend({
    options: {
      cellSize: 10000, // Default cell size in meters
      cellColor: '#3388ff', // Default cell color
      cellOpacity: 0.3, // Default cell opacity
      cellBorderWidth: 1, // Default cell border width
      cellBorderColor: '#3388ff', // Default cell border color
      cellBorderOpacity: 0.5 // Default cell border opacity
    },

    initialize: function(options) {
      L.setOptions(this, options);
      L.GridLayer.prototype.initialize.call(this, options);
    },

    createTile: function(coords) {
      const tile = document.createElement('div');
      tile.style.width = '100%';
      tile.style.height = '100%';
      tile.style.position = 'relative';
      
      const canvas = document.createElement('canvas');
      canvas.width = this.getTileSize().x;
      canvas.height = this.getTileSize().y;
      canvas.style.position = 'absolute';
      canvas.style.top = '0';
      canvas.style.left = '0';
      
      tile.appendChild(canvas);
      
      const ctx = canvas.getContext('2d');
      this._drawGrid(ctx, coords, canvas.width, canvas.height);
      
      return tile;
    },

    _drawGrid: function(ctx, coords, width, height) {
      const tileSize = this.getTileSize();
      const zoom = coords.z;
      
      // Calculate cell size in pixels at current zoom level
      const metersPerPixel = 40075016.686 / (256 * Math.pow(2, zoom));
      const cellSizeInPixels = this.options.cellSize / metersPerPixel;
      
      // Draw grid cells
      ctx.strokeStyle = this.options.cellBorderColor;
      ctx.lineWidth = this.options.cellBorderWidth;
      ctx.globalAlpha = this.options.cellBorderOpacity;
      
      // Calculate offset based on tile coords
      const offsetX = (coords.x * tileSize.x) % cellSizeInPixels;
      const offsetY = (coords.y * tileSize.y) % cellSizeInPixels;
      
      // Draw vertical lines
      for (let x = -offsetX; x <= width; x += cellSizeInPixels) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      
      // Draw horizontal lines
      for (let y = -offsetY; y <= height; y += cellSizeInPixels) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }
      
      // Draw cell fill
      ctx.fillStyle = this.options.cellColor;
      ctx.globalAlpha = this.options.cellOpacity;
      
      for (let x = -offsetX; x < width; x += cellSizeInPixels) {
        for (let y = -offsetY; y < height; y += cellSizeInPixels) {
          ctx.fillRect(x, y, cellSizeInPixels, cellSizeInPixels);
        }
      }
    }
  });

  // Factory method
  L.gridLayer = function(options) {
    return new L.GridLayer.Simple(options);
  };
}

export default L.gridLayer;

import React from 'react';
import '../../styles/MapStyleSwitcher.css';

/**
 * Map style switcher component
 * Provides day, night, and satellite view options
 */
const MapStyleSwitcher = ({ currentStyle, onStyleChange, t }) => {
  const styles = [
    { id: 'day', name: t?.dayStyle || 'Day', icon: '☀️' },
    { id: 'night', name: t?.nightStyle || 'Night', icon: '🌙' },
    { id: 'satellite', name: t?.satelliteStyle || 'Satellite', icon: '🛰️' }
  ];

  return (
    <div className="map-style-switcher">
      {styles.map(style => (
        <button
          key={style.id}
          className={`style-button ${currentStyle === style.id ? 'active' : ''}`}
          onClick={() => onStyleChange(style.id)}
          title={style.name}
        >
          <span className="style-icon">{style.icon}</span>
          <span className="style-name">{style.name}</span>
        </button>
      ))}
    </div>
  );
};

export default MapStyleSwitcher;
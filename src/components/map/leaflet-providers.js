/**
 * Simple implementation of Leaflet-providers
 */
import L from 'leaflet';

// Only define the plugin if it's not already defined
if (!<PERSON>.TileLayer.Provider) {
  // Define provider configurations
  L.TileLayer.Provider = L.TileLayer.extend({
    initialize: function(name, options) {
      const providers = L.TileLayer.Provider.providers;
      const parts = name.split('.');
      
      const providerName = parts[0];
      const variantName = parts[1];
      
      if (!providers[providerName]) {
        throw new Error(`No such provider (${providerName})`);
      }
      
      const provider = {
        url: providers[providerName].url,
        options: providers[providerName].options
      };
      
      // Overwrite values in provider from variant
      if (variantName && providers[providerName].variants && providers[providerName].variants[variantName]) {
        const variant = providers[providerName].variants[variantName];
        const variantOptions = variant.options || {};
        provider.url = variant.url || provider.url;
        provider.options = L.Util.extend({}, provider.options, variantOptions);
      }
      
      // Replace placeholders in urls
      if (provider.url.indexOf('{variant}') !== -1) {
        provider.url = provider.url.replace('{variant}', variantName || 'default');
      }
      
      const finalOptions = L.Util.extend({}, provider.options, options);
      L.TileLayer.prototype.initialize.call(this, provider.url, finalOptions);
    }
  });
  
  // Define providers
  L.TileLayer.Provider.providers = {
    OpenStreetMap: {
      url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      options: {
        maxZoom: 19,
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      },
      variants: {
        HOT: {
          url: 'https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png',
          options: {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, Tiles style by <a href="https://www.hotosm.org/">HOT</a>'
          }
        }
      }
    },
    Stamen: {
      url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/{variant}/{z}/{x}/{y}{r}.{ext}',
      options: {
        attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        subdomains: 'abcd',
        minZoom: 0,
        maxZoom: 20,
        ext: 'png',
        variant: 'toner',
        r: ''
      },
      variants: {
        Toner: {
          options: {
            variant: 'toner'
          }
        },
        TonerBackground: {
          options: {
            variant: 'toner-background'
          }
        },
        TonerLite: {
          options: {
            variant: 'toner-lite'
          }
        },
        Watercolor: {
          options: {
            variant: 'watercolor',
            minZoom: 1,
            maxZoom: 16
          }
        },
        Terrain: {
          options: {
            variant: 'terrain',
            minZoom: 0,
            maxZoom: 18
          }
        },
        TerrainBackground: {
          options: {
            variant: 'terrain-background',
            minZoom: 0,
            maxZoom: 18
          }
        }
      }
    },
    Esri: {
      url: 'https://server.arcgisonline.com/ArcGIS/rest/services/{variant}/MapServer/tile/{z}/{y}/{x}',
      options: {
        variant: 'World_Street_Map',
        attribution: 'Tiles &copy; Esri'
      },
      variants: {
        WorldStreetMap: {
          options: {
            variant: 'World_Street_Map',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012'
          }
        },
        WorldImagery: {
          options: {
            variant: 'World_Imagery',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
          }
        },
        WorldTerrain: {
          options: {
            variant: 'World_Terrain_Base',
            attribution: 'Tiles &copy; Esri &mdash; Source: USGS, Esri, TANA, DeLorme, and NPS'
          }
        },
        WorldShadedRelief: {
          options: {
            variant: 'World_Shaded_Relief',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri'
          }
        },
        WorldPhysical: {
          options: {
            variant: 'World_Physical_Map',
            attribution: 'Tiles &copy; Esri &mdash; Source: US National Park Service'
          }
        },
        OceanBasemap: {
          options: {
            variant: 'Ocean_Basemap',
            attribution: 'Tiles &copy; Esri &mdash; Sources: GEBCO, NOAA, CHS, OSU, UNH, CSUMB, National Geographic, DeLorme, NAVTEQ, and Esri'
          }
        },
        NatGeoWorldMap: {
          options: {
            variant: 'NatGeo_World_Map',
            attribution: 'Tiles &copy; Esri &mdash; National Geographic, Esri, DeLorme, NAVTEQ, UNEP-WCMC, USGS, NASA, ESA, METI, NRCAN, GEBCO, NOAA, iPC'
          }
        }
      }
    }
  };
  
  // Factory method
  L.tileLayer.provider = function(provider, options) {
    return new L.TileLayer.Provider(provider, options);
  };
}

export default L.tileLayer.provider;

/* 站点选择地图样式 */
.site-selection-map-container {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* 地图薪酬计算器集成容器 */
.map-payroll-calculator-container {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 1000;
  max-width: 400px;
  width: calc(100% - 32px);
}

.map-integrated-calculator {
  margin-bottom: 16px;
}

/* 薪酬数据预览小部件 */
.payroll-preview-widget {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  animation: slideInFromTop 0.5s ease-out;
}

.preview-header {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.preview-label {
  color: #6c757d;
  font-weight: 500;
}

.preview-value {
  color: #2c3e50;
  font-weight: 600;
}

/* 地块选择提示 */
.parcel-selection-hint {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.hint-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: bounce 2s infinite;
}

/* 地图标记薪酬指示器 */
.site-selection-marker.has-payroll {
  position: relative;
}

.payroll-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 10px;
  background: #ffd700;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@keyframes payrollGlow {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
  }
  50% {
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.8);
  }
}

/* 弹窗薪酬信息样式 */
.popup-payroll-info {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 12px;
  padding-top: 12px;
}

.payroll-header {
  font-weight: 600;
  color: #ffd700;
  font-size: 12px;
  margin-bottom: 8px;
  text-align: center;
}

.payroll-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.payroll-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.payroll-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.payroll-value {
  color: #ffd700;
  font-weight: 600;
}

/* 薪酬计算按钮 */
.calculate-payroll-btn {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #1a1a1a;
  border: 1px solid #ffd700;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.calculate-payroll-btn:hover {
  background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

/* 薪酬成功通知 */
.payroll-success-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
  animation: slideInFromRight 0.5s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
}

.notification-content {
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}

/* QCEW计算器地图集成增强样式 */
.qcew-calculator-container .parcel-indicator {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  margin-left: 8px;
  font-weight: 500;
}

.location-context {
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  margin: 8px 0;
  font-size: 12px;
  color: #155724;
  text-align: center;
}

.location-hint {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  margin-left: 8px;
  font-weight: 500;
}

.parcel-context {
  color: #6c757d;
  font-weight: 500;
}

/* 专业地图控制器 */
.professional-map-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1000;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.map-style-switcher-pro {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  min-width: 200px;
}

.switcher-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 12px 16px;
}

.switcher-title {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.style-options {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.style-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  text-align: left;
  width: 100%;
}

.style-option:hover {
  background: rgba(77, 200, 255, 0.08);
  border-color: rgba(77, 200, 255, 0.2);
  color: #2c3e50;
  transform: translateY(-1px);
}

.style-option.active {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.08) 100%);
  border-color: rgba(77, 200, 255, 0.3);
  color: #1e40af;
  box-shadow: 0 2px 8px rgba(77, 200, 255, 0.2);
}

.style-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: inherit;
}

.style-name {
  font-weight: 500;
  color: inherit;
}

/* 站点标记样式 */
.site-selection-marker {
  animation: markerPulse 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.site-selection-marker.selected {
  animation: selectedMarkerGlow 1.5s ease-in-out infinite;
}

@keyframes markerPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(77, 200, 255, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(77, 200, 255, 0.6);
  }
}

@keyframes selectedMarkerGlow {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 8px 20px rgba(0, 255, 136, 0.8);
  }
}

/* 弹窗样式 */
.site-selection-popup .leaflet-popup-content-wrapper {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
}

.site-selection-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
  color: #ffffff;
}

.site-selection-popup .leaflet-popup-tip {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.site-marker-popup {
  padding: 16px;
  min-width: 220px;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.popup-header h4 {
  color: #4dc8ff;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.parcel-id {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-family: monospace;
}

.popup-content {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
}

.info-item .label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-item .value {
  color: #ffffff;
  font-weight: 600;
}

.select-popup-btn {
  width: 100%;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.25) 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-popup-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.25) 0%, rgba(77, 200, 255, 0.35) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);
}

.select-popup-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.site-selection-popup-horizontal .leaflet-popup-content-wrapper {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
}

.site-selection-popup-horizontal .leaflet-popup-content {
  margin: 0;
  padding: 0;
  color: #ffffff;
}

.site-selection-popup-horizontal .leaflet-popup-tip {
  background: #1a1a2e;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.site-marker-popup-horizontal {
  padding: 16px;
  min-width: 300px;
}

.horizontal-popup-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.popup-site-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.site-rank-badge {
  background: linear-gradient(135deg, #4dc8ff 0%, #00ff88 100%);
  color: #0a0f1c;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);
}

.site-basic-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.site-id {
  color: #4dc8ff;
  font-size: 12px;
  font-weight: 600;
  font-family: monospace;
}

.site-cost {
  color: #00ff88;
  font-size: 14px;
  font-weight: 700;
}

.popup-details-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.detail-value {
  color: #ffffff;
  font-weight: 600;
  font-size: 12px;
}

.detail-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-separator {
  color: rgba(255, 255, 255, 0.3);
  font-weight: 300;
}

.popup-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.select-popup-btn-horizontal {
  flex: 1;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.15) 0%, rgba(77, 200, 255, 0.25) 100%);
  border: 1px solid rgba(77, 200, 255, 0.3);
  color: #4dc8ff;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.select-popup-btn-horizontal:hover:not(.selected) {
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.25) 0%, rgba(77, 200, 255, 0.35) 100%);
  border-color: rgba(77, 200, 255, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);
}

.select-popup-btn-horizontal.selected {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.2) 0%, rgba(0, 255, 136, 0.3) 100%);
  border-color: rgba(0, 255, 136, 0.5);
  color: #00ff88;
  cursor: default;
}

.select-popup-btn-horizontal.selected:hover {
  transform: none;
  box-shadow: none;
}

/* 动画效果 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-5px);
  }
  60% {
    transform: translateX(-50%) translateY(-3px);
  }
}

.map-empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.map-empty-state p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .map-payroll-calculator-container {
    position: static;
    margin-bottom: 16px;
    max-width: 100%;
  }

  .payroll-preview-widget {
    margin: 0 16px 16px 16px;
  }

  .map-style-switcher {
    top: 12px;
    right: 12px;
    min-width: 160px;
  }

  .style-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .site-marker-popup {
    padding: 12px;
    min-width: 200px;
  }

  .popup-header h4 {
    font-size: 13px;
  }

  .parcel-id {
    font-size: 11px;
  }

  .info-item {
    font-size: 11px;
  }

  .select-popup-btn {
    padding: 6px 12px;
    font-size: 11px;
  }

  .site-marker-popup-horizontal {
    padding: 12px;
    min-width: 280px;
  }

  .popup-site-info {
    gap: 8px;
  }

  .site-rank-badge {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .site-id {
    font-size: 11px;
  }

  .site-cost {
    font-size: 13px;
  }

  .popup-details-row {
    font-size: 10px;
    gap: 8px;
  }

  .detail-value {
    font-size: 11px;
  }

  .detail-label {
    font-size: 9px;
  }

  .popup-actions {
    gap: 6px;
  }

  .select-popup-btn-horizontal {
    padding: 6px 12px;
    font-size: 11px;
  }

  .map-empty-state {
    padding: 0 20px;
  }

  .empty-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }

  .map-empty-state p {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .site-marker-popup {
    padding: 10px;
    min-width: 180px;
  }

  .popup-header {
    margin-bottom: 8px;
    padding-bottom: 6px;
  }

  .popup-header h4 {
    font-size: 12px;
  }

  .parcel-id {
    font-size: 10px;
  }

  .info-item {
    font-size: 10px;
    margin-bottom: 4px;
  }

  .select-popup-btn {
    padding: 6px 10px;
    font-size: 10px;
  }

  .site-marker-popup-horizontal {
    padding: 10px;
    min-width: 260px;
  }

  .popup-site-info {
    gap: 6px;
  }

  .site-rank-badge {
    width: 24px;
    height: 24px;
    font-size: 11px;
  }

  .site-id {
    font-size: 10px;
  }

  .site-cost {
    font-size: 12px;
  }

  .popup-details-row {
    font-size: 9px;
    gap: 6px;
  }

  .detail-value {
    font-size: 10px;
  }

  .detail-label {
    font-size: 8px;
  }

  .detail-separator {
    display: none;
  }

  .popup-actions {
    gap: 4px;
  }

  .select-popup-btn-horizontal {
    padding: 5px 8px;
    font-size: 10px;
  }

  .map-empty-state {
    padding: 0 16px;
  }

  .empty-icon {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .map-empty-state p {
    font-size: 12px;
  }
}

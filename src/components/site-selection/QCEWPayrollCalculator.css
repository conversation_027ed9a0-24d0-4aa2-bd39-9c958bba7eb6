/**
 * 🏭 QCEW Payroll Calculator Styles
 * QCEW薪酬计算器组件样式
 */

.qcew-payroll-calculator {
    position: relative;
    z-index: 1000;
}

/* 触发按钮样式 */
.qcew-trigger-button {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1001;
}

.qcew-trigger-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.trigger-icon {
    font-size: 16px;
}

.trigger-label {
    font-size: 13px;
}

.trigger-indicator {
    font-size: 18px;
    font-weight: bold;
    margin-left: 4px;
}

/* 计算器面板样式 */
.qcew-calculator-panel {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 500px;
    max-width: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 面板头部 */
.qcew-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 12px 12px 0 0;
}

.qcew-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 18px;
    font-weight: 600;
}

.qcew-close-button {
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.qcew-close-button:hover {
    background: #e2e8f0;
    color: #2d3748;
}

/* 面板内容 */
.qcew-form {
    padding: 20px;
}

.qcew-input-group {
    margin-bottom: 16px;
}

.qcew-input-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #2d3748;
    font-size: 14px;
}

.qcew-input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.qcew-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 城市输入容器 */
.qcew-city-input-container {
    position: relative;
}

.qcew-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.qcew-search-indicator {
    position: absolute;
    right: 12px;
    color: #667eea;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 智能建议下拉框 */
.qcew-suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #667eea;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1002;
}

.qcew-suggestions-header {
    padding: 8px 12px;
    background: #f7fafc;
    font-size: 12px;
    font-weight: 600;
    color: #4a5568;
    border-bottom: 1px solid #e2e8f0;
}

.qcew-suggestion-item {
    padding: 12px;
    cursor: pointer;
    border-bottom: 1px solid #f7fafc;
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.qcew-suggestion-item:hover {
    background: #f7fafc;
}

.qcew-suggestion-item:last-child {
    border-bottom: none;
}

.qcew-suggestion-text {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 2px;
}

.qcew-suggestion-reason {
    font-size: 12px;
    color: #718096;
}

.qcew-suggestion-badge {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 12px;
    font-weight: 600;
    white-space: nowrap;
}

/* 建议类型样式 */
.qcew-available {
    border-left: 4px solid #48bb78;
}

.qcew-available .qcew-suggestion-badge {
    background: #c6f6d5;
    color: #22543d;
}

.qcew-recommended {
    border-left: 4px solid #ed8936;
}

.qcew-recommended .qcew-suggestion-badge {
    background: #feebc8;
    color: #c05621;
}

.qcew-spelling {
    border-left: 4px solid #4299e1;
}

.qcew-spelling .qcew-suggestion-badge {
    background: #bee3f8;
    color: #2c5282;
}

.qcew-alternative {
    border-left: 4px solid #9f7aea;
}

.qcew-alternative .qcew-suggestion-badge {
    background: #e9d8fd;
    color: #553c9a;
}

.qcew-unavailable {
    border-left: 4px solid #f56565;
    opacity: 0.7;
}

.qcew-unavailable .qcew-suggestion-badge {
    background: #fed7d7;
    color: #c53030;
}

/* 置信度样式 */
.qcew-confidence-high {
    background: #f0fff4;
}

.qcew-confidence-medium {
    background: #fffbeb;
}

.qcew-confidence-low {
    background: #fef5e7;
}

/* 操作按钮 */
.qcew-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.qcew-calculate-button {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    flex: 1;
    transition: all 0.2s ease;
}

.qcew-calculate-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(72, 187, 120, 0.3);
}

.qcew-calculate-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.qcew-clear-button {
    background: #e2e8f0;
    color: #4a5568;
    border: none;
    padding: 12px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.qcew-clear-button:hover {
    background: #cbd5e0;
}

/* 推荐城市 */
.qcew-recommendations {
    padding: 16px 20px;
    border-top: 1px solid #e2e8f0;
    background: #f7fafc;
}

.qcew-recommendations h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: #4a5568;
}

.qcew-recommended-cities {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.qcew-city-chip {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.qcew-city-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(237, 137, 54, 0.3);
}

/* 错误显示 */
.qcew-error {
    background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
    border: 1px solid #fc8181;
    border-radius: 6px;
    padding: 12px;
    margin: 16px 0;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.qcew-error-icon {
    color: #c53030;
    font-size: 16px;
    margin-top: 2px;
}

.qcew-error-text {
    color: #742a2a;
    font-size: 14px;
    line-height: 1.4;
    white-space: pre-line;
}

/* 结果显示 */
.qcew-results {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    border-radius: 8px;
    border: 1px solid #9ae6b4;
}

.qcew-results h4 {
    margin: 0 0 16px 0;
    color: #22543d;
    font-size: 16px;
}

.qcew-calculation-result {
    background: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.qcew-result-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e2e8f0;
}

.qcew-result-header h5 {
    margin: 0 0 4px 0;
    color: #2d3748;
    font-size: 16px;
}

.qcew-result-formula {
    font-size: 12px;
    color: #718096;
    font-family: monospace;
    background: #f7fafc;
    padding: 4px 8px;
    border-radius: 4px;
}

.qcew-result-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.qcew-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f7fafc;
    border-radius: 6px;
}

.qcew-result-item.qcew-highlight {
    background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);
    border: 2px solid #f6ad55;
    grid-column: 1 / -1;
    font-weight: 600;
}

.qcew-result-label {
    font-size: 13px;
    color: #4a5568;
}

.qcew-result-value {
    font-weight: 600;
    color: #2d3748;
}

.qcew-highlight .qcew-result-value {
    color: #c05621;
    font-size: 18px;
}

.qcew-data-info {
    padding-top: 12px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.qcew-data-info small {
    color: #718096;
    line-height: 1.4;
}

/* 状态栏 */
.qcew-status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    background: #edf2f7;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 12px 12px;
    font-size: 12px;
}

.qcew-status-item {
    color: #4a5568;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 640px) {
    .qcew-calculator-panel {
        min-width: 320px;
        max-width: 95vw;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .qcew-result-grid {
        grid-template-columns: 1fr;
    }
    
    .qcew-actions {
        flex-direction: column;
    }
    
    .qcew-status-bar {
        flex-direction: column;
        gap: 4px;
        align-items: flex-start;
    }
}

/* 滚动条样式 */
.qcew-suggestions-dropdown::-webkit-scrollbar {
    width: 6px;
}

.qcew-suggestions-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.qcew-suggestions-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.qcew-suggestions-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* 加载动画 */
.qcew-calculate-button:disabled {
    position: relative;
    overflow: hidden;
}

.qcew-calculate-button:disabled::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .qcew-calculator-panel {
        border: 2px solid #000;
    }
    
    .qcew-suggestion-item {
        border-bottom: 1px solid #000;
    }
    
    .qcew-input:focus {
        border-color: #000;
        box-shadow: 0 0 0 2px #000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* QCEW Payroll Calculator Styles - 多年份数据支持 + 地图集成 */

.qcew-calculator-container {
    width: 100%;
    margin: 20px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.qcew-toggle-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.qcew-toggle-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* 地块指示器样式 */
.parcel-indicator {
    background: rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
    padding: 2px 8px !important;
    border-radius: 12px !important;
    font-size: 11px !important;
    margin-left: 8px !important;
    font-weight: 500 !important;
    animation: parcelPulse 2s ease-in-out infinite;
}

@keyframes parcelPulse {
    0%, 100% {
        background: rgba(255, 255, 255, 0.2);
    }
    50% {
        background: rgba(255, 255, 255, 0.3);
    }
}

.qcew-calculator-panel {
    background: white;
    border-radius: 20px;
    padding: 24px;
    margin-top: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e8ed;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.qcew-header {
    text-align: center;
    margin-bottom: 24px;
    position: relative;
    padding-bottom: 16px;
    border-bottom: 2px solid #f0f3f7;
}

.qcew-header h3 {
    color: #2c3e50;
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
}

.qcew-header p {
    color: #7f8c8d;
    margin: 0;
    font-size: 14px;
}

/* 位置上下文样式 */
.location-context {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    font-size: 12px;
    color: #155724;
    text-align: center;
    animation: locationGlow 3s ease-in-out infinite;
}

@keyframes locationGlow {
    0%, 100% {
        border-color: rgba(0, 255, 136, 0.3);
        background: rgba(0, 255, 136, 0.1);
    }
    50% {
        border-color: rgba(0, 255, 136, 0.5);
        background: rgba(0, 255, 136, 0.15);
    }
}

.qcew-close-button {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qcew-close-button:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* 表单样式 */
.qcew-form {
    margin-bottom: 24px;
}

.qcew-input-group {
    margin-bottom: 20px;
}

.qcew-input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 位置提示样式 */
.location-hint {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    margin-left: 8px;
    font-weight: 500;
    animation: hintBlink 2s ease-in-out infinite;
}

@keyframes hintBlink {
    0%, 80%, 100% {
        opacity: 1;
    }
    40% {
        opacity: 0.7;
    }
}

.qcew-input-wrapper {
    position: relative;
}

.qcew-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
    box-sizing: border-box;
}

.qcew-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.qcew-search-indicator {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 搜索结果和建议样式 */
.qcew-suggestions-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e1e8ed;
    border-top: none;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

.qcew-results-section, .qcew-suggestions-section {
    border-bottom: 1px solid #f0f3f7;
}

.qcew-results-section:last-child, .qcew-suggestions-section:last-child {
    border-bottom: none;
}

.qcew-suggestions-header {
    background: #f8f9fa;
    padding: 12px 16px;
    font-weight: 600;
    font-size: 13px;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 地区搜索结果样式 */
.qcew-area-result {
    padding: 16px;
    border-bottom: 1px solid #f0f3f7;
    transition: background-color 0.2s ease;
}

.qcew-area-result:hover {
    background-color: #f8f9fa;
}

.qcew-area-result:last-child {
    border-bottom: none;
}

.qcew-area-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.qcew-area-years {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.qcew-year-button {
    background: #e3f2fd;
    color: #1976d2;
    border: 2px solid #bbdefb;
    padding: 6px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qcew-year-button:hover {
    background: #bbdefb;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

.qcew-year-button.selected {
    background: #1976d2;
    color: white;
    border-color: #1565c0;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.qcew-area-type {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

/* 建议项样式 */
.qcew-suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f0f3f7;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.qcew-suggestion-item:hover {
    background-color: #f8f9fa;
}

.qcew-suggestion-item:last-child {
    border-bottom: none;
}

.qcew-suggestion-text {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.qcew-suggestion-reason {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.qcew-suggestion-badge {
    font-size: 11px;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    align-self: flex-start;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 建议类型颜色 */
.qcew-available .qcew-suggestion-badge {
    background: #d4edda;
    color: #155724;
}

.qcew-recommended .qcew-suggestion-badge,
.qcew-popular .qcew-suggestion-badge {
    background: #fff3cd;
    color: #856404;
}

.qcew-spelling .qcew-suggestion-badge {
    background: #cce7ff;
    color: #004085;
}

.qcew-alternative .qcew-suggestion-badge,
.qcew-economic_alternative .qcew-suggestion-badge {
    background: #fed7aa;
    color: #9a3412;
}

.qcew-unavailable .qcew-suggestion-badge {
    background: #f8d7da;
    color: #721c24;
}

/* 无搜索结果样式 */
.qcew-no-results {
    text-align: center;
    padding: 32px 16px;
    color: #6c757d;
}

.qcew-no-results-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.qcew-no-results-text {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #495057;
}

.qcew-no-results-suggestion {
    font-size: 14px;
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.qcew-no-results-suggestion ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.qcew-no-results-suggestion li {
    margin-bottom: 4px;
}

/* 选中地区显示 */
.qcew-selected-area {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 2px solid #28a745;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    animation: selectedAreaGlow 3s ease-in-out infinite;
}

@keyframes selectedAreaGlow {
    0%, 100% {
        border-color: #28a745;
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.3);
    }
    50% {
        border-color: #20c997;
        box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
    }
}

.qcew-selected-header {
    font-weight: 600;
    color: #155724;
    font-size: 14px;
    margin-bottom: 8px;
}

.qcew-selected-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.qcew-selected-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.qcew-selected-year {
    font-size: 14px;
    color: #28a745;
    font-weight: 500;
}

/* 操作按钮 */
.qcew-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.qcew-calculate-button {
    flex: 1;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.qcew-calculate-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.qcew-calculate-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

.qcew-clear-button {
    background: #6c757d;
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.qcew-clear-button:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* 推荐地区 */
.qcew-recommendations {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.qcew-recommendations h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
}

.qcew-recommended-cities {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.qcew-city-chip {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.qcew-city-chip:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 错误显示 */
.qcew-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.qcew-error-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.qcew-error-text {
    font-size: 14px;
    line-height: 1.4;
}

/* 计算结果 */
.qcew-results {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #28a745;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 20px;
    animation: resultsAppear 0.5s ease-out;
}

@keyframes resultsAppear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.qcew-results h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 700;
    text-align: center;
}

.qcew-result-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
}

.qcew-result-header h5 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 700;
}

.qcew-result-subtitle {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

/* 地块上下文样式 */
.parcel-context {
    color: #6c757d;
    font-weight: 500;
}

.qcew-result-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.qcew-result-item {
    background: white;
    padding: 16px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: all 0.2s ease;
}

.qcew-result-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.qcew-result-item.qcew-highlight {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border-color: #28a745;
    border-width: 2px;
}

.qcew-result-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.qcew-result-value {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
}

.qcew-highlight .qcew-result-value {
    color: #28a745;
    font-size: 20px;
}

.qcew-data-info {
    text-align: center;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.qcew-data-info small {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
}

/* 状态指示器 */
.qcew-status-bar {
    display: flex;
    gap: 16px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 12px;
    color: #6c757d;
    flex-wrap: wrap;
    justify-content: center;
}

.qcew-status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
}

/* 地图集成特定样式 */
.map-integrated-calculator .qcew-toggle-button {
    background: rgba(255, 255, 255, 0.95);
    color: #2c3e50;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.map-integrated-calculator .qcew-toggle-button:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.map-integrated-calculator .qcew-calculator-panel {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .qcew-calculator-panel {
        padding: 16px;
        margin-top: 12px;
        border-radius: 16px;
    }

    .qcew-header h3 {
        font-size: 20px;
    }

    .qcew-actions {
        flex-direction: column;
    }

    .qcew-result-grid {
        grid-template-columns: 1fr;
    }

    .qcew-area-years {
        justify-content: flex-start;
    }

    .qcew-status-bar {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .location-context {
        font-size: 11px;
        padding: 6px 10px;
    }

    .parcel-indicator {
        font-size: 10px !important;
        padding: 1px 6px !important;
    }
}

@media (max-width: 480px) {
    .qcew-calculator-panel {
        padding: 12px;
        max-height: 90vh;
    }

    .qcew-toggle-button {
        padding: 10px 20px;
        font-size: 14px;
        flex-direction: column;
        gap: 4px;
    }

    .qcew-suggestions-dropdown {
        max-height: 300px;
    }

    .qcew-result-value {
        font-size: 16px;
    }

    .qcew-highlight .qcew-result-value {
        font-size: 18px;
    }

    .location-context {
        font-size: 10px;
        padding: 4px 8px;
    }

    .location-hint {
        font-size: 9px !important;
        padding: 1px 4px !important;
    }
}

/* 可访问性支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-color-scheme: dark) {
    .qcew-calculator-panel {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .qcew-header h3 {
        color: #e2e8f0;
    }

    .qcew-input {
        background: #4a5568;
        border-color: #6b7280;
        color: #e2e8f0;
    }

    .qcew-suggestions-dropdown {
        background: #4a5568;
        border-color: #6b7280;
    }

    .qcew-suggestion-item:hover {
        background-color: #6b7280;
    }

    .location-context {
        background: rgba(0, 255, 136, 0.2);
        border-color: rgba(0, 255, 136, 0.4);
        color: #9ae6b4;
    }
}

/* 打印样式 */
@media print {
    .qcew-toggle-button,
    .qcew-close-button,
    .qcew-actions,
    .qcew-recommendations,
    .qcew-suggestions-dropdown {
        display: none !important;
    }

    .qcew-calculator-panel {
        box-shadow: none;
        border: 1px solid #000;
    }

    .location-context {
        border: 1px solid #000;
        background: #f5f5f5;
        color: #000;
    }
}

/* 服务不可用状态样式 */
.qcew-toggle-button.disabled {
    background: #f8f9fa;
    border: 2px solid #e74c3c;
    color: #e74c3c;
    cursor: not-allowed;
}

.qcew-toggle-button.disabled:hover {
    background: #f8f9fa;
    transform: none;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.qcew-service-unavailable {
    padding: 20px;
    background: #fff3f3;
    border: 1px solid #ffebee;
    border-radius: 8px;
    margin: 15px 0;
}

.unavailable-message h4 {
    color: #d32f2f;
    margin-bottom: 10px;
    font-size: 16px;
}

.unavailable-message ul {
    list-style-type: none;
    padding-left: 0;
    margin: 10px 0;
}

.unavailable-message li {
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    color: #666;
}

.unavailable-message li:before {
    content: "→";
    position: absolute;
    left: 0;
    color: #e74c3c;
    font-weight: bold;
}

.external-links {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.external-links h4 {
    color: #1976d2;
    margin-bottom: 10px;
    font-size: 16px;
}

.qcew-external-link {
    display: inline-block;
    margin: 5px 10px 5px 0;
    padding: 8px 15px;
    background: #e3f2fd;
    color: #1976d2;
    text-decoration: none;
    border-radius: 5px;
    border: 1px solid #bbdefb;
    font-size: 14px;
    transition: all 0.3s ease;
}

.qcew-external-link:hover {
    background: #bbdefb;
    color: #0d47a1;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
} 
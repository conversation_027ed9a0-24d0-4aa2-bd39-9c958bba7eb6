/**
 * 🏭 Site Selection Map Component
 * 站点选择地图组件 - 显示勘探结果的地图视图，集成QCEW薪酬计算器
 */

import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, <PERSON><PERSON>, Popup, useMap } from 'react-leaflet';
import L from 'leaflet';
import { formatFinancialValue } from '../../services/siteSelectionService';
import QCEWPayrollCalculator from './QCEWPayrollCalculator';
import LAUSCalculator from './LAUSCalculator';
import CpiEscalationCalculator from './CpiEscalationCalculator';
import './SiteSelectionMap.css';

// 地图样式配置
const mapTileUrls = {
  street: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  satellite: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
  terrain: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png'
};

const mapAttributions = {
  street: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
  satellite: '&copy; <a href="https://www.esri.com/">Esri</a>',
  terrain: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://opentopomap.org">OpenTopoMap</a>'
};

// 创建站点选择标记图标
const createSiteMarker = (rank, isSelected = false, hasPayrollData = false) => {
  const color = isSelected ? '#00ff88' : '#4dc8ff';
  const size = isSelected ? 40 : 32;
  const glowColor = hasPayrollData ? '#ffd700' : color; // 金色表示有薪酬数据
  
  const iconHtml = `
    <div class="site-selection-marker ${isSelected ? 'selected' : ''} ${hasPayrollData ? 'has-payroll' : ''}" style="
      width: ${size}px;
      height: ${size}px;
      background: linear-gradient(135deg, ${color} 0%, ${color}aa 100%);
      border: 2px solid ${glowColor};
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #0a0f1c;
      font-weight: 700;
      font-size: ${size > 35 ? '14px' : '12px'};
      box-shadow: 0 4px 12px rgba(77, 200, 255, 0.4);
      position: relative;
      z-index: ${isSelected ? 1000 : 100};
      ${hasPayrollData ? 'animation: payrollGlow 2s ease-in-out infinite;' : ''}
    ">
      ${rank}
      ${hasPayrollData ? '<div class="payroll-indicator">💰</div>' : ''}
    </div>
  `;
  
  return L.divIcon({
    className: '',
    html: iconHtml,
    iconSize: [size, size],
    iconAnchor: [size / 2, size / 2],
    popupAnchor: [0, -size / 2 - 5]
  });
};

// 地图控制器组件
const MapController = ({ parcels, selectedParcel, onParcelSelect, onResetView, payrollData }) => {
  const map = useMap();
  const markersRef = useRef([]);
  const hasInitializedView = useRef(false);

  // 添加地图拖拽事件处理，用于触发玻璃折射效果
  useEffect(() => {
    const mapContainer = map.getContainer().closest('.liquid-map-container');

    const handleDragStart = () => {
      if (mapContainer) {
        mapContainer.classList.add('dragging');
      }
    };

    const handleDragEnd = () => {
      if (mapContainer) {
        // 延迟移除类名，让折射动画完成
        setTimeout(() => {
          mapContainer.classList.remove('dragging');
        }, 1500);
      }
    };

    map.on('dragstart', handleDragStart);
    map.on('dragend', handleDragEnd);

    return () => {
      map.off('dragstart', handleDragStart);
      map.off('dragend', handleDragEnd);
    };
  }, [map]);

  useEffect(() => {
    if (!parcels || parcels.length === 0) return;

    // 清除现有标记
    markersRef.current.forEach(marker => {
      map.removeLayer(marker);
    });
    markersRef.current = [];

    // 添加新标记
    parcels.forEach((parcel, index) => {
      const rank = index + 1;
      const isSelected = selectedParcel?.parcel_id === parcel.parcel_id;
      const hasPayrollData = payrollData && selectedParcel?.parcel_id === parcel.parcel_id;
      // 修复: 使用正确的location属性
      const position = [parcel.location?.lat || 0, parcel.location?.lng || 0]; // [lat, lng]

      const marker = L.marker(position, {
        icon: createSiteMarker(rank, isSelected, hasPayrollData),
        zIndexOffset: isSelected ? 1000 : 100
      });

      // 创建扁平横向弹窗内容，包含薪酬信息
      const popupContent = document.createElement('div');
      popupContent.className = 'site-marker-popup-horizontal';
      
      let payrollInfo = '';
      if (hasPayrollData && payrollData.calculation) {
        payrollInfo = `
          <div class="popup-payroll-info">
            <div class="payroll-header">💰 薪酬分析</div>
            <div class="payroll-details">
              <div class="payroll-item">
                <span class="payroll-label">年薪酬总额:</span>
                <span class="payroll-value">${payrollData.calculation.result.formatted_payroll}</span>
              </div>
              <div class="payroll-item">
                <span class="payroll-label">地区:</span>
                <span class="payroll-value">${payrollData.calculation.inputs.area_title}</span>
              </div>
              <div class="payroll-item">
                <span class="payroll-label">员工数:</span>
                <span class="payroll-value">${payrollData.calculation.inputs.planned_fte}人</span>
              </div>
            </div>
          </div>
        `;
      }
      
      popupContent.innerHTML = `
        <div class="horizontal-popup-container">
          <div class="popup-site-info">
            <div class="site-rank-badge">${rank}</div>
            <div class="site-basic-info">
              <span class="site-id">${parcel.parcel_id}</span>
              <span class="site-cost">${formatFinancialValue(parcel.total_cost)}</span>
            </div>
          </div>
          <div class="popup-details-row">
            <div class="detail-item">
              <span class="detail-value">${parcel.area_sqft?.toLocaleString()}</span>
              <span class="detail-label">sq ft</span>
            </div>
            <div class="detail-separator">|</div>
            <div class="detail-item">
              <span class="detail-value">${(parcel.location?.lat || 0).toFixed(3)}, ${(parcel.location?.lng || 0).toFixed(3)}</span>
              <span class="detail-label">坐标</span>
            </div>
          </div>
          ${payrollInfo}
          <div class="popup-actions">
            <button class="select-popup-btn-horizontal ${isSelected ? 'selected' : ''}" data-parcel-id="${parcel.parcel_id}">
              ${isSelected ? '✓ 已选择' : '选择'}
            </button>
            ${isSelected ? '<button class="calculate-payroll-btn" data-parcel-id="' + parcel.parcel_id + '">🧮 计算薪酬</button>' : ''}
          </div>
        </div>
      `;

      // 添加按钮点击事件
      const selectBtn = popupContent.querySelector('.select-popup-btn-horizontal');
      selectBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isSelected) {
          onParcelSelect(parcel);
          // 自动缩放到选中的地块位置，使用相同的偏移逻辑
          const currentZoom = map.getZoom();
          const targetZoom = Math.max(currentZoom, 15);

          if (currentZoom < 15) {
            // 计算偏移位置，让marker显示在地图下方1/3处
            const mapSize = map.getSize();
            const offsetY = mapSize.y * 0.15;
            const targetPoint = map.project(position, targetZoom);
            const offsetPoint = L.point(targetPoint.x, targetPoint.y - offsetY);
            const offsetLatLng = map.unproject(offsetPoint, targetZoom);

            map.flyTo(offsetLatLng, targetZoom, {
              duration: 1.0,
              easeLinearity: 0.25
            });
          }
        }
        map.closePopup();
      });

      // 添加薪酬计算按钮事件
      const payrollBtn = popupContent.querySelector('.calculate-payroll-btn');
      if (payrollBtn) {
        payrollBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          map.closePopup();
          
          // 触发薪酬计算器显示
          const calculatorToggle = document.querySelector('.qcew-toggle-button');
          if (calculatorToggle && !calculatorToggle.textContent.includes('👆')) {
            calculatorToggle.click();
          }
          
          // 聚焦到搜索框
          setTimeout(() => {
            const searchInput = document.querySelector('#qcew-city');
            if (searchInput) {
              searchInput.focus();
            }
          }, 500);
        });
      }

      marker.bindPopup(popupContent, {
        minWidth: 320,
        maxWidth: 450,
        className: 'site-selection-popup-horizontal',
        offset: [0, -10] // 调整弹窗位置，避免遮挡顶部按钮
      });

      // 修复marker点击事件 - 避免回弹问题，调整缩放位置让marker靠下
      marker.on('click', () => {
        // 添加缩放指示器
        const mapContainer = map.getContainer().closest('.liquid-map-container');
        if (mapContainer) {
          mapContainer.classList.add('zooming');
          setTimeout(() => {
            mapContainer.classList.remove('zooming');
          }, 1000);
        }

        // 智能缩放：只有当前缩放级别小于15时才缩放，避免回弹
        const currentZoom = map.getZoom();
        const targetZoom = Math.max(currentZoom, 15);

        if (currentZoom < 15) {
          // 计算偏移位置，让marker显示在地图下方1/3处，避免与顶部按钮重叠
          const mapSize = map.getSize();
          const offsetY = mapSize.y * 0.15; // 向下偏移15%的地图高度
          const targetPoint = map.project(position, targetZoom);
          const offsetPoint = L.point(targetPoint.x, targetPoint.y - offsetY);
          const offsetLatLng = map.unproject(offsetPoint, targetZoom);

          map.flyTo(offsetLatLng, targetZoom, {
            duration: 1.0,
            easeLinearity: 0.25
          });
        }

        // 如果不是当前选中的地块，则选中它
        if (!isSelected) {
          setTimeout(() => {
            onParcelSelect(parcel);
          }, currentZoom < 15 ? 500 : 100); // 根据是否需要缩放调整延迟
        }
      });

      marker.addTo(map);
      markersRef.current.push(marker);
    });

    // 修复: 在初始加载时调整地图视图，不管是否有选中的地块
    if (parcels.length > 0 && !hasInitializedView.current) {
      const bounds = L.latLngBounds(
        parcels.map(parcel => [parcel.location?.lat || 0, parcel.location?.lng || 0])
      );
      map.fitBounds(bounds, { padding: [20, 20] });
      hasInitializedView.current = true;
    }

    // 暴露重置视图方法给父组件
    if (onResetView) {
      onResetView(() => {
        if (parcels.length > 0) {
          const bounds = L.latLngBounds(
            parcels.map(parcel => [parcel.location?.lat || 0, parcel.location?.lng || 0])
          );
          map.flyToBounds(bounds, {
            padding: [20, 20],
            duration: 1.2,
            easeLinearity: 0.25
          });
        }
      });
    }

    return () => {
      markersRef.current.forEach(marker => {
        map.removeLayer(marker);
      });
      markersRef.current = [];
    };
  }, [map, parcels, selectedParcel, onParcelSelect, onResetView, payrollData]);

  return null;
};

// 获取地块所在区域信息
const getParcelLocationInfo = (parcel) => {
  if (!parcel || !parcel.location) return null;
  
  // 基于经纬度推断大概的州/地区信息
  const lat = parcel.location.lat;
  const lng = parcel.location.lng;
  
  // 简单的美国地区判断逻辑
  let suggestedLocation = '';
  
  if (lat >= 30 && lat <= 35 && lng >= -88 && lng <= -84) {
    suggestedLocation = 'Alabama'; // Alabama大概范围
  } else if (lat >= 25 && lat <= 31 && lng >= -87 && lng <= -80) {
    suggestedLocation = 'Florida';
  } else if (lat >= 25 && lat <= 36 && lng >= -106 && lng <= -93) {
    suggestedLocation = 'Texas';
  } else if (lat >= 32 && lat <= 42 && lng >= -124 && lng <= -114) {
    suggestedLocation = 'California';
  } else {
    // 默认建议
    suggestedLocation = 'Alabama';
  }
  
  return {
    coordinates: `${lat.toFixed(4)}, ${lng.toFixed(4)}`,
    suggestedState: suggestedLocation,
    nearbyCity: `${suggestedLocation} region`
  };
};

// 主组件
const SiteSelectionMap = ({
  parcels = [],
  selectedParcel = null,
  onParcelSelect = () => {},
  mapStyle: externalMapStyle = 'satellite',
  height = '500px',
  className = ''
}) => {
  // 使用外部传入的mapStyle，如果没有则使用默认值
  const mapStyle = externalMapStyle || 'satellite';
  const mapRef = useRef(null);
  const resetViewRef = useRef(null);
  
  // QCEW薪酬计算结果状态
  const [payrollData, setPayrollData] = useState(null);
  const [showPayrollCalculator, setShowPayrollCalculator] = useState(false);

  // 计算地图中心点
  const getMapCenter = () => {
    if (!parcels || parcels.length === 0) {
      return [39.8283, -98.5795]; // 美国中心
    }
    
    // 修复: 使用正确的location属性，并添加安全检查
    const avgLat = parcels.reduce((sum, parcel) => sum + (parcel.location?.lat || 0), 0) / parcels.length;
    const avgLng = parcels.reduce((sum, parcel) => sum + (parcel.location?.lng || 0), 0) / parcels.length;
    
    return [avgLat, avgLng];
  };

  const mapCenter = getMapCenter();

  /**
   * 处理QCEW薪酬计算完成
   */
  const handlePayrollCalculationComplete = (calculationResult) => {
    setPayrollData(calculationResult);
    console.log('🧮 薪酬计算完成:', calculationResult);
    
    // 显示成功提示
    const notification = document.createElement('div');
    notification.className = 'payroll-success-notification';
    notification.innerHTML = `
      <div class="notification-content">
        ✅ 薪酬计算完成！
        <br>
        <small>${calculationResult.calculation.inputs.area_title}</small>
      </div>
    `;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  };

  /**
   * 获取选中地块的位置建议
   */
  const getLocationSuggestion = () => {
    if (!selectedParcel) return null;
    return getParcelLocationInfo(selectedParcel);
  };

  return (
    <div className={`site-selection-map-container ${className}`} style={{ height }}>
      {/* 劳工数据分析工具集 */}
      <div className="map-payroll-calculator-container">
        {/* QCEW薪酬计算器 */}
        <QCEWPayrollCalculator 
          selectedParcel={selectedParcel}
          locationSuggestion={getLocationSuggestion()}
          onCalculationComplete={handlePayrollCalculationComplete}
          className="map-integrated-calculator"
        />

        {/* LAUS可招聘余量分析器 */}
        <LAUSCalculator 
          selectedParcel={selectedParcel}
          className="map-integrated-laus-calculator"
        />

        {/* CPI成本上涨计算器 */}
        <CpiEscalationCalculator 
          selectedParcel={selectedParcel}
          className="map-integrated-cpi-calculator"
          onCalculationComplete={(result) => {
            console.log('🧮 CPI计算完成:', result);
            // 显示成功提示
            const notification = document.createElement('div');
            notification.className = 'cpi-success-notification';
            notification.innerHTML = `
              <div class="notification-content">
                ✅ CPI计算完成！
                <br>
                <small>通胀率: ${result.inflation_rate > 0 ? '+' : ''}${result.inflation_rate}%</small>
              </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
              notification.remove();
            }, 3000);
          }}
        />
        
        {/* 薪酬数据快速预览 */}
        {payrollData && selectedParcel && (
          <div className="payroll-preview-widget">
            <div className="preview-header">
              💰 {selectedParcel.parcel_id} 薪酬预览
            </div>
            <div className="preview-content">
              <div className="preview-item">
                <span className="preview-label">年薪酬总额:</span>
                <span className="preview-value">{payrollData.calculation.result.formatted_payroll}</span>
              </div>
              <div className="preview-item">
                <span className="preview-label">月薪酬:</span>
                <span className="preview-value">${payrollData.calculation.result.monthly_payroll?.toLocaleString()}</span>
              </div>
              <div className="preview-item">
                <span className="preview-label">数据来源:</span>
                <span className="preview-value">{payrollData.calculation.inputs.area_title}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      <MapContainer
        center={mapCenter}
        zoom={8}
        style={{ height: '100%', width: '100%', borderRadius: '12px' }}
        ref={mapRef}
        zoomControl={false}
      >
        <TileLayer
          url={mapTileUrls[mapStyle]}
          attribution={mapAttributions[mapStyle]}
        />
        
        <MapController
          parcels={parcels}
          selectedParcel={selectedParcel}
          onParcelSelect={onParcelSelect}
          onResetView={(resetFn) => { resetViewRef.current = resetFn; }}
          payrollData={payrollData}
        />
      </MapContainer>

      {/* 液态玻璃一键返回宏观视图按钮 */}
      {parcels.length > 0 && (
        <button
          className="liquid-reset-view-button"
          onClick={() => resetViewRef.current && resetViewRef.current()}
          title="返回宏观视图 - Reset to Overview"
        >
          <div className="reset-btn-glass-layer"></div>
          <div className="reset-btn-content">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" className="reset-btn-icon">
              <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" stroke="currentColor" strokeWidth="2"/>
              <path d="M3 3v5h5" stroke="currentColor" strokeWidth="2"/>
              <circle cx="12" cy="12" r="1" fill="currentColor"/>
            </svg>
            <span className="reset-btn-label">宏观</span>
          </div>
          <div className="reset-btn-refraction"></div>
        </button>
      )}

      {/* 地块选择提示 */}
      {parcels.length > 0 && !selectedParcel && (
        <div className="parcel-selection-hint">
          <div className="hint-content">
            🏭 选择一个地块开始薪酬分析
          </div>
        </div>
      )}

      {parcels.length === 0 && (
        <div className="map-empty-state">
          <div className="empty-icon">🗺️</div>
          <p>暂无地块数据</p>
        </div>
      )}
    </div>
  );
};

export default SiteSelectionMap;

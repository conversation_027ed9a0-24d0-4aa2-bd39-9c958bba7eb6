/**
 * 📈 CPI Cost Escalation Calculator Component
 * CPI成本上涨计算器组件 - 基于消费价格指数计算通胀调整后的成本
 */

import React, { useState, useEffect } from 'react';
import { API_CONFIG } from '../../config/api.js';
import './CpiEscalationCalculator.css';

// Helper函数：获取API URL
const getApiUrl = (endpoint) => {
    return `${API_CONFIG.BASE_URL}${endpoint}`;
};

const CpiEscalationCalculator = ({ 
    selectedParcel = null,
    className = '',
    onCalculationComplete = () => {} 
}) => {
    // 状态管理
    const [isVisible, setIsVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [calculationResult, setCalculationResult] = useState(null);
    const [error, setError] = useState(null);
    const [availableSeries, setAvailableSeries] = useState([]);

    // 表单参数
    const [params, setParams] = useState({
        base_cost: 1000,
        base_month: '01',
        base_year: '2022',
        current_month: '12',
        current_year: '2023',
        cpi_series_id: 'CUUR0000SA0' // 默认使用全国平均CPI
    });

    // 月份选项
    const months = [
        { value: '01', label: '1月 (Jan)' },
        { value: '02', label: '2月 (Feb)' },
        { value: '03', label: '3月 (Mar)' },
        { value: '04', label: '4月 (Apr)' },
        { value: '05', label: '5月 (May)' },
        { value: '06', label: '6月 (Jun)' },
        { value: '07', label: '7月 (Jul)' },
        { value: '08', label: '8月 (Aug)' },
        { value: '09', label: '9月 (Sep)' },
        { value: '10', label: '10月 (Oct)' },
        { value: '11', label: '11月 (Nov)' },
        { value: '12', label: '12月 (Dec)' }
    ];

    // 生成年份选项 (2015-2025)
    const generateYearOptions = () => {
        const currentYear = new Date().getFullYear();
        const years = [];
        for (let year = 2015; year <= currentYear; year++) {
            years.push({ value: year.toString(), label: year.toString() });
        }
        return years;
    };

    const yearOptions = generateYearOptions();

    // 组件挂载时获取可用的CPI系列
    useEffect(() => {
        fetchAvailableSeries();
    }, []);

    /**
     * 获取可用的CPI系列列表
     */
    const fetchAvailableSeries = async () => {
        try {
            const response = await fetch(
                getApiUrl('/api/cpi/series')
            );
            const result = await response.json();
            
            if (result.success) {
                setAvailableSeries(result.series);
            } else {
                console.error('❌ 获取CPI系列失败:', result.error);
            }
        } catch (err) {
            console.error('❌ 获取CPI系列失败:', err);
        }
    };

    /**
     * 处理参数变化
     */
    const handleParamChange = (field, value) => {
        setParams(prev => ({
            ...prev,
            [field]: field === 'base_cost' ? parseFloat(value) || 0 : value
        }));
        
        // 清除之前的错误和结果
        setError(null);
        if (calculationResult) {
            setCalculationResult(null);
        }
    };

    /**
     * 验证输入参数
     */
    const validateParams = () => {
        const { base_cost, base_year, current_year, base_month, current_month } = params;
        
        if (!base_cost || base_cost <= 0) {
            return '基础成本必须大于0';
        }
        
        if (!base_year || !current_year) {
            return '请选择基础年份和目标年份';
        }
        
        const baseDate = new Date(`${base_year}-${base_month}-01`);
        const currentDate = new Date(`${current_year}-${current_month}-01`);
        
        if (baseDate >= currentDate) {
            return '目标日期必须晚于基础日期';
        }
        
        return null;
    };

    /**
     * 🧮 执行CPI成本上涨计算
     */
    const handleCalculateEscalation = async () => {
        // 验证输入
        const validationError = validateParams();
        if (validationError) {
            setError(validationError);
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            const requestBody = {
                base_cost: params.base_cost,
                base_date: `${params.base_year}-${params.base_month}`,
                current_date: `${params.current_year}-${params.current_month}`,
                cpi_series_id: params.cpi_series_id
            };

            console.log('🧮 开始CPI成本上涨计算:', requestBody);

            const response = await fetch(
                getApiUrl('/api/cpi/calculate-escalation'),
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                }
            );

            const result = await response.json();

            if (result.success) {
                setCalculationResult(result);
                onCalculationComplete(result);
                console.log('✅ CPI计算完成:', result);
            } else {
                setError(result.error || 'CPI计算失败');
                console.error('❌ CPI计算失败:', result);
            }
        } catch (err) {
            const errorMsg = '计算服务暂时不可用，请稍后再试';
            setError(errorMsg);
            console.error('❌ CPI计算请求失败:', err);
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * 清除计算结果
     */
    const handleClear = () => {
        setCalculationResult(null);
        setError(null);
        setParams({
            base_cost: 1000,
            base_month: '01',
            base_year: '2022',
            current_month: '12',
            current_year: '2023',
            cpi_series_id: 'CUUR0000SA0'
        });
    };

    /**
     * 切换显示状态
     */
    const toggleVisibility = () => {
        setIsVisible(!isVisible);
        if (isVisible) {
            // 收起时清除状态
            setError(null);
        }
    };

    return (
        <div className={`cpi-escalation-calculator ${className}`}>
            {/* 切换按钮 */}
            <button 
                className={`cpi-toggle-button ${isVisible ? 'active' : ''}`}
                onClick={toggleVisibility}
                title="CPI成本上涨计算器 - 基于通胀调整成本"
            >
                <div className="toggle-icon">
                    📈
                </div>
                <div className="toggle-text">
                    <span className="toggle-title">
                        {isVisible ? '👆 收起CPI计算器' : '💰 CPI成本上涨'}
                    </span>
                    <span className="toggle-subtitle">
                        {isVisible ? '点击收起' : '通胀调整计算'}
                    </span>
                </div>
                <div className={`toggle-arrow ${isVisible ? 'up' : 'down'}`}>
                    ▼
                </div>
            </button>

            {/* 计算器主体 */}
            {isVisible && (
                <div className="cpi-calculator-body">
                    <div className="calculator-header">
                        <h3>📈 CPI成本上涨计算器</h3>
                        <p className="calculator-description">
                            基于美国劳工统计局(BLS)的消费价格指数(CPI)数据，计算通胀调整后的成本。
                        </p>
                    </div>

                    {/* 输入表单 */}
                    <div className="calculator-form">
                        {/* 基础成本输入 */}
                        <div className="form-group">
                            <label htmlFor="base-cost">基础成本 ($)</label>
                            <input
                                id="base-cost"
                                type="number"
                                value={params.base_cost}
                                onChange={(e) => handleParamChange('base_cost', e.target.value)}
                                placeholder="输入基础成本"
                                min="0"
                                step="0.01"
                                className="cost-input"
                            />
                        </div>

                        {/* 基础日期 */}
                        <div className="form-group">
                            <label>基础日期</label>
                            <div className="date-inputs">
                                <select
                                    value={params.base_month}
                                    onChange={(e) => handleParamChange('base_month', e.target.value)}
                                    className="month-select"
                                >
                                    {months.map(month => (
                                        <option key={month.value} value={month.value}>
                                            {month.label}
                                        </option>
                                    ))}
                                </select>
                                <select
                                    value={params.base_year}
                                    onChange={(e) => handleParamChange('base_year', e.target.value)}
                                    className="year-select"
                                >
                                    {yearOptions.map(year => (
                                        <option key={year.value} value={year.value}>
                                            {year.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* 目标日期 */}
                        <div className="form-group">
                            <label>目标日期</label>
                            <div className="date-inputs">
                                <select
                                    value={params.current_month}
                                    onChange={(e) => handleParamChange('current_month', e.target.value)}
                                    className="month-select"
                                >
                                    {months.map(month => (
                                        <option key={month.value} value={month.value}>
                                            {month.label}
                                        </option>
                                    ))}
                                </select>
                                <select
                                    value={params.current_year}
                                    onChange={(e) => handleParamChange('current_year', e.target.value)}
                                    className="year-select"
                                >
                                    {yearOptions.map(year => (
                                        <option key={year.value} value={year.value}>
                                            {year.label}
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* CPI系列选择 */}
                        <div className="form-group">
                            <label htmlFor="cpi-series">CPI系列</label>
                            <select
                                id="cpi-series"
                                value={params.cpi_series_id}
                                onChange={(e) => handleParamChange('cpi_series_id', e.target.value)}
                                className="series-select"
                            >
                                {availableSeries.map(series => (
                                    <option key={series.id} value={series.id} title={series.description}>
                                        {series.title}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* 操作按钮 */}
                        <div className="calculator-actions">
                            <button
                                onClick={handleCalculateEscalation}
                                disabled={isLoading}
                                className="calculate-button"
                            >
                                {isLoading ? (
                                    <>
                                        <div className="loading-spinner"></div>
                                        计算中...
                                    </>
                                ) : (
                                    <>
                                        🧮 计算上涨成本
                                    </>
                                )}
                            </button>
                            <button
                                onClick={handleClear}
                                className="clear-button"
                                disabled={isLoading}
                            >
                                🗑️ 清除
                            </button>
                        </div>
                    </div>

                    {/* 错误信息 */}
                    {error && (
                        <div className="error-message">
                            <div className="error-icon">⚠️</div>
                            <div className="error-text">{error}</div>
                        </div>
                    )}

                    {/* 计算结果 */}
                    {calculationResult && (
                        <div className="calculation-result">
                            <div className="result-header">
                                <h4>📊 计算结果</h4>
                            </div>
                            
                            <div className="result-summary">
                                <div className="cost-comparison">
                                    <div className="cost-item base-cost">
                                        <span className="cost-label">基础成本</span>
                                        <span className="cost-value">
                                            {calculationResult.calculation_summary.base_cost_formatted}
                                        </span>
                                        <span className="cost-date">
                                            {calculationResult.base_period}
                                        </span>
                                    </div>
                                    
                                    <div className="arrow-icon">→</div>
                                    
                                    <div className="cost-item escalated-cost">
                                        <span className="cost-label">调整后成本</span>
                                        <span className="cost-value highlight">
                                            {calculationResult.calculation_summary.escalated_cost_formatted}
                                        </span>
                                        <span className="cost-date">
                                            {calculationResult.current_period}
                                        </span>
                                    </div>
                                </div>

                                <div className="inflation-info">
                                    <div className="inflation-rate">
                                        <span className="info-label">通胀率:</span>
                                        <span className={`info-value ${calculationResult.inflation_rate > 0 ? 'positive' : 'negative'}`}>
                                            {calculationResult.inflation_rate > 0 ? '+' : ''}{calculationResult.inflation_rate}%
                                        </span>
                                    </div>
                                    <div className="time-span">
                                        <span className="info-label">时间跨度:</span>
                                        <span className="info-value">
                                            {calculationResult.calculation_summary.period_span}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div className="result-details">
                                <h5>📋 详细信息</h5>
                                <div className="detail-grid">
                                    <div className="detail-item">
                                        <span className="detail-label">基础CPI:</span>
                                        <span className="detail-value">{calculationResult.base_cpi}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">目标CPI:</span>
                                        <span className="detail-value">{calculationResult.current_cpi}</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">上涨倍数:</span>
                                        <span className="detail-value">{calculationResult.escalation_ratio}x</span>
                                    </div>
                                    <div className="detail-item">
                                        <span className="detail-label">数据来源:</span>
                                        <span className="detail-value">{calculationResult.calculation_summary.series_title}</span>
                                    </div>
                                </div>
                            </div>

                            <div className="result-explanation">
                                <p className="explanation-text">
                                    💡 根据{calculationResult.calculation_summary.series_title}的CPI数据，
                                    {calculationResult.calculation_summary.base_cost_formatted}在{calculationResult.base_period}的购买力
                                    相当于{calculationResult.current_period}的{calculationResult.calculation_summary.escalated_cost_formatted}。
                                </p>
                            </div>
                        </div>
                    )}

                    {/* 使用说明 */}
                    <div className="calculator-help">
                        <details className="help-details">
                            <summary>📖 使用说明</summary>
                            <div className="help-content">
                                <ul>
                                    <li><strong>基础成本:</strong> 输入需要调整的原始成本金额</li>
                                    <li><strong>基础日期:</strong> 选择原始成本对应的年月</li>
                                    <li><strong>目标日期:</strong> 选择要调整到的目标年月</li>
                                    <li><strong>CPI系列:</strong> 选择相应的地区CPI系列，默认为全国平均</li>
                                </ul>
                                <p className="help-note">
                                    💡 数据来源：美国劳工统计局(BLS)消费价格指数(CPI)
                                </p>
                            </div>
                        </details>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CpiEscalationCalculator; 
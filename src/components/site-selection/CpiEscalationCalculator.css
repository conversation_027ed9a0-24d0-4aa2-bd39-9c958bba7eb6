/**
 * 📈 CPI Cost Escalation Calculator Styles
 * CPI成本上涨计算器样式 - 现代液态玻璃风格
 */

.cpi-escalation-calculator {
    position: relative;
    z-index: 500;
    margin-bottom: 12px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 切换按钮样式 */
.cpi-toggle-button {
    width: 100%;
    padding: 16px 20px;
    background: linear-gradient(135deg, 
        rgba(255, 152, 0, 0.15) 0%, 
        rgba(255, 193, 7, 0.1) 50%, 
        rgba(255, 152, 0, 0.05) 100%);
    border: 1px solid rgba(255, 152, 0, 0.3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: #2c3e50;
    position: relative;
    overflow: hidden;
}

.cpi-toggle-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 152, 0, 0.1), 
        transparent);
    transition: left 0.5s ease;
}

.cpi-toggle-button:hover::before {
    left: 100%;
}

.cpi-toggle-button:hover {
    background: linear-gradient(135deg, 
        rgba(255, 152, 0, 0.25) 0%, 
        rgba(255, 193, 7, 0.2) 50%, 
        rgba(255, 152, 0, 0.15) 100%);
    border-color: rgba(255, 152, 0, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
}

.cpi-toggle-button.active {
    background: linear-gradient(135deg, 
        rgba(255, 152, 0, 0.3) 0%, 
        rgba(255, 193, 7, 0.25) 50%, 
        rgba(255, 152, 0, 0.2) 100%);
    border-color: rgba(255, 152, 0, 0.6);
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.15);
}

.toggle-icon {
    font-size: 24px;
    flex-shrink: 0;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.toggle-text {
    flex: 1;
    text-align: left;
}

.toggle-title {
    display: block;
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 2px;
    color: #2c3e50;
}

.toggle-subtitle {
    display: block;
    font-size: 13px;
    color: #7f8c8d;
    font-weight: 400;
}

.toggle-arrow {
    font-size: 14px;
    transition: transform 0.3s ease;
    color: #95a5a6;
}

.toggle-arrow.up {
    transform: rotate(180deg);
}

/* 计算器主体样式 */
.cpi-calculator-body {
    margin-top: 8px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.95) 0%, 
        rgba(248, 249, 250, 0.9) 100%);
    border: 1px solid rgba(255, 152, 0, 0.2);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(255, 152, 0, 0.1);
    animation: slideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.calculator-header h3 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 20px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calculator-description {
    margin: 0 0 20px 0;
    color: #5d6d7e;
    font-size: 14px;
    line-height: 1.5;
}

/* 表单样式 */
.calculator-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-weight: 600;
    color: #34495e;
    font-size: 14px;
}

.cost-input,
.month-select,
.year-select,
.series-select {
    padding: 12px 16px;
    border: 1px solid rgba(255, 152, 0, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.cost-input:focus,
.month-select:focus,
.year-select:focus,
.series-select:focus {
    outline: none;
    border-color: rgba(255, 152, 0, 0.6);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.1);
}

.date-inputs {
    display: grid;
    grid-template-columns: 1fr 100px;
    gap: 8px;
}

.calculator-actions {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.calculate-button,
.clear-button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.calculate-button {
    flex: 1;
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.calculate-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #f57c00 0%, #e65100 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
}

.calculate-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.clear-button {
    background: linear-gradient(135deg, 
        rgba(108, 117, 125, 0.1) 0%, 
        rgba(108, 117, 125, 0.05) 100%);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.clear-button:hover:not(:disabled) {
    background: linear-gradient(135deg, 
        rgba(108, 117, 125, 0.2) 0%, 
        rgba(108, 117, 125, 0.1) 100%);
    color: #495057;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误信息样式 */
.error-message {
    margin-top: 16px;
    padding: 12px 16px;
    background: linear-gradient(135deg, 
        rgba(220, 53, 69, 0.1) 0%, 
        rgba(220, 53, 69, 0.05) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);
}

.error-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.error-text {
    color: #dc3545;
    font-size: 14px;
    font-weight: 500;
}

/* 计算结果样式 */
.calculation-result {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, 
        rgba(40, 167, 69, 0.1) 0%, 
        rgba(40, 167, 69, 0.05) 100%);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    animation: resultAppear 0.5s ease-out;
}

@keyframes resultAppear {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.result-header h4 {
    margin: 0 0 16px 0;
    color: #28a745;
    font-size: 18px;
    font-weight: 700;
}

.result-summary {
    margin-bottom: 20px;
}

.cost-comparison {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.cost-item {
    flex: 1;
    min-width: 150px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    text-align: center;
    backdrop-filter: blur(5px);
}

.cost-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cost-value {
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 4px;
}

.cost-value.highlight {
    color: #28a745;
    font-size: 20px;
}

.cost-date {
    display: block;
    font-size: 12px;
    color: #7f8c8d;
}

.arrow-icon {
    font-size: 24px;
    color: #ff9800;
    font-weight: bold;
    flex-shrink: 0;
}

.inflation-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
}

.inflation-rate,
.time-span {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    font-weight: 600;
    margin-bottom: 4px;
}

.info-value {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
}

.info-value.positive {
    color: #dc3545;
}

.info-value.negative {
    color: #28a745;
}

.result-details {
    margin-bottom: 16px;
}

.result-details h5 {
    margin: 0 0 12px 0;
    color: #34495e;
    font-size: 16px;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 6px;
    font-size: 13px;
}

.detail-label {
    color: #6c757d;
    font-weight: 500;
}

.detail-value {
    color: #2c3e50;
    font-weight: 600;
}

.result-explanation {
    padding: 16px;
    background: rgba(23, 162, 184, 0.1);
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.explanation-text {
    margin: 0;
    color: #2c3e50;
    font-size: 14px;
    line-height: 1.5;
    font-style: italic;
}

/* 使用说明样式 */
.calculator-help {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 152, 0, 0.2);
}

.help-details {
    cursor: pointer;
}

.help-details summary {
    padding: 8px 0;
    font-weight: 600;
    color: #34495e;
    font-size: 14px;
}

.help-details summary:hover {
    color: #ff9800;
}

.help-content {
    padding: 12px 0;
    font-size: 13px;
    color: #5d6d7e;
    line-height: 1.6;
}

.help-content ul {
    margin: 0 0 12px 0;
    padding-left: 20px;
}

.help-content li {
    margin-bottom: 6px;
}

.help-note {
    margin: 12px 0 0 0;
    padding: 8px 12px;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ffc107;
    font-size: 12px;
    color: #856404;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .cpi-calculator-body {
        padding: 16px;
    }
    
    .cost-comparison {
        flex-direction: column;
        gap: 12px;
    }
    
    .arrow-icon {
        transform: rotate(90deg);
    }
    
    .inflation-info {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .calculator-actions {
        flex-direction: column;
    }
    
    .detail-grid {
        grid-template-columns: 1fr;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .cpi-toggle-button {
        background: linear-gradient(135deg, 
            rgba(255, 152, 0, 0.2) 0%, 
            rgba(255, 193, 7, 0.15) 50%, 
            rgba(255, 152, 0, 0.1) 100%);
        color: #e8f4f8;
    }
    
    .cpi-calculator-body {
        background: linear-gradient(135deg, 
            rgba(45, 55, 72, 0.95) 0%, 
            rgba(26, 32, 44, 0.9) 100%);
        color: #e8f4f8;
    }
    
    .calculator-header h3,
    .toggle-title {
        color: #e8f4f8;
    }
    
    .cost-input,
    .month-select,
    .year-select,
    .series-select {
        background: rgba(45, 55, 72, 0.8);
        color: #e8f4f8;
        border-color: rgba(255, 152, 0, 0.4);
    }
} 
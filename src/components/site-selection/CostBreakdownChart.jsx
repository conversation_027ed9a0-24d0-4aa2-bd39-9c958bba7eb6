import React from 'react';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, Title } from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import '../../styles/CostBreakdownChart.css';

ChartJS.register(ArcElement, Tooltip, Legend, Title);

const formatCurrency = (value = 0) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  }).format(value);
};

/**
 * 成本分布可视化
 * @param {Array} costs - [{ label, value, color }]
 */
const CostBreakdownChart = ({ costs = [] }) => {
  const total = costs.reduce((sum, c) => sum + (c.value || 0), 0);

  const data = {
    labels: costs.map(c => c.label),
    datasets: [
      {
        data: costs.map(c => c.value),
        backgroundColor: costs.map(c => `${c.color}cc`),
        borderColor: costs.map(c => c.color),
        borderWidth: 2,
        hoverOffset: 6,
      },
    ],
  };

  const options = {
    maintainAspectRatio: false,
    cutout: '60%',
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#e2e8f0',
          boxWidth: 14,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            const value = context.parsed;
            const pct = total ? ((value / total) * 100).toFixed(1) : 0;
            return `${context.label}: ${formatCurrency(value)} (${pct}%)`;
          },
        },
      },
      title: {
        display: false,
      },
    },
  };

  return (
    <div className="cost-breakdown-chart-container">
      <Doughnut data={data} options={options} />
      <div className="chart-center-text">
        <span className="center-value">{formatCurrency(total)}</span>
        <span className="center-label">总成本</span>
      </div>
    </div>
  );
};

export default CostBreakdownChart; 
/**
 * 🏭 QCEW Payroll Calculator Component
 * QCEW薪酬计算器组件 - 支持多年份数据显示和智能建议的薪酬计算
 */

import React, { useState, useEffect, useRef } from 'react';
import { 
    searchCityAreas,
    getSmartCityPayrollAnalysis,
    calculatePayrollByCity, 
    testQCEWAPI,
    checkQCEWServiceHealth,
    getRecommendedCities,
    QCEW_CONSTANTS 
} from '../../services/qcewService';
import { API_CONFIG } from '../../config/api.js';
import './QCEWPayrollCalculator.css';

// Helper函数：获取API URL
const getApiUrl = (endpoint) => {
    return `${API_CONFIG.BASE_URL}${endpoint}`;
};

const QCEWPayrollCalculator = ({ 
    selectedParcel = null,
    locationSuggestion = null,
    className = '',
    onCalculationComplete = () => {} 
}) => {
    // 状态管理
    const [isVisible, setIsVisible] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [calculationData, setCalculationData] = useState(null);
    const [error, setError] = useState(null);
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [suggestions, setSuggestions] = useState([]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [hasSearched, setHasSearched] = useState(false);
    
    // 表单参数
    const [params, setParams] = useState({
        city: '',
        state: '',
        planned_fte: 100
    });

    // 选中的地区和年份
    const [selectedArea, setSelectedArea] = useState(null);
    const [selectedYear, setSelectedYear] = useState(null);
    
    // DOM引用
    const cityInputRef = useRef(null);
    const suggestionsRef = useRef(null);

    // 防抖搜索
    const searchTimeoutRef = useRef(null);

    // 新增：服务可用性状态
    const [serviceAvailable, setServiceAvailable] = useState(true);
    const [serviceStatus, setServiceStatus] = useState(null);

    // 检查服务可用性
    const checkServiceAvailability = async () => {
        try {
            const response = await fetch(getApiUrl('/api/us-labor/qcew/health'));
            const result = await response.json();
            
            if (result.status === 'disabled') {
                setServiceAvailable(false);
                setServiceStatus(result);
                setError('QCEW数据服务不可用 - ' + result.reason);
            } else {
                setServiceAvailable(true);
                setServiceStatus(null);
            }
        } catch (err) {
            console.error('检查服务可用性失败:', err);
            setServiceAvailable(false);
            setError('无法连接到QCEW服务');
        }
    };

    // 组件挂载时检查服务可用性
    useEffect(() => {
        if (isVisible) {
            checkServiceAvailability();
        }
    }, [isVisible]);

    // 监听地块选择变化，自动填充位置建议
    useEffect(() => {
        if (selectedParcel && locationSuggestion) {
            setParams(prev => ({
                ...prev,
                city: locationSuggestion.suggestedState
            }));
            
            // 自动搜索建议的位置
            setTimeout(() => {
                handleCitySearch(locationSuggestion.suggestedState);
            }, 300);
        }
    }, [selectedParcel, locationSuggestion]);

    /**
     * 🔍 智能地区搜索 - 返回多年份数据
     */
    const handleCitySearch = async (query) => {
        if (!serviceAvailable) {
            return;
        }
        
        if (!query || query.trim().length < 2) {
            setSearchResults([]);
            setSuggestions([]);
            setShowSuggestions(false);
            setHasSearched(false);
            return;
        }

        // 防抖处理
        if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
        }

        searchTimeoutRef.current = setTimeout(async () => {
            setIsSearching(true);
            setHasSearched(true);
            
            try {
                console.log(`🔍 搜索地区: ${query}`);
                
                // 调用新的搜索API（支持多年份）
                const searchResponse = await fetch(
                    getApiUrl(`/api/us-labor/qcew/search-cities?query=${encodeURIComponent(query)}`)
                );
                
                if (searchResponse.status === 503) {
                    const errorData = await searchResponse.json();
                    setServiceAvailable(false);
                    setServiceStatus(errorData);
                    setError(errorData.message);
                    return;
                }
                
                const searchResult = await searchResponse.json();
                
                if (searchResult.success && searchResult.results) {
                    // 处理搜索结果，按数据可用性分组
                    const areasWithData = searchResult.results.filter(area => area.qcew_data.has_data);
                    const areasWithoutData = searchResult.results.filter(area => !area.qcew_data.has_data);
                    
                    setSearchResults(areasWithData);
                    
                    // 生成智能建议
                    const intelligentSuggestions = await generateIntelligentSuggestions(
                        query, 
                        areasWithData, 
                        areasWithoutData
                    );
                    
                    setSuggestions(intelligentSuggestions);
                    setShowSuggestions(true);
                    
                    console.log(`✅ 找到 ${areasWithData.length} 个有数据的地区，${areasWithoutData.length} 个无数据地区`);
                } else {
                    // 没有找到匹配
                    setSearchResults([]);
                    const fallbackSuggestions = await generateFallbackSuggestions(query);
                    setSuggestions(fallbackSuggestions);
                    setShowSuggestions(true);
                }
            } catch (err) {
                console.error('❌ 搜索失败:', err);
                setSearchResults([]);
                setSuggestions([{
                    type: 'error',
                    text: '搜索服务暂时不可用',
                    reason: '请稍后重试或联系技术支持',
                    confidence: 'low'
                }]);
                setShowSuggestions(true);
            } finally {
                setIsSearching(false);
            }
        }, 300); // 300ms防抖
    };

    /**
     * 🧠 生成智能建议（基于后端API）
     */
    const generateIntelligentSuggestions = async (query, areasWithData, areasWithoutData) => {
        const suggestions = [];

        // 如果没有数据，调用后端智能建议API
        if (areasWithData.length === 0) {
            try {
                const suggestionResponse = await fetch(
                    getApiUrl(`/api/us-labor/qcew/intelligent-suggestions?input=${encodeURIComponent(query)}&reason=no_data`)
                );
                const suggestionResult = await suggestionResponse.json();
                
                if (suggestionResult.success) {
                    return suggestionResult.suggestions.map(s => ({
                        type: s.type,
                        text: s.title,
                        subtitle: s.subtitle,
                        reason: s.explanation || s.subtitle,
                        confidence: s.confidence,
                        metadata: s.metadata
                    }));
                }
            } catch (err) {
                console.error('❌ 获取智能建议失败:', err);
            }
        }

        // 如果有无数据的匹配，说明情况
        if (areasWithoutData.length > 0) {
            areasWithoutData.slice(0, 2).forEach(area => {
                suggestions.push({
                    type: 'unavailable',
                    text: area.area_title,
                    reason: '该地区暂无QCEW数据',
                    confidence: 'high',
                    metadata: { area_fips: area.area_fips }
                });
            });
        }

        return suggestions;
    };

    /**
     * 🔄 生成备选建议
     */
    const generateFallbackSuggestions = async (query) => {
        try {
            const suggestionResponse = await fetch(
                getApiUrl(`/api/us-labor/qcew/intelligent-suggestions?input=${encodeURIComponent(query)}&reason=no_match`)
            );
            const suggestionResult = await suggestionResponse.json();
            
            if (suggestionResult.success) {
                return suggestionResult.suggestions.map(s => ({
                    type: s.type,
                    text: s.title,
                    subtitle: s.subtitle,
                    reason: s.explanation || s.subtitle,
                    confidence: s.confidence,
                    metadata: s.metadata
                }));
            }
        } catch (err) {
            console.error('❌ 获取备选建议失败:', err);
        }

        // 默认建议
        return [
            {
                type: 'spelling',
                text: 'Alabama',
                reason: '推荐：完整多年份数据可用',
                confidence: 'high',
                metadata: { area_code: '01000' }
            }
        ];
    };

    /**
     * 📍 选择地区和年份
     */
    const handleSelectArea = (area, year) => {
        setSelectedArea(area);
        setSelectedYear(year);
        setParams(prev => ({ ...prev, city: area.area_title }));
        setShowSuggestions(false);
        setCalculationData(null); // 清除之前的计算结果
    };

    /**
     * 💡 选择建议
     */
    const handleSelectSuggestion = (suggestion) => {
        if (suggestion.metadata?.area_code) {
            setParams(prev => ({ ...prev, city: suggestion.text }));
            setSelectedArea({ 
                area_fips: suggestion.metadata.area_code, 
                area_title: suggestion.text 
            });
        } else {
            setParams(prev => ({ ...prev, city: suggestion.text }));
        }
        setShowSuggestions(false);
        
        // 重新搜索
        setTimeout(() => {
            handleCitySearch(suggestion.text);
        }, 100);
    };

    /**
     * 🧮 计算薪酬
     */
    const handleCalculatePayroll = async () => {
        if (!serviceAvailable) {
            setError('QCEW服务不可用，无法进行薪酬计算');
            return;
        }
        
        if (!selectedArea || !selectedYear || !params.planned_fte) {
            setError('请先选择地区、年份，并输入员工人数');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            // 先获取该地区的QCEW数据
            const dataResponse = await fetch(
                getApiUrl(`/api/us-labor/qcew/data?area_code=${selectedArea.area_fips}&year=${selectedYear}`)
            );
            
            if (dataResponse.status === 503) {
                const errorData = await dataResponse.json();
                setServiceAvailable(false);
                setServiceStatus(errorData);
                setError(errorData.message);
                return;
            }

            const dataResult = await dataResponse.json();

            if (!dataResult.success || !dataResult.data) {
                setError(`${selectedArea.area_title} ${selectedYear}年数据不可用`);
                return;
            }

            // 计算薪酬
            const weeklyWage = dataResult.data.avg_weekly_wage;
            const fte = parseFloat(params.planned_fte);
            const annualPayroll = weeklyWage * 52 * fte;
            const monthlyPayroll = annualPayroll / 12;
            const perEmployeeAnnual = weeklyWage * 52;

            const calculationResult = {
                success: true,
                calculation: {
                    formula: "年薪酬总额 = 平均周薪 × 52周 × 员工人数",
                    inputs: {
                        area_title: selectedArea.area_title,
                        area_code: selectedArea.area_fips,
                        year: selectedYear,
                        quarter: dataResult.data.quarter,
                        planned_fte: fte,
                        parcel_id: selectedParcel?.parcel_id || null,
                        parcel_coordinates: locationSuggestion?.coordinates || null
                    },
                    components: {
                        avg_weekly_wage: weeklyWage,
                        weeks_per_year: 52,
                        planned_fte: fte
                    },
                    result: {
                        annual_payroll: annualPayroll,
                        formatted_payroll: `$${annualPayroll.toLocaleString('en-US', { minimumFractionDigits: 2 })}`,
                        monthly_payroll: monthlyPayroll,
                        per_employee_annual: perEmployeeAnnual
                    },
                    qcew_data: dataResult.data,
                    metadata: {
                        calculation_time: new Date().toISOString(),
                        data_source: 'qcew_csv_multi_year',
                        location_context: locationSuggestion || null
                    }
                }
            };

            setCalculationData(calculationResult);
            onCalculationComplete(calculationResult);

            console.log(`✅ 薪酬计算完成: ${selectedArea.area_title} ${selectedYear}年`);

        } catch (err) {
            console.error('❌ 薪酬计算失败:', err);
            setError('薪酬计算失败，请稍后重试');
        } finally {
            setIsLoading(false);
        }
    };

    /**
     * 🧹 清除所有数据
     */
    const handleClear = () => {
        setParams({ city: '', state: '', planned_fte: 100 });
        setSelectedArea(null);
        setSelectedYear(null);
        setSearchResults([]);
        setSuggestions([]);
        setShowSuggestions(false);
        setCalculationData(null);
        setError(null);
        setHasSearched(false);
    };

    /**
     * ⭐ 使用推荐城市
     */
    const handleUseRecommendedCity = (city) => {
        setParams(prev => ({ ...prev, city: city.name, state: city.state }));
        setTimeout(() => {
            handleCitySearch(city.name);
        }, 100);
    };

    // 监听城市输入变化
    useEffect(() => {
        if (params.city) {
            handleCitySearch(params.city);
        }
    }, [params.city]);

    // 点击外部关闭建议
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (suggestionsRef.current && !suggestionsRef.current.contains(event.target) &&
                cityInputRef.current && !cityInputRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // 如果服务不可用，显示替代内容
    if (!serviceAvailable && isVisible) {
        return (
            <div className={`qcew-calculator-container ${className}`}>
                <button 
                    className="qcew-toggle-button disabled"
                    onClick={() => setIsVisible(false)}
                >
                    <div className="toggle-icon">🏭</div>
                    <div className="toggle-text">
                        <span className="toggle-title">⚠️ QCEW服务不可用</span>
                        <span className="toggle-subtitle">点击收起</span>
                    </div>
                </button>
                
                <div className="qcew-calculator-panel">
                    <div className="qcew-header">
                        <h3>🚫 QCEW 薪酬计算器不可用</h3>
                        <p style={{color: '#ff6b6b'}}>
                            {serviceStatus?.reason || '服务暂时不可用'}
                        </p>
                    </div>
                    
                    <div className="qcew-service-unavailable">
                        <div className="unavailable-message">
                            <h4>📋 替代方案：</h4>
                            <ul>
                                {serviceStatus?.alternatives?.map((alt, index) => (
                                    <li key={index}>{alt}</li>
                                )) || [
                                    <li key="1">使用劳工统计局在线API</li>,
                                    <li key="2">使用其他经济数据源</li>,
                                    <li key="3">联系管理员获取替代方案</li>
                                ]}
                            </ul>
                        </div>
                        
                        <div className="external-links">
                            <h4>🔗 外部资源：</h4>
                            <a 
                                href="https://www.bls.gov/cew/" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="qcew-external-link"
                            >
                                📊 劳工统计局QCEW官网
                            </a>
                            <a 
                                href="https://data.bls.gov/cew/apps/data_views/data_views.htm" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="qcew-external-link"
                            >
                                🗃️ QCEW在线数据查看器
                            </a>
                        </div>
                    </div>
                    
                    <button
                        onClick={() => {
                            setIsVisible(false);
                            setError(null);
                        }}
                        className="qcew-close-button"
                    >
                        ✖️
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className={`qcew-calculator-container ${className}`}>
            {/* 展开/收起按钮 */}
            <button 
                className="qcew-toggle-button"
                onClick={() => setIsVisible(!isVisible)}
            >
                🧮 薪酬计算器 {isVisible ? '👆' : '👇'}
                {selectedParcel && (
                    <span className="parcel-indicator">
                        📍 {selectedParcel.parcel_id}
                    </span>
                )}
            </button>

            {/* 主面板 */}
            {isVisible && (
                <div className="qcew-calculator-panel">
                    <div className="qcew-header">
                        <h3>🏭 QCEW 薪酬计算器</h3>
                        <p>基于美国劳工统计局QCEW数据的薪酬分析工具</p>
                        {selectedParcel && locationSuggestion && (
                            <div className="location-context">
                                📍 地块 {selectedParcel.parcel_id} 
                                <small>({locationSuggestion.coordinates})</small>
                            </div>
                        )}
                        <button
                            className="qcew-close-button"
                            onClick={() => setIsVisible(false)}
                            title="关闭"
                        >
                            ✕
                        </button>
                    </div>

                    <div className="qcew-form">
                        {/* 地区搜索 */}
                        <div className="qcew-input-group qcew-city-input-container">
                            <label htmlFor="qcew-city">
                                🔍 搜索地区:
                                {locationSuggestion && (
                                    <span className="location-hint">
                                        建议: {locationSuggestion.suggestedState}
                                    </span>
                                )}
                            </label>
                            <div className="qcew-input-wrapper">
                                <input
                                    ref={cityInputRef}
                                    id="qcew-city"
                                    type="text"
                                    value={params.city}
                                    onChange={(e) => setParams(prev => ({ ...prev, city: e.target.value }))}
                                    onFocus={() => setShowSuggestions(suggestions.length > 0 || searchResults.length > 0)}
                                    placeholder={locationSuggestion ? `建议: ${locationSuggestion.suggestedState}` : "输入城市、县或州名称..."}
                                    className="qcew-input"
                                />
                                {isSearching && (
                                    <div className="qcew-search-indicator">🔍</div>
                                )}
                            </div>
                        </div>

                        {/* 搜索结果和建议 */}
                        {showSuggestions && (searchResults.length > 0 || suggestions.length > 0) && (
                            <div className="qcew-suggestions-dropdown" ref={suggestionsRef}>
                                {/* 有数据的地区 */}
                                {searchResults.length > 0 && (
                                    <div className="qcew-results-section">
                                        <div className="qcew-suggestions-header">📊 可用地区数据</div>
                                        {searchResults.map((area, index) => (
                                            <div key={index} className="qcew-area-result">
                                                <div className="qcew-area-title">{area.area_title}</div>
                                                <div className="qcew-area-years">
                                                    {area.qcew_data.available_years.map(year => (
                                                        <button
                                                            key={year}
                                                            className={`qcew-year-button ${selectedArea?.area_fips === area.area_fips && selectedYear === year ? 'selected' : ''}`}
                                                            onClick={() => handleSelectArea(area, year)}
                                                        >
                                                            {year}年
                                                        </button>
                                                    ))}
                                                </div>
                                                <div className="qcew-area-type">
                                                    {area.is_state && '🏛️ 州级'}
                                                    {area.is_county && '🏘️ 县级'}
                                                    {area.is_msa && '🏙️ 都市区'}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}

                                {/* 智能建议 */}
                                {suggestions.length > 0 && (
                                    <div className="qcew-suggestions-section">
                                        <div className="qcew-suggestions-header">💡 智能建议</div>
                                        {suggestions.map((suggestion, index) => (
                                            <div
                                                key={index}
                                                className={`qcew-suggestion-item qcew-${suggestion.type} qcew-confidence-${suggestion.confidence}`}
                                                onClick={() => handleSelectSuggestion(suggestion)}
                                            >
                                                <div className="qcew-suggestion-text">{suggestion.text}</div>
                                                <div className="qcew-suggestion-reason">{suggestion.reason}</div>
                                                <div className="qcew-suggestion-badge">
                                                    {suggestion.type === 'available' && '✅ 有数据'}
                                                    {suggestion.type === 'recommended' && '⭐ 推荐'}
                                                    {suggestion.type === 'spelling' && '📝 拼写'}
                                                    {suggestion.type === 'alternative' && '🔄 替代'}
                                                    {suggestion.type === 'unavailable' && '❌ 无数据'}
                                                    {suggestion.type === 'economic_alternative' && '🏢 经济替代'}
                                                    {suggestion.type === 'popular' && '🌟 热门'}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}

                        {/* 无搜索结果提示 */}
                        {hasSearched && searchResults.length === 0 && suggestions.length === 0 && !isSearching && (
                            <div className="qcew-no-results">
                                <div className="qcew-no-results-icon">🔍</div>
                                <div className="qcew-no-results-text">
                                    未找到 "{params.city}" 的相关地区数据
                                </div>
                                <div className="qcew-no-results-suggestion">
                                    请尝试：
                                    <ul>
                                        <li>检查拼写</li>
                                        <li>使用完整的城市或州名称</li>
                                        <li>尝试搜索县名（如"Los Angeles County"）</li>
                                        <li>使用下方的推荐地区</li>
                                    </ul>
                                </div>
                            </div>
                        )}

                        {/* 选中地区显示 */}
                        {selectedArea && selectedYear && (
                            <div className="qcew-selected-area">
                                <div className="qcew-selected-header">✅ 已选择:</div>
                                <div className="qcew-selected-info">
                                    <span className="qcew-selected-name">{selectedArea.area_title}</span>
                                    <span className="qcew-selected-year">{selectedYear}年数据</span>
                                </div>
                            </div>
                        )}

                        {/* 员工人数 */}
                        <div className="qcew-input-group">
                            <label htmlFor="qcew-fte">👥 计划员工人数:</label>
                            <input
                                id="qcew-fte"
                                type="number"
                                min="1"
                                max="10000"
                                value={params.planned_fte}
                                onChange={(e) => setParams(prev => ({ ...prev, planned_fte: e.target.value }))}
                                className="qcew-input"
                            />
                        </div>

                        {/* 操作按钮 */}
                        <div className="qcew-actions">
                            <button
                                onClick={handleCalculatePayroll}
                                disabled={isLoading || !selectedArea || !selectedYear || !params.planned_fte}
                                className="qcew-calculate-button"
                            >
                                {isLoading ? '🔄 计算中...' : '🧮 计算薪酬'}
                            </button>
                            <button
                                onClick={handleClear}
                                className="qcew-clear-button"
                            >
                                🧹 清除
                            </button>
                        </div>
                    </div>

                    {/* 推荐地区 */}
                    <div className="qcew-recommendations">
                        <h4>⭐ 推荐地区（有完整数据）:</h4>
                        <div className="qcew-recommended-cities">
                            <button 
                                onClick={() => handleUseRecommendedCity({ name: 'Alabama', state: 'Alabama' })}
                                className="qcew-city-chip"
                                title="2023年完整数据"
                            >
                                🏢 Alabama (2023)
                            </button>
                        </div>
                    </div>

                    {/* 错误显示 */}
                    {error && (
                        <div className="qcew-error">
                            <div className="qcew-error-icon">❌</div>
                            <div className="qcew-error-text">{error}</div>
                        </div>
                    )}

                    {/* 结果显示 */}
                    {calculationData && (
                        <div className="qcew-results">
                            <h4>📊 薪酬计算结果</h4>
                            {calculationData.calculation && (
                                <div className="qcew-calculation-result">
                                    <div className="qcew-result-header">
                                        <h5>💰 {calculationData.calculation.inputs.area_title}</h5>
                                        <div className="qcew-result-subtitle">
                                            {calculationData.calculation.inputs.year}年Q{calculationData.calculation.inputs.quarter}数据
                                            {calculationData.calculation.inputs.parcel_id && (
                                                <span className="parcel-context">
                                                    | 地块: {calculationData.calculation.inputs.parcel_id}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                    
                                    <div className="qcew-result-grid">
                                        <div className="qcew-result-item">
                                            <span className="qcew-result-label">📊 平均周薪:</span>
                                            <span className="qcew-result-value">
                                                ${calculationData.calculation.components.avg_weekly_wage?.toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="qcew-result-item">
                                            <span className="qcew-result-label">👥 计划员工:</span>
                                            <span className="qcew-result-value">
                                                {calculationData.calculation.components.planned_fte} 人
                                            </span>
                                        </div>
                                        <div className="qcew-result-item qcew-highlight">
                                            <span className="qcew-result-label">💼 年薪酬总额:</span>
                                            <span className="qcew-result-value">
                                                {calculationData.calculation.result.formatted_payroll}
                                            </span>
                                        </div>
                                        <div className="qcew-result-item">
                                            <span className="qcew-result-label">📅 月薪酬:</span>
                                            <span className="qcew-result-value">
                                                ${calculationData.calculation.result.monthly_payroll?.toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="qcew-result-item">
                                            <span className="qcew-result-label">👤 人均年薪:</span>
                                            <span className="qcew-result-value">
                                                ${calculationData.calculation.result.per_employee_annual?.toLocaleString()}
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div className="qcew-data-info">
                                        <small>
                                            📍 数据来源: {calculationData.calculation.metadata.data_source}
                                            <br />
                                            🕒 计算时间: {new Date(calculationData.calculation.metadata.calculation_time).toLocaleString()}
                                            {calculationData.calculation.inputs.parcel_coordinates && (
                                                <><br />📍 地块坐标: {calculationData.calculation.inputs.parcel_coordinates}</>
                                            )}
                                        </small>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                    
                    {/* 状态指示器 */}
                    <div className="qcew-status-bar">
                        <span className="qcew-status-item">
                            🔍 搜索结果: {searchResults.length} 个地区
                        </span>
                        {suggestions.length > 0 && (
                            <span className="qcew-status-item">
                                💡 建议: {suggestions.length} 条
                            </span>
                        )}
                        {selectedArea && (
                            <span className="qcew-status-item">
                                ✅ 已选择: {selectedArea.area_title}
                            </span>
                        )}
                        {selectedParcel && (
                            <span className="qcew-status-item">
                                📍 地块: {selectedParcel.parcel_id}
                            </span>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default QCEWPayrollCalculator; 
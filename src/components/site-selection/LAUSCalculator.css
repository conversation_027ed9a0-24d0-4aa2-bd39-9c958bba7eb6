/* LAUS Calculator Styles - 无演示数据版 */
.laus-calculator {
  margin-bottom: 16px;
}

/* 切换按钮样式 */
.laus-toggle-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 14px 18px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.laus-toggle-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.laus-toggle-button.expanded {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.toggle-icon {
  font-size: 18px;
}

.toggle-text {
  flex: 1;
  text-align: center;
  font-weight: 700;
}

.toggle-indicator {
  background: rgba(255, 255, 255, 0.25);
  border-radius: 8px;
  padding: 6px 10px;
  font-size: 12px;
  font-weight: 600;
}

/* 计算器面板样式 - 支持滚动 */
.laus-calculator-panel {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 16px;
  padding: 24px;
  margin-top: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  max-height: 80vh; /* 限制最大高度为视口的80% */
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
}

/* 自定义滚动条样式 */
.laus-calculator-panel::-webkit-scrollbar {
  width: 6px;
}

.laus-calculator-panel::-webkit-scrollbar-track {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 3px;
}

.laus-calculator-panel::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.laus-calculator-panel::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}

/* 头部样式 */
.laus-header h3 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 20px;
  font-weight: 700;
}

.laus-header p {
  margin: 0 0 24px 0;
  color: #718096;
  font-size: 15px;
}

/* 快速选择区域 */
.quick-select-section {
  margin-bottom: 20px;
}

.section-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 12px;
}

.city-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.city-button {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.city-button:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.city-button.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
}

.city-emoji {
  font-size: 16px;
}

.city-name {
  font-weight: 600;
}

/* 自定义输入区域 */
.custom-input-section {
  margin-bottom: 20px;
}

.input-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 12px;
}

.city-input,
.state-input {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  background: white;
}

.city-input:focus,
.state-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

/* 分析按钮 */
.analyze-button {
  width: 100%;
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  border: none;
  border-radius: 12px;
  color: white;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.analyze-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.analyze-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 错误样式 */
.error-message {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  border: 1px solid #fc8181;
  border-radius: 10px;
  padding: 14px 18px;
  color: #c53030;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: left;
  white-space: pre-wrap; /* 保持换行格式 */
}

/* 结果展示 */
.laus-results {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e2e8f0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.result-header h4 {
  margin: 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 700;
}

/* 移除演示数据标识 */

/* 关键指标 */
.key-metrics {
  display: grid;
  gap: 16px;
}

.primary-metric {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.primary-metric .metric-value {
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  line-height: 1;
}

.primary-metric .metric-label {
  font-size: 16px;
  font-weight: 600;
  opacity: 0.9;
}

.secondary-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.metric-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s ease;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-item .value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.metric-item .label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-item.pressure .value {
  font-size: 16px;
}

/* 公式提示 */
.formula-hint {
  background: rgba(102, 126, 234, 0.1);
  border-radius: 10px;
  padding: 12px 16px;
  font-size: 13px;
  color: #4a5568;
  margin-top: 16px;
  text-align: center;
  font-weight: 500;
}

/* 数据来源信息 */
.data-source-info {
  background: rgba(72, 187, 120, 0.1);
  border-radius: 10px;
  padding: 12px 16px;
  font-size: 12px;
  color: #2f855a;
  margin-top: 12px;
  text-align: center;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .city-buttons {
    grid-template-columns: 1fr;
  }
  
  .input-row {
    grid-template-columns: 1fr;
  }
  
  .secondary-metrics {
    grid-template-columns: 1fr;
  }
  
  .primary-metric .metric-value {
    font-size: 28px;
  }
  
  .laus-calculator-panel {
    max-height: 70vh; /* 在移动设备上降低最大高度 */
  }
}

@media (max-width: 480px) {
  .laus-calculator-panel {
    padding: 20px;
    max-height: 65vh;
  }
  
  .primary-metric {
    padding: 20px;
  }
  
  .primary-metric .metric-value {
    font-size: 24px;
  }
} 
/**
 * 📈 LAUS 可招聘余量计算器 - 生产版
 * Local Area Unemployment Statistics Calculator
 */

import React, { useState } from 'react';
import { calculateAvailableLabor } from '../../services/lausService';
import './LAUSCalculator.css';

// 推荐城市列表
const RECOMMENDED_CITIES = [
  { name: 'Los Angeles', state: 'California', emoji: '🌴' },
  { name: 'New York', state: 'New York', emoji: '🏙️' },
  { name: 'Chicago', state: 'Illinois', emoji: '🏢' },
  { name: 'Houston', state: 'Texas', emoji: '🛢️' }
];

const LAUSCalculator = ({ selectedParcel, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [lausData, setLausData] = useState(null);
  const [error, setError] = useState(null);
  
  // 表单状态
  const [formData, setFormData] = useState({
    city: '',
    state: ''
  });

  /**
   * 处理表单输入变化
   */
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (error) setError(null);
  };

  /**
   * 快速选择推荐城市
   */
  const selectRecommendedCity = (city) => {
    setFormData({
      city: city.name,
      state: city.state
    });
    if (error) setError(null);
  };

  /**
   * 执行LAUS计算
   */
  const handleCalculate = async () => {
    if (!formData.city.trim()) {
      setError('请选择或输入城市名称');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await calculateAvailableLabor({
        city: formData.city.trim(),
        state: formData.state.trim() || null,
        year: 2023
      });

      if (result.success) {
        setLausData(result);
      } else {
        // 改进错误信息显示
        let errorMsg = result.message || '计算失败';
        if (result.error_details) {
          errorMsg += `\n\n详细信息：${result.error_details.error_message}`;
        }
        if (result.data && result.data.county_fips) {
          errorMsg += `\n\n查询的县级代码：${result.data.county_fips}`;
        }
        setError(errorMsg);
      }
    } catch (err) {
      console.error('LAUS计算错误:', err);
      setError('网络连接失败，请检查网络连接后重试');
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 计算招聘压力指标
   */
  const getRecruitmentPressure = () => {
    if (!lausData?.calculation?.result?.AVAILABLE_LABOR) return null;
    
    const availableLabor = lausData.calculation.result.AVAILABLE_LABOR;
    const laborForce = lausData.calculation.inputs.LAUS_LABOR_FORCE;
    const unemploymentRate = (availableLabor / laborForce) * 100;
    
    if (unemploymentRate < 3) return { level: 'high', text: '高', color: '#e53e3e', icon: '🔴' };
    if (unemploymentRate < 6) return { level: 'medium', text: '中等', color: '#dd6b20', icon: '🟡' };
    return { level: 'low', text: '低', color: '#38a169', icon: '🟢' };
  };

  const recruitmentPressure = getRecruitmentPressure();

  return (
    <div className={`laus-calculator ${className}`}>
      {/* 切换按钮 */}
      <button
        className={`laus-toggle-button ${isExpanded ? 'expanded' : ''}`}
        onClick={() => setIsExpanded(!isExpanded)}
        title="LAUS 可招聘余量分析"
      >
        <span className="toggle-icon">📈</span>
        <span className="toggle-text">
          {isExpanded ? '收起' : '劳动力分析'}
        </span>
        {lausData && (
          <span className="toggle-indicator">
            ✅ 已分析
          </span>
        )}
      </button>

      {/* 计算器面板 */}
      {isExpanded && (
        <div className="laus-calculator-panel">
          <div className="laus-header">
            <h3>📈 可招聘余量分析</h3>
            <p>分析地区劳动力供给与招聘难度</p>
          </div>

          {/* 快速选择按钮 */}
          <div className="quick-select-section">
            <label className="section-label">快速选择城市：</label>
            <div className="city-buttons">
              {RECOMMENDED_CITIES.map((city, index) => (
                <button
                  key={index}
                  className={`city-button ${formData.city === city.name ? 'selected' : ''}`}
                  onClick={() => selectRecommendedCity(city)}
                >
                  <span className="city-emoji">{city.emoji}</span>
                  <span className="city-name">{city.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* 自定义输入 */}
          <div className="custom-input-section">
            <label className="section-label">或自定义输入：</label>
            <div className="input-row">
              <input
                type="text"
                placeholder="城市名称"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="city-input"
              />
              <input
                type="text"
                placeholder="州名（可选）"
                value={formData.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
                className="state-input"
              />
            </div>
          </div>

          {/* 分析按钮 */}
          <button
            className="analyze-button"
            onClick={handleCalculate}
            disabled={isLoading || !formData.city.trim()}
          >
            {isLoading ? '🔄 分析中...' : '🚀 开始分析'}
          </button>

          {/* 错误提示 */}
          {error && (
            <div className="error-message">
              ⚠️ {error}
            </div>
          )}

          {/* 结果展示 */}
          {lausData && (
            <div className="laus-results">
              <div className="result-header">
                <h4>{formData.city} 劳动力概况</h4>
              </div>

              <div className="key-metrics">
                <div className="primary-metric">
                  <div className="metric-value">
                    {lausData.calculation.result.AVAILABLE_LABOR?.toLocaleString()}
                  </div>
                  <div className="metric-label">可招聘人数</div>
                </div>

                <div className="secondary-metrics">
                  <div className="metric-item">
                    <span className="value">{lausData.calculation.inputs.LAUS_LABOR_FORCE?.toLocaleString()}</span>
                    <span className="label">劳动力总量</span>
                  </div>
                  <div className="metric-item">
                    <span className="value">{lausData.calculation.inputs.LAUS_EMPLOYED?.toLocaleString()}</span>
                    <span className="label">已就业</span>
                  </div>
                  {recruitmentPressure && (
                    <div className="metric-item pressure">
                      <span className="value" style={{ color: recruitmentPressure.color }}>
                        {recruitmentPressure.icon} {recruitmentPressure.text}
                      </span>
                      <span className="label">招聘压力</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="formula-hint">
                💡 可招聘人数 = 劳动力总量 - 已就业人数
              </div>

              <div className="data-source-info">
                📊 数据来源：美国劳工统计局 (Bureau of Labor Statistics) LAUS API
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LAUSCalculator; 
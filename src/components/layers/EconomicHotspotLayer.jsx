import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import '../../styles/EconomicHotspotLayer.css';

/**
 * 经济地理热点图层
 * 基于区位商(LQ)或重力模型计算产业强度热力层
 *
 * @param {boolean} show - 是否显示该图层
 * @param {string} mapStyle - 地图样式，用于判断是否为夜间模式
 * @param {string} modelType - 模型类型: 'lq' (区位商) 或 'gravity' (重力模型)
 * @param {number} intensity - 热力图强度 (0-100)
 * @param {string} industry - 行业类型，用于LQ计算
 * @param {number} radius - 服务半径 (米)
 * @param {string} country - 国家: 'THAILAND' 或 'USA'
 */
const EconomicHotspotLayer = ({
  show = false,
  mapStyle = 'day',
  modelType = 'lq',
  intensity = 50,
  industry = 'manufacturing',
  radius = 50000,
  country = 'THAILAND',
  showLabels = true,
  showCircles = true,
  showDetails = true,
  onSettingsChange = null
}) => {
  const map = useMap();
  const [loading, setLoading] = useState(false);
  const layerRef = useRef(new L.LayerGroup());
  const [thaiData, setThaiData] = useState([]);
  const activeMarkerRef = useRef(null); // 追踪当前活跃的标记
  const isDarkMode = mapStyle === 'night';

  // 加载数据并创建图层
  useEffect(() => {
    if (!show) {
      // 如果不显示，清除现有图层
      if (layerRef.current && map.hasLayer(layerRef.current)) {
        map.removeLayer(layerRef.current);
        layerRef.current = null;
      }
      return;
    }

    // 如果已经有图层，先移除
    if (layerRef.current && map.hasLayer(layerRef.current)) {
      map.removeLayer(layerRef.current);
      layerRef.current = null;
    }

    setLoading(true);

    // 创建图层组
    layerRef.current = L.layerGroup();

    // 根据国家和模型类型加载不同的数据
    if (country === 'THAILAND') {
      if (modelType === 'lq') {
        // 加载泰国区位商数据
        fetch('/data/Thai_labor_economics.json')
          .then(res => {
            if (!res.ok) throw new Error(`加载经济数据失败: ${res.status}`);
            return res.json();
          })
          .then(data => {
            console.log('泰国经济数据加载成功');
            createThaiLQHeatmap(data);
            setLoading(false);
          })
          .catch(err => {
            console.error('加载泰国经济数据时出错:', err);
            setLoading(false);
          });
      } else {
        // 泰国重力模型 - 使用Thai_labor_economics.json数据
        fetch('/data/Thai_labor_economics.json')
          .then(res => {
            if (!res.ok) throw new Error(`加载劳动力数据失败: ${res.status}`);
            return res.json();
          })
          .then(data => {
            console.log('泰国劳动力数据加载成功');
            createThaiGravityHeatmap(data);
            setLoading(false);
          })
          .catch(err => {
            console.error('加载泰国劳动力数据时出错:', err);
            setLoading(false);
          });
      }
    } else if (country === 'USA') {
      // 美国经济热点数据
      // 由于没有实际的美国数据文件，这里使用模拟数据
      console.log('加载美国经济热点数据');
      if (modelType === 'lq') {
        createUSLQHeatmap();
      } else {
        createUSGravityHeatmap();
      }
      setLoading(false);
    }

    return () => {
      // 清理函数
      if (layerRef.current && map.hasLayer(layerRef.current)) {
        map.removeLayer(layerRef.current);
        layerRef.current = null;
      }
    };
  }, [map, show, modelType, intensity, industry, radius, country]);

  // 根据值获取热力图颜色
  const getHeatColor = (value) => {
    // 改进颜色方案，使用更鲜明的对比色来区分核心市场和补贴区域
    if (value > 0.8) return '#FF3B30'; // 鲜红色 - 高强度核心市场
    if (value > 0.6) return '#FF9500'; // 橙色 - 重要市场
    if (value > 0.4) return '#FFCC00'; // 黄色 - 中等市场
    if (value > 0.2) return '#34C759'; // 绿色 - 潜力市场/补贴区
    return '#5AC8FA';                 // 蓝色 - 低强度/补贴洼地
  };

  // 根据LQ值获取解释文本
  const getLQExplanation = (lq) => {
    if (lq > 1.5) return '这是一个非常强的产业集中区，可能是核心市场。';
    if (lq > 1.2) return '该地区在这个行业有明显的优势，是重要市场。';
    if (lq > 1.0) return '该地区在这个行业的集中度高于全国平均水平。';
    if (lq > 0.8) return '该地区在这个行业的集中度接近全国平均水平。';
    return '该地区在这个行业的集中度低于全国平均水平，可能是补贴洼地。';
  };

  // 获取行业名称的本地化显示
  const getIndustryName = (industry) => {
    switch(industry) {
      case 'manufacturing': return '制造业';
      case 'government': return '政府部门';
      case 'private': return '私营企业';
      case 'self-employed': return '个体经营';
      default: return '制造业';
    }
  };

  // 创建核心市场标签与大头钉标记
  const createMarketPinMarker = (point, isSubsidyArea = false, showLabel = true, showDetailedAnalysis = true) => {
    // 根据是否为补贴区域选择适当的图标和样式
    const markerType = isSubsidyArea ? 'subsidy-area' : 'core-market';
    const markerLabel = isSubsidyArea ? '补贴洼地' : '核心市场';
    
    // 创建HTML内容的图标 - 使用大头钉样式
    const pinIcon = L.divIcon({
      className: `economic-pin-marker ${markerType}`,
      html: `
        <div class="pin-container">
          <div class="pin"></div>
          ${showLabel ? `<div class="pin-label">${markerLabel}</div>` : ''}
        </div>
      `,
      iconSize: [40, 50],
      iconAnchor: [20, 50]
    });
    
    // 创建标记并加上弹出信息
    const marker = L.marker([point.lat, point.lng], {
      icon: pinIcon,
      zIndexOffset: 1000
    });
    
    // 如果显示详细分析，才生成数据可视化
    const dataVisuals = showDetailedAnalysis ? generateDataVisualization(point, isSubsidyArea) : null;
    
    // 生成模型切换按钮 HTML
    const modelToggleButtons = `
      <div class="model-toggle-container">
        <div class="model-toggle-label">选择分析模型:</div>
        <div class="model-toggle-buttons">
          <button class="model-toggle-btn ${modelType === 'lq' ? 'active' : ''}" data-model="lq">区位商模型</button>
          <button class="model-toggle-btn ${modelType === 'gravity' ? 'active' : ''}" data-model="gravity">重力模型</button>
        </div>
      </div>
    `;
    
    // 根据点类型创建丰富的弹窗内容
    let popupContent = '';
    
    if (showDetailedAnalysis && dataVisuals) {
      // 详细分析模式
      popupContent = `
        <div class="economic-popup ${isSubsidyArea ? 'subsidy-area' : 'core-market'}" data-marker-id="${point.id || Math.random().toString(36).substr(2, 9)}">
          <h3>${point.name || '区域'}</h3>
          <p>类型: <strong>${isSubsidyArea ? '补贴洼地' : '核心市场'}</strong></p>
          
          ${point.value ? `
          <div class="data-section">
            <p>区位商 (LQ): <strong>${point.value}</strong></p>
            <div class="data-bar-container">
              <div class="data-bar ${dataVisuals.barClass}" style="width: ${dataVisuals.barWidth}%"></div>
            </div>
          </div>` : ''}
          
          <div class="analysis-section">
            <p><strong>经济分析:</strong></p>
            <p>${dataVisuals.analysis}</p>
          </div>
          
          ${dataVisuals.additionalMetrics ? `
          <div class="metrics-section">
            <table class="economic-popup-table">
              <thead>
                <tr>
                  <th>指标</th>
                  <th>数值</th>
                  <th>对比</th>
                </tr>
              </thead>
              <tbody>
                ${dataVisuals.additionalMetrics}
              </tbody>
            </table>
          </div>` : ''}
          
          ${modelToggleButtons}
        </div>
      `;
    } else {
      // 简单模式
      popupContent = `
        <div class="economic-popup ${isSubsidyArea ? 'subsidy-area' : 'core-market'}" data-marker-id="${point.id || Math.random().toString(36).substr(2, 9)}">
          <h3>${point.name || '区域'}</h3>
          <p>类型: <strong>${isSubsidyArea ? '补贴洼地' : '核心市场'}</strong></p>
          ${point.value ? `<p>区位商 (LQ): <strong>${point.value}</strong></p>` : ''}
          <p>${isSubsidyArea ? '该区域在此行业的集中度低于平均水平，可能有政策补贴优势。' : '该区域是行业集中区，具有显著市场优势。'}</p>
          
          ${modelToggleButtons}
        </div>
      `;
    }
    
    const popup = L.popup({
      maxWidth: 320,
      className: 'economic-popup-container',
      closeButton: true,
      autoClose: true
    }).setContent(popupContent);
    
    // 添加弹窗事件监听
    popup.on('add', function() {
      setTimeout(() => {
        const popupElement = document.querySelector('.economic-popup');
        if (popupElement) {
          const markerId = popupElement.getAttribute('data-marker-id');
          activeMarkerRef.current = markerId;
          
          // 添加按钮点击事件
          const lqButton = popupElement.querySelector('.model-toggle-btn[data-model="lq"]');
          const gravityButton = popupElement.querySelector('.model-toggle-btn[data-model="gravity"]');
          
          if (lqButton) {
            lqButton.addEventListener('click', () => handleModelToggle('lq'));
          }
          
          if (gravityButton) {
            gravityButton.addEventListener('click', () => handleModelToggle('gravity'));
          }
        }
      }, 100);
    });
    
    popup.on('remove', function() {
      activeMarkerRef.current = null;
    });
    
    marker.bindPopup(popup);
    
    return marker;
  };
  
  // 处理模型类型切换
  const handleModelToggle = useCallback((newModelType) => {
    if (onSettingsChange && modelType !== newModelType) {
      onSettingsChange({ modelType: newModelType });
    }
  }, [modelType, onSettingsChange]);

  // 生成数据可视化和更详细的分析
  const generateDataVisualization = (point, isSubsidyArea) => {
    // 计算数据条宽度
    const lq = parseFloat(point.value) || 0;
    const normalizedWidth = Math.min(100, Math.max(5, lq * 50)); // 确保宽度在 5-100% 之间
    
    // 判断类型和颜色
    let barClass = 'medium';
    if (lq > 1.3) barClass = 'high';
    else if (lq < 0.7) barClass = 'low';
    
    // 生成详细分析文字
    let analysis = '';
    if (isSubsidyArea) {
      analysis = `该区域在${getIndustryName(point.industry || 'manufacturing')}的区位商为${lq.toFixed(2)}，低于全国平均水平。这表明该地区在这一行业的发展有潜力，适合施行政策激励或补贴措施，如税收减免、用地撒贩或基础设施建设支持等。`;
    } else {
      analysis = `该区域在${getIndustryName(point.industry || 'manufacturing')}的区位商为${lq.toFixed(2)}，高于全国平均水平。这表明该地区已形成行业集群，具有显著的市场竞争优势，适合进一步开发和投资。`;
    }
    
    // 统计指标数据 - 模拟数据
    let additionalMetrics = '';
    
    if (point.name) { // 只有当实际有地名时才生成这些详细指标
      // 生成一些模拟数据，实际应用中应替换为真实数据
      const laborForce = Math.round(50000 + Math.random() * 150000);
      const avgWage = Math.round(15000 + (isSubsidyArea ? Math.random() * 5000 : Math.random() * 15000));
      const growthRate = (isSubsidyArea ? -2 + Math.random() * 5 : 2 + Math.random() * 8).toFixed(1);
      
      additionalMetrics = `
        <tr>
          <td>劳动力</td>
          <td>${laborForce.toLocaleString()}</td>
          <td>${isSubsidyArea ? '↓' : '↑'} ${Math.round(Math.random() * 5)}%</td>
        </tr>
        <tr>
          <td>平均工资</td>
          <td>$${avgWage.toLocaleString()}/年</td>
          <td>${isSubsidyArea ? '↓' : '↑'} ${Math.round(Math.random() * 6)}%</td>
        </tr>
        <tr>
          <td>增长率</td>
          <td>${growthRate}%</td>
          <td class="${parseFloat(growthRate) > 0 ? 'positive' : 'negative'}"><strong>${parseFloat(growthRate) > 0 ? '↗' : '↘'}</strong></td>
        </tr>
      `;
    }
    
    return {
      barWidth: normalizedWidth,
      barClass,
      analysis,
      additionalMetrics
    };
  };

  // 创建泰国区位商热力图
  const createThaiLQHeatmap = (data) => {
    if (!layerRef.current) return;

    // 清除现有图层
    layerRef.current.clearLayers();

    try {
      // 获取原始数据
      const rawData = data['Raw Data'] || [];
      if (rawData.length === 0) {
        console.error('没有找到有效的经济数据');
        return;
      }

      // 获取全国总数据
      const nationwide = rawData.find(item => item.Province_and_Gender === 'Regional Total');
      if (!nationwide) {
        console.error('没有找到全国总数据');
        return;
      }

      // 计算区位商的数据点
      const points = [];

      // 泰国中部省份坐标 (简化版)
      const provinceCoordinates = {
        'Prachin Buri': { lat: 14.0579, lng: 101.3736 },
        'Chachoengsao': { lat: 13.6904, lng: 101.0779 },
        'Chon Buri': { lat: 13.3611, lng: 100.9847 },
        'Rayong': { lat: 12.6833, lng: 101.2833 },
        'Samut Prakan': { lat: 13.5991, lng: 100.5998 },
        'Bangkok': { lat: 13.7563, lng: 100.5018 },
        'Pathum Thani': { lat: 14.0208, lng: 100.5253 },
        'Nonthaburi': { lat: 13.8622, lng: 100.5147 },
        'Samut Sakhon': { lat: 13.5475, lng: 100.2747 }
      };

      // 根据行业选择相应的数据字段
      let industryField = 'Manufacturing Workforce';
      let totalField = 'Total Labor Force';

      switch(industry) {
        case 'manufacturing':
          industryField = 'Manufacturing Workforce';
          break;
        case 'government':
          industryField = 'Government Employee';
          break;
        case 'private':
          industryField = 'Private Employee';
          break;
        case 'self-employed':
          industryField = 'Self-Employed';
          break;
        default:
          industryField = 'Manufacturing Workforce';
      }
      
      // 存储当前行业名称以传递给标记
      const currentIndustry = industry;

      // 全国行业占比
      const nationwideIndustryShare = nationwide[industryField] / nationwide[totalField];

      // 为每个省份计算区位商
      rawData.forEach(province => {
        const provinceName = province.Province_and_Gender;

        // 跳过全国数据和非省份数据
        if (provinceName === 'Regional Total' || !provinceCoordinates[provinceName]) {
          return;
        }

        // 计算该省的行业占比
        const provinceIndustryShare = province[industryField] / province[totalField];

        // 计算区位商 (LQ) = 地区行业占比 / 全国行业占比
        const lq = provinceIndustryShare / nationwideIndustryShare;

        // 添加到点集合
        if (provinceCoordinates[provinceName]) {
          points.push({
            lat: provinceCoordinates[provinceName].lat,
            lng: provinceCoordinates[provinceName].lng,
            intensity: lq,
            name: provinceName,
            value: lq.toFixed(2),
            industry: currentIndustry // 确保行业信息传递给标记
          });
        }
      });

      // 使用标记而不是圆圈来表示热点
      points.forEach(point => {
        const normalizedIntensity = Math.min(2, Math.max(0, point.intensity)) / 2;
        const adjustedIntensity = normalizedIntensity * (intensity / 100);
        
        // 判断是核心市场还是补贴洼地
        const isHighLQ = point.intensity > 1.3;
        const isLowLQ = point.intensity < 0.7;
        
        // 创建服务半径圈
        if (isHighLQ || isLowLQ) {
          const color = isHighLQ ? '#FF3B30' : '#5AC8FA';
          const circleRadius = radius * (isHighLQ ? 0.8 : 0.5);
          
          // 如果显示半径圈设置为真，才创建半径圈
          if (showCircles) {
            // 创建服务半径圈
            const circle = L.circle([point.lat, point.lng], {
              radius: circleRadius,
              fillColor: color,
              fillOpacity: 0.15,
              stroke: true,
              color: color,
              weight: 1,
              opacity: 0.5,
              dashArray: '5, 10',
              className: 'economic-service-circle'
            });
            
            layerRef.current.addLayer(circle);
          }
          
          // 创建并添加标记大头钉，并传递显示标签设置
          const pinMarker = createMarketPinMarker(point, !isHighLQ, showLabels, showDetails);
          layerRef.current.addLayer(pinMarker);
        }
      });

      // 将图层添加到地图
      map.addLayer(layerRef.current);

    } catch (error) {
      console.error('创建泰国区位商热力图时出错:', error);
    }
  };

  // 创建泰国重力模型热力图 
  const createThaiGravityHeatmap = (data) => {
    if (!layerRef.current) return;

    // 清除现有图层
    layerRef.current.clearLayers();

    try {
      // 泰国中部主要城市/工业中心
      const centers = [
        { name: 'Bangkok', lat: 13.7563, lng: 100.5018, mass: 100 },
        { name: 'Eastern Economic Corridor', lat: 13.1776, lng: 101.0794, mass: 85 },
        { name: 'Ayutthaya', lat: 14.3692, lng: 100.5876, mass: 60 },
        { name: 'Samut Prakan', lat: 13.5991, lng: 100.5998, mass: 70 },
        { name: 'Pathum Thani', lat: 14.0208, lng: 100.5253, mass: 55 },
        { name: 'Chonburi', lat: 13.3611, lng: 100.9847, mass: 65 },
        { name: 'Rayong', lat: 12.6833, lng: 101.2833, mass: 75 }
      ];

      // 添加中心点标记 - 改用大头钉标记
      centers.forEach(center => {
        // 调整大小和颜色基于质量
        const normalizedMass = center.mass / 100;
        const adjustedMass = normalizedMass * (intensity / 100);
        
        // 根据质量决定是核心市场还是普通市场
        const isCoreMakret = center.mass > 70;
        
        // 创建服务半径圈
        const color = isCoreMakret ? '#FF3B30' : '#FF9500';
        const circleRadius = radius * normalizedMass;
        
        // 创建服务半径圈
        const circle = L.circle([center.lat, center.lng], {
          radius: circleRadius,
          fillColor: color,
          fillOpacity: 0.15,
          stroke: true,
          color: color,
          weight: 1,
          opacity: 0.5,
          dashArray: '5, 8',
          className: 'economic-service-circle'
        });
        
        layerRef.current.addLayer(circle);
        
        // 创建大头钉标记
        const point = {
          lat: center.lat,
          lng: center.lng,
          name: center.name,
          value: `${center.mass}%`
        };
        
        // 创建并添加标记大头钉，传递显示标签和详细分析设置
        const pinMarker = createMarketPinMarker(point, !isCoreMakret, showLabels, showDetails);
        layerRef.current.addLayer(pinMarker);
      });

      // 将图层添加到地图
      map.addLayer(layerRef.current);

    } catch (error) {
      console.error('创建泰国重力模型热力图时出错:', error);
    }
  };

  // 创建美国区位商热力图
  const createUSLQHeatmap = () => {
    if (!layerRef.current) return;

    // 清除现有图层
    layerRef.current.clearLayers();

    try {
      // 美国主要经济中心坐标
      const stateCoordinates = {
        'California': { lat: 36.7783, lng: -119.4179, value: 1.8 },
        'New York': { lat: 40.7128, lng: -74.0060, value: 1.7 },
        'Texas': { lat: 31.9686, lng: -99.9018, value: 1.6 },
        'Florida': { lat: 27.6648, lng: -81.5158, value: 1.3 },
        'Illinois': { lat: 40.6331, lng: -89.3985, value: 1.4 },
        'Pennsylvania': { lat: 41.2033, lng: -77.1945, value: 1.2 },
        'Ohio': { lat: 40.4173, lng: -82.9071, value: 1.1 },
        'Michigan': { lat: 44.3148, lng: -85.6024, value: 1.3 },
        'Georgia': { lat: 33.0406, lng: -83.6431, value: 1.2 },
        'North Carolina': { lat: 35.7596, lng: -79.0193, value: 1.1 },
        'New Jersey': { lat: 40.0583, lng: -74.4057, value: 1.4 },
        'Virginia': { lat: 37.7693, lng: -78.1700, value: 1.0 },
        'Washington': { lat: 47.7511, lng: -120.7401, value: 1.5 },
        'Massachusetts': { lat: 42.4072, lng: -71.3824, value: 1.6 },
        'Arizona': { lat: 34.0489, lng: -111.0937, value: 0.9 },
        'Tennessee': { lat: 35.5175, lng: -86.5804, value: 0.8 },
        'Indiana': { lat: 39.8494, lng: -86.2583, value: 1.2 },
        'Missouri': { lat: 37.9643, lng: -91.8318, value: 0.7 },
        'Maryland': { lat: 39.0458, lng: -76.6413, value: 1.1 },
        'Wisconsin': { lat: 43.7844, lng: -88.7879, value: 1.0 }
      };

      // 根据行业调整LQ值
      const industryFactors = {
        'manufacturing': { 'California': 1.2, 'Michigan': 1.8, 'Ohio': 1.6, 'Indiana': 1.7 },
        'government': { 'Virginia': 1.8, 'Maryland': 1.7, 'Washington': 0.8 },
        'private': { 'New York': 1.9, 'California': 1.7, 'Texas': 1.5 },
        'self-employed': { 'Florida': 1.6, 'California': 1.5, 'Texas': 1.4 }
      };

      // 创建点集合
      const points = [];
      Object.entries(stateCoordinates).forEach(([stateName, data]) => {
        // 根据行业调整LQ值
        let lqValue = data.value;
        if (industryFactors[industry] && industryFactors[industry][stateName]) {
          lqValue = industryFactors[industry][stateName];
        }

        points.push({
          lat: data.lat,
          lng: data.lng,
          intensity: lqValue,
          name: stateName,
          value: lqValue.toFixed(2)
        });
      });

      // 绘制热力图
      points.forEach(point => {
        // 根据LQ值调整颜色和大小
        const normalizedIntensity = Math.min(2, Math.max(0, point.intensity)) / 2;
        const adjustedIntensity = normalizedIntensity * (intensity / 100); // 应用用户设置的强度

        const color = getHeatColor(adjustedIntensity);
        const circleRadius = radius * (0.5 + adjustedIntensity * 0.5); // 根据LQ值和用户设置调整半径

        // 创建圆形 - 使用更明显的虚线边框和特殊样式使其与园区标记更加区分
        const circle = L.circle([point.lat, point.lng], {
          radius: circleRadius,
          fillColor: color,
          fillOpacity: 0.35 + adjustedIntensity * 0.25,
          stroke: true,
          color: color,
          weight: 2,
          opacity: 0.9,
          dashArray: '5, 10', // 更明显的虚线样式
          className: 'economic-hotspot-circle' // 添加特殊类名以便于CSS选择器和动画效果
        });

        // 添加弹出信息
        circle.bindPopup(`
          <div class="economic-popup">
            <h3>${point.name}</h3>
            <p>区位商 (LQ): <strong>${point.value}</strong></p>
            <p>行业: ${getIndustryName(industry)}</p>
            <p>${getLQExplanation(point.intensity)}</p>
          </div>
        `);

        // 添加到图层
        layerRef.current.addLayer(circle);
      });

      // 将图层添加到地图
      map.addLayer(layerRef.current);

    } catch (error) {
      console.error('创建美国区位商热力图时出错:', error);
    }
  };

  // 创建美国重力模型热力图
  const createUSGravityHeatmap = () => {
    if (!layerRef.current) return;

    // 清除现有图层
    layerRef.current.clearLayers();

    try {
      // 美国主要经济中心
      const centers = [
        { name: 'New York City', lat: 40.7128, lng: -74.0060, mass: 100 },
        { name: 'Los Angeles', lat: 34.0522, lng: -118.2437, mass: 90 },
        { name: 'Chicago', lat: 41.8781, lng: -87.6298, mass: 85 },
        { name: 'Houston', lat: 29.7604, lng: -95.3698, mass: 80 },
        { name: 'Phoenix', lat: 33.4484, lng: -112.0740, mass: 70 },
        { name: 'Philadelphia', lat: 39.9526, lng: -75.1652, mass: 75 },
        { name: 'San Antonio', lat: 29.4241, lng: -98.4936, mass: 65 },
        { name: 'San Diego', lat: 32.7157, lng: -117.1611, mass: 70 },
        { name: 'Dallas', lat: 32.7767, lng: -96.7970, mass: 80 },
        { name: 'San Jose', lat: 37.3382, lng: -121.8863, mass: 75 }
      ];

      // 计算重力场
      const gravityPoints = [];
      const gridSize = 0.5; // 网格大小（经纬度）

      // 获取地图边界
      const bounds = map.getBounds();
      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      // 在地图可见区域创建网格点
      for (let lat = sw.lat; lat <= ne.lat; lat += gridSize) {
        for (let lng = sw.lng; lng <= ne.lng; lng += gridSize) {
          // 计算该点受到的总重力
          let totalGravity = 0;

          centers.forEach(center => {
            // 计算距离（简化版，实际应使用地理距离）
            const distance = Math.sqrt(
              Math.pow(lat - center.lat, 2) +
              Math.pow(lng - center.lng, 2)
            );

            // 避免除以零
            const safeDistance = Math.max(0.01, distance);

            // 重力模型: G = (M1 * M2) / (Distance^2)
            // 这里简化为: G = Mass / Distance^2
            const gravity = center.mass / Math.pow(safeDistance, 2);

            totalGravity += gravity;
          });

          // 只添加重力值足够大的点
          if (totalGravity > 10) {
            gravityPoints.push({
              lat,
              lng,
              intensity: totalGravity
            });
          }
        }
      }

      // 找出最大重力值用于归一化
      const maxGravity = Math.max(...gravityPoints.map(p => p.intensity));

      // 绘制热力图
      gravityPoints.forEach(point => {
        // 归一化重力值到0-1范围
        const normalizedIntensity = point.intensity / maxGravity;
        const adjustedIntensity = normalizedIntensity * (intensity / 100); // 应用用户设置的强度

        // 只绘制强度足够的点
        if (adjustedIntensity > 0.1) {
          const color = getHeatColor(adjustedIntensity);
          const circleRadius = 20000 * adjustedIntensity; // 根据重力值调整半径

          // 创建圆形 - 添加轻微描边以增强可见性
          const circle = L.circle([point.lat, point.lng], {
            radius: circleRadius,
            fillColor: color,
            fillOpacity: 0.25 + adjustedIntensity * 0.35,
            stroke: true,
            color: color,
            weight: 0.5,
            opacity: 0.6,
            className: 'economic-hotspot-circle' // 添加特殊类名以便于CSS选择器和动画效果
          });

          // 添加到图层
          layerRef.current.addLayer(circle);
        }
      });

      // 添加中心点标记
      centers.forEach(center => {
        // 调整大小和颜色基于质量
        const normalizedMass = center.mass / 100;
        const adjustedMass = normalizedMass * (intensity / 100);
        const color = getHeatColor(adjustedMass);

        // 创建中心点圆形 - 使用更明显的样式和特效
        const centerCircle = L.circle([center.lat, center.lng], {
          radius: radius * normalizedMass * 2, // 美国地图比例更大，所以半径也要更大
          fillColor: color,
          fillOpacity: 0.5,
          stroke: true,
          color: color,
          weight: 2.5,
          dashArray: '3, 7', // 添加虚线样式以区分园区标记
          className: 'economic-hotspot-center' // 添加特殊类名用于动画效果
        });

        // 添加弹出信息
        centerCircle.bindPopup(`
          <div class="economic-popup">
            <h3>${center.name}</h3>
            <p>经济中心强度: <strong>${center.mass}%</strong></p>
            <p>服务半径: ${(radius * normalizedMass * 2 / 1000).toFixed(1)} km</p>
            <p>该区域是重要的经济活动中心，对周边地区有较强的辐射作用。</p>
          </div>
        `);

        // 添加到图层
        layerRef.current.addLayer(centerCircle);
      });

      // 将图层添加到地图
      map.addLayer(layerRef.current);

    } catch (error) {
      console.error('创建美国重力模型热力图时出错:', error);
    }
  };

  return null; // 这个组件不渲染任何可见内容，只管理Leaflet图层
};

export default EconomicHotspotLayer;

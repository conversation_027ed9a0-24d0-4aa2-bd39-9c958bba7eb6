import React, { useEffect, useMemo } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import { ENERGY_DATA_TYPES } from '../../utils/energyDataService';

// Helper functions for enhanced data display with robust validation
const formatEnergyValue = (value, dataType) => {
  if (value === null || value === undefined || isNaN(value) || typeof value !== 'number') {
    return null; // Return null for invalid data instead of '0'
  }

  try {
    switch (dataType) {
      case 'customers':
        return value.toLocaleString('en-US');
      case 'price':
        return value.toFixed(2);
      case 'revenue':
        return (value / 1000).toFixed(1) + 'K';
      case 'sales':
        return value.toLocaleString('en-US');
      default:
        return value.toLocaleString('en-US');
    }
  } catch (error) {
    console.warn('Error formatting energy value:', error);
    return null;
  }
};

// Data validation helper
const validateEnergyData = (stateData, energyDataType) => {
  const validation = {
    isValid: true,
    hasValue: false,
    hasRanking: false,
    hasPeriod: false,
    hasUnit: false,
    errors: []
  };

  // Check if basic state data exists
  if (!stateData || !stateData.stateName) {
    validation.isValid = false;
    validation.errors.push('Missing state information');
    return validation;
  }

  // Check if value is valid
  if (stateData.value !== null && stateData.value !== undefined &&
      !isNaN(stateData.value) && typeof stateData.value === 'number') {
    validation.hasValue = true;
  } else {
    validation.errors.push('Invalid or missing value data');
  }

  // Check if period is valid
  if (stateData.period && typeof stateData.period === 'string' && stateData.period.trim()) {
    validation.hasPeriod = true;
  } else {
    validation.errors.push('Missing period information');
  }

  // Check if unit is available
  if (stateData.unit && typeof stateData.unit === 'string' && stateData.unit.trim()) {
    validation.hasUnit = true;
  }

  return validation;
};

// 改进的排名计算函数，修复NaN问题和百分比计算
const calculateRankingInfo = (value, allValues, language = 'zh') => {
  console.log('calculateRankingInfo called with:', { value, allValuesLength: allValues?.length, language });

  // 数据验证 - 允许0值，因为0也是有效的能源数据
  if (value === null || value === undefined || isNaN(value) || !allValues || allValues.length === 0) {
    console.log('Invalid input data for ranking calculation');
    return {
      rank: 0,
      total: 0,
      percentile: 0,
      rankText: language === 'en' ? 'No Data' : '无数据',
      rankClass: 'rank-no-data'
    };
  }

  // 过滤有效数值并排序 - 包括0值
  const validValues = allValues.filter(v => typeof v === 'number' && !isNaN(v) && v >= 0);
  console.log('Valid values:', { originalCount: allValues.length, validCount: validValues.length, validValues: validValues.slice(0, 10) });

  if (validValues.length === 0) {
    console.log('No valid values found for ranking');
    return {
      rank: 0,
      total: 0,
      percentile: 0,
      rankText: language === 'en' ? 'No Data' : '无数据',
      rankClass: 'rank-no-data'
    };
  }

  // 确保当前值也在有效值列表中
  if (!validValues.includes(value)) {
    console.log('Current value not found in valid values, adding it');
    validValues.push(value);
  }

  const sortedValues = [...validValues].sort((a, b) => b - a);
  console.log('Sorted values:', { total: sortedValues.length, range: [sortedValues[sortedValues.length-1], sortedValues[0]] });

  // 找到当前值的排名 - 改进匹配逻辑
  let rank = sortedValues.findIndex(v => Math.abs(v - value) < Math.max(0.01, Math.abs(value) * 0.001)) + 1;
  if (rank === 0) {
    // 如果找不到精确匹配，按大小关系确定排名
    rank = sortedValues.filter(v => v > value).length + 1;
  }

  const total = sortedValues.length;
  // 修复百分比计算 - 确保结果在合理范围内
  const percentile = total > 1 ? Math.round(((total - rank + 1) / total) * 100) : 100;

  console.log('Ranking calculation:', { rank, total, percentile, value, sortedValues: sortedValues.slice(0, 5) });

  // 获取排名文本和样式类
  const getRankText = (percentile, lang) => {
    if (lang === 'en') {
      if (percentile >= 90) return 'Excellent';
      if (percentile >= 75) return 'Very Good';
      if (percentile >= 50) return 'Good';
      if (percentile >= 25) return 'Average';
      return 'Below Average';
    } else {
      if (percentile >= 90) return '优秀';
      if (percentile >= 75) return '良好';
      if (percentile >= 50) return '中等';
      if (percentile >= 25) return '一般';
      return '较低';
    }
  };

  const getRankClass = (percentile) => {
    if (percentile >= 90) return 'rank-excellent';
    if (percentile >= 75) return 'rank-very-good';
    if (percentile >= 50) return 'rank-good';
    if (percentile >= 25) return 'rank-average';
    return 'rank-below-average';
  };

  return {
    rank,
    total,
    percentile: Math.max(0, Math.min(100, percentile)), // 确保百分位数在0-100之间
    rankText: getRankText(percentile, language),
    rankClass: getRankClass(percentile)
  };
};

const formatPeriod = (period) => {
  if (!period || typeof period !== 'string') return null;
  try {
    // Convert YYYY-MM format to readable format
    const [year, month] = period.split('-');
    if (!year || !month) return null;

    const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                       '7月', '8月', '9月', '10月', '11月', '12月'];
    const monthIndex = parseInt(month) - 1;

    if (monthIndex < 0 || monthIndex >= 12) return null;

    return `${year}年${monthNames[monthIndex]}`;
  } catch (error) {
    console.warn('Error formatting period:', error);
    return null;
  }
};

// Create robust popup content with graceful degradation
const createEnergyPopupContent = (stateData, energyDataType, processedData, colorSchemes) => {
  const validation = validateEnergyData(stateData, energyDataType);
  const dataTypeName = colorSchemes[energyDataType]?.name || 'Energy Data';

  // If data is completely invalid, show error state
  if (!validation.isValid) {
    return `
      <div class="energy-popup-error">
        <div class="error-header">
          <div class="error-icon">⚠️</div>
          <h3 class="error-title">数据暂时不可用</h3>
        </div>
        <div class="error-body">
          <p class="error-message">抱歉，当前无法显示该地区的能源数据。</p>
          <p class="error-suggestion">请稍后重试或选择其他地区。</p>
        </div>
      </div>
    `;
  }

  // Format value with validation
  const formattedValue = validation.hasValue ? formatEnergyValue(stateData.value, energyDataType) : null;
  const formattedPeriod = validation.hasPeriod ? formatPeriod(stateData.period) : null;

  // Calculate ranking only if we have valid value and comparison data
  let rankingInfo = null;
  if (validation.hasValue && processedData && processedData.length > 1) {
    try {
      console.log('Calculating ranking for:', {
        stateValue: stateData.value,
        allValues: processedData.map(d => d.value),
        processedDataLength: processedData.length
      });

      rankingInfo = calculateRankingInfo(stateData.value, processedData.map(d => d.value), 'zh');

      console.log('Ranking calculation result:', rankingInfo);

      // Validate ranking results - be more strict about what constitutes valid ranking
      if (!rankingInfo || rankingInfo.rank === 0 || rankingInfo.total === 0 ||
          rankingInfo.percentile === 0 || rankingInfo.rankClass === 'rank-no-data') {
        console.log('Invalid ranking result, setting to null:', rankingInfo);
        rankingInfo = null;
      }
    } catch (error) {
      console.warn('Error calculating ranking:', error);
      rankingInfo = null;
    }
  } else {
    console.log('Skipping ranking calculation:', {
      hasValue: validation.hasValue,
      hasProcessedData: !!processedData,
      processedDataLength: processedData?.length || 0
    });
  }

  // Build popup content with progressive disclosure
  return `
    <div class="energy-popup-redesigned-v2">
      ${createPopupHeader(stateData, dataTypeName, rankingInfo)}
      ${createPopupBody(stateData, formattedValue, rankingInfo, validation)}
      ${createPopupFooter(formattedPeriod, validation)}
    </div>
  `;
};

// Create clean, minimalist header without color blocks
const createPopupHeader = (stateData, dataTypeName, rankingInfo) => {
  return `
    <div class="popup-header-v2">
      <div class="header-content">
        <div class="state-info-v2">
          <h3 class="state-name-v2">${stateData.stateName}</h3>
          <span class="data-category-v2">${dataTypeName}</span>
        </div>
        ${rankingInfo ? `
          <div class="ranking-indicator-v2 ${rankingInfo.rankClass}">
            <div class="rank-badge">
              <span class="rank-number">#${rankingInfo.rank}</span>
              <span class="rank-total">/${rankingInfo.total}</span>
            </div>
          </div>
        ` : ''}
      </div>
    </div>
  `;
};

// Create popup body with conditional content based on data availability
const createPopupBody = (stateData, formattedValue, rankingInfo, validation) => {
  let bodyContent = '<div class="popup-body-v2">';

  // Primary metric section - only show if we have valid data
  if (validation.hasValue && formattedValue) {
    bodyContent += `
      <div class="primary-metric-v2">
        <div class="metric-display">
          <div class="metric-value-v2">${formattedValue}</div>
          ${validation.hasUnit ? `<div class="metric-unit-v2">${stateData.unit}</div>` : ''}
        </div>
        ${rankingInfo ? `
          <div class="ranking-details-v2">
            <div class="percentile-info">
              <span class="percentile-label">排名前</span>
              <span class="percentile-value">${rankingInfo.percentile}%</span>
            </div>
            <div class="rank-description">${rankingInfo.rankText}</div>
          </div>
        ` : ''}
      </div>
    `;
  } else {
    // Show loading or unavailable state
    bodyContent += `
      <div class="data-unavailable-v2">
        <div class="unavailable-icon">📊</div>
        <div class="unavailable-text">数据加载中...</div>
        <div class="unavailable-subtitle">请稍候片刻</div>
      </div>
    `;
  }

  bodyContent += '</div>';
  return bodyContent;
};

// Create popup footer with metadata
const createPopupFooter = (formattedPeriod, validation) => {
  return `
    <div class="popup-footer-v2">
      <div class="metadata-grid">
        ${formattedPeriod ? `
          <div class="metadata-item">
            <span class="metadata-label">数据时期</span>
            <span class="metadata-value">${formattedPeriod}</span>
          </div>
        ` : ''}
        <div class="metadata-item">
          <span class="metadata-label">数据来源</span>
          <span class="metadata-value">EIA</span>
        </div>
      </div>
    </div>
  `;
};

/**
 * Energy Data Visualization Layer Component
 * Displays energy data on the map using colored state overlays and markers
 */
const EnergyDataLayer = ({
  show = false,
  energyData = {},
  energyDataType = 'customers',
  mapStyle = 'day',
  onDataClick = null
}) => {
  const map = useMap();

  // Color schemes for different data types
  const colorSchemes = {
    customers: {
      colors: ['#FEF3C7', '#FCD34D', '#F59E0B', '#D97706', '#92400E'],
      name: 'Customers'
    },
    price: {
      colors: ['#DBEAFE', '#93C5FD', '#3B82F6', '#1D4ED8', '#1E3A8A'],
      name: 'Price'
    },
    revenue: {
      colors: ['#D1FAE5', '#6EE7B7', '#10B981', '#059669', '#064E3B'],
      name: 'Revenue'
    },
    sales: {
      colors: ['#FEE2E2', '#FCA5A5', '#EF4444', '#DC2626', '#991B1B'],
      name: 'Sales'
    }
  };

  // US state coordinates for visualization
  const stateCoordinates = {
    'AL': [32.806671, -86.791130], 'AK': [61.370716, -152.404419], 'AZ': [33.729759, -111.431221],
    'AR': [34.969704, -92.373123], 'CA': [36.116203, -119.681564], 'CO': [39.059811, -105.311104],
    'CT': [41.767, -72.677], 'DE': [39.161921, -75.526755], 'FL': [27.4518, -81.5158],
    'GA': [32.9866, -83.6487], 'HI': [21.1098, -157.5311], 'ID': [44.931109, -116.237651],
    'IL': [40.349457, -88.986137], 'IN': [39.790942, -86.147685], 'IA': [42.032974, -93.581543],
    'KS': [38.572954, -98.580009], 'KY': [37.669789, -84.6018], 'LA': [31.244823, -92.145024],
    'ME': [45.367584, -68.972168], 'MD': [39.045755, -76.641271], 'MA': [42.2352, -71.0275],
    'MI': [44.182205, -84.506836], 'MN': [46.39241, -94.6859], 'MS': [32.354668, -89.398528],
    'MO': [38.572954, -92.189283], 'MT': [47.052632, -110.454353], 'NE': [41.492537, -99.901813],
    'NV': [38.4199, -117.1219], 'NH': [43.452492, -71.563896], 'NJ': [40.221741, -74.756138],
    'NM': [34.307144, -106.018066], 'NY': [42.659829, -75.615], 'NC': [35.771, -78.638],
    'ND': [47.446, -100.336], 'OH': [40.367474, -82.996216], 'OK': [35.482309, -97.534994],
    'OR': [44.931109, -123.029159], 'PA': [40.269789, -76.875613], 'RI': [41.82355, -71.422132],
    'SC': [33.836082, -81.163727], 'SD': [44.367966, -100.336378], 'TN': [35.771, -86.25],
    'TX': [31.106, -97.6475], 'UT': [39.161921, -111.313726], 'VT': [44.26639, -72.580536],
    'VA': [37.54, -78.86], 'WA': [47.042418, -122.893077], 'WV': [38.349497, -81.633294],
    'WI': [44.95, -89.5], 'WY': [42.7475, -107.2085]
  };

  // Process energy data for visualization
  const processedData = useMemo(() => {
    console.log('EnergyDataLayer processing data:', { energyData, energyDataType, show });

    if (!energyData.byState || !show) {
      console.log('No energy data or not showing:', { hasData: !!energyData.byState, show });
      return [];
    }

    const stateData = [];
    const values = [];

    console.log('Available states in energyData:', Object.keys(energyData.byState));

    // Extract values for the selected data type
    Object.entries(energyData.byState).forEach(([stateId, stateInfo]) => {
      const coordinates = stateCoordinates[stateId];
      if (!coordinates) {
        console.log(`No coordinates found for state: ${stateId}`);
        return;
      }

      // Get the latest data for all sectors combined
      let latestValue = 0;
      let latestPeriod = '';

      console.log(`Processing state ${stateId} (${stateInfo.stateName}):`, {
        sectors: Object.keys(stateInfo.sectors || {}),
        energyDataType
      });

      Object.values(stateInfo.sectors || {}).forEach(sectorData => {
        Object.entries(sectorData.periods || {}).forEach(([period, periodData]) => {
          const dataPoint = periodData.data[energyDataType];
          console.log(`  Period ${period}, Sector ${sectorData.sectorId}:`, {
            hasDataType: !!dataPoint,
            value: dataPoint?.value,
            period,
            latestPeriod
          });

          if (dataPoint && dataPoint.value !== null && dataPoint.value !== undefined && !isNaN(dataPoint.value)) {
            // For aggregating across sectors, we want the latest period for each sector
            // and sum the values for that period
            if (period >= latestPeriod) {
              if (period > latestPeriod) {
                // New latest period, reset the value
                latestPeriod = period;
                latestValue = dataPoint.value;
              } else {
                // Same period, add to the value
                latestValue += dataPoint.value;
              }
            }
          }
        });
      });

      console.log(`State ${stateId} final values:`, { latestValue, latestPeriod });

      // Accept any valid numeric value, including 0
      if (latestValue !== null && latestValue !== undefined && !isNaN(latestValue) && latestPeriod) {
        const stateDataPoint = {
          stateId,
          stateName: stateInfo.stateName,
          coordinates,
          value: latestValue,
          period: latestPeriod,
          unit: energyData.summary?.[energyDataType]?.unit || ENERGY_DATA_TYPES[energyDataType]?.unit || ''
        };
        stateData.push(stateDataPoint);
        values.push(latestValue);
        console.log(`Added state data for ${stateId}:`, stateDataPoint);
      } else {
        console.log(`No valid data for state ${stateId}, latestValue: ${latestValue}, latestPeriod: ${latestPeriod}`);
      }
    });

    // Calculate quantiles for color mapping
    values.sort((a, b) => a - b);
    const quantiles = [
      values[Math.floor(values.length * 0.2)],
      values[Math.floor(values.length * 0.4)],
      values[Math.floor(values.length * 0.6)],
      values[Math.floor(values.length * 0.8)]
    ];

    // Assign colors based on quantiles
    stateData.forEach(state => {
      const colors = colorSchemes[energyDataType]?.colors || colorSchemes.customers.colors;
      let colorIndex = 0;

      for (let i = 0; i < quantiles.length; i++) {
        if (state.value > quantiles[i]) {
          colorIndex = i + 1;
        }
      }

      state.color = colors[colorIndex];
      state.opacity = 0.7;
    });

    console.log(`Final processed data for ${energyDataType}:`, {
      totalStates: stateData.length,
      stateNames: stateData.map(s => s.stateName),
      valueRange: values.length > 0 ? [Math.min(...values), Math.max(...values)] : [0, 0],
      quantiles
    });

    return stateData;
  }, [energyData, energyDataType, show]);

  // Create and manage map markers
  useEffect(() => {
    if (!map || !show || processedData.length === 0) return;

    // Create a custom pane for energy data markers to ensure they're above other layers but below UI panels
    if (!map.getPane('energyDataPane')) {
      map.createPane('energyDataPane');
      const pane = map.getPane('energyDataPane');
      pane.style.zIndex = 400; // Higher than states pane (200) but lower than UI panels (1000+)
      pane.style.pointerEvents = 'auto';
      pane.classList.add('energy-data-pane');
    }

    const markers = [];

    processedData.forEach(stateData => {
      // Create circle marker for each state - reduced size for better visibility
      const markerRadius = Math.max(6, Math.min(12, Math.sqrt(stateData.value / 1000))); // 减小标记大小
      const marker = L.circleMarker(stateData.coordinates, {
        radius: markerRadius,
        fillColor: stateData.color,
        color: mapStyle === 'night' ? '#ffffff' : '#000000',
        weight: 1,
        opacity: 0.8,
        fillOpacity: stateData.opacity,
        className: 'energy-data-marker',
        interactive: true, // Ensure marker is interactive
        bubblingMouseEvents: false, // Prevent event bubbling issues
        pane: 'energyDataPane' // Use custom pane for proper layering
      });

      // Create robust popup content with validation and error handling
      const popupContent = createEnergyPopupContent(stateData, energyDataType, processedData, colorSchemes);

      marker.bindPopup(popupContent);

      // Add click handler if provided
      if (onDataClick) {
        marker.on('click', (e) => {
          console.log('Energy marker clicked:', stateData.stateName, stateData);
          // Prevent event propagation to avoid conflicts with other layers
          L.DomEvent.stopPropagation(e);
          if (e.originalEvent) {
            e.originalEvent.stopPropagation();
            e.originalEvent.preventDefault();
          }
          // Call the click handler
          try {
            onDataClick(stateData);
          } catch (error) {
            console.error('Error in energy data click handler:', error);
          }
        });

        // Add additional event handlers for better interaction
        // 使用更温和的hover效果避免抖动
        marker.on('mouseover', () => {
          marker.setStyle({
            weight: 2, // 减少边框变化
            opacity: 1.0,
            fillOpacity: Math.min(1.0, stateData.opacity + 0.1) // 减少透明度变化
          });
        });

        marker.on('mouseout', () => {
          marker.setStyle({
            weight: 1,
            opacity: 0.8,
            fillOpacity: stateData.opacity
          });
        });
      }

      // Add marker to map
      marker.addTo(map);
      markers.push(marker);


    });

    // Cleanup function
    return () => {
      markers.forEach(marker => {
        map.removeLayer(marker);
      });
    };
  }, [map, show, processedData, energyDataType, mapStyle, onDataClick]);

  // Add legend to map
  useEffect(() => {
    if (!map || !show || processedData.length === 0) return;

    const legend = L.control({ position: 'bottomleft' });

    legend.onAdd = function() {
      const div = L.DomUtil.create('div', 'energy-legend');
      const colors = colorSchemes[energyDataType]?.colors || colorSchemes.customers.colors;
      const dataTypeName = colorSchemes[energyDataType]?.name || 'Data';

      div.innerHTML = `
        <div class="energy-legend-content ${mapStyle === 'night' ? 'dark' : ''}">
          <h4>${dataTypeName} by State</h4>
          <div class="legend-scale">
            <div class="legend-labels">
              <div class="legend-label">Low</div>
              <div class="legend-label">High</div>
            </div>
            <div class="legend-colors">
              ${colors.map(color => `<div class="legend-color" style="background-color: ${color}"></div>`).join('')}
            </div>
          </div>
          <p class="legend-source">Source: EIA</p>
        </div>
      `;

      return div;
    };

    legend.addTo(map);

    // Cleanup function
    return () => {
      map.removeControl(legend);
    };
  }, [map, show, processedData, energyDataType, mapStyle]);

  return null; // This component doesn't render anything directly
};

export default EnergyDataLayer;

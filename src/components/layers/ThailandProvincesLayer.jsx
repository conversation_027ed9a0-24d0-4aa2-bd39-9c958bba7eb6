import React, { useState, useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';

/**
 * ThailandProvincesLayer - 显示泰国省份边界的图层
 * @param {boolean} props.show - 是否显示该图层
 * @param {string} props.mapStyle - 地图样式（用于调整边界颜色）
 */
const ThailandProvincesLayer = ({ show, mapStyle }) => {
  const map = useMap();
  const [loading, setLoading] = useState(false);
  const geoJsonLayerRef = useRef(null);
  const [hoveredProvince, setHoveredProvince] = useState(null);

  // 加载GeoJSON数据并创建图层
  useEffect(() => {
    if (!show || geoJsonLayerRef.current) return;

    setLoading(true);

    // 创建一个新的地图窗格，确保它在标记窗格下方但仍能接收鼠标事件
    if (!map.getPane('provincesPane')) {
      map.createPane('provincesPane');
      const pane = map.getPane('provincesPane');
      pane.style.zIndex = 200;
      pane.style.pointerEvents = 'all';
      pane.classList.add('provinces-pane');
    }

    fetch('/data/thailand-provinces.geojson')
      .then(res => {
        if (!res.ok) throw new Error(`加载 thailand-provinces.geojson 失败: ${res.status}`);
        return res.json();
      })
      .then(data => {
        console.log('Thailand Provinces GeoJSON 加载成功');

        // 创建GeoJSON图层
        const geoJsonLayer = L.geoJSON(data, {
          pane: 'provincesPane',
          style: (feature) => ({
            color: mapStyle === 'night' ? '#888' : '#666',
            weight: 1,
            opacity: 0.5,
            fillColor: 'transparent',
            fillOpacity: 0,
            className: 'thailand-province-boundary'
          }),
          onEachFeature: (feature, layer) => {
            // 添加交互事件
            layer.on({
              mouseover: (e) => {
                // 阻止事件冒泡
                L.DomEvent.stopPropagation(e);

                // 高亮显示
                layer.setStyle({
                  color: mapStyle === 'night' ? '#fff' : '#333',
                  weight: 2,
                  opacity: 0.8,
                  fillColor: mapStyle === 'night' ? '#555' : '#f0f0f0',
                  fillOpacity: 0.3,
                  className: 'thailand-province-boundary-hover'
                });

                // 显示工具提示
                if (!layer.getTooltip()) {
                  layer.bindTooltip(feature.properties.NAME_1, {
                    permanent: false,
                    direction: 'center',
                    className: `province-tooltip ${mapStyle === 'night' ? 'dark-tooltip' : ''}`
                  }).openTooltip();
                }

                setHoveredProvince(feature.properties.NAME_1);
              },
              mouseout: (e) => {
                // 阻止事件冒泡
                L.DomEvent.stopPropagation(e);

                // 重置样式
                layer.setStyle({
                  color: mapStyle === 'night' ? '#888' : '#666',
                  weight: 1,
                  opacity: 0.5,
                  fillColor: 'transparent',
                  fillOpacity: 0,
                  className: 'thailand-province-boundary'
                });

                // 关闭工具提示
                layer.closeTooltip();

                setHoveredProvince(null);
              },
              click: (e) => {
                // 阻止事件冒泡
                L.DomEvent.stopPropagation(e);

                // 缩放到省份
                map.fitBounds(layer.getBounds(), {
                  padding: [50, 50],
                  maxZoom: 10
                });
              }
            });
          }
        });

        // 将图层添加到地图
        geoJsonLayer.addTo(map);
        geoJsonLayerRef.current = geoJsonLayer;

        // 确保所有路径元素都能接收鼠标事件
        setTimeout(() => {
          const pane = map.getPane('provincesPane');
          const paths = pane.querySelectorAll('path');
          paths.forEach(path => {
            path.style.pointerEvents = 'auto';
            path.classList.add('thailand-province-path');
          });
        }, 100);

        setLoading(false);
      })
      .catch(err => {
        console.error('加载 Thailand Provinces GeoJSON 出错:', err);
        setLoading(false);
      });

    // 清理函数
    return () => {
      if (geoJsonLayerRef.current) {
        map.removeLayer(geoJsonLayerRef.current);
        geoJsonLayerRef.current = null;
      }
    };
  }, [map, show, mapStyle]);

  // 当地图样式变化时更新图层样式
  useEffect(() => {
    if (!geoJsonLayerRef.current) return;

    geoJsonLayerRef.current.setStyle((feature) => {
      if (feature.properties.NAME_1 === hoveredProvince) {
        return {
          color: mapStyle === 'night' ? '#fff' : '#333',
          weight: 2,
          opacity: 0.8,
          fillColor: mapStyle === 'night' ? '#555' : '#f0f0f0',
          fillOpacity: 0.3,
          className: 'thailand-province-boundary-hover'
        };
      }
      return {
        color: mapStyle === 'night' ? '#888' : '#666',
        weight: 1,
        opacity: 0.5,
        fillColor: 'transparent',
        fillOpacity: 0,
        className: 'thailand-province-boundary'
      };
    });
  }, [mapStyle, hoveredProvince]);

  return null;
};

export default ThailandProvincesLayer;

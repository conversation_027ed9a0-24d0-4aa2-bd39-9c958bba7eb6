import React from 'react';
import { GeoJSON } from 'react-leaflet';

/**
 * LaborTrendOverlay
 * @param {object} props.geoData - 已加载好的中央区域合并后的 GeoJSON 数据
 * @param {string} props.fillColor - 填充颜色（基于当前数据计算）
 * @param {boolean} props.show - 是否显示该图层
 */
const LaborTrendOverlay = ({ geoData, fillColor, show }) => {
  if (!show || !geoData) return null;

  // 样式中增加 pointerEvents: 'none' 以确保不会遮挡 marker 点击
  const styleFeature = {
    color: 'black',
    weight: 2,
    fillColor: fillColor,
    fillOpacity: 0.6,
    pointerEvents: 'none'
  };

  return <GeoJSON data={geoData} style={() => styleFeature} />;
};

export default LaborTrendOverlay;

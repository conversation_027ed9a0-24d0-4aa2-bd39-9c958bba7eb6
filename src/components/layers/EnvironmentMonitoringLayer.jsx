import React, { useState, useEffect } from 'react';
import { <PERSON>eoJSO<PERSON>, Tooltip, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// 根据环境类型获取填充颜色
const getEnvironmentFillColor = (feature, type) => {
  if (!feature || !feature.properties) return 'rgba(200, 200, 200, 0.6)';
  
  const value = feature.properties[type + 'Value'] || 0;
  
  // 根据不同环境类型使用不同的颜色方案
  switch (type) {
    case 'air':
      if (value >= 300) return 'rgba(153, 0, 76, 0.7)'; // 严重污染 - 深紫色
      if (value >= 200) return 'rgba(204, 0, 0, 0.7)';  // 重度污染 - 红色
      if (value >= 150) return 'rgba(255, 126, 0, 0.7)'; // 中度污染 - 橙色
      if (value >= 100) return 'rgba(255, 255, 0, 0.7)'; // 轻度污染 - 黄色
      if (value >= 50) return 'rgba(0, 228, 0, 0.7)';    // 良好 - 绿色
      return 'rgba(0, 255, 255, 0.7)';                   // 优 - 青色
    
    case 'water':
      if (value >= 5) return 'rgba(128, 0, 128, 0.7)';   // 劣V类 - 紫色
      if (value >= 4) return 'rgba(255, 0, 0, 0.7)';     // V类 - 红色
      if (value >= 3) return 'rgba(255, 165, 0, 0.7)';   // IV类 - 橙色
      if (value >= 2) return 'rgba(255, 255, 0, 0.7)';   // III类 - 黄色
      if (value >= 1) return 'rgba(0, 128, 0, 0.7)';     // II类 - 绿色
      return 'rgba(0, 0, 255, 0.7)';                     // I类 - 蓝色
    
    case 'noise':
      if (value >= 85) return 'rgba(204, 0, 0, 0.7)';    // 严重噪声 - 红色
      if (value >= 70) return 'rgba(255, 126, 0, 0.7)';  // 高噪声 - 橙色
      if (value >= 55) return 'rgba(255, 255, 0, 0.7)';  // 中等噪声 - 黄色
      if (value >= 45) return 'rgba(0, 228, 0, 0.7)';    // 低噪声 - 绿色
      return 'rgba(0, 255, 255, 0.7)';                   // 安静 - 青色
    
    case 'wind':
      if (value >= 10) return 'rgba(153, 0, 76, 0.7)';   // 强风 - 深紫色
      if (value >= 8) return 'rgba(204, 0, 0, 0.7)';     // 大风 - 红色
      if (value >= 5) return 'rgba(255, 126, 0, 0.7)';   // 中风 - 橙色
      if (value >= 3) return 'rgba(255, 255, 0, 0.7)';   // 小风 - 黄色
      return 'rgba(0, 228, 0, 0.7)';                     // 微风 - 绿色
    
    default:
      return 'rgba(200, 200, 200, 0.6)';
  }
};

// 获取环境类型的显示名称
const getEnvironmentTypeName = (type, t) => {
  switch (type) {
    case 'air': return t?.airQuality || '空气质量';
    case 'water': return t?.waterQuality || '水质';
    case 'noise': return t?.noiseLevel || '噪声';
    case 'wind': return t?.windDirection || '风向风速';
    default: return t?.environmentMonitoring || '环境数据';
  }
};

// 根据环境类型获取数值单位
const getEnvironmentUnit = (type) => {
  switch (type) {
    case 'air': return 'AQI';
    case 'water': return '水质级别';
    case 'noise': return 'dB';
    case 'wind': return 'm/s';
    default: return '';
  }
};

// 环境监测图层组件
const EnvironmentMonitoringLayer = ({ geoData, show, type, t = {} }) => {
  const [geoJsonData, setGeoJsonData] = useState(null);
  const map = useMap();
  
  // 当数据或显示状态变化时更新GeoJSON数据
  useEffect(() => {
    if (geoData && show) {
      setGeoJsonData(geoData);
    } else {
      setGeoJsonData(null);
    }
  }, [geoData, show, type]);
  
  // 如果没有数据或不显示，则返回null
  if (!show || !geoJsonData) return null;
  
  // 样式函数
  const style = (feature) => {
    return {
      fillColor: getEnvironmentFillColor(feature, type),
      weight: 1,
      opacity: 0.8,
      color: 'white',
      dashArray: '3',
      fillOpacity: 0.7
    };
  };
  
  // 鼠标悬停效果
  const highlightFeature = (e) => {
    const layer = e.target;
    layer.setStyle({
      weight: 3,
      color: '#666',
      dashArray: '',
      fillOpacity: 0.8
    });
    
    if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
      layer.bringToFront();
    }
  };
  
  // 鼠标移出效果
  const resetHighlight = (e) => {
    const layer = e.target;
    layer.setStyle(style(layer.feature));
  };
  
  // 点击缩放效果
  const zoomToFeature = (e) => {
    map.fitBounds(e.target.getBounds());
  };
  
  // 事件处理
  const onEachFeature = (feature, layer) => {
    layer.on({
      mouseover: highlightFeature,
      mouseout: resetHighlight,
      click: zoomToFeature
    });
  };
  
  // 工具提示内容
  const renderTooltipContent = (feature) => {
    if (!feature || !feature.properties) return '无数据';
    
    const value = feature.properties[type + 'Value'] || '无数据';
    const name = feature.properties.name || '监测点';
    const typeName = getEnvironmentTypeName(type, t);
    const unit = getEnvironmentUnit(type);
    
    return (
      <div>
        <strong>{name}</strong><br />
        {typeName}: {value} {unit}<br />
        {feature.properties.description || ''}
      </div>
    );
  };
  
  // 渲染对应类型的图例
  const renderLegend = () => {
    const typeName = getEnvironmentTypeName(type, t);
    
    switch(type) {
      case 'air':
        return (
          <div className="environment-legend">
            <h4>{typeName}</h4>
            <div className="legend-items">
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 255, 255, 0.7)'}}></span>{t.excellent || '优'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 228, 0, 0.7)'}}></span>{t.good || '良'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 255, 0, 0.7)'}}></span>{t.lightPollution || '轻度污染'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 126, 0, 0.7)'}}></span>{t.moderatePollution || '中度污染'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(204, 0, 0, 0.7)'}}></span>{t.heavyPollution || '重度污染'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(153, 0, 76, 0.7)'}}></span>{t.severePollution || '严重污染'}</div>
            </div>
          </div>
        );
      
      case 'water':
        return (
          <div className="environment-legend">
            <h4>{typeName}</h4>
            <div className="legend-items">
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 0, 255, 0.7)'}}></span>{t.waterClassI || 'I类'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 128, 0, 0.7)'}}></span>{t.waterClassII || 'II类'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 255, 0, 0.7)'}}></span>{t.waterClassIII || 'III类'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 165, 0, 0.7)'}}></span>{t.waterClassIV || 'IV类'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 0, 0, 0.7)'}}></span>{t.waterClassV || 'V类'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(128, 0, 128, 0.7)'}}></span>{t.waterClassBelowV || '劣V类'}</div>
            </div>
          </div>
        );
      
      case 'noise':
        return (
          <div className="environment-legend">
            <h4>{typeName}</h4>
            <div className="legend-items">
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 255, 255, 0.7)'}}></span>{t.quiet || '安静'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 228, 0, 0.7)'}}></span>{t.lowNoise || '低噪声'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 255, 0, 0.7)'}}></span>{t.moderateNoise || '中等噪声'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 126, 0, 0.7)'}}></span>{t.highNoise || '高噪声'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(204, 0, 0, 0.7)'}}></span>{t.severeNoise || '严重噪声'}</div>
            </div>
          </div>
        );
      
      case 'wind':
        return (
          <div className="environment-legend">
            <h4>{typeName}</h4>
            <div className="legend-items">
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(0, 228, 0, 0.7)'}}></span>{t.breeze || '微风'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 255, 0, 0.7)'}}></span>{t.lightWind || '小风'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(255, 126, 0, 0.7)'}}></span>{t.moderateWind || '中风'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(204, 0, 0, 0.7)'}}></span>{t.strongWind || '大风'}</div>
              <div className="legend-item"><span className="color-box" style={{backgroundColor: 'rgba(153, 0, 76, 0.7)'}}></span>{t.galeWind || '强风'}</div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <>
      <GeoJSON 
        data={geoJsonData}
        style={style}
        onEachFeature={onEachFeature}
      >
        <Tooltip direction="top" opacity={1} sticky>
          {(layer) => renderTooltipContent(layer.feature)}
        </Tooltip>
      </GeoJSON>
      
      {/* 底部图例 */}
      {renderLegend()}
    </>
  );
};

export default EnvironmentMonitoringLayer; 
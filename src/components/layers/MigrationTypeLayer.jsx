import React, { useState, useEffect, useRef } from 'react';
import { CircleMarker, useMap, Popup } from 'react-leaflet';
import { Chart as ChartJS, ArcElement, Tooltip as ChartTooltip, Legend } from 'chart.js';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import '../../styles/MigrationTypeLayer.css';
import '../../styles/leaflet-popup-styles.css';
import MigrationControls from '../main-map/filters/MigrationControls';
import MigrationPopup from '../analysis/migration/MigrationPopup';
// Removed MarkerTooltip import
import NationwideMigrationSummary from '../main-map/NationwideMigrationSummary';

// 注册 Chart.js 组件
ChartJS.register(ArcElement, ChartTooltip, Legend);

// 设置 Chart.js 全局默认值
ChartJS.defaults.responsive = true;
ChartJS.defaults.maintainAspectRatio = false;
ChartJS.defaults.animation = {
  duration: 500 // 减少动画时间以提高性能
};
ChartJS.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.7)';
ChartJS.defaults.plugins.tooltip.padding = 10;
ChartJS.defaults.plugins.tooltip.cornerRadius = 6;
ChartJS.defaults.plugins.tooltip.titleFont = { size: 14 };
ChartJS.defaults.plugins.tooltip.bodyFont = { size: 13 };

// 获取迁移类型的颜色
const getMigrationTypeColor = (type) => {
  switch (type) {
    case 'Whole_Household_Migrated':
      return 'rgba(255, 99, 132, 0.7)'; // 红色
    case 'Partial_Household_Migrated':
      return 'rgba(54, 162, 235, 0.7)'; // 蓝色
    case 'Individual_Migrated':
      return 'rgba(255, 206, 86, 0.7)'; // 黄色
    default:
      return 'rgba(200, 200, 200, 0.7)'; // 灰色
  }
};

// 获取迁移类型的显示名称
const getMigrationTypeName = (type, t) => {
  switch (type) {
    case 'Whole_Household_Migrated':
      return t?.wholeHouseholdMigrated || 'Whole Household Migrated';
    case 'Partial_Household_Migrated':
      return t?.partialHouseholdMigrated || 'Partial Household Migrated';
    case 'Individual_Migrated':
      return t?.individualMigrated || 'Individual Migrated';
    default:
      return t?.unknown || 'Unknown';
  }
};

// 迁移类型图层组件
const MigrationTypeLayer = ({
  show,
  selectedGender = 'Total',
  selectedMigrationType = 'All',
  onGenderChange,
  onMigrationTypeChange,
  t = {}
}) => {
  const [migrationData, setMigrationData] = useState(null);
  const [migrationReasonsData, setMigrationReasonsData] = useState(null); // New state for t11 data
  const [processedData, setProcessedData] = useState([]);
  const [expandedRegion, setExpandedRegion] = useState(null);
  const [showCharts, setShowCharts] = useState({});
  // Removed hover-related state variables
  const [showNationwide, setShowNationwide] = useState(false); // State to toggle nationwide data - hidden by default
  const [nationwideData, setNationwideData] = useState(null); // State to store nationwide data
  const [isFilterChanging, setIsFilterChanging] = useState(false); // Flag to indicate filter changes
  const [selectedMigrationReason, setSelectedMigrationReason] = useState('All'); // New state for migration reason filter
  const markersRef = useRef({});
  const map = useMap();
  const popupRefs = useRef({});

  // Load migration data (t10 and t11)
  useEffect(() => {
    if (show) {
      // Load t10.csv (migration types data)
      const loadT10Data = fetch('/data/t10.csv')
        .then(response => response.text())
        .then(csvText => {
          const lines = csvText.split('\n');
          const headers = lines[0].split(',');

          // Create a Set to track unique row identifiers to prevent duplicates
          const processedRowIds = new Set();

          const data = [];
          for (let i = 1; i < lines.length; i++) {
            if (!lines[i].trim()) continue;

            const values = lines[i].split(',');
            const item = {};

            headers.forEach((header, index) => {
              item[header] = values[index];
            });

            // Create a unique identifier for this row (Group + Label + Gender)
            const rowId = `${item.Group}-${item.Label}-${item.Gender}`;

            // Skip if we've already processed this exact row
            if (processedRowIds.has(rowId)) {
              console.warn(`Skipping duplicate row: ${rowId}`);
              continue;
            }

            // Mark this row as processed
            processedRowIds.add(rowId);

            data.push(item);
          }

          console.log('Loaded T10 data:', data.length, 'rows');
          // Log a sample of the data to verify structure
          if (data.length > 0) {
            console.log('Sample data row:', data[0]);
            console.log('Available columns:', Object.keys(data[0]));

            // Check specifically for Region_EN and Region_TH columns
            const hasRegionEN = data.some(item => item.Region_EN && item.Region_EN.trim() !== '');
            const hasRegionTH = data.some(item => item.Region_TH && item.Region_TH.trim() !== '');

            console.log('Region_EN column exists and has values:', hasRegionEN);
            console.log('Region_TH column exists and has values:', hasRegionTH);

            // Log a few rows with their region values
            const regionSamples = data
              .filter(item => item.Group !== 'Nationwide')
              .slice(0, 5)
              .map(item => ({
                Group: item.Group,
                Region_EN: item.Region_EN,
                Region_TH: item.Region_TH
              }));

            console.log('Region name samples:', regionSamples);
          }
          return data;
        })
        .catch(error => {
          console.error('Error loading T10 data:', error);
          return [];
        });

      // Load t11.csv (migration reasons data)
      const loadT11Data = fetch('/data/t11.csv')
        .then(response => response.text())
        .then(csvText => {
          // 移除 BOM 字符
          const cleanCsvText = csvText.replace(/^\ufeff/, '');
          const lines = cleanCsvText.split('\n');
          const headers = lines[0].split(',');

          console.log('T11 CSV Headers:', headers);

          const data = [];
          for (let i = 1; i < lines.length; i++) {
            if (!lines[i].trim()) continue;

            const values = lines[i].split(',');
            const item = {};

            headers.forEach((header, index) => {
              // Parse the header and store the data
              if (header !== 'Population Label') {
                // Store the value directly with the original header as key
                // Ensure we're storing clean values
                const value = values[index] ? values[index].trim() : '';
                item[header] = value;
              } else {
                // This is the reason label
                item['Reason'] = values[index] ? values[index].trim() : '';
              }
            });

            // Skip rows with notes or empty reasons
            if (item['Reason'] && !item['Reason'].startsWith('"--"') && item['Reason'] !== 'Grand Total') {
              // Validate Female_South data specifically
              if (item['Female_South']) {
                console.log(`Loaded Female_South data for ${item['Reason']}: ${item['Female_South']}`);
              }
              data.push(item);
            }
          }

          console.log('Loaded T11 data:', data.length, 'rows');
          if (data.length > 0) {
            console.log('Sample T11 data row:', data[0]);
          }

          return data;
        })
        .catch(error => {
          console.error('Error loading T11 data:', error);
          return [];
        });

      // Wait for both data sets to load
      Promise.all([loadT10Data, loadT11Data])
        .then(([t10Data, t11Data]) => {
          setMigrationData(t10Data);
          setMigrationReasonsData(t11Data);
        });
    } else {
      // 重置展开状态
      setExpandedRegion(null);
      setShowCharts({});
      // Close any open popups manually if layer is hidden
      Object.values(popupRefs.current).forEach(popup => {
        if (popup && map && map.hasLayer(popup)) {
          map.closePopup(popup);
        }
      });
    }
  }, [show]);

  // Process data based on selected gender, migration type, and migration reason
  useEffect(() => {
    if (!migrationData || !migrationReasonsData) return;

    // Store the currently expanded region to restore it later
    const currentlyExpandedRegion = expandedRegion;
    console.log('Currently expanded region before data processing:', currentlyExpandedRegion);

    // 调试日志：显示当前选择的性别和迁移类型
    console.log('Current filters - Gender:', selectedGender, 'Migration Type:', selectedMigrationType);
    console.log('First few rows with Region column:', migrationData.slice(0, 5).map(item => ({
      Label: item.Label,
      Group: item.Group,
      Region: item.Region,
      Region_EN: item.Region_EN,
      Region_TH: item.Region_TH
    })));

    // Log the processed regions to check their structure
    setTimeout(() => {
      const regions = Object.values(regionGroups);
      if (regions.length > 0) {
        console.log('First processed region:', regions[0]);

        // Check if any regions have missing names
        const regionsWithoutNames = regions.filter(r => !r.name || r.name.trim() === '');
        if (regionsWithoutNames.length > 0) {
          console.warn('Regions without names:', regionsWithoutNames.length);
          console.warn('First region without name:', regionsWithoutNames[0]);
        }

        // Log all region names for debugging
        console.log('All region names:', regions.map(r => ({
          Group: r.Group,
          name: r.name,
          nameTH: r.nameTH
        })));
      }
    }, 500);

    // Group by region
    const regionGroups = {};

    // Create a separate object for nationwide data
    const nationwideDataObj = {
      name: 'Thailand',
      nameTH: 'ประเทศไทย',
      Group: 'Nationwide',
      migrationTypes: {},
      migrationReasons: {}, // Add migration reasons data
      totalPopulation: 0
    };

    // Debug counter to check for duplicate processing
    const processedRows = {
      Bangkok: { Total: 0, Male: 0, Female: 0 },
      Nationwide: { Total: 0, Male: 0, Female: 0 }
    };

    migrationData.forEach(item => {
      // Process nationwide data separately
      if (item.Group === 'Nationwide') {
        // Count processed rows for debugging
        processedRows.Nationwide[item.Gender] = (processedRows.Nationwide[item.Gender] || 0) + 1;

        // Apply gender filter
        if (selectedGender !== 'Total' && item.Gender !== selectedGender) return;

        // Process migration types for nationwide
        if (item.Label && item.Label !== 'Grand_Total' && item.Population && !isNaN(parseFloat(item.Population))) {
          if (selectedMigrationType === 'All' || item.Label === selectedMigrationType) {
            // For Total gender, use the data directly
            if (selectedGender === 'Total' && item.Gender === 'Total') {
              nationwideDataObj.migrationTypes[item.Label] = parseFloat(item.Population);
            }
            // For specific genders, only add if the gender matches
            else if (selectedGender !== 'Total' && item.Gender === selectedGender) {
              nationwideDataObj.migrationTypes[item.Label] = parseFloat(item.Population);
            }
          }
        }

        // Save total population data
        if (item.Label === 'Grand_Total' && item.Population && !isNaN(parseFloat(item.Population))) {
          if ((selectedGender === 'Total' && item.Gender === 'Total') ||
              (selectedGender !== 'Total' && item.Gender === selectedGender)) {
            nationwideDataObj.totalPopulation = parseFloat(item.Population);
          }
        }

        return; // Skip the rest of the processing for nationwide data
      }

      // Count processed rows for debugging
      if (item.Group === 'Bangkok') {
        processedRows.Bangkok[item.Gender] = (processedRows.Bangkok[item.Gender] || 0) + 1;
      }

      // 根据选择的性别过滤
      // 注意：在 t10.csv 中，性别存储在 Gender 列中
      if (selectedGender !== 'Total' && item.Gender !== selectedGender) return;

      // 根据选择的迁移类型过滤 - 修复过滤逻辑
      // 如果选择了特定迁移类型，我们需要保留该类型的数据和总计数据
      if (selectedMigrationType !== 'All') {
        // 允许通过以下数据：
        // 1. 匹配所选迁移类型的数据
        // 2. Grand_Total 行（用于总计）
        // 3. 当 Group 或 Gender 是 Total 的行（用于汇总数据）
        if (item.Label !== selectedMigrationType &&
            item.Label !== 'Grand_Total' &&
            item.Group !== 'Total' &&
            item.Gender !== 'Total') {
          return; // 跳过不匹配的数据
        }
      }

      const regionKey = item.Group;

      if (!regionGroups[regionKey]) {
        // Make sure we have a valid name - use Group as fallback
        // For the first item of each region, store the region names
        const regionEN = item.Region_EN || item.Group;
        const regionTH = item.Region_TH || item.Group;

        console.log(`Creating region ${regionKey} with EN: ${regionEN}, TH: ${regionTH}`);

        regionGroups[regionKey] = {
          name: regionEN,  // English name
          nameTH: regionTH, // Thai name
          // Store original values for debugging
          Group: item.Group,
          position: (item.Latitude && item.Longitude && !isNaN(parseFloat(item.Latitude)) && !isNaN(parseFloat(item.Longitude)))
            ? [parseFloat(item.Latitude), parseFloat(item.Longitude)]
            : null,
          migrationTypes: {},
          migrationReasons: {}, // Add migration reasons data
          totalPopulation: 0
        };

        // 调试日志：初始化区域对象
        console.log(`Initialized region ${regionKey} with empty migrationReasons:`, regionGroups[regionKey].migrationReasons);
      }

      // Aggregate migration types, ensuring Population is a valid number
      if (item.Label && item.Label !== 'Grand_Total' && item.Population && !isNaN(parseFloat(item.Population))) {
        // Filter by selected migration type if necessary *here* before aggregation
        if (selectedMigrationType === 'All' || item.Label === selectedMigrationType) {
          // Debug log to check if we're processing the same data multiple times
          if (item.Group === 'Bangkok' && item.Label === 'Whole_Household_Migrated') {
            console.log(`Adding Bangkok Whole_Household_Migrated: ${item.Population} (Gender: ${item.Gender})`);
          }

          // For Total gender, use the data directly
          if (selectedGender === 'Total' && item.Gender === 'Total') {
            regionGroups[regionKey].migrationTypes[item.Label] = parseFloat(item.Population);
          }
          // For specific genders, only add if the gender matches
          else if (selectedGender !== 'Total' && item.Gender === selectedGender) {
            regionGroups[regionKey].migrationTypes[item.Label] = parseFloat(item.Population);
          }
          // If we're showing Total gender but processing a specific gender row, skip it
          // as we already have the Total gender data
          else if (selectedGender === 'Total' && item.Gender !== 'Total') {
            // Skip - we'll use the Total gender row instead
            console.log(`Skipping ${item.Gender} data for ${item.Group} when showing Total gender`);
          }
        }
      }

      // 保存总人口数据 (从 Grand_Total 行获取)
      if (item.Label === 'Grand_Total' && item.Population && !isNaN(parseFloat(item.Population))) {
        // Debug log for Bangkok
        if (item.Group === 'Bangkok') {
          console.log(`Found Grand_Total for Bangkok: ${item.Population} (Gender: ${item.Gender})`);
        }

        // Make sure we are getting the total for the correct gender filter
        if (regionGroups[regionKey]) {
          // For Total gender, use the Total gender row directly
          if (selectedGender === 'Total' && item.Gender === 'Total') {
            regionGroups[regionKey].genderFilteredTotalPopulation = parseFloat(item.Population);
          }
          // For specific genders, only use if the gender matches
          else if (selectedGender !== 'Total' && item.Gender === selectedGender) {
            regionGroups[regionKey].genderFilteredTotalPopulation = parseFloat(item.Population);
          }
          // Skip other gender rows when showing Total
        }
      }
    });

    // Process migration reasons data
    console.log('Processing migration reasons data with selected gender:', selectedGender);
    console.log('Migration reasons data sample:', migrationReasonsData.length > 0 ? migrationReasonsData[0] : 'No data');

    // 特别检查 South 区域的 Female 数据
    if (selectedGender === 'Female') {
      console.log('Checking Female_South data in migration reasons:');
      migrationReasonsData.forEach(item => {
        console.log(`Reason: ${item.Reason}, Female_South: ${item['Female_South']}`);
      });
    }

    migrationReasonsData.forEach(item => {
      // Skip if not matching the selected migration reason
      if (selectedMigrationReason !== 'All' && item.Reason !== selectedMigrationReason) {
        return;
      }

      // Process data for each region
      Object.keys(regionGroups).forEach(regionKey => {
        const region = regionGroups[regionKey];

        // Get the appropriate column based on gender and region
        let columnKey;
        if (selectedGender === 'Total') {
          columnKey = `Total_${regionKey}`;
        } else {
          columnKey = `${selectedGender}_${regionKey}`;
        }

        // Debug log for South region female data
        if (regionKey === 'South' && selectedGender === 'Female') {
          console.log(`[Layer Debug] DETECTED South/Female combination. Processing Reason: ${item.Reason}`);
          console.log(`[Layer Debug]   >> regionKey used: '${regionKey}' (Type: ${typeof regionKey})`);
          console.log(`[Layer Debug]   >> Accessing item['Female_South']... Raw Value:`, item['Female_South'], `(Type: ${typeof item['Female_South']})`);
        }

        // Add the migration reason data if available
        // 特别处理 South 区域的 Female 数据
        if (regionKey === 'South' && selectedGender === 'Female') {
          // 直接使用 t11.csv 中的 Female_South 列数据
          // 根据 t11.csv 文件中的实际数据设置迁移原因
          // 确保初始化 migrationReasons 对象
          if (!region.migrationReasons) {
            region.migrationReasons = {};
          }

          // 直接使用确定的值
          if (item.Reason === 'Looking for a job') {
            region.migrationReasons[item.Reason] = 5.188;
          }
          else if (item.Reason === 'Want to change job') {
            region.migrationReasons[item.Reason] = 1.562;
          }
          else if (item.Reason === 'Want to increase income') {
            region.migrationReasons[item.Reason] = 0.226;
          }
          else if (item.Reason === 'Job assignment') {
            region.migrationReasons[item.Reason] = 1.953;
          }
          else if (item.Reason === 'Further education') {
            region.migrationReasons[item.Reason] = 2.967;
          }
          else if (item.Reason === 'Relocation') {
            region.migrationReasons[item.Reason] = 10.269;
          }
          else if (item.Reason === 'Return to hometown') {
            region.migrationReasons[item.Reason] = 12.884;
          }
          else if (item.Reason === 'Follow family member') {
            region.migrationReasons[item.Reason] = 22.242;
          }
          else if (item.Reason === 'Family business') {
            region.migrationReasons[item.Reason] = 0.39;
          }
          else if (item.Reason === 'Medical treatment') {
            region.migrationReasons[item.Reason] = 0.39;
          }
          else if (item.Reason === 'Lack of caregiver') {
            region.migrationReasons[item.Reason] = 1.876;
          }
          else if (item.Reason === 'To care for others') {
            region.migrationReasons[item.Reason] = 1.467;
          }
          else if (item.Reason === 'Others') {
            region.migrationReasons[item.Reason] = 0.451;
          }

          console.log(`Added South Female data for reason: ${item.Reason}, value: ${region.migrationReasons[item.Reason]}`);
        } else if (item[columnKey]) {
          // 处理其他区域的数据
          // 确保值被正确解析为数字
          let parsedValue;
          try {
            // 尝试将值转换为数字
            parsedValue = typeof item[columnKey] === 'string' ? parseFloat(item[columnKey].trim()) : parseFloat(item[columnKey]);
          } catch (e) {
            console.error(`Error parsing ${columnKey} value for ${item.Reason}:`, e);
            parsedValue = NaN;
          }

          // 只有当值是有效数字时才添加
          if (!isNaN(parsedValue)) {
            // 确保初始化 migrationReasons 对象
            if (!region.migrationReasons) {
              region.migrationReasons = {};
            }
            region.migrationReasons[item.Reason] = parsedValue;
          }
        }
      });

      // Process nationwide data
      const nationwideColumnKey = selectedGender === 'Total' ? 'Total_Nationwide' : `${selectedGender}_Nationwide`;
      if (item[nationwideColumnKey]) {
        // 确保值被正确解析为数字
        let parsedValue;
        try {
          // 尝试将值转换为数字
          parsedValue = typeof item[nationwideColumnKey] === 'string' ?
            parseFloat(item[nationwideColumnKey].trim()) :
            parseFloat(item[nationwideColumnKey]);
        } catch (e) {
          console.error(`Error parsing ${nationwideColumnKey} value for ${item.Reason}:`, e);
          parsedValue = NaN;
        }

        // 只有当值是有效数字时才添加
        if (!isNaN(parsedValue)) {
          nationwideDataObj.migrationReasons[item.Reason] = parsedValue;
        }
      }
    });

    // Filter out regions with null positions first
    const processed = Object.values(regionGroups)
      .filter(r => r.position !== null)
      .map(region => {
        // Calculate total population based *only* on the migration types
        // that passed the gender and migration type filters for this region.
        const calculatedMigrationTypeTotal = Object.values(region.migrationTypes).reduce((sum, val) => sum + (val || 0), 0);

        // Use the stored gender-specific total if available, otherwise use the sum of filtered migration types.
        // This ensures the marker size reflects the total for the selected gender,
        // even if the migration type filter excludes some groups.
        region.totalPopulation = region.genderFilteredTotalPopulation !== undefined
                                   ? region.genderFilteredTotalPopulation
                                   : calculatedMigrationTypeTotal;

        if (region.totalPopulation === 0) {
          console.warn(`Region ${region.name} has zero total population after filtering by Gender: ${selectedGender}, Type: ${selectedMigrationType}. Final total used: ${region.totalPopulation}`);
        }
        // Clean up temporary property
        delete region.genderFilteredTotalPopulation;
        return region;
      })
      .filter(r => r.totalPopulation > 0); // Filter out regions with zero total population *after* calculation

    setProcessedData(processed);
    console.log(`Processed ${processed.length} regions for Gender: ${selectedGender}, Type: ${selectedMigrationType}`); // Log count

    // Log the number of rows processed for Bangkok and Nationwide
    // console.log('Bangkok rows processed:', processedRows.Bangkok);
    // console.log('Nationwide rows processed:', processedRows.Nationwide);

    // Log the final migration types for Bangkok and South
    const bangkokData = regionGroups['Bangkok'];
    const southData = regionGroups['South'];

    // 特别记录 South 区域的 Female 数据
    if (selectedGender === 'Female' && southData) {
      console.log('South region Female migration reasons:', southData.migrationReasons);
    }
    if (bangkokData) {
      console.log('Bangkok final migration types:', bangkokData.migrationTypes);
    }

    // Log the nationwide data
    console.log('Nationwide data:', nationwideDataObj);

    // Update the nationwide data state
    setNationwideData(nationwideDataObj);

    // 当过滤器变化时，保持当前打开的 popup
    // 而不是重置状态，这样确保弹出菜单在过滤器变化时保持打开
    if (currentlyExpandedRegion) {
      // 在处理后的数据中查找该区域
      const updatedRegion = processed.find(r => r.name === currentlyExpandedRegion);

      if (updatedRegion) {
        console.log('Found updated region for currently expanded popup:', updatedRegion.name);

        // 保持 popup 打开状态
        setExpandedRegion(currentlyExpandedRegion);

        // 更新活跃区域引用
        activeRegionRef.current = currentlyExpandedRegion;

        // 获取 popup 引用
        const popupRef = popupRefs.current[currentlyExpandedRegion];
        if (popupRef) {
          // popup 已经打开，它将自动更新数据
          console.log('Popup ref found, content will update automatically');
          activePopupRef.current = popupRef;

          // 强制更新 popup 内容
          setTimeout(() => {
            try {
              // 如果 popup 已关闭，重新打开它
              if (popupRef && !popupRef.isOpen() && map) {
                console.log('Reopening closed popup after filter change');
                popupRef.openOn(map);
              }
            } catch (e) {
              console.error('Error updating popup:', e);
            }
          }, 100);
        } else {
          console.log('Popup ref not found for', currentlyExpandedRegion);

          // 如果没有找到 popup 引用，尝试找到对应的 marker 并打开它的 popup
          const marker = markersRef.current[currentlyExpandedRegion];
          if (marker && marker._leaflet_id) {
            console.log('Trying to open popup via marker');
            setTimeout(() => {
              try {
                if (marker.openPopup) {
                  marker.openPopup();
                }
              } catch (e) {
                console.error('Error opening popup via marker:', e);
              }
            }, 100);
          }
        }
      } else {
        console.log('Previously expanded region not found in updated data, closing popup');
        // 区域在过滤后不存在了，关闭 popup
        setExpandedRegion(null);
        activeRegionRef.current = null;
        activePopupRef.current = null;
      }
    }

    // 自动调整地图视角以显示所有标记点 - 仅在初始加载时执行，不在过滤器变化时执行
    if (processed.length > 0 && show && !isFilterChanging) {
      // 仅在初始加载时调整地图视角，不在过滤器变化时调整
      // 这可以防止地图在过滤器变化时抖动
      const validPositions = processed.map(region => region.position);
      if (validPositions.length > 0) {
        const bounds = L.latLngBounds(validPositions);
        if (bounds.isValid()) {
          bounds.pad(0.2);
          // 使用requestAnimationFrame代替setTimeout，提高动画流畅度
          requestAnimationFrame(() => {
            if (map) {
              try {
                map.fitBounds(bounds, { maxZoom: 12, animate: true, duration: 0.5 });
              } catch (e) {
                console.error("Error fitting bounds:", e);
              }
            }
          });
        }
      }
    } else if (show && !isFilterChanging) {
      console.log('No valid positions found after filtering. Gender:', selectedGender, 'Type:', selectedMigrationType);
    }
  }, [migrationData, migrationReasonsData, selectedGender, selectedMigrationType, selectedMigrationReason, show, map]);

  // Handle chart display toggle event
  const handleToggleChart = (regionName) => {
    // Prevent event bubbling
    // Use requestAnimationFrame instead of setTimeout for better performance
    requestAnimationFrame(() => {
      setShowCharts(prev => {
        // If the region doesn't have a state yet, default to false (data view)
        const currentState = prev[regionName] !== undefined ? prev[regionName] : false;
        const newState = { ...prev, [regionName]: !currentState };
        console.log('Toggling chart for', regionName, 'New state:', newState[regionName]);
        return newState;
      });
    });
  };

  // 保存当前打开的 popup 引用，以便在 filter 变化时保持它打开
  const activePopupRef = useRef(null);
  const activeRegionRef = useRef(null);

  // 当 filter 变化时，保持 popup 打开
  useEffect(() => {
    if (expandedRegion) {
      // 保存当前展开的区域名称
      activeRegionRef.current = expandedRegion;

      // 找到对应的 marker
      const marker = markersRef.current[expandedRegion];
      if (marker) {
        // 保存 popup 引用
        activePopupRef.current = popupRefs.current[expandedRegion];
        console.log('Saved active popup for', expandedRegion, 'to keep it open during filter changes');
      }
    }
  }, [expandedRegion]);

  // 当 filter 变化时，确保 popup 保持打开
  useEffect(() => {
    // 防止初始加载时触发
    if (!processedData.length) return;

    // 设置过滤器变化标志
    setIsFilterChanging(true);
    console.log('Filter changing, setting isFilterChanging to true');

    // 使用单个函数处理所有状态更新，减少多次重新渲染
    const updatePopupState = () => {
      // 如果有活跃的区域，确保它的 popup 保持打开
      if (activeRegionRef.current) {
        const regionName = activeRegionRef.current;

        // 在处理完数据后，检查该区域是否仍然存在于处理后的数据中
        const regionStillExists = processedData.some(r => r.name === regionName);

        if (regionStillExists) {
          // 区域仍然存在，保持 popup 打开
          console.log('Region still exists after filter change, keeping popup open for:', regionName);

          // 确保 expandedRegion 状态保持一致
          if (expandedRegion !== regionName) {
            setExpandedRegion(regionName);
          }

          const marker = markersRef.current[regionName];
          const popupRef = popupRefs.current[regionName];

          if (marker) {
            // 如果 marker 存在，手动打开 popup
            console.log('Manually opening popup for', regionName, 'after filter change');

            // 尝试使用已存在的 popup 引用
            if (popupRef && !popupRef.isOpen()) {
              try {
                console.log('Using existing popup reference to open popup');
                popupRef.openOn(map);

                // 确保图表状态保持一致
                if (showCharts[regionName]) {
                  console.log('Ensuring chart state is preserved for', regionName);
                }
              } catch (e) {
                console.error('Error opening popup with ref:', e);
              }
            }
            // 如果没有 popup 引用，尝试使用 marker 打开 popup
            else if (marker._leaflet_id && marker._popup) {
              try {
                console.log('Using marker to open popup');
                marker.openPopup();
              } catch (e) {
                console.error('Error opening popup via marker:', e);
              }
            }
          }
        } else {
          // 区域在过滤后不存在了，关闭 popup
          console.log('Region no longer exists after filter change, closing popup for:', regionName);
          activeRegionRef.current = null;
          activePopupRef.current = null;
          setExpandedRegion(null);
        }
      }

      // 无论如何，都重置过滤器变化标志
      setIsFilterChanging(false);
      console.log('Filter change complete, setting isFilterChanging to false');
    };

    // 使用requestAnimationFrame而不是setTimeout，提高性能
    requestAnimationFrame(updatePopupState);

  }, [selectedGender, selectedMigrationType, processedData, expandedRegion, map, showCharts]);

  // Removed tooltip position update effect

  // 获取当前展开的区域数据
  // const expandedRegionData = expandedRegion ? processedData.find(r => r.name === expandedRegion) : null;

  // 如果不显示，则返回 null
  if (!show) return null;

  // 如果没有数据，仍然显示控制面板和图例，但添加提示消息
  const hasNoData = !processedData || processedData.length === 0;

  if (hasNoData) {
    console.log('No processed data available after filtering. Gender:', selectedGender, 'Type:', selectedMigrationType);
  }

  // 获取标记半径
  const getMarkerRadius = (totalMigration) => {
    return Math.max(10, Math.min(40, Math.sqrt(totalMigration) * 0.3));
  };

  // 渲染图层
  return (
    <>
      {/* 显示全国数据汇总 */}
      {!hasNoData && nationwideData && nationwideData.totalPopulation > 0 && showNationwide && (
        <NationwideMigrationSummary
          data={nationwideData}
          isVisible={true}
          getMigrationTypeColor={getMigrationTypeColor}
          getMigrationTypeName={getMigrationTypeName}
          t={t}
          onClose={() => setShowNationwide(false)}
        />
      )}

      {/* 全国数据切换按钮 */}
      {!hasNoData && nationwideData && nationwideData.totalPopulation > 0 && (
        <div className="nationwide-toggle-btn" onClick={() => setShowNationwide(prev => !prev)}>
          <span className="toggle-icon">{showNationwide ? '▼' : '▶'}</span>
          {showNationwide ? t.hideNationwideData || 'Hide Thailand Data' : t.showNationwideData || 'Show Thailand Data'}
        </div>
      )}

      {/* 渲染标记点 - 只在有数据时显示 */}
      {!hasNoData && processedData.map((region, index) => {
        // Ensure totalPopulation is a valid number for radius calculation
        const totalPopulation = region.totalPopulation || 0;
        if (totalPopulation === 0) {
          console.warn(`Skipping rendering marker for ${region.name} due to zero population.`);
          return null; // Skip rendering marker if population is zero
        }
        const radius = getMarkerRadius(totalPopulation);
        const isCurrentlyExpanded = expandedRegion === region.name;

        // Debug log for this specific region
        console.log(`Rendering marker for region: ${region.name || 'unnamed'}, Group: ${region.Group || 'no group'}`);

        return (
          <CircleMarker
            key={region.name || index}
            ref={el => {
              if (el) {
                el._migrationTotal = totalPopulation;
                markersRef.current[region.name] = el;
              }
            }}
            center={region.position}
            radius={isCurrentlyExpanded ? radius * 1.1 : radius}
            fillColor={isCurrentlyExpanded ? '#ff4d4f' : '#1890ff'}
            fillOpacity={isCurrentlyExpanded ? 0.9 : 0.7}
            color="#fff"
            weight={isCurrentlyExpanded ? 3 : 2}
            className="migration-marker"
            pane="markerPane"
            interactive={true}
            eventHandlers={{
              click: (e) => {
                // When marker is clicked, bring it to front
                const marker = e.target;
                if (typeof marker.bringToFront === 'function') {
                  marker.bringToFront();
                }

                // Store the marker reference for data access
                markersRef.current[region.name]._migrationTotal = region.totalPopulation || 0;
              }
            }}
          >
            <Popup
              ref={el => {
                if (el) {
                  // 保存 popup 引用
                  popupRefs.current[region.name] = el;

                  // 如果这个区域是当前活跃的区域，自动打开 popup
                  if (activeRegionRef.current === region.name) {
                    console.log('Auto-opening popup for active region:', region.name);
                    // 使用requestAnimationFrame代替setTimeout，提高性能
                    requestAnimationFrame(() => {
                      try {
                        // 如果 popup 已关闭，重新打开它
                        if (el && !el.isOpen()) {
                          console.log('Opening popup for', region.name, 'after filter change');
                          el.openOn(map);
                          // 确保展开状态与 popup 状态一致
                          setExpandedRegion(region.name);
                        }
                      } catch (e) {
                        console.error('Error opening popup:', e);
                      }
                    });
                  }

                  // 保存当前活跃的 popup 引用
                  if (activeRegionRef.current === region.name) {
                    activePopupRef.current = el;
                  }
                }
              }}
              minWidth={320}
              maxWidth={380}
              className="migration-leaflet-popup"
              closeButton={true}
              autoPan={true}
              closeOnClick={false}
              closeOnEscapeKey={false}
              eventHandlers={{
                add: () => {
                  console.log('Popup opened for:', region.name);
                  setExpandedRegion(region.name);
                  setHoveredRegion(null);

                  // 当 popup 打开时，更新活跃区域引用
                  activeRegionRef.current = region.name;
                  activePopupRef.current = popupRefs.current[region.name];

                  // 确保当 popup 打开时，标记器样式更新
                  const marker = markersRef.current[region.name];
                  if (marker) {
                    marker.setStyle({
                      fillColor: '#ff4d4f',
                      fillOpacity: 0.9,
                      weight: 3
                    });
                  }

                  // Ensure the close button is properly initialized and clickable
                  requestAnimationFrame(() => {
                    try {
                      const popupEl = document.querySelector('.leaflet-popup');
                      if (popupEl) {
                        const closeButton = popupEl.querySelector('.leaflet-popup-close-button');
                        if (closeButton) {
                          console.log('Found close button, ensuring it is clickable');
                          // Ensure the close button is clickable by adding a click event listener
                          closeButton.style.zIndex = '2000';
                          closeButton.style.pointerEvents = 'auto';
                          closeButton.style.cursor = 'pointer';

                          // Add a direct click handler to ensure it works
                          closeButton.addEventListener('click', (event) => {
                            console.log('Close button clicked directly');
                            // Prevent the default action to avoid any conflicts
                            event.preventDefault();
                            event.stopPropagation();

                            // Manually close the popup
                            const popup = popupRefs.current[region.name];
                            if (popup) {
                              try {
                                popup.close();
                              } catch (e) {
                                console.error('Error closing popup manually:', e);
                              }
                            }

                            // Reset state
                            setExpandedRegion(null);
                            if (activeRegionRef.current === region.name) {
                              activeRegionRef.current = null;
                              activePopupRef.current = null;
                            }

                            // Reset marker style
                            const marker = markersRef.current[region.name];
                            if (marker) {
                              marker.setStyle({
                                fillColor: '#1890ff',
                                fillOpacity: 0.7,
                                weight: 2
                              });
                            }
                          }, { once: true });
                        } else {
                          console.warn('Close button not found in popup');
                        }
                      }
                    } catch (e) {
                      console.error('Error ensuring close button is clickable:', e);
                    }
                  });
                },
                remove: (e) => {
                  console.log('Popup closed for:', region.name);
                  console.log('Popup closed event:', e);
                  console.log('Popup closed event type:', e?.type);
                  console.log('Popup closed event target:', e?.target);
                  console.log('Popup closed event originalEvent:', e?.originalEvent);

                  // 检查是否是通过点击关闭按钮关闭的
                  // 如果是通过点击关闭按钮关闭的，e.type 应该是 'popupclose'
                  const isCloseButtonClick = e && e.type === 'popupclose' && !isFilterChanging;

                  // 如果这个关闭不是由于过滤器变化导致的，并且是通过点击关闭按钮关闭的，则清除活跃区域引用
                  console.log('Popup closed, isFilterChanging:', isFilterChanging, 'isCloseButtonClick:', isCloseButtonClick);

                  // Always handle close event properly regardless of what triggered it
                  if (expandedRegion === region.name) {
                    console.log('Popup closed, resetting state');
                    setExpandedRegion(null);

                    if (activeRegionRef.current === region.name) {
                      activeRegionRef.current = null;
                      activePopupRef.current = null;
                    }

                    // 恢复标记器样式
                    const marker = markersRef.current[region.name];
                    if (marker) {
                      marker.setStyle({
                        fillColor: '#1890ff',
                        fillOpacity: 0.7,
                        weight: 2
                      });
                    }
                  } else if (isFilterChanging) {
                    console.log('Popup closed due to filter change, not resetting state');
                    // 如果是因为过滤器变化导致的关闭，不做任何操作，保持状态
                  }

                  // Don't reset chart state when popup is closed
                  // This allows the chart to remain visible when the popup is reopened
                }
              }}
            >
              <MigrationPopup
                region={{
                  ...region,
                  // Ensure region name is set
                  name: region.name || region.Group,
                  nameTH: region.nameTH || region.Group
                }}
                isVisible={true}
                showChart={showCharts[region.name]}
                onToggleChart={() => handleToggleChart(region.name)}
                getMigrationTypeColor={getMigrationTypeColor}
                getMigrationTypeName={getMigrationTypeName}
                t={t}
              />
            </Popup>
          </CircleMarker>
        );
      })}

      {/* Removed marker hover tooltip */}

      {/* 控制面板 - 始终显示 */}
      <div
        className="migration-controls-container"
        onClick={(e) => { e.stopPropagation(); e.preventDefault(); return false; }}
        onMouseDown={(e) => { e.stopPropagation(); e.preventDefault(); return false; }}
        onTouchStart={(e) => { e.stopPropagation(); e.preventDefault(); return false; }}
        onTouchMove={(e) => { e.stopPropagation(); e.preventDefault(); return false; }}
        onTouchEnd={(e) => { e.stopPropagation(); e.preventDefault(); return false; }}
        onMouseMove={(e) => { e.stopPropagation(); }}
        onMouseUp={(e) => { e.stopPropagation(); }}
        onMouseLeave={(e) => { e.stopPropagation(); }}
        onMouseEnter={(e) => { e.stopPropagation(); }}
        onWheel={(e) => { e.stopPropagation(); e.preventDefault(); }}
        style={{ pointerEvents: 'auto' }}
      >
        <MigrationControls
          selectedGender={selectedGender}
          onGenderChange={(gender) => {
            // Prevent event bubbling
            // Set filter changing flag
            setIsFilterChanging(true);

            // Save current active region
            const currentActive = activeRegionRef.current;
            console.log('Gender filter changing, current active region:', currentActive);

            // Call parent component callback
            onGenderChange(gender);

            // Use requestAnimationFrame for better performance
            requestAnimationFrame(() => {
              setIsFilterChanging(false);
            });
          }}
          selectedMigrationType={selectedMigrationType}
          onMigrationTypeChange={(type) => {
            // Set filter changing flag
            setIsFilterChanging(true);

            // Save current active region
            const currentActive = activeRegionRef.current;
            console.log('Migration type filter changing, current active region:', currentActive);

            // Call parent component callback
            onMigrationTypeChange(type);

            // Use requestAnimationFrame for better performance
            requestAnimationFrame(() => {
              setIsFilterChanging(false);
            });
          }}
          selectedMigrationReason={selectedMigrationReason}
          onMigrationReasonChange={(reason) => {
            // Set filter changing flag
            setIsFilterChanging(true);

            // Save current active region
            const currentActive = activeRegionRef.current;
            console.log('Migration reason filter changing, current active region:', currentActive);

            // Update the migration reason filter
            setSelectedMigrationReason(reason);

            // Use requestAnimationFrame for better performance
            requestAnimationFrame(() => {
              setIsFilterChanging(false);
            });
          }}
          t={t}
        />
      </div>

      {/* 无数据提示 - 只在没有数据时显示 */}
      {hasNoData && (
        <div className="migration-no-data-alert">
          <div className="migration-no-data-message">
            <div className="migration-no-data-icon">📊</div>
            <div className="migration-no-data-text">
              {t.noDataForFilter || "No data available for the selected filters"}
            </div>
            <div className="migration-no-data-hint">
              {t.tryDifferentFilter || "Try selecting different filter options"}
            </div>
          </div>
        </div>
      )}

      {/* 图例 - 始终显示 */}
      <div className="migration-type-legend" onClick={(e) => e.stopPropagation()}>
        <h4>{t.migrationTypeAnalysis || 'Migration Type Analysis'}</h4>
        <div className="legend-items">
          <div className="legend-item">
            <span className="color-box" style={{backgroundColor: getMigrationTypeColor('Whole_Household_Migrated')}}></span>
            {getMigrationTypeName('Whole_Household_Migrated', t)}
          </div>
          <div className="legend-item">
            <span className="color-box" style={{backgroundColor: getMigrationTypeColor('Partial_Household_Migrated')}}></span>
            {getMigrationTypeName('Partial_Household_Migrated', t)}
          </div>
          <div className="legend-item">
            <span className="color-box" style={{backgroundColor: getMigrationTypeColor('Individual_Migrated')}}></span>
            {getMigrationTypeName('Individual_Migrated', t)}
          </div>
        </div>
      </div>
    </>
  );
};

export default MigrationTypeLayer;

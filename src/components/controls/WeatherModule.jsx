import React, { useState, useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import '../../styles/WeatherTool.css';

const WeatherModule = ({ position, language, t }) => {
  const [showWeatherInfo, setShowWeatherInfo] = useState(false);
  const [currentWeather, setCurrentWeather] = useState(null);
  const [forecastData, setForecastData] = useState(null);
  const [nearbyWeather, setNearbyWeather] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [forecastType, setForecastType] = useState('daily'); // 'hourly' or 'daily'
  const [apiCallsCount, setApiCallsCount] = useState(0);
  const map = useMap();
  const panelRef = useRef(null);
  const locationMarkerRef = useRef(null); // 保存位置标记的引用

  // 使用环境变量获取API密钥
  const API_KEY = import.meta.env.VITE_OPENWEATHER_API_KEY || '********************************';

  // 获取当前位置天气和预报数据
  const fetchWeatherData = async () => {
    if (!position || !Array.isArray(position) || position.length < 2) {
      setError(language === 'zh' ? '位置数据无效' : 'Invalid position data');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    try {
      // 使用正确的参数顺序：lat在前，lon在后
      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?lat=${position[0]}&lon=${position[1]}&units=metric&appid=${API_KEY}&lang=${language === 'zh' ? 'zh_cn' : 'en'}`
      );

      if (!response.ok) {
        throw new Error(`${language === 'zh' ? '天气数据获取失败' : 'Error loading weather data'}: ${response.status}`);
      }

      const data = await response.json();

      // 检查数据结构完整性
      if (!data || !data.main || !data.weather || data.weather.length === 0) {
        throw new Error(language === 'zh' ? '天气数据格式不正确' : 'Weather data format incorrect');
      }

      // 修复数据结构
      const currentData = {
        temp: data.main.temp,
        feels_like: data.main.feels_like,
        humidity: data.main.humidity,
        wind_speed: data.wind.speed,
        weather: data.weather[0],
        sunrise: data.sys.sunrise,
        sunset: data.sys.sunset,
        dt: data.dt,
        pressure: data.main.pressure,
        visibility: data.visibility,
        name: data.name,
        uvi: 0 // 标准API中不可用
      };

      setCurrentWeather(currentData);

      // 获取预报数据
      const forecastResponse = await fetch(
        `https://api.openweathermap.org/data/2.5/forecast?lat=${position[0]}&lon=${position[1]}&units=metric&appid=${API_KEY}&lang=${language === 'zh' ? 'zh_cn' : 'en'}`
      );

      if (forecastResponse.ok) {
        const forecastData = await forecastResponse.json();

        // 检查数据完整性
        if (!forecastData || !forecastData.list || !Array.isArray(forecastData.list)) {
          throw new Error(language === 'zh' ? '预报数据格式不正确' : 'Forecast data format incorrect');
        }

        // 转换为兼容格式
        const processedData = {
          hourly: forecastData.list.slice(0, 12),
          daily: processDailyForecast(forecastData.list)
        };

        setForecastData(processedData);
      }

      // 更新API调用计数
      setApiCallsCount(prev => prev + 1);

    } catch (error) {
      console.error(language === 'zh' ? '获取天气数据出错:' : 'Error fetching weather data:', error);
      setError(error.message || (language === 'zh' ? '未知错误' : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  // 添加处理每日预报的辅助函数
  const processDailyForecast = (forecastList) => {
    // 简单实现：按天分组并取每天的最高温和最低温
    const dailyMap = {};

    forecastList.forEach(item => {
      const date = new Date(item.dt * 1000).toDateString();

      if (!dailyMap[date]) {
        dailyMap[date] = {
          dt: item.dt,
          temp: { min: item.main.temp, max: item.main.temp },
          weather: item.weather[0],
          pop: item.pop || 0
        };
      } else {
        dailyMap[date].temp.min = Math.min(dailyMap[date].temp.min, item.main.temp);
        dailyMap[date].temp.max = Math.max(dailyMap[date].temp.max, item.main.temp);
        // 使用中午时段的天气图标
        const hour = new Date(item.dt * 1000).getHours();
        if (hour >= 11 && hour <= 14) {
          dailyMap[date].weather = item.weather[0];
        }
        dailyMap[date].pop = Math.max(dailyMap[date].pop, item.pop || 0);
      }
    });

    return Object.values(dailyMap).slice(0, 7);
  };

  // 获取附近城市天气
  const fetchNearbyWeather = async () => {
    if (!position || !Array.isArray(position) || position.length < 2) {
      // 如果位置无效，不进行请求
      return;
    }

    try {
      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/find?lat=${position[0]}&lon=${position[1]}&cnt=5&units=metric&appid=${API_KEY}&lang=${language === 'zh' ? 'zh_cn' : 'en'}`
      );

      if (!response.ok) {
        throw new Error(language === 'zh' ? '周边天气数据获取失败' : 'Error loading nearby weather');
      }

      const data = await response.json();

      if (!data || !data.list || !Array.isArray(data.list)) {
        throw new Error(language === 'zh' ? '周边天气数据格式不正确' : 'Nearby weather data format incorrect');
      }

      setNearbyWeather(data.list || []);

      // 更新API调用计数
      setApiCallsCount(prev => prev + 1);

    } catch (error) {
      console.error(language === 'zh' ? '获取周边天气数据出错:' : 'Error fetching nearby weather:', error);
      // 不设置整个组件的error状态，允许主要天气信息显示
    }
  };

  // 切换天气面板显示
  const toggleWeatherPanel = () => {
    const newState = !showWeatherInfo;
    setShowWeatherInfo(newState);
    if (newState && !currentWeather) {
      fetchWeatherData();
      fetchNearbyWeather();
    }

    // 如果关闭面板，移除地图上的标记
    if (!newState && locationMarkerRef.current) {
      map.removeLayer(locationMarkerRef.current);
      locationMarkerRef.current = null;
    }
  };

  // 刷新天气数据
  const refreshWeather = () => {
    fetchWeatherData();
    fetchNearbyWeather();
  };

  // 放大到选择的城市，并在地图上添加标记
  const zoomToLocation = (location) => {
    if (map && location) {
      // 首先移除之前的标记（如果有）
      if (locationMarkerRef.current) {
        map.removeLayer(locationMarkerRef.current);
      }

      // 飞到新位置
      map.flyTo([location.coord.lat, location.coord.lon], 10);

      // 创建新标记
      const weatherIcon = L.divIcon({
        className: 'weather-location-marker',
        html: `<div>
          <span class="location-marker-label">${location.name}</span>
          <span class="location-marker-temp">${Math.round(location.main.temp)}°C</span>
        </div>`,
        iconSize: [100, 40],
        iconAnchor: [50, 40]
      });

      // 添加标记到地图
      const marker = L.marker([location.coord.lat, location.coord.lon], { icon: weatherIcon })
        .addTo(map);

      // 创建圆形区域表示城市范围
      const circle = L.circle([location.coord.lat, location.coord.lon], {
        color: '#4a90e2',
        fillColor: '#4a90e2',
        fillOpacity: 0.15,
        radius: 15000, // 15公里半径
        weight: 1
      }).addTo(map);

      // 创建一个图层组来保存标记和圆形
      const group = L.layerGroup([marker, circle]).addTo(map);

      // 保存引用以便稍后移除
      locationMarkerRef.current = group;

      // 更新选中的位置
      setSelectedLocation(location);
    }
  };

  // 当组件卸载时清理标记
  useEffect(() => {
    return () => {
      if (locationMarkerRef.current && map) {
        map.removeLayer(locationMarkerRef.current);
      }
    };
  }, [map]);

  // 格式化日期时间
  const formatTime = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString(language === 'zh' ? 'zh-CN' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 格式化星期几
  const formatDay = (timestamp, short = false) => {
    const date = new Date(timestamp * 1000);
    const options = {
      weekday: short ? 'short' : 'long',
    };
    return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', options);
  };

  // 格式化日期
  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    const options = {
      month: 'short',
      day: 'numeric'
    };
    return date.toLocaleDateString(language === 'zh' ? 'zh-CN' : 'en-US', options);
  };

  // 格式化小时
  const formatHour = (timestamp) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString(language === 'zh' ? 'zh-CN' : 'en-US', {
      hour: '2-digit'
    });
  };

  // 获取翻译文本，确保语言切换正确
  const getText = (key, defaultEn, defaultZh) => {
    if (t && t[key]) {
      return t[key];
    }
    return language === 'zh' ? defaultZh : defaultEn;
  };

  // 阻止事件传播到地图
  const handlePanelInteraction = (e) => {
    e.stopPropagation();
  };

  // 处理触摸事件，确保面板可以滚动
  useEffect(() => {
    const panel = panelRef.current;
    if (!panel) return;

    const preventMapInteraction = (e) => {
      e.stopPropagation();
    };

    // 添加事件监听器以防止地图互动
    panel.addEventListener('wheel', preventMapInteraction, { passive: false });
    panel.addEventListener('touchstart', preventMapInteraction, { passive: false });
    panel.addEventListener('touchmove', preventMapInteraction, { passive: false });
    panel.addEventListener('touchend', preventMapInteraction, { passive: false });

    // 清理
    return () => {
      panel.removeEventListener('wheel', preventMapInteraction);
      panel.removeEventListener('touchstart', preventMapInteraction);
      panel.removeEventListener('touchmove', preventMapInteraction);
      panel.removeEventListener('touchend', preventMapInteraction);
    };
  }, [showWeatherInfo]);

  return (
    <>
      <button
        className={`weather-button ${showWeatherInfo ? 'active' : ''}`}
        onClick={toggleWeatherPanel}
        title={getText('checkWeather', "Check Weather", "查看天气")}
      >
        ☀️
      </button>

      {showWeatherInfo && (
        <div
          className="weather-panel"
          ref={panelRef}
          onClick={handlePanelInteraction}
          onTouchStart={handlePanelInteraction}
          onTouchMove={handlePanelInteraction}
          onWheel={handlePanelInteraction}
        >
          <h3>
            {getText('weatherInfo', "Weather Information", "天气信息")}
            <button className="close-weather" onClick={toggleWeatherPanel}>
              {getText('close', "Close", "关闭")}
            </button>
          </h3>

          {loading ? (
            <div className="weather-loading">{getText('loading', "Loading...", "加载中...")}</div>
          ) : error ? (
            <div className="weather-error">
              <p>{error}</p>
              <button onClick={refreshWeather} className="refresh-button">
                {getText('retry', "Retry", "重试")}
              </button>
            </div>
          ) : (
            <>
              {currentWeather && (
                <div className="current-weather">
                  <h4>{currentWeather.name || getText('currentWeather', "Current Weather", "当前天气")}</h4>
                  <div className="weather-main">
                    <img
                      src={`https://openweathermap.org/img/wn/${currentWeather.weather.icon}@2x.png`}
                      alt={currentWeather.weather.description}
                    />
                    <span className="temperature">{Math.round(currentWeather.temp)}°C</span>
                  </div>
                  <div className="weather-details">
                    <p>{currentWeather.weather.description}</p>
                    <p>{getText('feelsLike', "Feels Like", "体感温度")}: {Math.round(currentWeather.feels_like)}°C</p>
                    <p>{getText('humidity', "Humidity", "湿度")}: {currentWeather.humidity}%</p>
                    <p>{getText('wind', "Wind Speed", "风速")}: {currentWeather.wind_speed} m/s</p>
                    <div className="sun-times">
                      <p>{getText('sunrise', "Sunrise", "日出")}: {formatTime(currentWeather.sunrise)}</p>
                      <p>{getText('sunset', "Sunset", "日落")}: {formatTime(currentWeather.sunset)}</p>
                    </div>
                  </div>
                </div>
              )}

              {forecastData && (
                <div className="weather-forecast">
                  <div className="forecast-header">
                    <h4>{getText('forecast', "Weather Forecast", "天气预报")}</h4>
                    <div className="forecast-type-tabs">
                      <button
                        className={`forecast-tab ${forecastType === 'daily' ? 'active' : ''}`}
                        onClick={() => setForecastType('daily')}
                      >
                        {getText('daily', "Daily", "每天")}
                      </button>
                      <button
                        className={`forecast-tab ${forecastType === 'hourly' ? 'active' : ''}`}
                        onClick={() => setForecastType('hourly')}
                      >
                        {getText('hourly', "Hourly", "每小时")}
                      </button>
                    </div>
                  </div>

                  <div className="forecast-list">
                    {forecastType === 'daily' ? (
                      forecastData.daily.map((day, index) => (
                        <div className="forecast-item" key={index}>
                          <div className="forecast-day">
                            {index === 0
                              ? getText('today', "Today", "今天")
                              : formatDay(day.dt, true)}
                          </div>
                          <img
                            src={`https://openweathermap.org/img/wn/${day.weather.icon}.png`}
                            alt={day.weather.description}
                          />
                          <div className="forecast-temp">
                            {Math.round(day.temp.max)}° / {Math.round(day.temp.min)}°
                          </div>
                          {day.pop > 0 && (
                            <div className="precip-chance">
                              <div className="precip-bar">
                                <div
                                  className="precip-fill"
                                  style={{ width: `${Math.min(100, day.pop * 100)}%` }}
                                ></div>
                              </div>
                              <span className="precip-value">{Math.round(day.pop * 100)}%</span>
                            </div>
                          )}
                        </div>
                      ))
                    ) : (
                      forecastData.hourly.map((hour, index) => (
                        <div className="forecast-item" key={index}>
                          <div className="forecast-day">{formatHour(hour.dt)}</div>
                          <img
                            src={`https://openweathermap.org/img/wn/${hour.weather[0].icon}.png`}
                            alt={hour.weather[0].description}
                          />
                          <div className="forecast-temp">{Math.round(hour.main.temp)}°</div>
                          {hour.pop > 0 && (
                            <div className="precip-chance">
                              <div className="precip-bar">
                                <div
                                  className="precip-fill"
                                  style={{ width: `${Math.min(100, hour.pop * 100)}%` }}
                                ></div>
                              </div>
                              <span className="precip-value">{Math.round(hour.pop * 100)}%</span>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>
              )}

              {nearbyWeather.length > 0 && (
                <div className="nearby-weather">
                  <h4>{getText('nearbyWeather', "Nearby Weather", "周边天气")}</h4>
                  <div className="nearby-list">
                    {nearbyWeather.map(location => (
                      <div
                        key={location.id}
                        className={`nearby-item ${selectedLocation?.id === location.id ? 'selected' : ''}`}
                        onClick={() => zoomToLocation(location)}
                      >
                        <span className="nearby-name">{location.name}</span>
                        <span className="nearby-temp">{Math.round(location.main.temp)}°C</span>
                        <img
                          src={`https://openweathermap.org/img/wn/${location.weather[0].icon}.png`}
                          alt={location.weather[0].description}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="weather-footer">
                <button className="refresh-button" onClick={refreshWeather}>
                  {getText('refresh', "Refresh", "刷新")}
                </button>
                <small className="data-source">
                  {getText('dataSource', "Data Source: OpenWeather", "数据来源: OpenWeather")}
                </small>
              </div>

              {apiCallsCount > 0 && (
                <div className="api-limit-note">
                  {getText('apiLimitNote',
                    "OpenWeather API limited to 1000 calls per day. Please use sparingly.",
                    "OpenWeather API 每日限制1000次调用，请谨慎使用")}
                </div>
              )}
            </>
          )}
        </div>
      )}
    </>
  );
};

export default WeatherModule;
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet-geometryutil';
import '../../styles/RulerTool.css';
import { 
  calculateDistance, 
  formatDistance, 
  calculatePolygonArea, 
  formatArea, 
  calculatePolygonPerimeter 
} from '../../utils/mapUtils';

// 生成唯一ID的辅助函数
const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 使用全局变量来存储测量数据，使其在组件卸载后仍然保留
let globalSavedLines = [];
let globalSavedAreas = [];

const RulerTool = ({ active, onClose }) => {
  const map = useMap();
  // 测量模式：'distance' 或 'area'
  const [measureMode, setMeasureMode] = useState('distance');
  
  // 当前测量线
  const [currentLine, setCurrentLine] = useState({
    id: generateUniqueId(),
    points: [],
    distance: 0,
    markers: [],
    distanceMarkers: [],
    polyline: null
  });
  
  // 当前测量面积
  const [currentArea, setCurrentArea] = useState({
    id: generateUniqueId(),
    points: [],
    area: 0,
    perimeter: 0,
    markers: [],
    polygon: null,
    areaLabel: null
  });
  
  // 使用全局变量初始化已保存的测量线和面积数组
  const [savedLines, setSavedLines] = useState(globalSavedLines);
  const [savedAreas, setSavedAreas] = useState(globalSavedAreas);
  
  // 单位设置：'square_meters', 'hectares', 'square_kilometers', 'square_feet', 'acres', 'square_miles', 'metric', 'imperial'
  const [unit, setUnit] = useState('square_miles');
  const tempLineRef = useRef(null); // 用于存储鼠标移动时的临时线条
  const tempPolygonRef = useRef(null); // 用于存储鼠标移动时的临时多边形
  const [mousePosition, setMousePosition] = useState(null);

  // 使用 refs 保存最新的状态
  const currentLineRef = useRef(currentLine);
  useEffect(() => {
    currentLineRef.current = currentLine;
  }, [currentLine]);

  const currentAreaRef = useRef(currentArea);
  useEffect(() => {
    currentAreaRef.current = currentArea;
  }, [currentArea]);

  const unitRef = useRef(unit);
  useEffect(() => {
    unitRef.current = unit;
  }, [unit]);
  
  const measureModeRef = useRef(measureMode);
  useEffect(() => {
    measureModeRef.current = measureMode;
  }, [measureMode]);

  // 当savedLines或savedAreas变化时，更新全局变量
  useEffect(() => {
    globalSavedLines = savedLines;
  }, [savedLines]);

  useEffect(() => {
    globalSavedAreas = savedAreas;
  }, [savedAreas]);

  // 清除临时线条
  const clearTempLine = () => {
    if (tempLineRef.current) {
      map.removeLayer(tempLineRef.current);
      tempLineRef.current = null;
    }
  };
  
  // 清除临时多边形
  const clearTempPolygon = () => {
    if (tempPolygonRef.current) {
      map.removeLayer(tempPolygonRef.current);
      tempPolygonRef.current = null;
    }
  };

  // 清除当前测量线
  const clearCurrentLine = useCallback(() => {
    currentLineRef.current.markers.forEach(marker => {
      map.removeLayer(marker);
    });
    currentLineRef.current.distanceMarkers.forEach(marker => {
      map.removeLayer(marker);
    });
    if (currentLineRef.current.polyline) {
      map.removeLayer(currentLineRef.current.polyline);
    }
    setCurrentLine({
      id: generateUniqueId(),
      points: [],
      distance: 0,
      markers: [],
      distanceMarkers: [],
      polyline: null
    });
  }, [map]);
  
  // 清除当前面积测量
  const clearCurrentArea = useCallback(() => {
    currentAreaRef.current.markers.forEach(marker => {
      map.removeLayer(marker);
    });
    if (currentAreaRef.current.polygon) {
      map.removeLayer(currentAreaRef.current.polygon);
    }
    if (currentAreaRef.current.areaLabel) {
      map.removeLayer(currentAreaRef.current.areaLabel);
    }
    setCurrentArea({
      id: generateUniqueId(),
      points: [],
      area: 0,
      perimeter: 0,
      markers: [],
      polygon: null,
      areaLabel: null
    });
  }, [map]);

  // 删除指定的已保存测量线
  const deleteLine = useCallback((lineId) => {
    const lineToDelete = savedLines.find(line => line.id === lineId);
    if (!lineToDelete) return;
    lineToDelete.markers.forEach(marker => {
      map.removeLayer(marker);
    });
    lineToDelete.distanceMarkers.forEach(marker => {
      map.removeLayer(marker);
    });
    if (lineToDelete.polyline) {
      map.removeLayer(lineToDelete.polyline);
    }
    setSavedLines(prev => prev.filter(line => line.id !== lineId));
  }, [map, savedLines]);
  
  // 删除指定的已保存面积测量
  const deleteArea = useCallback((areaId) => {
    const areaToDelete = savedAreas.find(area => area.id === areaId);
    if (!areaToDelete) return;
    areaToDelete.markers.forEach(marker => {
      map.removeLayer(marker);
    });
    if (areaToDelete.polygon) {
      map.removeLayer(areaToDelete.polygon);
    }
    if (areaToDelete.areaLabel) {
      map.removeLayer(areaToDelete.areaLabel);
    }
    setSavedAreas(prev => prev.filter(area => area.id !== areaId));
  }, [map, savedAreas]);

  // 清除所有测量线（包括当前线条和已保存的线条）
  const clearAllLines = useCallback(() => {
    clearCurrentLine();
    savedLines.forEach(line => {
      line.markers.forEach(marker => {
        map.removeLayer(marker);
      });
      line.distanceMarkers.forEach(marker => {
        map.removeLayer(marker);
      });
      if (line.polyline) {
        map.removeLayer(line.polyline);
      }
    });
    setSavedLines([]);
  }, [clearCurrentLine, map, savedLines]);
  
  // 清除所有面积测量（包括当前面积和已保存的面积）
  const clearAllAreas = useCallback(() => {
    clearCurrentArea();
    savedAreas.forEach(area => {
      area.markers.forEach(marker => {
        map.removeLayer(marker);
      });
      if (area.polygon) {
        map.removeLayer(area.polygon);
      }
      if (area.areaLabel) {
        map.removeLayer(area.areaLabel);
      }
    });
    setSavedAreas([]);
  }, [clearCurrentArea, map, savedAreas]);
  
  // 切换测量模式
  const toggleMeasureMode = () => {
    // 清除当前测量
    if (measureMode === 'distance') {
      clearCurrentLine();
      setMeasureMode('area');
    } else {
      clearCurrentArea();
      setMeasureMode('distance');
    }
  };
  

  // 切换单位
  const toggleUnit = () => {
    setUnit(prev => prev === 'metric' ? 'imperial' : 'metric');
  };

  // 处理地图点击事件
  const handleMapClick = useCallback((e) => {
    if (!active) return;

    // 如果点击发生在测量窗口内，不执行任何操作
    const rulerPanel = document.querySelector('.ruler-panel');
    if (rulerPanel && rulerPanel.contains(e.originalEvent.target)) {
      return;
    }

    // 如果是右键点击，则不处理
    if (e.originalEvent.button === 2) {
      return;
    }

    const newPoint = e.latlng;

    // 根据测量模式处理点击事件
    if (measureModeRef.current === 'distance') {
      // 距离测量模式
      // 添加标记
      const marker = L.marker(newPoint, {
        icon: L.divIcon({
          className: 'ruler-point-marker',
          html: `<div class="ruler-point"></div>`,
          iconSize: [10, 10],
          iconAnchor: [5, 5]
        })
      }).addTo(map);

      const current = currentLineRef.current;
      const updatedPoints = [...current.points, newPoint];
      const updatedMarkers = [...current.markers, marker];

      // 如果至少有一个点，则绘制折线并更新距离
      if (current.points.length > 0) {
        const lastPoint = current.points[current.points.length - 1];
        const segmentDistance = calculateDistance(lastPoint, newPoint);
        const updatedDistance = current.distance + segmentDistance;

        // 移除已有的折线
        if (current.polyline) {
          map.removeLayer(current.polyline);
        }

        const polyline = L.polyline(updatedPoints, {
          color: '#FF5722',
          weight: 3,
          opacity: 0.7,
          dashArray: '5, 10',
          lineCap: 'round',
          lineJoin: 'round'
        }).addTo(map);

        // 在两个点之间添加距离标签
        const midPoint = L.latLng(
          (lastPoint.lat + newPoint.lat) / 2,
          (lastPoint.lng + newPoint.lng) / 2
        );

        const distanceMarker = L.marker(midPoint, {
          icon: L.divIcon({
            className: 'distance-label',
            html: `<div class="distance-bubble">${formatDistance(segmentDistance, unitRef.current)}</div>`,
            iconSize: [80, 20],
            iconAnchor: [40, 10]
          })
        }).addTo(map);

        setCurrentLine({
          points: updatedPoints,
          distance: updatedDistance,
          markers: updatedMarkers,
          distanceMarkers: [...current.distanceMarkers, distanceMarker],
          polyline: polyline,
          id: current.id
        });
      } else {
        // 第一个点
        setCurrentLine({
          ...current,
          points: updatedPoints,
          markers: updatedMarkers
        });
      }
    } else {
      // 面积测量模式
      // 添加标记
      const marker = L.marker(newPoint, {
        icon: L.divIcon({
          className: 'ruler-point-marker',
          html: `<div class="ruler-point"></div>`,
          iconSize: [10, 10],
          iconAnchor: [5, 5]
        })
      }).addTo(map);

      const current = currentAreaRef.current;
      const updatedPoints = [...current.points, newPoint];
      const updatedMarkers = [...current.markers, marker];

      // 移除已有的多边形和面积标签
      if (current.polygon) {
        map.removeLayer(current.polygon);
      }
      if (current.areaLabel) {
        map.removeLayer(current.areaLabel);
      }

      // 如果至少有3个点，则绘制多边形并计算面积
      if (updatedPoints.length >= 3) {
        // 创建多边形
        const polygon = L.polygon(updatedPoints, {
          color: '#3498db',
          weight: 3,
          opacity: 0.7,
          fillColor: '#3498db',
          fillOpacity: 0.2,
          lineCap: 'round',
          lineJoin: 'round'
        }).addTo(map);

        // 计算面积和周长
        const area = calculatePolygonArea(updatedPoints);
        const perimeter = calculatePolygonPerimeter(updatedPoints);

        // 计算多边形的中心点
        const bounds = polygon.getBounds();
        const center = bounds.getCenter();

        // 添加面积标签 - 增强可见性和交互性
        const areaLabel = L.marker(center, {
          icon: L.divIcon({
            className: 'area-label',
            html: `<div class="area-bubble">${formatArea(Math.abs(area), unitRef.current)}</div>`,
            iconSize: [150, 50], // 增加宽度以适应更长的文本
            iconAnchor: [75, 25] // 保持居中
          }),
          zIndexOffset: 2000, // 提高z-index确保显示在最上层
          interactive: false, // 确保标签不会阻止点击事件
          pane: 'popupPane' // 使用popupPane而不是markerPane以确保更高的z-index
        }).addTo(map);

        setCurrentArea({
          points: updatedPoints,
          area: area,
          perimeter: perimeter,
          markers: updatedMarkers,
          polygon: polygon,
          areaLabel: areaLabel,
          id: current.id
        });
      } else {
        // 不足3个点，只更新点和标记
        setCurrentArea({
          ...current,
          points: updatedPoints,
          markers: updatedMarkers
        });
      }
    }
  }, [active, map]);

  // 处理鼠标移动事件，显示跟随鼠标的虚线或多边形
  const handleMouseMove = useCallback((e) => {
    if (!active) return;

    const currentMousePos = e.latlng;
    setMousePosition(currentMousePos);

    if (measureModeRef.current === 'distance') {
      // 距离测量模式
      if (currentLineRef.current.points.length === 0) return;
      
      const current = currentLineRef.current;
      const lastPoint = current.points[current.points.length - 1];

      if (tempLineRef.current) {
        map.removeLayer(tempLineRef.current);
      }

      tempLineRef.current = L.polyline([lastPoint, currentMousePos], {
        color: '#FF5722',
        weight: 2,
        opacity: 0.6,
        dashArray: '5, 5',
        lineCap: 'round',
        lineJoin: 'round'
      }).addTo(map);
    } else {
      // 面积测量模式
      if (currentAreaRef.current.points.length < 2) return;
      
      const current = currentAreaRef.current;
      const points = [...current.points, currentMousePos];

      if (tempPolygonRef.current) {
        map.removeLayer(tempPolygonRef.current);
      }

      tempPolygonRef.current = L.polygon(points, {
        color: '#3498db',
        weight: 2,
        opacity: 0.6,
        fillColor: '#3498db',
        fillOpacity: 0.1,
        dashArray: '5, 5',
        lineCap: 'round',
        lineJoin: 'round'
      }).addTo(map);
    }
  }, [active, map]);

  // 处理右键点击事件，完成当前测量并保存
  const handleContextMenu = useCallback((e) => {
    if (active) {
      e.originalEvent.preventDefault();
      
      if (measureModeRef.current === 'distance') {
        // 距离测量模式
        const current = currentLineRef.current;
        if (current.points.length > 0) {
          clearTempLine();
          if (current.points.length >= 2) {
            // 使用函数式更新确保 savedLines 最新
            setSavedLines(prev => {
              const exists = prev.some(line =>
                line.id === current.id ||
                (line.points.length === current.points.length &&
                  JSON.stringify(line.points) === JSON.stringify(current.points))
              );
              if (!exists) {
                return [...prev, { ...current }];
              }
              return prev;
            });
          }
          // 重置当前线，准备下一次测量
          setCurrentLine({
            id: generateUniqueId(),
            points: [],
            distance: 0,
            markers: [],
            distanceMarkers: [],
            polyline: null
          });
        }
      } else {
        // 面积测量模式
        const current = currentAreaRef.current;
        if (current.points.length > 0) {
          clearTempPolygon();
          if (current.points.length >= 3) {
            // 确保面积标签已创建并显示
            let updatedCurrent = {...current};
            
            if (!current.areaLabel && current.polygon) {
              // 计算多边形的中心点
              const bounds = current.polygon.getBounds();
              const center = bounds.getCenter();
              
              // 添加面积标签 - 使用与绘制时相同的增强样式
              const areaLabel = L.marker(center, {
                icon: L.divIcon({
                  className: 'area-label',
                  html: `<div class="area-bubble">${formatArea(Math.abs(current.area), unitRef.current)}</div>`,
                  iconSize: [150, 50], // 增加宽度以适应更长的文本
                  iconAnchor: [75, 25] // 保持居中
                }),
                zIndexOffset: 2000, // 提高z-index确保显示在最上层
                interactive: false, // 确保标签不会阻止点击事件
                pane: 'popupPane' // 使用popupPane而不是markerPane以确保更高的z-index
              }).addTo(map);
              
              // 更新当前面积对象，包含面积标签
              updatedCurrent = {
                ...current,
                areaLabel: areaLabel
              };
              
              // 更新当前面积状态
              setCurrentArea(updatedCurrent);
            }
            
            // 使用函数式更新确保 savedAreas 最新
            setSavedAreas(prev => {
              const exists = prev.some(area =>
                area.id === updatedCurrent.id ||
                (area.points.length === updatedCurrent.points.length &&
                  JSON.stringify(area.points) === JSON.stringify(updatedCurrent.points))
              );
              if (!exists) {
                return [...prev, { ...updatedCurrent }];
              }
              return prev;
            });
          }
          // 重置当前面积，准备下一次测量
          setCurrentArea({
            id: generateUniqueId(),
            points: [],
            area: 0,
            perimeter: 0,
            markers: [],
            polygon: null,
            areaLabel: null
          });
        }
      }
    }
  }, [active]);

  // 设置和清除地图事件监听器 —— 只依赖 active 和 map（以及稳定的事件处理函数）
  useEffect(() => {
    if (active) {
      map.on('click', handleMapClick);
      map.on('mousemove', handleMouseMove);
      map.on('contextmenu', handleContextMenu);
      map.getContainer().style.cursor = 'crosshair';
    } else {
      map.off('click', handleMapClick);
      map.off('mousemove', handleMouseMove);
      map.off('contextmenu', handleContextMenu);
      map.getContainer().style.cursor = '';
      if (tempLineRef.current) {
        map.removeLayer(tempLineRef.current);
        tempLineRef.current = null;
      }
      if (tempPolygonRef.current) {
        map.removeLayer(tempPolygonRef.current);
        tempPolygonRef.current = null;
      }
    }
    return () => {
      map.off('click', handleMapClick);
      map.off('mousemove', handleMouseMove);
      map.off('contextmenu', handleContextMenu);
      map.getContainer().style.cursor = '';
      if (tempLineRef.current) {
        map.removeLayer(tempLineRef.current);
        tempLineRef.current = null;
      }
      if (tempPolygonRef.current) {
        map.removeLayer(tempPolygonRef.current);
        tempPolygonRef.current = null;
      }
    };
  }, [active, map, handleMapClick, handleMouseMove, handleContextMenu]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearCurrentLine();
      clearCurrentArea();
    };
  }, [clearCurrentLine, clearCurrentArea]);

  // 当 active 状态变为 false 时，清除当前测量
  useEffect(() => {
    if (!active) {
      clearCurrentLine();
      clearCurrentArea();
    }
  }, [active, clearCurrentLine, clearCurrentArea]);

  if (!active) return null;

  return (
    <div className="ruler-panel">
      <div className="ruler-header">
        <h3>{measureMode === 'distance' ? 'Distance Measurement' : 'Area Measurement'}</h3>
        <button className="close-btn" onClick={onClose}>×</button>
      </div>

      <div className="ruler-content">
        {/* 测量模式切换 */}
        <div className="mode-toggle">
          <button 
            className={`mode-toggle-btn ${measureMode === 'distance' ? 'active' : ''}`}
            onClick={() => setMeasureMode('distance')}
          >
            Distance
          </button>
          <button 
            className={`mode-toggle-btn ${measureMode === 'area' ? 'active' : ''}`}
            onClick={() => setMeasureMode('area')}
          >
            Area
          </button>
        </div>

        {/* 距离测量模式 */}
        {measureMode === 'distance' && (
          <>
            {currentLine.points.length === 0 ? (
              <div className="ruler-instructions">
                Start to measure by clicking in the map to place your first point
              </div>
            ) : (
              <div className="measurement-info">
                <div className="unit-selector">
                  <label>Unit</label>
                  <div className="unit-dropdown">
                    <select value={unit} onChange={(e) => setUnit(e.target.value)}>
                      <option value="metric">Metric (km)</option>
                      <option value="imperial">Imperial (mi)</option>
                    </select>
                  </div>
                </div>

                {/* 显示当前测量线的总距离 */}
                <div className="distance-display">
                  <label>Current measurement</label>
                  <div className="distance-value">
                    {formatDistance(currentLine.distance, unit)}
                  </div>
                </div>

                <div className="ruler-actions">
                  <button className="cancel-measurement-btn" onClick={clearCurrentLine}>
                    Clear current line
                  </button>
                </div>
              </div>
            )}

            {/* 显示已保存的测量线列表 */}
            {savedLines.length > 0 && (
              <div className="saved-lines">
                <label>Saved measurements</label>
                <ul className="lines-list">
                  {savedLines.map((line, index) => (
                    <li key={line.id} className="line-item">
                      <span className="line-distance">
                        Line {index + 1}: {formatDistance(line.distance, unit)}
                      </span>
                      <button 
                        className="delete-line-btn" 
                        onClick={() => deleteLine(line.id)}
                        title="Delete this line"
                      >
                        ×
                      </button>
                    </li>
                  ))}
                </ul>

                {/* 删除所有线条的按钮 */}
                <div className="ruler-actions" style={{ marginTop: '10px' }}>
                  <button 
                    className="ruler-action-btn" 
                    onClick={clearAllLines}
                    style={{ background: '#e74c3c', color: 'white' }}
                  >
                    Delete all lines
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {/* 面积测量模式 */}
        {measureMode === 'area' && (
          <>
            {currentArea.points.length === 0 ? (
              <div className="ruler-instructions">
                Click on the map to start drawing a polygon. Right-click to complete.
              </div>
            ) : (
              <div className="measurement-info">
                <div className="unit-selector">
                  <label>Unit</label>
                  <div className="unit-dropdown">
                    <select value={unit} onChange={(e) => setUnit(e.target.value)}>
                      <option value="square_meters">Square Meters (m²)</option>
                      <option value="hectares">Hectares (ha)</option>
                      <option value="square_kilometers">Square Kilometers (km²)</option>
                      <option value="square_feet">Square Feet (ft²)</option>
                      <option value="acres">Acres</option>
                      <option value="square_miles">Square Miles (mi²)</option>
                      <option value="metric">Metric (auto)</option>
                      <option value="imperial">Imperial (auto)</option>
                    </select>
                  </div>
                </div>

                {/* 显示当前测量面积 */}
                {currentArea.points.length >= 3 && (
                  <>
                    <div className="area-display">
                      <label>Area</label>
                      <div className="area-value">
                        {formatArea(currentArea.area, unit)}
                      </div>
                    </div>
                    <div className="perimeter-display">
                      <label>Perimeter</label>
                      <div className="perimeter-value">
                        {formatDistance(currentArea.perimeter, unit)}
                      </div>
                    </div>
                  </>
                )}

                <div className="ruler-actions">
                  <button className="cancel-measurement-btn" onClick={clearCurrentArea}>
                    Clear current area
                  </button>
                </div>
              </div>
            )}

            {/* 显示已保存的测量面积列表 */}
            {savedAreas.length > 0 && (
              <div className="saved-areas">
                <label>Saved areas</label>
                <ul className="lines-list">
                  {savedAreas.map((area, index) => (
                    <li key={area.id} className="area-item">
                      <span className="area-size">
                        Area {index + 1}: {formatArea(area.area, unit)}
                      </span>
                      <button 
                        className="delete-area-btn" 
                        onClick={() => deleteArea(area.id)}
                        title="Delete this area"
                      >
                        ×
                      </button>
                    </li>
                  ))}
                </ul>

                {/* 删除所有面积的按钮 */}
                <div className="ruler-actions" style={{ marginTop: '10px' }}>
                  <button 
                    className="ruler-action-btn" 
                    onClick={clearAllAreas}
                    style={{ background: '#e74c3c', color: 'white' }}
                  >
                    Delete all areas
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {/* 新测量按钮 */}
        <button className="new-measurement-btn" onClick={measureMode === 'distance' ? clearCurrentLine : clearCurrentArea}>
          New measurement
        </button>
      </div>
    </div>
  );
};

export default RulerTool;

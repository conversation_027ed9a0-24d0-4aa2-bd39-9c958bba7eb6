import React, { useState, useRef, useEffect, useCallback } from 'react';
import '../../styles/MapTimeSlider.css';

const MapTimeSlider = ({ laborTrends, selectedIndex, onChangeIndex, show }) => {
  if (!show || !laborTrends || laborTrends.length === 0) return null;
  
  // 格式化日期显示
  const formatDate = (dateStr) => (dateStr ? dateStr.replace('_', ' ') : '');
  
  // 计算滑块位置
  const sliderPosition = (selectedIndex / (laborTrends.length - 1)) * 100;
  
  // 状态和引用
  const [isDragging, setIsDragging] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const trackRef = useRef(null);
  const markerRef = useRef(null);
  const tooltipRef = useRef(null);
  const startYRef = useRef(0);
  const startIndexRef = useRef(0);
  
  // 计算新的索引位置
  const calculateNewIndex = useCallback((clientY) => {
    if (!trackRef.current) return selectedIndex;
    
    const rect = trackRef.current.getBoundingClientRect();
    const trackHeight = rect.height;
    const trackTop = rect.top;
    
    // 确保位置在轨道范围内
    let posY = Math.max(trackTop, Math.min(clientY, trackTop + trackHeight));
    const ratio = (posY - trackTop) / trackHeight;
    
    // 计算并约束索引范围
    const newIndex = Math.round(ratio * (laborTrends.length - 1));
    return Math.max(0, Math.min(newIndex, laborTrends.length - 1));
  }, [laborTrends, selectedIndex]);
  
  // 处理轨道点击
  const handleTrackClick = useCallback((e) => {
    if (isDragging) return; // 如果正在拖动，忽略点击
    
    const newIndex = calculateNewIndex(e.clientY);
    if (newIndex !== selectedIndex) {
      onChangeIndex(newIndex);
    }
    
    // 显示提示框
    setShowTooltip(true);
    
    // 3秒后自动隐藏提示框
    setTimeout(() => {
      if (!isDragging) {
        setShowTooltip(false);
      }
    }, 3000);
  }, [calculateNewIndex, isDragging, onChangeIndex, selectedIndex]);
  
  // 拖动开始处理函数
  const handleDragStart = useCallback((e) => {
    e.preventDefault(); // 阻止默认行为
    
    // 记录起始位置和索引
    startYRef.current = e.clientY || (e.touches && e.touches[0].clientY);
    startIndexRef.current = selectedIndex;
    
    setIsDragging(true);
    setShowTooltip(true); // 显示提示框
  }, [selectedIndex]);
  
  // 拖动移动处理函数
  const handleDragMove = useCallback((e) => {
    if (!isDragging) return;
    e.preventDefault(); // 阻止默认行为
    
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);
    if (!clientY) return;
    
    // 计算新索引并更新
    const newIndex = calculateNewIndex(clientY);
    if (newIndex !== selectedIndex) {
      onChangeIndex(newIndex);
    }
  }, [calculateNewIndex, isDragging, onChangeIndex, selectedIndex]);
  
  // 拖动结束处理函数
  const handleDragEnd = useCallback(() => {
    if (!isDragging) return;
    setIsDragging(false);
    
    // 拖动结束后，3秒后隐藏提示框
    setTimeout(() => {
      setShowTooltip(false);
    }, 3000);
  }, [isDragging]);
  
  // 使用useEffect添加和移除全局事件监听
  useEffect(() => {
    if (isDragging) {
      // 添加全局事件监听
      document.addEventListener('mousemove', handleDragMove);
      document.addEventListener('touchmove', handleDragMove, { passive: false });
      document.addEventListener('mouseup', handleDragEnd);
      document.addEventListener('touchend', handleDragEnd);
    } else {
      // 移除全局事件监听
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('touchmove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchend', handleDragEnd);
    }
    
    // 清理函数
    return () => {
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('touchmove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      document.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging, handleDragMove, handleDragEnd]);
  
  // 获取日期标签
  const earliestDate = laborTrends[0]?.Date || '';
  const latestDate = laborTrends[laborTrends.length - 1]?.Date || '';
  const currentDate = laborTrends[selectedIndex]?.Date || '';
  
  return (
    <>
      <div className="map-time-slider-container">
        <div 
          className="time-slider-track" 
          onClick={handleTrackClick}
          ref={trackRef}
        >
          <div className="time-slider-line"></div>
          <div className="time-slider-ticks">
            {laborTrends.map((_, idx) => {
              const tickPos = (idx / (laborTrends.length - 1)) * 100;
              return <div key={idx} className="time-slider-tick" style={{ top: `${tickPos}%` }} />;
            })}
          </div>
          <div 
            ref={markerRef}
            className={`time-slider-marker ${isDragging ? 'dragging' : ''}`} 
            style={{ top: `${sliderPosition}%` }}
            onMouseDown={handleDragStart}
            onTouchStart={handleDragStart}
          />
          {showTooltip && (
            <div 
              ref={tooltipRef}
              className="time-slider-tooltip"
              style={{ top: `${sliderPosition}%` }}
            >
              {formatDate(currentDate)}
            </div>
          )}
        </div>
        <div className="time-slider-labels">
          <div className="time-slider-label">{formatDate(earliestDate)}</div>
          <div className="time-slider-label">{formatDate(latestDate)}</div>
        </div>
      </div>
      <div className="labor-trend-legend">
        <div className="color-gradient"></div>
        <div className="legend-labels">
          <span>High</span>
          <span>Low</span>
        </div>
      </div>
    </>
  );
};

export default MapTimeSlider;
import React, { useEffect } from 'react';
import {
  BarChart, Bar, PieChart, Pie, LineChart, Line,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend,
  ResponsiveContainer, Cell, RadialBarChart, RadialBar
} from 'recharts';
import '../../styles/ParkCharts.css';

// 自定义颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

// 自定义工具提示
const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="custom-tooltip">
        <p className="label">{`${payload[0].name} : ${payload[0].value}`}</p>
        <p className="intro">{payload[0].payload.description || ''}</p>
      </div>
    );
  }
  return null;
};

// 定义一个安全获取函数，用于组件内外部使用
const safeGet = (obj, path, defaultValue = 0) => {
  try {
    if (!obj) return defaultValue;
    const result = path.split('.').reduce((o, key) => (o && o[key] !== undefined) ? o[key] : null, obj);
    return (result !== null && result !== undefined && !isNaN(Number(result))) ? Number(result) : defaultValue;
  } catch (e) {
    console.error(`访问路径 ${path} 时出错:`, e);
    return defaultValue;
  }
};

// 准备图表数据
const prepareChartData = (park, t = {}) => {
  console.log("准备图表数据，接收到的园区数据:", park);
  
  if (!park) {
    console.error("未接收到园区数据");
    return null;
  }
  
  console.log("园区名称:", park.name);
  
  const is304Park = park.name && (
    park.name === "304 Industrial Park (Prachinburi）" || 
    park.name === "304 Industrial Park 1" ||
    park.name.includes("304 Industrial Park")
  );
  
  if (!is304Park) {
    console.warn("当前园区不是304工业园，可能没有详细数据");
  }
  
  if (!park.extra_details) {
    console.error("园区数据中缺少 extra_details 字段");
    return null;
  }
  
  // 土地使用
  const totalArea = park.extra_details?.statistics?.total_area?.value || 0;
  const developedArea = park.extra_details?.statistics?.developed_area?.value || 0;
  const undevelopedArea = Math.max(0, totalArea - developedArea); // 确保不为负数
  
  // 电力分配
  const to304Factories = park.extra_details?.power_plants?.electricity_distribution?.to_304_factories?.value || 0;
  const toDoubleA = park.extra_details?.power_plants?.electricity_distribution?.to_double_a?.value || 0;
  const toEGAT = park.extra_details?.power_plants?.electricity_distribution?.to_egat?.value || 0;
  
  // 土地销售
  const landSold2012 = park.extra_details?.statistics?.land_sold_2012_to_date?.value || 0;
  const expectedLandSales2012 = park.extra_details?.statistics?.expected_land_sales_2012?.value || 0;
  const remainingLandSales = Math.max(0, expectedLandSales2012 - landSold2012);
  
  // 发电能力
  const biofuelCapacity = park.extra_details?.power_plants?.biofuel_capacity?.value || 0;
  const totalCapacity = park.extra_details?.power_plants?.total_capacity?.value || 0;
  const otherCapacity = Math.max(0, totalCapacity - biofuelCapacity);
  
  console.log("获取到的数据值:", {
    totalArea,
    developedArea,
    undevelopedArea,
    to304Factories,
    toDoubleA,
    toEGAT,
    landSold2012,
    expectedLandSales2012,
    remainingLandSales,
    biofuelCapacity,
    totalCapacity,
    otherCapacity
  });
  
  // 构建图表数据
  const chartData = {
    landUsage: [
      { name: t?.developedArea || '已开发区域', value: developedArea },
      { name: t?.undevelopedArea || '未开发区域', value: undevelopedArea }
    ],
    electricityDistribution: [
      { name: t?.factoryUsage || '工厂使用', value: to304Factories },
      { name: t?.doubleAUsage || 'Double A使用', value: toDoubleA },
      { name: t?.toEGAT || '输送至EGAT', value: toEGAT }
    ],
    landSales: [
      { name: t?.soldSince2012 || '2012年已售', value: landSold2012 },
      { name: t?.expectedRemaining2012 || '2012年预期剩余', value: remainingLandSales }
    ],
    powerCapacity: [
      { name: t?.biofuel || '生物燃料', value: biofuelCapacity },
      { name: t?.otherEnergy || '其他能源', value: otherCapacity }
    ]
  };
  
  console.log("处理后的图表数据:", chartData);
  return chartData;
};

const ParkCharts = ({ park, t }) => {
  useEffect(() => {
    console.log("ParkCharts 组件接收到的 park 数据:", park);
    if (park) {
      console.log("园区名称:", park.name);
      console.log("extra_details 存在:", !!park.extra_details);
      if (park.extra_details) {
        console.log("statistics 存在:", !!park.extra_details.statistics);
        console.log("power_plants 存在:", !!park.extra_details.power_plants);
        console.log("double_a_investment 存在:", !!park.extra_details.double_a_investment);
      }
    }
  }, [park, t]);
  
  if (!park) {
    console.error("ParkCharts: park 数据为空");
    return <div className="chart-error">{t?.cannotLoadChartData || '无法加载图表数据：数据为空'}</div>;
  }
  
  let chartData;
  try {
    chartData = prepareChartData(park, t);
    if (!chartData) {
      console.error("无法准备图表数据");
      return <div className="chart-error">{t?.dataProcessingFailed || '无法加载图表数据：数据处理失败'}</div>;
    }
  } catch (error) {
    console.error("渲染图表时发生错误:", error);
    return <div className="chart-error">{t?.chartRenderingFailed || '图表渲染失败'}: {error.message}</div>;
  }
  
  // 强制显示所有图表（如有数据则渲染）
  const hasLandUsageData = true;
  const hasElectricityData = true;
  const hasLandSalesData = true;
  const hasPowerCapacityData = true;
  
  console.log("图表数据有效性检查 (强制显示):", {
    hasLandUsageData,
    hasElectricityData,
    hasLandSalesData,
    hasPowerCapacityData
  });
  
  return (
    <section className="statistics-section">
      <h2><i className="section-icon chart-icon">📊</i> {t?.parkStatistics || '园区统计'}</h2>
      
      <div className="charts-container">
        {/* 第一行：土地使用、电力分配、土地销售 */}
        <div className="chart-row">
          {hasLandUsageData && (
            <div className="chart-card">
              <h3>{t?.landUsage || '土地使用情况'}</h3>
              <div className="chart-description">
                <p>
                  {t?.totalArea || '总面积'}: {park.extra_details?.statistics?.total_area?.value || '未知'}{' '}
                  {park.extra_details?.statistics?.total_area?.unit || ''}
                </p>
                <p>
                  {t?.developedArea || '已开发面积'}: {park.extra_details?.statistics?.developed_area?.value || '未知'}{' '}
                  {park.extra_details?.statistics?.developed_area?.unit || ''}
                </p>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={chartData.landUsage}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {chartData.landUsage.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}
          
          {hasElectricityData && (
            <div className="chart-card">
              <h3>{t?.electricityDistribution || '电力分配'}</h3>
              <div className="chart-description">
                <p>
                  {t?.totalCapacity || '总发电量'}: {park.extra_details?.power_plants?.total_capacity?.value || '未知'}{' '}
                  {park.extra_details?.power_plants?.total_capacity?.unit || ''}
                </p>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={chartData.electricityDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {chartData.electricityDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}
          
          {hasLandSalesData && (
            <div className="chart-card">
              <h3>{t?.landSales || '土地销售情况'}</h3>
              <div className="chart-description">
                <p>
                  {t?.expectedSales2012 || '2012年预期销售'}: {park.extra_details?.statistics?.expected_land_sales_2012?.value || '未知'}{' '}
                  {park.extra_details?.statistics?.expected_land_sales_2012?.unit || ''}
                </p>
                <p>
                  {t?.annualAverageSales || '年均销售'}: {park.extra_details?.statistics?.annual_average_land_sales?.value || '未知'}{' '}
                  {park.extra_details?.statistics?.annual_average_land_sales?.unit || ''}
                </p>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={[
                    { name: t?.soldToDate || '已售出', value: chartData.landSales[0].value },
                    { name: t?.expectedTotal || '预期总量', value: chartData.landSales[0].value + chartData.landSales[1].value },
                    { name: t?.annualAverage || '年均', value: park.extra_details?.statistics?.annual_average_land_sales?.value || 0 }
                  ]}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" name={t?.landArea || '土地面积'} fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>

        {/* 第二行：发电能力、Double A投资情况、生产能力 */}
        <div className="chart-row">
          {hasPowerCapacityData && (
            <div className="chart-card">
              <h3>{t?.powerGeneration || '发电能力'}</h3>
              <div className="chart-description">
                <p>
                  {t?.totalPowerPlants || '电厂总数'}: {park.extra_details?.power_plants?.total_plants?.value || '未知'}{' '}
                  {park.extra_details?.power_plants?.total_plants?.unit || ''}
                </p>
                <p>
                  {t?.biofuelCapacity || '生物燃料发电量'}: {park.extra_details?.power_plants?.biofuel_capacity?.value || '未知'}{' '}
                  {park.extra_details?.power_plants?.biofuel_capacity?.unit || ''}
                </p>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={chartData.powerCapacity}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {chartData.powerCapacity.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}

          {park.extra_details?.double_a_investment && (
            <div className="chart-card">
              <h3>{t?.doubleAInvestment || 'Double A投资情况'}</h3>
              <div className="chart-description">
                <p>
                  {t?.thirdPaperPlantInvestment || '第三造纸厂投资'}: {park.extra_details?.double_a_investment?.third_paper_plant?.value?.toLocaleString() || '未知'}{' '}
                  {park.extra_details?.double_a_investment?.third_paper_plant?.unit || ''}
                </p>
                <p>
                  {t?.lastYearSales || '去年销售额'}: {park.extra_details?.double_a_investment?.last_year_sales?.value?.toLocaleString() || '未知'}{' '}
                  {park.extra_details?.double_a_investment?.last_year_sales?.unit || ''}
                </p>
                <p>
                  {t?.salesGrowthProjection || '销售增长预期'}: {park.extra_details?.double_a_investment?.sales_growth_projection?.value || '未知'}%
                </p>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={[
                    {
                      name: t?.investment || '投资',
                      [t?.thirdPaperPlant || '第三造纸厂']: safeGet(park, 'extra_details.double_a_investment.third_paper_plant.value', 0) / 1000000,
                      [t?.lastYearSales || '去年销售额']: safeGet(park, 'extra_details.double_a_investment.last_year_sales.value', 0) / 1000000
                    }
                  ]}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis label={{ value: t?.millionBaht || '百万泰铢', angle: -90, position: 'insideLeft' }} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey={t?.thirdPaperPlant || '第三造纸厂'} fill="#8884d8" />
                  <Bar dataKey={t?.lastYearSales || '去年销售额'} fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}

          {park.extra_details?.double_a_investment && (
            <div className="chart-card">
              <h3>{t?.productionCapacity || '生产能力'}</h3>
              <div className="chart-description">
                <p>
                  {t?.initialCapacity || '初始年产能'}: {park.extra_details?.double_a_investment?.initial_annual_capacity?.value?.toLocaleString() || '未知'}{' '}
                  {park.extra_details?.double_a_investment?.initial_annual_capacity?.unit || ''}
                </p>
                <p>
                  {t?.fullCapacity || '三厂满负荷产能'}: {park.extra_details?.double_a_investment?.full_capacity_three_plants?.value?.toLocaleString() || '未知'}{' '}
                  {park.extra_details?.double_a_investment?.full_capacity_three_plants?.unit || ''}
                </p>
              </div>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={[
                    {
                      name: t?.paperProduction || '纸张生产',
                      [t?.initialCapacity || '初始年产能']: safeGet(park, 'extra_details.double_a_investment.initial_annual_capacity.value', 0),
                      [t?.fullCapacity || '三厂满负荷产能']: safeGet(park, 'extra_details.double_a_investment.full_capacity_three_plants.value', 0)
                    }
                  ]}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis label={{ value: t?.tonnes || '吨', angle: -90, position: 'insideLeft' }} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey={t?.initialCapacity || '初始年产能'} fill="#8884d8" />
                  <Bar dataKey={t?.fullCapacity || '三厂满负荷产能'} fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default ParkCharts;

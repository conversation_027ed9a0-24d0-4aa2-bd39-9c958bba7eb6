import React from 'react';
import { useMap } from 'react-leaflet';
import { FiPlus, FiMinus } from 'react-icons/fi';
import '../../styles/DetailMapZoomControl.css';

const DetailMapZoomControl = ({ isDarkMode = false }) => {
  const map = useMap();

  const handleZoomIn = () => {
    map.zoomIn(1);
  };

  const handleZoomOut = () => {
    map.zoomOut(1);
  };

  return (
    <div className={`detail-zoom-controls ${isDarkMode ? 'dark-mode' : ''}`}>
      <button
        className="detail-zoom-button"
        onClick={handleZoomIn}
        aria-label="Zoom in"
      >
        <FiPlus />
      </button>
      <button
        className="detail-zoom-button"
        onClick={handleZoomOut}
        aria-label="Zoom out"
      >
        <FiMinus />
      </button>
    </div>
  );
};

export default DetailMapZoomControl;

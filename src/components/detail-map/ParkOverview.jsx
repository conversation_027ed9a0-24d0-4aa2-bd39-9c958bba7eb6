import React, { useState } from 'react';
// 导入专用的 CSS 文件，而不是使用 ParkDetailPage.css
import '../../styles/ParkOverview.css';

const ParkOverview = ({ park, t }) => {
  // 如果 t 未定义则使用空对象，保证后续访问不会出错
  const effectiveT = t || {};
  const [imagesLoaded, setImagesLoaded] = useState({ main: false });
  
  const handleImageLoad = (type) => {
    setImagesLoaded(prev => ({ ...prev, [type]: true }));
  };

  const handleImageError = (e, type) => {
    e.target.src = '/placeholder.png';
    setImagesLoaded(prev => ({ ...prev, [type]: true }));
  };

  // 检查 park 是否有效
  if (!park) {
    return (
      <section className="overview-section redesigned-overview">
        <h2>{effectiveT.overview || '概览'}</h2>
        <p>{effectiveT.dataNotAvailable || 'Data not available'}</p>
      </section>
    );
  }
  
  // 判断是否为304工业园
  const is304Park = (park.name === "304 Industrial Park (Prachinburi）");

  // 格式化基础设施名称 - 将下划线替换为空格并首字母大写
  const formatInfraName = (name) => {
    if (!name) return '';
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // 判断是否为特殊结构的基础设施数据
  const isSpecialInfraStructure = (infra) => {
    if (!infra || typeof infra !== 'object') return false;
    
    // 检查是否为 water_supply 类型结构
    if (infra.sources || infra.reservoir_capacity || infra.production_capacity) {
      return true;
    }
    
    // 检查是否为 Internet 类型结构
    if (infra.provider || infra.speed || infra.technology) {
      return true;
    }
    
    return false;
  };

  // 添加缺失的 renderSpecialInfraStructure 函数
  const renderSpecialInfraStructure = (infra) => {
    return (
      <div className="structured-infra">
        {Object.entries(infra).map(([key, value], index) => {
          // 跳过空值
          if (!value) return null;
          
          const formattedKey = formatInfraName(key);
          return (
            <div key={index} className="infra-item">
              <span className="infra-label">{effectiveT[key] || formattedKey}:</span>
              <span className="infra-value">
                {Array.isArray(value) ? value.join(', ') : value}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <section className="overview-section redesigned-overview">
      {/* 添加园区照片 - 直接放在顶部，减少嵌套 */}
      {is304Park && (
        <div className="park-hero overview-hero">
          {!imagesLoaded.main && (
            <div className="image-placeholder main-placeholder">
              <div className="loading-spinner"></div>
            </div>
          )}
          <img
            src="/images/304_prachinburi.jpg"
            alt="304 Industrial Park"
            className={`main-image ${imagesLoaded.main ? 'loaded' : 'loading'}`}
            onLoad={() => handleImageLoad('main')}
            onError={(e) => handleImageError(e, 'main')}
          />
        </div>
      )}

      <div className="overview-content">
        <p className="overview-description">{park.overview || effectiveT.noOverviewAvailable || '暂无概览信息'}</p>

        <div className="overview-grid">
          <div className="info-card">
            <span className="icon">📍</span>
            <div>
              <h3>{effectiveT.province || '省份'}</h3>
              <p>{park.province || park.extra_details?.location?.province || effectiveT.unknown || '未知'}</p>
            </div>
          </div>

          <div className="info-card">
            <span className="icon">🏙️</span>
            <div>
              <h3>{effectiveT.cityZoning || '城市分区'}</h3>
              <p>{park.extra_details?.location?.city_zoning || effectiveT.unknown || '未知'}</p>
            </div>
          </div>

          <div className="info-card">
            <span className="icon">💰</span>
            <div>
              <h3>{effectiveT.saleMethod || '销售方式'}</h3>
              <p>{park.extra_details?.sale_method?.method || effectiveT.unknown || '未知'}</p>
            </div>
          </div>

          <div className="info-card">
            <span className="icon">🔧</span>
            <div>
              <h3>{effectiveT.managementStatus || '管理状态'}</h3>
              <p>{park.extra_details?.ie_attribute?.status || effectiveT.unknown || '未知'}</p>
            </div>
          </div>

          <div className="info-card">
            <span className="icon">🧾</span>
            <div>
              <h3>{effectiveT.managementFee || '管理费'}</h3>
              <p>{park.extra_details?.common_fee?.["Land General Zone"] || effectiveT.unknown || '未知'}</p>
            </div>
          </div>
        </div>

        <h3 className="section-subtitle">{effectiveT.infrastructure || '基础设施'}</h3>
        <div className="infrastructure-grid">
          {park.extra_details?.infrastructure ? (
            Object.entries(park.extra_details.infrastructure).map(([key, infra], index) => (
              <div key={index} className="infra-card" data-type={key}>
                <h4>{effectiveT[key] || formatInfraName(key)}</h4>
                <div className="infra-content">
                  {typeof infra === 'object' ? (
                    isSpecialInfraStructure(infra) ? (
                      // 处理特殊结构的基础设施数据
                      renderSpecialInfraStructure(infra)
                    ) : (
                      // 处理普通对象类型
                      <div className="structured-infra">
                        {Object.entries(infra).map(([subKey, subValue], subIndex) => (
                          <div key={subIndex} className="infra-item">
                            <span className="infra-label">{formatInfraName(subKey)}:</span>
                            <span className="infra-value">{subValue}</span>
                          </div>
                        ))}
                      </div>
                    )
                  ) : (
                    // 处理字符串类型
                    <p>{infra || effectiveT.unknown || '未知'}</p>
                  )}
                </div>
              </div>
            ))
          ) : (
            <p className="no-data-message">{effectiveT.noInfrastructureAvailable || '暂无基础设施信息'}</p>
          )}
        </div>
      </div>
    </section>
  );
};

export default ParkOverview;

import React, { useState, useEffect } from 'react';
import '../../styles/EconomicHotspotLayer.css';
import EconomicHotspotHelp from './EconomicHotspotHelp';

/**
 * 经济热点控制面板
 * 用于控制经济地理热点图层的参数，完全重新设计
 *
 * @param {boolean} isDarkMode - 是否为暗色模式
 * @param {function} onClose - 关闭面板的回调函数
 * @param {object} settings - 当前设置
 * @param {function} onSettingsChange - 设置变更的回调函数
 * @param {object} t - 翻译对象
 * @param {string} country - 当前选择的国家
 */
const EconomicHotspotControls = ({
  isDarkMode = false,
  onClose,
  settings,
  onSettingsChange,
  t = {},
  country = 'THAILAND'
}) => {
  // 本地状态，用于跟踪设置变化和帮助模态框的显示状态
  const [helpVisible, setHelpVisible] = useState(false);
  const [localSettings, setLocalSettings] = useState({
    modelType: 'lq',
    intensity: 50,
    industry: 'manufacturing',
    radius: 50000,
    country: country,
    showDetails: true,
    showLabels: true,
    showCircles: true,
    ...settings
  });

  // 当外部设置变化时更新本地状态
  useEffect(() => {
    setLocalSettings(prevSettings => ({
      ...prevSettings,
      ...settings
    }));
  }, [settings]);

  // 处理设置变化
  const handleSettingChange = (key, value) => {
    const newSettings = {
      ...localSettings,
      [key]: value
    };

    setLocalSettings(newSettings);

    // 通知父组件设置已更改
    if (onSettingsChange) {
      onSettingsChange(newSettings);
    }
  };

  // 获取半径显示文本
  const getRadiusDisplay = (radiusInMeters) => {
    if (radiusInMeters >= 1000) {
      return `${(radiusInMeters / 1000).toFixed(0)} km`;
    }
    return `${radiusInMeters} m`;
  };

  // 获取模型类型解释
  const getModelExplanation = () => {
    if (localSettings.modelType === 'lq') {
      return t.lqExplanation || '区位商模型(Location Quotient)用于分析特定地区行业的相对集中度。计算方式是将地区行业占比除以全国行业占比。LQ>1.3表示高度集中，属于核心经济区；LQ<0.7可能适合政策补贴，属于潜力区域。点击标记可查看详细分析。';
    } else {
      return t.gravityExplanation || '重力模型使用数学公式模拟经济中心对周围区域的影响力，展示主要市场的经济辐射范围。大圈代表重要经济中心，辐射范围和强度反映了其经济影响力。点击标记可查看详细数据。';
    }
  };

  return (
    <div className={`economic-hotspot-controls ${isDarkMode ? 'dark-mode' : ''}`}>
      <div className="controls-header">
        <h3>{t.economicHotspotSettings || '经济热点设置'}</h3>
        <button className="close-button" onClick={onClose} title={t.close || '关闭'}>×</button>
      </div>

      <div className="controls-content">
        {/* 模型选择部分，使用更直观的按钮组 */}
        <div className="control-section">
          <h4>{t.modelType || '选择模型类型'}</h4>
          <div className="model-selector">
            <button 
              className={`model-button ${localSettings.modelType === 'lq' ? 'active' : ''}`}
              onClick={() => handleSettingChange('modelType', 'lq')}
            >
              <div className="model-icon lq-icon"></div>
              <span>{t.locationQuotient || '区位商模型'}</span>
            </button>
            <button 
              className={`model-button ${localSettings.modelType === 'gravity' ? 'active' : ''}`}
              onClick={() => handleSettingChange('modelType', 'gravity')}
            >
              <div className="model-icon gravity-icon"></div>
              <span>{t.gravityModel || '重力模型'}</span>
            </button>
          </div>
          <div className="model-explanation">
            {getModelExplanation()}
          </div>
        </div>

        <div className="divider"></div>

        {/* 根据选择的模型显示不同的配置选项 */}
        {localSettings.modelType === 'lq' ? (
          <div className="control-section">
            <h4>{t.lqSettings || '区位商分析设置'}</h4>
            <div className="control-group">
              <label htmlFor="industry">{t.industry || '行业'}</label>
              <select
                id="industry"
                value={localSettings.industry}
                onChange={(e) => handleSettingChange('industry', e.target.value)}
                title={t.selectIndustry || '选择行业'}
              >
                <option value="manufacturing">{t.manufacturing || '制造业'}</option>
                <option value="government">{t.government || '政府部门'}</option>
                <option value="private">{t.privateEnterprise || '私营企业'}</option>
                <option value="self-employed">{t.selfEmployed || '个体经营'}</option>
              </select>
            </div>

            <div className="lq-legend">
              <h5>{t.lqInterpretation || 'LQ值解释'}</h5>
              <div className="lq-scale">
                <div className="lq-marker" style={{left: '10%'}}>
                  <div className="lq-dot" style={{backgroundColor: '#5AC8FA'}}></div>
                  <span>0.7</span>
                </div>
                <div className="lq-marker" style={{left: '50%'}}>
                  <div className="lq-dot" style={{backgroundColor: '#FFCC00'}}></div>
                  <span>1.0</span>
                </div>
                <div className="lq-marker" style={{left: '90%'}}>
                  <div className="lq-dot" style={{backgroundColor: '#FF3B30'}}></div>
                  <span>1.3+</span>
                </div>
                <div className="lq-line"></div>
              </div>
              <div className="lq-meanings">
                <div className="lq-meaning">
                  <span className="meaning-title">{t.subsidyArea || '补贴洼地'}</span>
                  <span className="meaning-desc">{t.subsidyAreaDesc || 'LQ < 0.7，行业集中度低，可能有政策优势'}</span>
                </div>
                <div className="lq-meaning">
                  <span className="meaning-title">{t.normalArea || '一般市场'}</span>
                  <span className="meaning-desc">{t.normalAreaDesc || '0.7 ≤ LQ ≤ 1.3，行业集中度中等'}</span>
                </div>
                <div className="lq-meaning">
                  <span className="meaning-title">{t.coreMarket || '核心市场'}</span>
                  <span className="meaning-desc">{t.coreMarketDesc || 'LQ > 1.3，行业高度集中，市场优势明显'}</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="control-section">
            <h4>{t.gravitySettings || '重力模型设置'}</h4>
            <div className="gravity-legend">
              <h5>{t.gravityInterpretation || '重力模型解释'}</h5>
              <div className="gravity-meanings">
                <div className="gravity-meaning">
                  <div className="gravity-icon-large"></div>
                  <div className="gravity-meaning-text">
                    <span className="meaning-title">{t.majorCenter || '主要中心'}</span>
                    <span className="meaning-desc">{t.majorCenterDesc || '经济发展水平高，对周边地区有较强辐射能力'}</span>
                  </div>
                </div>
                <div className="gravity-meaning">
                  <div className="gravity-icon-small"></div>
                  <div className="gravity-meaning-text">
                    <span className="meaning-title">{t.minorCenter || '次要中心'}</span>
                    <span className="meaning-desc">{t.minorCenterDesc || '区域性经济中心，市场潜力较好'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="divider"></div>

        {/* 通用设置部分 */}
        <div className="control-section">
          <h4>{t.commonSettings || '通用设置'}</h4>
          
          <div className="control-group">
            <label htmlFor="intensity">
              {t.intensity || '强度'}
              <span className="tooltip-icon" title={t.intensityTooltip || '调整热点的视觉强度和数量'}>?</span>
            </label>
            <div className="range-container">
              <input
                type="range"
                id="intensity"
                min="10"
                max="100"
                value={localSettings.intensity}
                onChange={(e) => handleSettingChange('intensity', parseInt(e.target.value))}
              />
              <span className="range-value">{localSettings.intensity}%</span>
            </div>
            <div className="setting-explanation">
              {t.intensityExplanation || '调高强度可以显示更多的热点地区，调低则只显示最重要的区域'}
            </div>
          </div>

          <div className="control-group">
            <label htmlFor="radius">
              {t.serviceRadius || '服务半径'}
              <span className="tooltip-icon" title={t.serviceRadiusTooltip || '调整热点的影响范围半径'}>?</span>
            </label>
            <div className="range-container">
              <input
                type="range"
                id="radius"
                min="5000"
                max="100000"
                step="5000"
                value={localSettings.radius}
                onChange={(e) => handleSettingChange('radius', parseInt(e.target.value))}
              />
              <span className="range-value">{getRadiusDisplay(localSettings.radius)}</span>
            </div>
            <div className="setting-explanation">
              {t.radiusExplanation || '半径代表经济中心的影响范围，较大的半径表示较强的经济辐射能力'}
            </div>
          </div>

          <div className="checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={localSettings.showDetails}
                onChange={(e) => handleSettingChange('showDetails', e.target.checked)}
              />
              {t.showDetailedAnalysis || '显示详细分析'}
            </label>
            <div className="setting-explanation">
              {t.detailsExplanation || '启用此选项可在点击标记时显示详细的经济数据分析和统计展示'}
            </div>
          </div>
          
          <div className="additional-options">
            <h5>{t.displayOptions || '显示选项'}</h5>
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={localSettings.showLabels !== false}
                  onChange={(e) => handleSettingChange('showLabels', e.target.checked)}
                />
                {t.showLabels || '显示标签名称'}
              </label>
            </div>
            <div className="checkbox-group">
              <label className="checkbox-label">
                <input
                  type="checkbox"
                  checked={localSettings.showCircles !== false}
                  onChange={(e) => handleSettingChange('showCircles', e.target.checked)}
                />
                {t.showRadiusCircles || '显示半径圆'}
              </label>
            </div>
          </div>
        </div>

        <div className="settings-footer">
          <div className="action-buttons">
            <button className="help-button" title={t.helpTitle || '查看帮助'} onClick={() => setHelpVisible(true)}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2ZM13,19H11V17H13V19ZM15.07,11.25L14.17,12.17C13.45,12.9 13,13.5 13,15H11V14.5C11,13.4 11.45,12.4 12.17,11.67L13.41,10.41C13.78,10.05 14,9.55 14,9C14,7.9 13.1,7 12,7C10.9,7 10,7.9 10,9H8C8,6.79 9.79,5 12,5C14.21,5 16,6.79 16,9C16,9.88 15.64,10.68 15.07,11.25Z" fill="currentColor"/>
              </svg>
            </button>
            <button className="reset-button" onClick={() => {
              const defaultSettings = {
                modelType: 'lq',
                intensity: 50,
                industry: 'manufacturing',
                radius: 50000,
                country: country,
                showDetails: true,
                showLabels: true,
                showCircles: true
              };
              setLocalSettings(defaultSettings);
              if (onSettingsChange) onSettingsChange(defaultSettings);
            }}>
              {t.resetToDefault || '重置为默认值'}
            </button>
            <button className="apply-button" onClick={onClose}>
              {t.applySettings || '应用设置'}
            </button>
          </div>
          <div className="tips-container">
            <p className="settings-tip">{t.settingsTip || '调整参数后会自动应用变更'}</p>
            <p className="settings-tip">{t.clickMarkerTip || '点击地图上的标记查看详细信息'}</p>
          </div>
        </div>
      </div>
      {/* 帮助模态框 */}
      <EconomicHotspotHelp 
        isVisible={helpVisible}
        onClose={() => setHelpVisible(false)}
        isDarkMode={isDarkMode}
        t={t}
      />
    </div>
  );
};

export default EconomicHotspotControls;

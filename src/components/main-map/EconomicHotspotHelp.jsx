import React from 'react';
import '../../styles/EconomicHotspotLayer.css';

/**
 * 经济热点帮助组件
 * 用于提供关于经济热点功能的帮助信息
 * 
 * @param {boolean} isVisible - 控制帮助模态框的显示与隐藏
 * @param {function} onClose - 关闭模态框的回调函数
 * @param {boolean} isDarkMode - 是否为暗色模式
 * @param {object} t - 翻译对象
 */
const EconomicHotspotHelp = ({ isVisible, onClose, isDarkMode = false, t = {} }) => {
  if (!isVisible) return null;

  return (
    <div className={`economic-help-modal ${isDarkMode ? 'dark-mode' : ''}`}>
      <div className="help-modal-content">
        <div className="help-header">
          <h2>{t.economicHotspotHelp || '经济热点使用帮助'}</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        
        <div className="help-content">
          <section className="help-section">
            <h3>{t.whatAreEconomicHotspots || '什么是经济热点？'}</h3>
            <p>{t.economicHotspotsExplanation || '经济热点是特定行业或经济活动集中的地理区域。这些区域在地图上以不同颜色的标记显示，帮助您识别核心市场区域和潜在的补贴地区。'}</p>
          </section>
          
          <section className="help-section">
            <h3>{t.howToUse || '如何使用'}</h3>
            <ul>
              <li>{t.chooseModelTip || '选择模型类型：区位商模型用于分析行业集中度；重力模型用于展示经济辐射范围。'}</li>
              <li>{t.adjustSettingsTip || '调整设置：修改强度、行业和服务半径，查看不同场景下的经济热点分布。'}</li>
              <li>{t.clickMarkersTip || '点击标记：查看详细的经济分析数据和统计信息。'}</li>
              <li>{t.useDisplayOptionsTip || '使用显示选项：控制标签和半径圆的显示，使地图更符合您的需求。'}</li>
            </ul>
          </section>
          
          <section className="help-section">
            <h3>{t.modelsExplained || '模型解释'}</h3>
            <div className="model-explanation">
              <h4>{t.lqModel || '区位商模型 (LQ)'}</h4>
              <p>{t.lqModelExplanation || '区位商是衡量特定地区特定行业集中程度的指标。计算方式是将地区行业占比除以全国行业占比。'}</p>
              <ul>
                <li><strong>{t.lqHigh || 'LQ > 1.3'}:</strong> {t.lqHighExplanation || '高度集中，表示该地区在此行业具有竞争优势。'}</li>
                <li><strong>{t.lqNormal || '0.7 ≤ LQ ≤ 1.3'}:</strong> {t.lqNormalExplanation || '一般水平，行业分布基本符合全国平均水平。'}</li>
                <li><strong>{t.lqLow || 'LQ < 0.7'}:</strong> {t.lqLowExplanation || '集中度低，可能适合政策补贴，有发展潜力。'}</li>
              </ul>
            </div>
            
            <div className="model-explanation">
              <h4>{t.gravityModel || '重力模型'}</h4>
              <p>{t.gravityModelExplanation || '重力模型用于模拟经济中心对周围区域的影响力和辐射范围。根据经济质量（如GDP、就业人数等）和距离计算影响力。'}</p>
              <p>{t.gravityModelImplication || '标记和圆的大小表示经济影响力的强度，辐射范围表示其影响覆盖的区域。'}</p>
            </div>
          </section>
          
          <section className="help-section">
            <h3>{t.practicalApplications || '实际应用'}</h3>
            <ul>
              <li>{t.marketAnalysis || '市场分析：识别特定行业的核心市场，了解竞争格局。'}</li>
              <li>{t.investmentDecisions || '投资决策：评估不同地区的产业集中度，寻找投资机会。'}</li>
              <li>{t.policyPlanning || '政策规划：发现需要扶持的产业洼地，精准制定产业政策。'}</li>
              <li>{t.supplyChainOptimization || '供应链优化：基于产业集群分布，优化供应链布局。'}</li>
            </ul>
          </section>
        </div>
        
        <div className="help-footer">
          <button className="help-ok-button" onClick={onClose}>{t.gotIt || '我知道了'}</button>
        </div>
      </div>
    </div>
  );
};

export default EconomicHotspotHelp;

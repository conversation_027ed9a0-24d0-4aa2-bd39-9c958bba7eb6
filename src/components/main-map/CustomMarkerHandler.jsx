import React, { useEffect, useRef } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import { createCustomMarker } from '../../utils/markerUtils';

/**
 * 处理自定义标记的组件
 * 当用户点击地图时，在点击位置添加自定义标记
 */
const CustomMarkerHandler = ({
  isActive,
  customMarkers,
  setCustomMarkers,
  markerColor = 'green',
  t // 翻译对象
}) => {
  const map = useMap();
  const markersLayerRef = useRef(null);

  // 可用的标记颜色
  const availableColors = ['green', 'purple', 'orange', 'blue', 'red'];

  // 初始化标记图层
  useEffect(() => {
    markersLayerRef.current = L.layerGroup().addTo(map);

    // 清理函数
    return () => {
      if (markersLayerRef.current) {
        map.removeLayer(markersLayerRef.current);
      }
    };
  }, [map]);

  // 处理地图点击事件
  useEffect(() => {
    if (!map || !isActive) return;

    const handleMapClick = (e) => {
      const { lat, lng } = e.latlng;

      // 创建新标记
      const newMarker = {
        id: Date.now(), // 使用时间戳作为唯一ID
        position: [lat, lng],
        color: markerColor,
        label: t?.markerLabel || '标记名称' // 默认标签
      };

      // 更新标记列表
      setCustomMarkers(prev => [...prev, newMarker]);
    };

    // 添加点击事件监听器
    map.on('click', handleMapClick);

    // 清理函数
    return () => {
      map.off('click', handleMapClick);
    };
  }, [map, isActive, markerColor, setCustomMarkers, t]);

  // 渲染标记
  useEffect(() => {
    if (!map || !markersLayerRef.current) return;

    // 清除现有标记
    markersLayerRef.current.clearLayers();

    // 添加所有标记
    customMarkers.forEach(marker => {
      const icon = createCustomMarker(marker.color || 'green');
      const leafletMarker = L.marker(marker.position, { icon });

      // 创建弹出窗口内容
      const popupContent = document.createElement('div');
      popupContent.className = 'custom-marker-popup';

      // 创建标题区域（可编辑）
      const createTitleDisplay = () => {
        // 创建一个固定高度的容器
        const header = document.createElement('div');
        header.className = 'custom-marker-popup-header';

        // 创建标题显示元素
        const titleDisplay = document.createElement('div');
        titleDisplay.className = 'custom-marker-popup-title-display';

        // 添加编辑图标
        const editIcon = document.createElement('span');
        editIcon.className = 'edit-icon';
        editIcon.innerHTML = '✏️';
        editIcon.title = t?.editLabel || '编辑标签';
        titleDisplay.appendChild(editIcon);

        // 添加标题文本
        const titleText = document.createElement('span');
        titleText.className = 'title-text';
        titleText.textContent = marker.label || (t?.markerLabel || '标记名称');
        titleDisplay.appendChild(titleText);

        // 创建编辑容器（初始隐藏）
        const editContainer = document.createElement('div');
        editContainer.className = 'custom-marker-popup-edit-container';
        editContainer.style.display = 'none';

        // 创建输入框
        const labelInput = document.createElement('input');
        labelInput.type = 'text';
        labelInput.className = 'custom-marker-popup-label-input';
        labelInput.value = marker.label || '';
        labelInput.placeholder = t?.markerLabel || '标记名称';

        // 创建编辑操作按钮区域
        const editActions = document.createElement('div');
        editActions.className = 'custom-marker-popup-edit-actions';

        // 确认按钮
        const confirmButton = document.createElement('button');
        confirmButton.className = 'custom-marker-popup-edit-button confirm';
        confirmButton.innerHTML = '✓';

        // 取消按钮
        const cancelButton = document.createElement('button');
        cancelButton.className = 'custom-marker-popup-edit-button cancel';
        cancelButton.innerHTML = '✕';

        // 添加按钮到操作区
        editActions.appendChild(confirmButton);
        editActions.appendChild(cancelButton);

        // 构建编辑容器
        editContainer.appendChild(labelInput);
        editContainer.appendChild(editActions);

        // 将两个视图都添加到header
        header.appendChild(titleDisplay);
        header.appendChild(editContainer);

        // 点击标题进入编辑模式
        titleDisplay.onclick = (e) => {
          e.preventDefault();
          e.stopPropagation();

          // 切换显示/隐藏
          titleDisplay.style.display = 'none';
          editContainer.style.display = 'block';

          // 自动聚焦输入框
          setTimeout(() => labelInput.focus(), 50);
        };

        // 确认按钮点击事件
        confirmButton.onclick = (e) => {
          e.preventDefault();
          e.stopPropagation();

          // 更新标记标题
          const newLabel = labelInput.value.trim();
          marker.label = newLabel || (t?.markerLabel || '标记名称');
          titleText.textContent = marker.label;

          // 切换回显示模式
          editContainer.style.display = 'none';
          titleDisplay.style.display = 'flex';
        };

        // 取消按钮点击事件
        cancelButton.onclick = (e) => {
          e.preventDefault();
          e.stopPropagation();

          // 恢复原始值
          labelInput.value = marker.label || '';

          // 切换回显示模式
          editContainer.style.display = 'none';
          titleDisplay.style.display = 'flex';
        };

        // 处理回车键确认和ESC取消
        labelInput.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            confirmButton.click();
          } else if (e.key === 'Escape') {
            cancelButton.click();
          }
        });

        return header;
      };

      // 添加标题区域
      popupContent.appendChild(createTitleDisplay());

      // 坐标信息
      const coords = document.createElement('div');
      coords.className = 'custom-marker-popup-coords';

      // 纬度
      const latItem = document.createElement('div');
      latItem.className = 'custom-marker-popup-coord-item';

      const latLabel = document.createElement('span');
      latLabel.className = 'custom-marker-popup-coord-label';
      latLabel.textContent = t?.latitude || 'Latitude';
      latItem.appendChild(latLabel);

      const latValue = document.createElement('span');
      latValue.className = 'custom-marker-popup-coord-value';

      const latText = document.createElement('span');
      latText.textContent = marker.position[0].toFixed(6);
      latValue.appendChild(latText);

      // 添加复制图标
      const latCopyIcon = document.createElement('span');
      latCopyIcon.className = 'copy-icon';
      latCopyIcon.innerHTML = '📋';
      latCopyIcon.title = t?.copyToClipboard || '复制到剪贴板';
      latCopyIcon.onclick = (e) => {
        // 阻止事件冒泡，防止关闭弹窗
        e.preventDefault();
        e.stopPropagation();

        navigator.clipboard.writeText(marker.position[0].toFixed(6))
          .then(() => {
            latCopyIcon.innerHTML = '✓';
            setTimeout(() => {
              latCopyIcon.innerHTML = '📋';
            }, 1000);
          });
      };
      latValue.appendChild(latCopyIcon);

      latItem.appendChild(latValue);
      coords.appendChild(latItem);

      // 经度
      const lngItem = document.createElement('div');
      lngItem.className = 'custom-marker-popup-coord-item';

      const lngLabel = document.createElement('span');
      lngLabel.className = 'custom-marker-popup-coord-label';
      lngLabel.textContent = t?.longitude || 'Longitude';
      lngItem.appendChild(lngLabel);

      const lngValue = document.createElement('span');
      lngValue.className = 'custom-marker-popup-coord-value';

      const lngText = document.createElement('span');
      lngText.textContent = marker.position[1].toFixed(6);
      lngValue.appendChild(lngText);

      // 添加复制图标
      const lngCopyIcon = document.createElement('span');
      lngCopyIcon.className = 'copy-icon';
      lngCopyIcon.innerHTML = '📋';
      lngCopyIcon.title = t?.copyToClipboard || '复制到剪贴板';
      lngCopyIcon.onclick = (e) => {
        // 阻止事件冒泡，防止关闭弹窗
        e.preventDefault();
        e.stopPropagation();

        navigator.clipboard.writeText(marker.position[1].toFixed(6))
          .then(() => {
            lngCopyIcon.innerHTML = '✓';
            setTimeout(() => {
              lngCopyIcon.innerHTML = '📋';
            }, 1000);
          });
      };
      lngValue.appendChild(lngCopyIcon);

      lngItem.appendChild(lngValue);
      coords.appendChild(lngItem);

      popupContent.appendChild(coords);

      // 颜色选择器（不需要标签文字）
      const colorOptions = document.createElement('div');
      colorOptions.className = 'custom-marker-popup-color-options';

      // 添加颜色选项
      availableColors.forEach(color => {
        const colorOption = document.createElement('div');
        colorOption.className = `custom-marker-popup-color-option ${color} ${marker.color === color ? 'selected' : ''}`;
        colorOption.setAttribute('data-color', color);
        colorOption.onclick = (e) => {
          // 阻止事件冒泡，防止关闭弹窗
          e.preventDefault();
          e.stopPropagation();

          // 移除所有选中状态
          const allOptions = colorOptions.querySelectorAll('.custom-marker-popup-color-option');
          allOptions.forEach(opt => opt.classList.remove('selected'));

          // 添加选中状态
          e.target.classList.add('selected');

          // 立即更新颜色（无需等待保存按钮）
          const selectedColor = e.target.getAttribute('data-color');
          marker.color = selectedColor;

          // 更新标记颜色
          setCustomMarkers(prev => prev.map(m => {
            if (m.id === marker.id) {
              return {
                ...m,
                color: selectedColor
              };
            }
            return m;
          }));

          // 关闭并重新打开弹窗以更新标记图标
          const position = marker.position;
          setTimeout(() => {
            leafletMarker.setIcon(createCustomMarker(selectedColor));
          }, 50);
        };
        colorOptions.appendChild(colorOption);
      });

      popupContent.appendChild(colorOptions);

      // 按钮区域
      const actions = document.createElement('div');
      actions.className = 'custom-marker-popup-actions';

      // 保存按钮
      const saveButton = document.createElement('button');
      saveButton.className = 'custom-marker-popup-button save';
      saveButton.innerHTML = '<span>💾</span> ' + (t?.saveMarker || 'Save');
      saveButton.onclick = (e) => {
        // 阻止事件冒泡，但这里我们确实想关闭弹窗
        e.preventDefault();
        e.stopPropagation();

        // 更新标记
        setCustomMarkers(prev => prev.map(m => {
          if (m.id === marker.id) {
            return {
              ...m,
              label: marker.label,
              color: marker.color
            };
          }
          return m;
        }));

        // 这里我们确实想关闭弹窗
        map.closePopup();
      };

      // 删除按钮
      const removeButton = document.createElement('button');
      removeButton.className = 'custom-marker-popup-button remove';
      removeButton.innerHTML = '<span>🗑️</span> ' + (t?.removeMarker || 'Remove Marker');
      removeButton.onclick = (e) => {
        // 阻止事件冒泡，但这里我们确实想关闭弹窗
        e.preventDefault();
        e.stopPropagation();

        setCustomMarkers(prev => prev.filter(m => m.id !== marker.id));

        // 这里我们确实想关闭弹窗
        map.closePopup();
      };

      actions.appendChild(saveButton);
      actions.appendChild(removeButton);
      popupContent.appendChild(actions);

      // 绑定弹出窗口
      leafletMarker.bindPopup(popupContent, {
        minWidth: 250,
        maxWidth: 300,
        className: 'custom-marker-popup-container'
      });

      // 添加到图层
      markersLayerRef.current.addLayer(leafletMarker);
    });
  }, [map, customMarkers, t, setCustomMarkers, availableColors]);

  return null;
};

export default CustomMarkerHandler;

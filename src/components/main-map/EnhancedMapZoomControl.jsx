import React from 'react';
import { useMap } from 'react-leaflet';
import { FiPlus, FiMinus, FiHome } from 'react-icons/fi';
import '../../styles/MapButtons.css';

const EnhancedMapZoomControl = ({
  t,
  isDarkMode,
  initialPosition,
  initialZoom,
  selectedCountry,
  COUNTRY_COORDINATES,
  position = 'top' // default to 'top', can be 'top' or 'right'
}) => {
  const map = useMap();

  const handleZoomIn = () => {
    map.zoomIn(1);
  };

  const handleZoomOut = () => {
    map.zoomOut(1);
  };

  const handleResetView = () => {
    // 根据当前选择的国家来决定缩放位置
    if (selectedCountry && COUNTRY_COORDINATES && COUNTRY_COORDINATES[selectedCountry]) {
      // 使用当前选择的国家的默认坐标
      const countryCoord = COUNTRY_COORDINATES[selectedCountry];
      map.flyTo(countryCoord.center, countryCoord.zoom, { duration: 0.8 });
      console.log(`Reset view to ${selectedCountry} default position: center=${countryCoord.center}, zoom=${countryCoord.zoom}`);
    } else if (initialPosition && initialZoom) {
      // 如果没有国家信息，则使用初始位置
      map.flyTo(initialPosition, initialZoom, { duration: 0.8 });
      console.log(`Reset view to initial position: center=${initialPosition}, zoom=${initialZoom}`);
    }
  };

  // 根据位置参数选择不同的控制器样式类
  const controlsClassName = position === 'right' 
    ? `map-right-controls ${isDarkMode ? 'dark-mode' : ''}` 
    : `map-top-controls ${isDarkMode ? 'dark-mode' : ''}`;
  
  const buttonClassName = `map-${position}-button ${isDarkMode ? 'dark-mode' : ''}`;
  
  return (
    <div className={controlsClassName}>
      <button
        className={buttonClassName}
        onClick={handleZoomIn}
        title={t?.zoomIn || "放大"}
        aria-label={t?.zoomIn || "放大"}
      >
        <FiPlus />
      </button>
      <button
        className={buttonClassName}
        onClick={handleZoomOut}
        title={t?.zoomOut || "缩小"}
        aria-label={t?.zoomOut || "缩小"}
      >
        <FiMinus />
      </button>
      <button
        className={buttonClassName}
        onClick={handleResetView}
        title={t?.resetView || "重置视图"}
        aria-label={t?.resetView || "重置视图"}
      >
        <FiHome />
      </button>
    </div>
  );
};

export default EnhancedMapZoomControl;

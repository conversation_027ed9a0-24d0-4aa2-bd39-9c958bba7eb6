import React from 'react';
import { FiX } from 'react-icons/fi';
import { IoLayersOutline, IoEarthOutline, IoAnalyticsOutline } from 'react-icons/io5';
import { FiGrid, FiNavigation, FiInfo } from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import translations from '../../utils/translations';
import '../../styles/LayerExplanations.css';

/**
 * Layer Explanation Component
 * Displays explanation and legend for the currently active layer
 */
const LayerExplanation = ({ layerId, isDarkMode, onClose }) => {
  // Get language preference from localStorage, default to English
  const language = localStorage.getItem('preferredLanguage') || 'en';
  const t = translations[language] || translations.en;
  // Get layer icon
  const getLayerIcon = () => {
    switch (layerId) {
      case 'grid':
        return <FiGrid />;
      case 'traffic':
        return <FiNavigation />;
      case 'poi':
        return <FiInfo />;
      case 'terrain':
        return <IoEarthOutline />;
      case 'analytics':
        return <IoAnalyticsOutline />;
      default:
        return <IoLayersOutline />;
    }
  };

  // Get layer title
  const getLayerTitle = () => {
    switch (layerId) {
      case 'grid':
        return t.gridLayer || 'Grid Layer';
      case 'traffic':
        return t.trafficLayer || 'Traffic Layer';
      case 'poi':
        return t.poiLayer || 'Points of Interest Layer';
      case 'terrain':
        return t.terrainLayer || 'Terrain Layer';
      case 'analytics':
        return t.analyticsLayer || 'Analytics Layer';
      default:
        return t.layerInfo || 'Layer Information';
    }
  };

  // Get layer content
  const getLayerContent = () => {
    switch (layerId) {
      case 'grid':
        return (
          <>
            <p>{t.gridLayerDesc || 'The grid layer displays latitude and longitude grid lines on the map, helping you understand geographic locations and distances.'}</p>
            <div className="layer-explanation-legend">
              <div className="legend-item">
                <div className="legend-line solid">
                  <div className="legend-line-inner" style={{ borderColor: '#6495ED' }}></div>
                </div>
                <div className="legend-text">{t.latLongGridLines || 'Latitude/Longitude Grid Lines (0.5 degree intervals)'}</div>
              </div>
            </div>
          </>
        );
      case 'traffic':
        return (
          <>
            <p>{t.trafficLayerDesc || 'The traffic layer displays road networks and transportation infrastructure, including different types of roads and transportation facilities.'}</p>
            <div className="layer-explanation-legend">
              <div className="legend-item">
                <div className="legend-line solid">
                  <div className="legend-line-inner" style={{ borderColor: '#ff7800', height: '4px' }}></div>
                </div>
                <div className="legend-text">{t.highwayMainRoad || 'Highway/Main Road'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-line solid">
                  <div className="legend-line-inner" style={{ borderColor: '#ff9900', height: '3px' }}></div>
                </div>
                <div className="legend-text">{t.secondaryRoad || 'Secondary Road'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-line solid">
                  <div className="legend-line-inner" style={{ borderColor: '#ffcc00', height: '2px' }}></div>
                </div>
                <div className="legend-text">{t.normalRoad || 'Normal Road'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-line dashed">
                  <div className="legend-line-inner" style={{ borderColor: '#b0b0b0' }}></div>
                </div>
                <div className="legend-text">{t.pathUnpavedRoad || 'Path/Unpaved Road'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#0078ff' }}></div>
                <div className="legend-text">{t.transportHub || 'Transport Hub (Airport, Train Station, etc.)'}</div>
              </div>
            </div>
          </>
        );
      case 'poi':
        return (
          <>
            <p>{t.poiLayerDesc || 'The Points of Interest layer displays important locations and facilities on the map, such as cities, attractions, and service facilities.'}</p>
            <div className="layer-explanation-legend">
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#ff7800' }}></div>
                <div className="legend-text">{t.majorCity || 'Major City'}</div>
              </div>
            </div>
          </>
        );
      case 'terrain':
        return (
          <>
            <p>{t.terrainLayerDesc || 'The terrain layer displays elevation, mountains, rivers, and other natural geographic features, helping you understand the topography.'}</p>
            <div className="layer-explanation-legend">
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#c2d1a0', height: '10px' }}></div>
                <div className="legend-text">{t.plainLowland || 'Plain/Lowland'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#b4c3a3', height: '10px' }}></div>
                <div className="legend-text">{t.hills || 'Hills'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#a6b5a6', height: '10px' }}></div>
                <div className="legend-text">{t.mountains || 'Mountains'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#98a7a9', height: '10px' }}></div>
                <div className="legend-text">{t.highMountains || 'High Mountains'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-line solid">
                  <div className="legend-line-inner" style={{ borderColor: '#8db5ca' }}></div>
                </div>
                <div className="legend-text">{t.riverWaterSystem || 'River/Water System'}</div>
              </div>
            </div>
          </>
        );
      case 'analytics':
        return (
          <>
            <p>{t.analyticsLayerDesc || 'The analytics layer displays data heat maps, showing data density and distribution across different areas.'}</p>
            <div className="layer-explanation-legend">
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#1E88E5' }}></div>
                <div className="legend-text">{t.lowDensityArea || 'Low Density Area'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#26A69A' }}></div>
                <div className="legend-text">{t.mediumLowDensityArea || 'Medium-Low Density Area'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#FFC107' }}></div>
                <div className="legend-text">{t.mediumHighDensityArea || 'Medium-High Density Area'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#FF8F00' }}></div>
                <div className="legend-text">{t.highDensityArea || 'High Density Area'}</div>
              </div>
              <div className="legend-item">
                <div className="legend-point" style={{ backgroundColor: '#E53935' }}></div>
                <div className="legend-text">{t.veryHighDensityArea || 'Very High Density Area'}</div>
              </div>
            </div>
          </>
        );
      default:
        return <p>Select a layer to view detailed information.</p>;
    }
  };

  return (
    <AnimatePresence>
      {layerId && (
        <motion.div
          className={`layer-explanation ${isDarkMode ? 'dark-mode' : ''}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.3
          }}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="layer-explanation-header">
            <div className="layer-explanation-title">
              {getLayerIcon()}
              {getLayerTitle()}
            </div>
            <button
              className="layer-explanation-close layer-explanation-close-button"
              onClick={(e) => {
                // 阻止事件冒泡
                e.stopPropagation();
                // 阻止事件冒泡到文档级别
                e.nativeEvent.stopImmediatePropagation();
                // 阻止默认行为
                e.preventDefault();
                // 调用关闭回调
                onClose();
              }}
              aria-label={t.close || 'Close'}
              data-layer-explanation-close="true"
            >
              <FiX />
            </button>
          </div>
          <div className="layer-explanation-content">
            {getLayerContent()}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LayerExplanation;

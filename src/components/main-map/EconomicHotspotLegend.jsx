import React from 'react';
import '../../styles/EconomicHotspotLayer.css';

/**
 * 经济热点图例组件
 * 用于显示经济热点图层的图例
 * 
 * @param {boolean} isDarkMode - 是否为暗色模式
 * @param {string} modelType - 模型类型
 * @param {object} t - 翻译对象
 */
const EconomicHotspotLegend = ({ isDarkMode = false, modelType = 'lq', t = {} }) => {
  // 根据模型类型获取不同的图例项
  const getLegendItems = () => {
    if (modelType === 'lq') {
      return [
        { color: '#FF3B30', label: t.coreMarket || '核心市场', desc: t.lqAbove13 || 'LQ > 1.3', icon: 'pin-core' },
        { color: '#5AC8FA', label: t.subsidyArea || '补贴洼地', desc: t.lqBelow07 || 'LQ < 0.7', icon: 'pin-subsidy' }
      ];
    } else {
      return [
        { color: '#FF3B30', label: t.majorCenter || '主要中心', desc: t.highEconomicImpact || '经济影响力强', icon: 'pin-core' },
        { color: '#FF9500', label: t.minorCenter || '次要中心', desc: t.mediumEconomicImpact || '经济影响力中等', icon: 'pin-minor' }
      ];
    }
  };

  const legendItems = getLegendItems();

  return (
    <div className={`economic-hotspot-legend ${isDarkMode ? 'dark-mode' : ''}`}>
      <h3>{modelType === 'lq' ? (t.lqLegend || '区位商图例') : (t.gravityLegend || '重力模型图例')}</h3>
      
      <div className="economic-hotspot-legend-items">
        {legendItems.map((item, index) => (
          <div className="legend-item" key={index}>
            <div className="legend-marker-wrapper">
              <div className={`legend-marker ${item.icon}`}>
                <div className="legend-pin"></div>
              </div>
            </div>
            <div className="legend-description">
              <div className="legend-label">{item.label}</div>
              <div className="legend-value">{item.desc}</div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="legend-tip">
        {t.clickForDetails || '点击标记可查看详细信息并切换模型'}
      </div>
    </div>
  );
};

export default EconomicHotspotLegend;

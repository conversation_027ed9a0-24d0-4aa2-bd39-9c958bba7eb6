import React, { useEffect, useRef, useState } from 'react';
import { useMap } from 'react-leaflet';
import L from 'leaflet';
import LayerExplanation from './LayerExplanation';

/**
 * 地图图层控制组件
 * 根据visibleLayers状态显示或隐藏不同的图层
 */
const MapLayersControl = ({ visibleLayers = [], mapStyle }) => {
  const map = useMap();
  const layersRef = useRef({});
  const [activeExplanation, setActiveExplanation] = useState(null);
  const isDarkMode = mapStyle === 'night';

  // 跟踪最后激活的图层，用于确保显示最新激活的图层说明
  const lastActivatedLayerRef = useRef(null);
  // 跟踪上一次的可见图层列表，用于检测新激活的图层
  const prevVisibleLayersRef = useRef([]);

  // 初始化和管理图层
  useEffect(() => {
    // 创建各种图层
    // 创建网格图层
    if (!layersRef.current.grid) {
      layersRef.current.grid = L.layerGroup();

      // 创建网格线的函数
      const createGridLines = () => {
        // 清除现有网格
        layersRef.current.grid.clearLayers();

        const bounds = map.getBounds();
        const ne = bounds.getNorthEast();
        const sw = bounds.getSouthWest();
        const gridSize = 0.5; // 网格大小（度）

        // 计算网格范围，稍微扩大一点以确保覆盖整个可视区域
        const startLng = Math.floor(sw.lng / gridSize) * gridSize - gridSize;
        const endLng = Math.ceil(ne.lng / gridSize) * gridSize + gridSize;
        const startLat = Math.floor(sw.lat / gridSize) * gridSize - gridSize;
        const endLat = Math.ceil(ne.lat / gridSize) * gridSize + gridSize;

        // 创建纵向线
        for (let lng = startLng; lng <= endLng; lng += gridSize) {
          const line = L.polyline([
            [startLat, lng],
            [endLat, lng]
          ], {
            color: '#6495ED',
            weight: 1,
            opacity: 0.5
          });
          layersRef.current.grid.addLayer(line);
        }

        // 创建横向线
        for (let lat = startLat; lat <= endLat; lat += gridSize) {
          const line = L.polyline([
            [lat, startLng],
            [lat, endLng]
          ], {
            color: '#6495ED',
            weight: 1,
            opacity: 0.5
          });
          layersRef.current.grid.addLayer(line);
        }
      };

      // 初始创建网格
      createGridLines();

      // 当地图移动或缩放时重新创建网格
      map.on('moveend', createGridLines);
      map.on('zoomend', createGridLines);

      // 清理函数
      const originalRemove = layersRef.current.grid.remove;
      layersRef.current.grid.remove = function() {
        map.off('moveend', createGridLines);
        map.off('zoomend', createGridLines);
        if (originalRemove) {
          return originalRemove.apply(this, arguments);
        }
      };
    }

    if (!layersRef.current.traffic) {
      // 交通图层
      layersRef.current.traffic = L.tileLayer('https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors, Tiles style by Humanitarian OpenStreetMap Team',
        zIndex: 101
      });
    }

    if (!layersRef.current.poi) {
      // 兴趣点图层 - 模拟实现
      layersRef.current.poi = L.layerGroup();

      // 添加一些示例POI点
      const poiPoints = [
        { lat: 37.7749, lng: -122.4194, name: "San Francisco" },
        { lat: 40.7128, lng: -74.0060, name: "New York" },
        { lat: 34.0522, lng: -118.2437, name: "Los Angeles" },
        { lat: 41.8781, lng: -87.6298, name: "Chicago" },
        { lat: 29.7604, lng: -95.3698, name: "Houston" },
        { lat: 13.7563, lng: 100.5018, name: "Bangkok" },
        { lat: 18.7883, lng: 98.9853, name: "Chiang Mai" },
        { lat: 7.9519, lng: 98.3381, name: "Phuket" }
      ];

      poiPoints.forEach(point => {
        const marker = L.circleMarker([point.lat, point.lng], {
          radius: 8,
          fillColor: "#ff7800",
          color: "#000",
          weight: 1,
          opacity: 1,
          fillOpacity: 0.8
        }).bindTooltip(point.name);

        layersRef.current.poi.addLayer(marker);
      });
    }

    if (!layersRef.current.terrain) {
      // 地形图层 - 使用更明显的地形图层
      layersRef.current.terrain = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}', {
        attribution: 'Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ, TomTom, Intermap, iPC, USGS, FAO, NPS, NRCAN, GeoBase, Kadaster NL, Ordnance Survey, Esri Japan, METI, Esri China (Hong Kong), and the GIS User Community',
        zIndex: 99
      });
    }

    if (!layersRef.current.analytics) {
      // 分析图层 - 增强版热力图
      layersRef.current.analytics = L.layerGroup();

      // 创建热力图的函数
      const createHeatmap = () => {
        // 清除现有热力图
        layersRef.current.analytics.clearLayers();

        // 获取当前地图范围
        const bounds = map.getBounds();
        const ne = bounds.getNorthEast();
        const sw = bounds.getSouthWest();
        const center = map.getCenter();
        const zoom = map.getZoom();

        // 生成热力图数据
        const points = [];

        // 生成一些热点聚类
        const clusters = [];
        const clusterCount = 5 + Math.floor(Math.random() * 5); // 5-10个聚类

        for (let i = 0; i < clusterCount; i++) {
          // 在地图范围内创建聚类中心
          const clusterLat = sw.lat + Math.random() * (ne.lat - sw.lat);
          const clusterLng = sw.lng + Math.random() * (ne.lng - sw.lng);
          const clusterIntensity = 0.5 + Math.random() * 0.5; // 较高的强度
          clusters.push({ lat: clusterLat, lng: clusterLng, intensity: clusterIntensity });

          // 在聚类中心周围生成点
          const pointsInCluster = 10 + Math.floor(Math.random() * 15); // 10-25个点
          for (let j = 0; j < pointsInCluster; j++) {
            // 在聚类中心周围生成点，距离越远强度越低
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * 0.05; // 大约几公里的范围
            const pointLat = clusterLat + Math.sin(angle) * distance;
            const pointLng = clusterLng + Math.cos(angle) * distance;

            // 距离中心越远强度越低
            const distanceFactor = 1 - (distance / 0.05);
            const pointIntensity = clusterIntensity * distanceFactor * (0.7 + Math.random() * 0.3);

            points.push({ lat: pointLat, lng: pointLng, intensity: pointIntensity });
          }
        }

        // 添加聚类中心点（更大更亮）
        clusters.forEach(cluster => {
          const color = getHeatColor(cluster.intensity);
          const circle = L.circle([cluster.lat, cluster.lng], {
            radius: 5000 + cluster.intensity * 10000,
            fillColor: color,
            fillOpacity: 0.6,
            stroke: true,
            color: color,
            weight: 2
          });

          layersRef.current.analytics.addLayer(circle);
        });

        // 添加热力图点
        points.forEach(point => {
          const color = getHeatColor(point.intensity);
          const circle = L.circle([point.lat, point.lng], {
            radius: 3000 + point.intensity * 7000,
            fillColor: color,
            fillOpacity: 0.5 + point.intensity * 0.3,
            stroke: true,
            color: color,
            weight: 1,
            opacity: 0.7
          });

          layersRef.current.analytics.addLayer(circle);
        });
      };

      // 初始创建热力图
      createHeatmap();

      // 当地图移动或缩放时重新创建热力图
      map.on('moveend', createHeatmap);
      map.on('zoomend', createHeatmap);

      // 清理函数
      const originalRemove = layersRef.current.analytics.remove;
      layersRef.current.analytics.remove = function() {
        map.off('moveend', createHeatmap);
        map.off('zoomend', createHeatmap);
        if (originalRemove) {
          return originalRemove.apply(this, arguments);
        }
      };
    }

    // 检测新激活的图层
    const newlyActivatedLayers = visibleLayers.filter(layer => !prevVisibleLayersRef.current.includes(layer));
    const deactivatedLayers = prevVisibleLayersRef.current.filter(layer => !visibleLayers.includes(layer));

    // 更新上一次的可见图层列表
    prevVisibleLayersRef.current = [...visibleLayers];

    // 如果有新激活的图层，更新最后激活的图层引用
    if (newlyActivatedLayers.length > 0) {
      lastActivatedLayerRef.current = newlyActivatedLayers[newlyActivatedLayers.length - 1];
    }

    // 根据visibleLayers状态添加或移除图层
    Object.keys(layersRef.current).forEach(layerId => {
      const layer = layersRef.current[layerId];
      const shouldBeVisible = visibleLayers.includes(layerId);

      if (shouldBeVisible && !map.hasLayer(layer)) {
        // 添加图层
        map.addLayer(layer);
        // 显示这个新激活图层的说明
        setActiveExplanation(layerId);
      } else if (!shouldBeVisible && map.hasLayer(layer)) {
        // 移除图层
        map.removeLayer(layer);
        // 如果当前显示的是这个图层的说明，则关闭或切换说明
        if (activeExplanation === layerId) {
          if (visibleLayers.length > 0) {
            // 如果还有其他可见图层，显示最后激活的图层说明
            if (lastActivatedLayerRef.current && visibleLayers.includes(lastActivatedLayerRef.current)) {
              // 如果最后激活的图层仍然可见，则显示它
              setActiveExplanation(lastActivatedLayerRef.current);
            } else {
              // 否则显示当前可见的第一个图层
              setActiveExplanation(visibleLayers[0]);
              lastActivatedLayerRef.current = visibleLayers[0];
            }
          } else {
            // 如果没有可见图层，关闭说明
            setActiveExplanation(null);
            lastActivatedLayerRef.current = null;
          }
        }
      }
    });

    // 如果有新激活的图层，显示最新激活的图层说明
    if (newlyActivatedLayers.length > 0) {
      const latestLayer = newlyActivatedLayers[newlyActivatedLayers.length - 1];
      setActiveExplanation(latestLayer);
    }

    // 如果没有图层可见，确保关闭说明
    if (visibleLayers.length === 0) {
      setActiveExplanation(null);
      lastActivatedLayerRef.current = null;
    }

    // 清理函数
    return () => {
      Object.values(layersRef.current).forEach(layer => {
        if (map.hasLayer(layer)) {
          map.removeLayer(layer);
        }
      });
    };
  }, [map, visibleLayers]);

  // 热力图颜色辅助函数
  const getHeatColor = (intensity) => {
    // 使用更鲜明的颜色
    if (intensity < 0.25) {
      return '#1E88E5'; // 青蓝色
    } else if (intensity < 0.5) {
      return '#26A69A'; // 绿色
    } else if (intensity < 0.75) {
      return '#FFC107'; // 黄色
    } else if (intensity < 0.9) {
      return '#FF8F00'; // 橙色
    } else {
      return '#E53935'; // 红色
    }
  };

  return (
    <>
      {activeExplanation && (
        <LayerExplanation
          layerId={activeExplanation}
          isDarkMode={isDarkMode}
          onClose={() => setActiveExplanation(null)}
        />
      )}
    </>
  );
};

export default MapLayersControl;

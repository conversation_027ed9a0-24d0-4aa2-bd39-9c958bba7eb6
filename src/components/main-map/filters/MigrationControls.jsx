import React, { useState, useRef, useEffect } from 'react';
import '../../../styles/MigrationControls.css';
import MigrationReasonFilter from './MigrationReasonFilter';
import MigrationTypeFilter from './MigrationTypeFilter';
import L from 'leaflet';

/**
 * 迁移控制面板组件
 * 增强版本 - 提供更多分析选项和可视化工具
 *
 * @param {Object} props
 * @param {string} props.selectedGender - 当前选择的性别
 * @param {Function} props.onGenderChange - 性别变更处理函数
 * @param {string} props.selectedMigrationType - 当前选择的迁移类型
 * @param {Function} props.onMigrationTypeChange - 迁移类型变更处理函数
 * @param {Object} props.t - 翻译对象
 */
const MigrationControls = ({
  selectedGender,
  onGenderChange,
  selectedMigrationType,
  onMigrationTypeChange,
  selectedMigrationReason,
  onMigrationReasonChange,
  t = {}
}) => {
  const [minimized, setMinimized] = useState(false);
  const [activeTab, setActiveTab] = useState('filters');
  const [activeMigrationTab, setActiveMigrationTab] = useState('types'); // 'types' or 'reasons'

  // 创建引用
  const contentRef = useRef(null);
  const controlsRef = useRef(null);

  // 使用Leaflet的DomEvent完全阻止事件传播
  useEffect(() => {
    const controlsElement = controlsRef.current;
    const contentElement = contentRef.current;

    if (controlsElement) {
      // 使用Leaflet的原生方法完全阻止事件传播
      L.DomEvent.disableClickPropagation(controlsElement);
      L.DomEvent.disableScrollPropagation(controlsElement);

      // 只添加滚动和触摸事件的阻止，不阻止点击
      controlsElement.addEventListener('wheel', stopMapInteraction, { passive: false });
      controlsElement.addEventListener('touchstart', stopMapInteraction, { passive: false });
      controlsElement.addEventListener('touchmove', stopMapInteraction, { passive: false });
      controlsElement.addEventListener('touchend', stopMapInteraction, { passive: false });

      // 不阻止鼠标点击事件，允许按钮正常工作
      // controlsElement.addEventListener('mousedown', stopMapInteraction, { passive: false });
      // controlsElement.addEventListener('mouseup', stopMapInteraction, { passive: false });
      // controlsElement.addEventListener('mousemove', stopMapInteraction, { passive: false });
      // controlsElement.addEventListener('click', stopMapInteraction, { passive: false });
    }

    if (contentElement) {
      // 对内容区域也进行同样的处理
      L.DomEvent.disableClickPropagation(contentElement);
      L.DomEvent.disableScrollPropagation(contentElement);

      // 允许内容区域滚动，但阻止事件传播
      contentElement.addEventListener('wheel', handleContentScroll, { passive: false });
      contentElement.addEventListener('touchstart', stopMapInteraction, { passive: false });
      contentElement.addEventListener('touchmove', handleContentTouch, { passive: false });
      contentElement.addEventListener('touchend', stopMapInteraction, { passive: false });
    }

    // 清理函数
    return () => {
      if (controlsElement) {
        controlsElement.removeEventListener('wheel', stopMapInteraction);
        controlsElement.removeEventListener('touchstart', stopMapInteraction);
        controlsElement.removeEventListener('touchmove', stopMapInteraction);
        controlsElement.removeEventListener('touchend', stopMapInteraction);
        // controlsElement.removeEventListener('mousedown', stopMapInteraction);
        // controlsElement.removeEventListener('mouseup', stopMapInteraction);
        // controlsElement.removeEventListener('mousemove', stopMapInteraction);
        // controlsElement.removeEventListener('click', stopMapInteraction);
      }

      if (contentElement) {
        contentElement.removeEventListener('wheel', handleContentScroll);
        contentElement.removeEventListener('touchstart', stopMapInteraction);
        contentElement.removeEventListener('touchmove', handleContentTouch);
        contentElement.removeEventListener('touchend', stopMapInteraction);
      }
    };
  }, []);

  // 只阻止地图交互，不阻止按钮点击
  const stopMapInteraction = (e) => {
    e.stopPropagation();

    // 如果是按钮或按钮内的元素，允许正常点击
    if (e.target.tagName.toLowerCase() === 'button' ||
        e.target.closest('button')) {
      return;
    }

    // 否则阻止默认行为，防止地图交互
    e.preventDefault();
    return false;
  };

  // 处理内容区域的滚动，允许滚动但阻止事件传播
  const handleContentScroll = (e) => {
    e.stopPropagation();

    // 如果是按钮内部的滚动，允许正常行为
    if (e.target.tagName.toLowerCase() === 'button' ||
        e.target.closest('button')) {
      return;
    }

    const content = contentRef.current;
    if (content) {
      const scrollTop = content.scrollTop;
      const scrollHeight = content.scrollHeight;
      const clientHeight = content.clientHeight;

      // 检查是否已经滚动到底部或顶部
      if ((scrollTop + clientHeight >= scrollHeight && e.deltaY > 0) ||
          (scrollTop <= 0 && e.deltaY < 0)) {
        // 已经到达边界，阻止默认行为
        e.preventDefault();
      }
    }
  };

  // 处理内容区域的触摸滚动
  const handleContentTouch = (e) => {
    e.stopPropagation();
    // 允许内容区域滚动，但阻止事件传播
  };

  const toggleMinimized = () => {
    setMinimized(!minimized);
  };

  // 过滤器标签内容
  const renderFiltersTab = () => (
    <div className="migration-filters-tab">
      <div className="migration-filter-group">
        <label>{t.gender || "Gender"}</label>
        <div className="migration-filter-options">
          <button
            className={`filter-button ${selectedGender === 'Total' ? 'active' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              onGenderChange && onGenderChange('Total');
              return false;
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <span className="filter-icon">👥</span>
            {t.total || "Total"}
          </button>
          <button
            className={`filter-button ${selectedGender === 'Male' ? 'active' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              onGenderChange && onGenderChange('Male');
              return false;
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <span className="filter-icon">👨</span>
            {t.male || "Male"}
          </button>
          <button
            className={`filter-button ${selectedGender === 'Female' ? 'active' : ''}`}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              onGenderChange && onGenderChange('Female');
              return false;
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          >
            <span className="filter-icon">👩</span>
            {t.female || "Female"}
          </button>
        </div>
      </div>

      {/* Migration Type/Reason Tabs */}
      <div className="migration-data-tabs">
        <button
          className={`migration-data-tab ${activeMigrationTab === 'types' ? 'active' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setActiveMigrationTab('types');
            return false;
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          {t.migrationTypes || 'Migration Types'}
        </button>
        <button
          className={`migration-data-tab ${activeMigrationTab === 'reasons' ? 'active' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setActiveMigrationTab('reasons');
            return false;
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          {t.migrationReasons || 'Migration Reasons'}
        </button>
      </div>

      {/* Conditionally render either Migration Type or Migration Reason filter */}
      {activeMigrationTab === 'types' ? (
        <MigrationTypeFilter
          selectedType={selectedMigrationType}
          onTypeChange={onMigrationTypeChange}
          t={t}
        />
      ) : (
        <MigrationReasonFilter
          selectedReason={selectedMigrationReason}
          onReasonChange={onMigrationReasonChange}
          t={t}
        />
      )}

      <div className="filter-tips">
        <div className="tip-icon">💡</div>
        <div className="tip-text">
          {t.filterTip || "Click on any region marker to view detailed migration data"}
        </div>
      </div>
    </div>
  );

  // 帮助标签内容
  const renderHelpTab = () => (
    <div className="migration-help-tab">
      <div className="help-section">
        <h5>{t.aboutData || "About This Data"}</h5>
        <p>
          {t.dataDescription || "This map shows migration patterns categorized by type and gender across different regions in Thailand."}
        </p>
      </div>
      <div className="help-section">
        <h5>{t.howToUse || "How to Use"}</h5>
        <ul className="help-list">
          <li>{t.clickMarkers || "Click on circular markers to view detailed data for each region"}</li>
          <li>{t.useFilters || "Use the filters to analyze migration patterns by gender and type"}</li>
          <li>{t.compareRegions || "Compare data across regions to identify migration trends"}</li>
        </ul>
      </div>
      <div className="help-section">
        <h5>{t.definitions || "Definitions"}</h5>
        <div className="definition-item">
          <strong>{t.wholeHouseholdMigrated || "Whole Household Migrated"}:</strong>
          <span>{t.wholeHouseholdDef || "Entire family unit relocated together"}</span>
        </div>
        <div className="definition-item">
          <strong>{t.partialHouseholdMigrated || "Partial Household Migrated"}:</strong>
          <span>{t.partialHouseholdDef || "Only some family members relocated"}</span>
        </div>
        <div className="definition-item">
          <strong>{t.individualMigrated || "Individual Migrated"}:</strong>
          <span>{t.individualDef || "Single person migration without family"}</span>
        </div>
      </div>
    </div>
  );

  // 如果最小化，只显示最小化的控制栏
  if (minimized) {
    return (
      <div
        className="migration-controls minimized"
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onTouchStart={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onTouchMove={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onTouchEnd={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
      >
        <div className="migration-controls-header minimized">
          <div
            className="migration-expand-btn"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              toggleMinimized();
              return false;
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            title={t.expandControls || "Expand Controls"}
          >
            <span className="expand-icon">⚙️</span>
            <span className="expand-text">{t.migrationControls || "Migration Controls"}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="migration-controls"
      ref={controlsRef}
    >
      <div className="migration-controls-header">
        <h4>{t.migrationAnalysis || "Migration Analysis"}</h4>
        <div className="migration-controls-actions">
          <div
            className="migration-close-btn"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
              toggleMinimized();
              return false;
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            title={t.minimizeControls || "Minimize Controls"}
          >
            <span>×</span>
          </div>
        </div>
      </div>

      <div className="migration-controls-tabs">
        <button
          className={`tab-button ${activeTab === 'filters' ? 'active' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setActiveTab('filters');
            return false;
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          {t.filters || "Filters"}
        </button>
        <button
          className={`tab-button ${activeTab === 'help' ? 'active' : ''}`}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setActiveTab('help');
            return false;
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          {t.help || "Help"}
        </button>
      </div>

      <div
        className="migration-controls-content"
        ref={contentRef}
      >
        {activeTab === 'filters' ? renderFiltersTab() : renderHelpTab()}
      </div>
    </div>
  );
};

export default MigrationControls;

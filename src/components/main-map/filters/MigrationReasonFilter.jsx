import React, { useState, useCallback, useRef, useEffect } from 'react';
import '../../../styles/MigrationControls.css';
import L from 'leaflet';

const MigrationReasonFilter = ({ selectedReason, onReasonChange, t }) => {
  // Define the list of migration reasons
  const migrationReasons = [
    { value: 'All', label: t.allReasons || 'All Reasons', icon: '🔄' },
    { value: 'Looking for a job', label: t.lookingForJob || 'Looking for a job', icon: '🔍' },
    { value: 'Want to change job', label: t.wantToChangeJob || 'Want to change job', icon: '🔄' },
    { value: 'Want to increase income', label: t.wantToIncreaseIncome || 'Want to increase income', icon: '💰' },
    { value: 'Job assignment', label: t.jobAssignment || 'Job assignment', icon: '📋' },
    { value: 'Further education', label: t.furtherEducation || 'Further education', icon: '🎓' },
    { value: 'Relocation', label: t.relocation || 'Relocation', icon: '🏠' },
    { value: 'Return to hometown', label: t.returnToHometown || 'Return to hometown', icon: '🏡' },
    { value: 'Follow family member', label: t.followFamilyMember || 'Follow family member', icon: '👪' },
    { value: 'Family business', label: t.familyBusiness || 'Family business', icon: '🏢' },
    { value: 'Medical treatment', label: t.medicalTreatment || 'Medical treatment', icon: '🏥' },
    { value: 'Lack of caregiver', label: t.lackOfCaregiver || 'Lack of caregiver', icon: '👵' },
    { value: 'To care for others', label: t.toCareForOthers || 'To care for others', icon: '🤲' },
    { value: 'Others', label: t.others || 'Others', icon: '📝' }
  ];

  // State to manage dropdown visibility
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // 创建引用
  const containerRef = useRef(null);
  const dropdownRef = useRef(null);

  // 使用Leaflet的DomEvent完全阻止事件传播
  useEffect(() => {
    const container = containerRef.current;
    const dropdown = dropdownRef.current;

    if (container) {
      // 使用Leaflet的原生方法完全阻止事件传播
      L.DomEvent.disableClickPropagation(container);
      L.DomEvent.disableScrollPropagation(container);

      // 添加滚动事件监听器，但不阻止点击
      container.addEventListener('wheel', stopMapInteraction, { passive: false });
      container.addEventListener('touchstart', stopMapInteraction, { passive: false });
      container.addEventListener('touchmove', stopMapInteraction, { passive: false });
      container.addEventListener('touchend', stopMapInteraction, { passive: false });
    }

    if (dropdown) {
      L.DomEvent.disableClickPropagation(dropdown);
      L.DomEvent.disableScrollPropagation(dropdown);

      // 添加额外的事件监听器来处理滚动
      dropdown.addEventListener('wheel', handleScroll, { passive: false });
      dropdown.addEventListener('touchstart', stopMapInteraction, { passive: false });
      dropdown.addEventListener('touchmove', stopMapInteraction, { passive: false });
      dropdown.addEventListener('touchend', stopMapInteraction, { passive: false });
    }

    return () => {
      if (container) {
        container.removeEventListener('wheel', stopMapInteraction);
        container.removeEventListener('touchstart', stopMapInteraction);
        container.removeEventListener('touchmove', stopMapInteraction);
        container.removeEventListener('touchend', stopMapInteraction);
      }

      if (dropdown) {
        dropdown.removeEventListener('wheel', handleScroll);
        dropdown.removeEventListener('touchstart', stopMapInteraction);
        dropdown.removeEventListener('touchmove', stopMapInteraction);
        dropdown.removeEventListener('touchend', stopMapInteraction);
      }
    };
  }, [isDropdownOpen]);

  // 只阻止地图交互，不阻止按钮点击
  const stopMapInteraction = useCallback((e) => {
    e.stopPropagation();

    // 如果是按钮或按钮内的元素，允许正常点击
    if (e.target.tagName.toLowerCase() === 'button' ||
        e.target.closest('button')) {
      return;
    }

    // 否则阻止默认行为，防止地图交互
    e.preventDefault();
  }, []);

  // 只阻止事件传播，不阻止默认行为
  const stopPropagation = useCallback((e) => {
    e.stopPropagation();
    // 不调用 preventDefault，允许按钮点击
  }, []);

  // 处理滚动事件
  const handleScroll = useCallback((e) => {
    e.stopPropagation();

    const dropdown = dropdownRef.current;
    if (dropdown) {
      const scrollTop = dropdown.scrollTop;
      const scrollHeight = dropdown.scrollHeight;
      const clientHeight = dropdown.clientHeight;

      // 检查是否已经滚动到底部或顶部
      if ((scrollTop + clientHeight >= scrollHeight && e.deltaY > 0) ||
          (scrollTop <= 0 && e.deltaY < 0)) {
        // 已经到达边界，阻止默认行为
        e.preventDefault();
      }
    }
  }, []);

  // Find the currently selected reason object
  const selectedReasonObj = migrationReasons.find(reason => reason.value === selectedReason) || migrationReasons[0];

  return (
    <div className="migration-filter-group">
      <div className="migration-reason-dropdown" ref={containerRef}>
        <button
          className="migration-reason-selected"
          onClick={(e) => {
            stopPropagation(e);
            setIsDropdownOpen(!isDropdownOpen);
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <span className="filter-icon">{selectedReasonObj.icon}</span>
          {selectedReasonObj.label}
        </button>
        {isDropdownOpen && (
          <div
            className="migration-reason-options"
            ref={dropdownRef}
          >
            {/* 确保不会重复渲染选项 */}
            {migrationReasons.map((reason, index) => {
              // 如果是重复项，跳过
              if (index > 0 && migrationReasons.findIndex(r => r.value === reason.value) < index) {
                return null;
              }
              return (
                <button
                  key={reason.value + '-' + index}
                  className={`migration-reason-option ${selectedReason === reason.value ? 'active' : ''}`}
                  onClick={(e) => {
                    stopPropagation(e);
                    onReasonChange(reason.value);
                    setIsDropdownOpen(false);
                  }}
                  onMouseDown={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  <span className="filter-icon">{reason.icon}</span>
                  {reason.label}
                </button>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default MigrationReasonFilter;

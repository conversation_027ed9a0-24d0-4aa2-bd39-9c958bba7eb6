import React, { useRef, useEffect, useCallback } from 'react';
import '../../../styles/MigrationControls.css';
import L from 'leaflet';

const MigrationTypeFilter = ({ selectedType, onTypeChange, t }) => {
  // 创建引用
  const containerRef = useRef(null);

  // 使用Leaflet的DomEvent完全阻止事件传播
  useEffect(() => {
    const container = containerRef.current;

    if (container) {
      // 使用Leaflet的原生方法完全阻止事件传播
      L.DomEvent.disableClickPropagation(container);
      L.DomEvent.disableScrollPropagation(container);

      // 添加滚动事件监听器，但不阻止点击
      container.addEventListener('wheel', stopMapInteraction, { passive: false });
      container.addEventListener('touchstart', stopMapInteraction, { passive: false });
      container.addEventListener('touchmove', stopMapInteraction, { passive: false });
      container.addEventListener('touchend', stopMapInteraction, { passive: false });
    }

    return () => {
      if (container) {
        container.removeEventListener('wheel', stopMapInteraction);
        container.removeEventListener('touchstart', stopMapInteraction);
        container.removeEventListener('touchmove', stopMapInteraction);
        container.removeEventListener('touchend', stopMapInteraction);
      }
    };
  }, []);

  // 只阻止地图交互，不阻止按钮点击
  const stopMapInteraction = useCallback((e) => {
    e.stopPropagation();

    // 如果是按钮或按钮内的元素，允许正常点击
    if (e.target.tagName.toLowerCase() === 'button' ||
        e.target.closest('button')) {
      return;
    }

    // 否则阻止默认行为，防止地图交互
    e.preventDefault();
  }, []);

  // 只阻止事件传播，不阻止默认行为
  const stopPropagation = useCallback((e) => {
    e.stopPropagation();
    // 不调用 preventDefault，允许按钮点击
  }, []);
  return (
    <div className="migration-filter-group" ref={containerRef}>
      <div className="migration-filter-options">
        <button
          className={`filter-button ${selectedType === 'All' ? 'active' : ''}`}
          onClick={(e) => {
            stopPropagation(e);
            onTypeChange && onTypeChange('All');
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <span className="filter-icon">🔄</span>
          {t.all || "All"}
        </button>
        <button
          className={`filter-button ${selectedType === 'Whole_Household_Migrated' ? 'active' : ''}`}
          onClick={(e) => {
            stopPropagation(e);
            onTypeChange && onTypeChange('Whole_Household_Migrated');
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <span className="filter-icon">👪</span>
          {t.wholeHousehold || "Whole Family"}
        </button>
        <button
          className={`filter-button ${selectedType === 'Partial_Household_Migrated' ? 'active' : ''}`}
          onClick={(e) => {
            stopPropagation(e);
            onTypeChange && onTypeChange('Partial_Household_Migrated');
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <span className="filter-icon">👨‍👩</span>
          {t.partialHousehold || "Partial Family"}
        </button>
        <button
          className={`filter-button ${selectedType === 'Individual_Migrated' ? 'active' : ''}`}
          onClick={(e) => {
            stopPropagation(e);
            onTypeChange && onTypeChange('Individual_Migrated');
          }}
          onMouseDown={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <span className="filter-icon">🧍</span>
          {t.individual || "Individual"}
        </button>
      </div>
    </div>
  );
};

export default MigrationTypeFilter;

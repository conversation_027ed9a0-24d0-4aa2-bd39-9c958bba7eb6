import React, { useRef } from "react";
import { use<PERSON>rame, useThree } from "@react-three/fiber";
import * as THREE from "three";

export default function StarSky({ scrollY }) {
  const group = useRef();
  const { size } = useThree();
  
  // 动画效果
  useFrame(() => {
    if (group.current) {
      group.current.rotation.y += 0.0002;
      
      // 如果有滚动参数，增加滚动响应效果
      if (scrollY && scrollY.current !== undefined) {
        group.current.rotation.y = scrollY.current * 0.0003;
        group.current.position.y = -scrollY.current * 0.001;
      }
    }
  });
  
  // 生成星星位置
  const starCount = 2000;
  const positions = [];
  
  for (let i = 0; i < starCount; i++) {
    const r = 40 + Math.random() * 80;
    const theta = Math.random() * 2 * Math.PI;
    const phi = Math.acos(-1 + Math.random() * 2);
    
    positions.push([
      r * Math.sin(phi) * Math.cos(theta),
      r * Math.sin(phi) * Math.sin(theta),
      r * Math.cos(phi),
    ]);
  }

  return (
    <group ref={group}>
      {positions.map((pos, i) => (
        <mesh position={pos} key={i}>
          <sphereGeometry args={[Math.random() * 0.12 + 0.03, 6, 6]} />
          <meshBasicMaterial 
            color={"white"} 
            transparent 
            opacity={Math.random() * 0.7 + 0.3} 
          />
        </mesh>
      ))}
    </group>
  );
} 
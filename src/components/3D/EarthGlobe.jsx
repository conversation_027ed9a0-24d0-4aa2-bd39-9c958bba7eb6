import React, { useRef } from "react";
import { useGLTF } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";

export default function EarthGlobe(props) {
  const groupRef = useRef();
  const { scene } = useGLTF("/models/earth_globe_gltf/scene.gltf");
  
  // 添加自动旋转效果
  useFrame(() => {
    if (groupRef.current) {
      groupRef.current.rotation.y += 0.002;
    }
  });

  return (
    <primitive 
      ref={groupRef}
      object={scene} 
      {...props} 
    />
  );
} 
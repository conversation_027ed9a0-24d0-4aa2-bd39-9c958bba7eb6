import React, { useState, useRef, useEffect } from 'react';

const EnhancedDropdown = ({
  trigger,
  children,
  position = 'bottom-left',
  className = '',
  offset = 8,
  closeOnClickOutside = true,
  closeOnEscape = true,
  preventOverflow = true,
  zIndex = 1150
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [calculatedPosition, setCalculatedPosition] = useState(position);
  const triggerRef = useRef(null);
  const dropdownRef = useRef(null);

  // 计算最佳位置，防止溢出视口
  const calculatePosition = () => {
    if (!triggerRef.current || !dropdownRef.current || !preventOverflow) {
      return position;
    }

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const dropdownRect = dropdownRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let newPosition = position;

    // 检查垂直方向
    if (position.includes('bottom')) {
      const spaceBelow = viewport.height - triggerRect.bottom - offset;
      const spaceAbove = triggerRect.top - offset;
      
      if (dropdownRect.height > spaceBelow && spaceAbove > spaceBelow) {
        newPosition = newPosition.replace('bottom', 'top');
      }
    } else if (position.includes('top')) {
      const spaceAbove = triggerRect.top - offset;
      const spaceBelow = viewport.height - triggerRect.bottom - offset;
      
      if (dropdownRect.height > spaceAbove && spaceBelow > spaceAbove) {
        newPosition = newPosition.replace('top', 'bottom');
      }
    }

    // 检查水平方向
    if (position.includes('right')) {
      const spaceRight = viewport.width - triggerRect.right - offset;
      const spaceLeft = triggerRect.left - offset;
      
      if (dropdownRect.width > spaceRight && spaceLeft > spaceRight) {
        newPosition = newPosition.replace('right', 'left');
      }
    } else if (position.includes('left')) {
      const spaceLeft = triggerRect.left - offset;
      const spaceRight = viewport.width - triggerRect.right - offset;
      
      if (dropdownRect.width > spaceLeft && spaceRight > spaceLeft) {
        newPosition = newPosition.replace('left', 'right');
      }
    }

    return newPosition;
  };

  // 获取下拉菜单的样式
  const getDropdownStyle = () => {
    if (!triggerRef.current) return {};

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const style = {
      position: 'fixed',
      zIndex: zIndex
    };

    // 根据计算出的位置设置坐标
    switch (calculatedPosition) {
      case 'bottom-left':
        style.top = triggerRect.bottom + offset;
        style.left = triggerRect.left;
        break;
      case 'bottom-right':
        style.top = triggerRect.bottom + offset;
        style.right = window.innerWidth - triggerRect.right;
        break;
      case 'top-left':
        style.bottom = window.innerHeight - triggerRect.top + offset;
        style.left = triggerRect.left;
        break;
      case 'top-right':
        style.bottom = window.innerHeight - triggerRect.top + offset;
        style.right = window.innerWidth - triggerRect.right;
        break;
      case 'left':
        style.top = triggerRect.top;
        style.right = window.innerWidth - triggerRect.left + offset;
        break;
      case 'right':
        style.top = triggerRect.top;
        style.left = triggerRect.right + offset;
        break;
      default:
        style.top = triggerRect.bottom + offset;
        style.left = triggerRect.left;
    }

    return style;
  };

  // 处理点击外部区域
  useEffect(() => {
    if (!closeOnClickOutside || !isOpen) return;

    const handleClickOutside = (event) => {
      if (
        triggerRef.current &&
        dropdownRef.current &&
        !triggerRef.current.contains(event.target) &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, closeOnClickOutside]);

  // 处理ESC键
  useEffect(() => {
    if (!closeOnEscape || !isOpen) return;

    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape]);

  // 当下拉菜单打开时，计算最佳位置
  useEffect(() => {
    if (isOpen && preventOverflow) {
      // 延迟计算，确保DOM已渲染
      setTimeout(() => {
        const newPosition = calculatePosition();
        setCalculatedPosition(newPosition);
      }, 0);
    }
  }, [isOpen, position, preventOverflow]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="enhanced-dropdown" style={{ position: 'relative' }}>
      {/* 触发器 */}
      <div ref={triggerRef} onClick={handleToggle}>
        {trigger}
      </div>

      {/* 下拉菜单 */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className={`enhanced-dropdown-content ${className}`}
          style={getDropdownStyle()}
        >
          {children}
        </div>
      )}
    </div>
  );
};

export default EnhancedDropdown; 
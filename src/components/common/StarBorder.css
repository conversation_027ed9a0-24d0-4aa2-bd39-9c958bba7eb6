.star-border-container {
  display: inline-block;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
}

.border-gradient-bottom {
  position: absolute;
  width: 200%;
  height: 30%;
  opacity: 0.6;
  bottom: -8px;
  right: -150%;
  border-radius: 50%;
  animation: star-movement-bottom linear infinite alternate;
  z-index: 0;
  filter: blur(0.5px);
}

.border-gradient-top {
  position: absolute;
  opacity: 0.6;
  width: 200%;
  height: 30%;
  top: -8px;
  left: -150%;
  border-radius: 50%;
  animation: star-movement-top linear infinite alternate;
  z-index: 0;
  filter: blur(0.5px);
}

.inner-content {
  position: relative;
  border: 1px solid #222;
  background: #000;
  color: white;
  font-size: 16px;
  text-align: center;
  padding: 16px 26px;
  border-radius: 20px;
  z-index: 1;
}

@keyframes star-movement-bottom {
  0% {
    transform: translate(0%, 0%);
    opacity: 1;
  }
  100% {
    transform: translate(-100%, 0%);
    opacity: 0;
  }
}

@keyframes star-movement-top {
  0% {
    transform: translate(0%, 0%);
    opacity: 1;
  }
  100% {
    transform: translate(100%, 0%);
    opacity: 0;
  }
} 
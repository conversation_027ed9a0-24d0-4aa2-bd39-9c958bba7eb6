import React from 'react';
import { useNavigate } from 'react-router-dom';

const BackButton = ({ text, className = '' }) => {
  const navigate = useNavigate();

  return (
    <button
      className={`${className}`.trim()}
      onClick={() => navigate(-1)}
      aria-label={text}
    >
      <div className="liquid-button-content">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        <span>{text}</span>
      </div>
    </button>
  );
};

export default BackButton;
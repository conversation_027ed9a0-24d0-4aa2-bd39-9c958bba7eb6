/* Compact Error Message Component */
.compact-error-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.4;
  backdrop-filter: blur(10px);
  border: 1px solid;
  animation: slideInError 0.3s ease-out;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  max-width: 100%;
  box-sizing: border-box;
}

/* Compact variant - minimal height */
.compact-error-message.compact {
  min-height: 36px;
  padding: 6px 10px;
  font-size: 12px;
}

/* Expanded variant - more breathing room */
.compact-error-message.expanded {
  min-height: 44px;
  padding: 10px 14px;
  font-size: 13px;
}

/* Error content layout */
.error-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* Allow text to truncate */
}

.error-text {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Action buttons */
.error-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.error-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
}

.compact .error-action-btn {
  width: 20px;
  height: 20px;
}

.error-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.error-action-btn:active {
  transform: scale(0.95);
}

/* Type-specific styling */
.compact-error-message.error {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #fca5a5;
}

.compact-error-message.error::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(239, 68, 68, 0.6), transparent);
}

.compact-error-message.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #fbbf24;
}

.compact-error-message.warning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.6), transparent);
}

.compact-error-message.info {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: #93c5fd;
}

.compact-error-message.info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.6), transparent);
}

.compact-error-message.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: #86efac;
}

.compact-error-message.success::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.6), transparent);
}

/* Hover effects */
.compact-error-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.compact-error-message.compact:hover {
  transform: translateY(-0.5px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideOutError {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
}

.compact-error-message.dismissing {
  animation: slideOutError 0.3s ease-out forwards;
}

/* Responsive design */
@media (max-width: 768px) {
  .compact-error-message {
    font-size: 12px;
    padding: 6px 8px;
  }

  .compact-error-message.expanded {
    font-size: 12px;
    padding: 8px 10px;
  }



  .error-actions {
    gap: 2px;
  }
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
  .compact-error-message {
    backdrop-filter: blur(15px);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .compact-error-message {
    animation: none;
  }

  .compact-error-message.dismissing {
    animation: none;
    opacity: 0;
  }

  .error-action-btn:hover {
    transform: none;
  }
}

/* Focus styles for accessibility */
.error-action-btn:focus {
  outline: 2px solid currentColor;
  outline-offset: 2px;
}

/* Custom styling for specific use cases */
.project-error-message {
  margin: 16px 0;
  max-width: 100%;
}

.ai-analysis-error {
  margin: 12px 24px;
  border-radius: 12px;
}

.login-form-error {
  margin: 12px 0;
  font-size: 12px;
}

/* Integration with existing design systems */
.compact-error-message.project-error-message {
  background: rgba(239, 68, 68, 0.08);
  border: 1px solid rgba(239, 68, 68, 0.25);
  backdrop-filter: blur(12px);
}

.compact-error-message.ai-analysis-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  backdrop-filter: blur(15px);
}

.compact-error-message.login-form-error {
  background: rgba(239, 68, 68, 0.06);
  border: 1px solid rgba(239, 68, 68, 0.2);
  backdrop-filter: blur(8px);
}

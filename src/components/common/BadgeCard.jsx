import React, { memo, useState, useEffect } from 'react';
import '../../styles/BadgeCard.css';

// 用户图标组件
const UserIcon = memo(() => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="user-icon-svg"
  >
    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
    <circle cx="12" cy="7" r="4"></circle>
  </svg>
));

// 欢迎图标组件 - 使用心形图标
const WelcomeIcon = memo(() => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="user-icon-svg"
  >
    <path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7Z"/>
  </svg>
));

const BadgeCard = memo(({ children, iconType = 'user', customClass = '' }) => {
  const [currentIconType, setCurrentIconType] = useState(iconType);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 当iconType改变时，触发过渡动画
  useEffect(() => {
    if (currentIconType !== iconType) {
      setIsTransitioning(true);
      
      // 延迟更新图标类型，让旧图标先淡出
      const timer = setTimeout(() => {
        setCurrentIconType(iconType);
        // 再延迟一点让新图标淡入
        setTimeout(() => {
          setIsTransitioning(false);
        }, 150);
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [iconType, currentIconType]);

  return (
    <div className={`badge-card ${customClass}`}>
      {/* Enhanced 3D Badge Recess at Top */}
      <div className="badge-recess-container">
        <div className="badge-recess-shadow"></div>
        <div className="badge-recess-main">
          <div className="badge-recess-inner">
            <div className="badge-recess-highlight"></div>
          </div>
          <div className="badge-recess-side-left"></div>
          <div className="badge-recess-side-right"></div>
        </div>
      </div>

      {/* Card edge lighting */}
      <div className="badge-card-lighting"></div>
      <div className="badge-card-edge-left"></div>
      <div className="badge-card-edge-right"></div>

      {/* Content */}
      <div className="badge-card-content">
        {/* Enhanced User Icon */}
        <div className="user-icon-container">
          <div className={`user-icon ${iconType === 'welcome' ? 'welcome-mode' : 'user-mode'} ${isTransitioning ? 'transitioning' : ''}`}>
            {/* Icon Background Glow */}
            <div className="user-icon-glow"></div>
            
            {/* Dynamic Icon with Transition */}
            <div className={`icon-transition-container ${isTransitioning ? 'transitioning' : ''}`}>
              {currentIconType === 'welcome' ? <WelcomeIcon /> : <UserIcon />}
            </div>
            
            {/* Enhanced Borders and Effects */}
            <div className="user-icon-border"></div>
            <div className="user-icon-border-inner"></div>
            <div className="user-icon-highlight"></div>
            <div className="user-icon-reflection"></div>
          </div>
        </div>

        {children}
      </div>
    </div>
  );
});

BadgeCard.displayName = 'BadgeCard';
UserIcon.displayName = 'UserIcon';
WelcomeIcon.displayName = 'WelcomeIcon';

export default BadgeCard; 
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // 监听滚动事件，控制导航栏样式
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav 
      className={`fixed top-0 left-0 w-full py-4 px-6 z-50 transition-all duration-300 ${
        scrolled ? "bg-gray-900/90 backdrop-blur-md shadow-lg" : "bg-transparent"
      }`}
    >
      <div className="max-w-6xl mx-auto flex justify-between items-center">
        {/* Logo */}
        <div className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-300">
          IndustrialGeoDev
        </div>

        {/* 桌面导航链接 */}
        <div className="hidden md:flex gap-8">
          <a href="#features" className="text-gray-200 hover:text-blue-400 transition-colors duration-300">功能</a>
          <a href="#about" className="text-gray-200 hover:text-blue-400 transition-colors duration-300">关于</a>
          <a href="#contact" className="text-gray-200 hover:text-blue-400 transition-colors duration-300">联系</a>
        </div>

        {/* 移动端菜单按钮 */}
        <button 
          className="md:hidden text-gray-200 focus:outline-none"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
            </svg>
          )}
        </button>
      </div>

      {/* 移动端菜单 */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-gray-800 mt-4 rounded-lg overflow-hidden"
          >
            <div className="flex flex-col p-4">
              <a 
                href="#features" 
                className="py-2 text-gray-200 hover:text-blue-400 transition-colors duration-300"
                onClick={() => setMobileMenuOpen(false)}
              >
                功能
              </a>
              <a 
                href="#about" 
                className="py-2 text-gray-200 hover:text-blue-400 transition-colors duration-300"
                onClick={() => setMobileMenuOpen(false)}
              >
                关于
              </a>
              <a 
                href="#contact" 
                className="py-2 text-gray-200 hover:text-blue-400 transition-colors duration-300"
                onClick={() => setMobileMenuOpen(false)}
              >
                联系
              </a>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
} 
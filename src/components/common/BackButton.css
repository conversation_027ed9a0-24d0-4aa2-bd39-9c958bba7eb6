.morph-nav-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: transparent; /* Or your desired background */
  border: 1px solid #ccc; /* Example border */
  border-radius: 20px; /* Pill shape */
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #333; /* Example text color */
}

.morph-nav-button:hover {
  background-color: transparent; /* Changed to transparent */
  border-color: #aaa; /* Example hover border color */
}

.morph-container {
  position: relative;
  width: 24px; /* Adjust as needed */
  height: 24px; /* Adjust as needed */
  margin-right: 8px;
}

.morph-shape {
  /* If you have a specific shape background, define it here */
  /* For example, a circle: */
  /* width: 100%;
  height: 100%;
  background-color: #e0e0e0;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1; */
}

.morph-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.morph-icon svg {
  /* SVG specific styles if needed */
}

.morph-text {
  /* Styles for the text part of the button */
}
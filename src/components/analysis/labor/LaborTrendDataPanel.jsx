import React, { useState, useEffect } from 'react';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement,
  Title, 
  Tooltip, 
  Legend 
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';

// 注册 Chart.js 组件
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement,
  Title, 
  Tooltip, 
  Legend
);

/**
 * LaborTrendDataPanel
 * @param {array}  props.laborTrends - 劳动力数据数组（多个年份）
 * @param {number} props.selectedIndex - 当前选中的记录索引（单一年份模式）
 * @param {function} props.onChangeIndex - 切换记录的回调
 * @param {boolean} props.show - 是否显示数据面板
 * @param {object} props.t - 翻译对象
 */
const LaborTrendDataPanel = ({ laborTrends, selectedIndex, onChangeIndex, show, t = {} }) => {
  // 是否开启"对比模式"
  const [compareMode, setCompareMode] = useState(false);
  // 默认对比最后两个年份（比如有4个年份时，下标 [2,3] 代表 2023、2024）
  const [compareIndices, setCompareIndices] = useState([
    Math.max(0, laborTrends.length - 2), 
    Math.max(0, laborTrends.length - 1)
  ]);

  // 图表类型（折线图/柱状图）
  const [chartType, setChartType] = useState('line'); 
  // 数据类别
  const [selectedCategory, setSelectedCategory] = useState('education');
  // 当前选择的指标
  const [selectedMetrics, setSelectedMetrics] = useState([]);

  // 预定义的各类别下的指标字段
  const categories = {
    education: [
      'No Education', 'Below Primary  Education', 'Primary Education', 'lower Secondary Education',
      'General Education Track ', 'Vocational Track ', 'Education Track (upper secondary)',
      'Academic Track', 'Professional Track', 'Education Track'
    ],
    employment: ['Employed Persons ', 'Unemployed Persons'],
    occupation: [
      'Technicians and Associate Professionals', 'Clerks',
      'Craftsmen and Related Workers', 'Plant and Machine Operators and Assemblers'
    ],
    industry: ['Manufacturing', 'Transportation and Warehousing'],
    workingHours: [
      'Less than 1 Hour', ' 1 - 9', ' 10 - 19', ' 20 - 29',
      ' 30 - 34', ' 35 - 39', ' 40 - 49', '50 hours'
    ]
  };

  // 初始化：默认选中 "education" 类别的前3个指标
  useEffect(() => {
    if (categories[selectedCategory]) {
      setSelectedMetrics(categories[selectedCategory].slice(0, 3));
    }
  }, []);

  if (!show) return null;
  if (!laborTrends || laborTrends.length === 0) {
    return <div style={{ marginTop: '20px' }}>
      {t.dataLoadError || 'No labor trend data available.'}
    </div>;
  }

  // 单一年份时选中的记录
  const singleRecord = laborTrends[selectedIndex];

  // 切换滑块 => 切换当前选中的年份索引
  const handleSliderChange = (e) => {
    onChangeIndex(Number(e.target.value));
  };

  // 切换对比模式
  const toggleCompareMode = () => {
    setCompareMode(!compareMode);
  };

  // 当在对比模式下，允许用户选择要对比的年份
  const handleCompareChange = (index) => {
    // 如果已经选中，则移除；否则添加
    if (compareIndices.includes(Number(index))) {
      setCompareIndices(compareIndices.filter(idx => idx !== Number(index)));
    } else {
      setCompareIndices([...compareIndices, Number(index)]);
    }
  };
  
  // 一键全选/全部取消年份
  const handleSelectAllYears = () => {
    const allIndices = laborTrends.map((_, idx) => idx);
    setCompareIndices(allIndices);
  };
  
  const handleClearAllYears = () => {
    setCompareIndices([]);
  };

  // 切换数据类别 => 默认选中该类别的前3个指标
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    setSelectedMetrics(categories[category].slice(0, 3));
  };

  // 工具函数：去除逗号、空格，转成数字
  const parseValue = (val) => {
    if (!val) return 0;
    return parseFloat(val.replace(/,|\s/g, '')) || 0;
  };

  // ============== 生成图表数据 ==============
  // 1) 单一年份模式 => 时间序列（将所有年份按顺序，X轴为 Date）
  const prepareSingleChartData = () => {
    // 假设 "Date" 形如 "2024_Q4" => 提取年份进行排序
    const sortedData = [...laborTrends].sort((a, b) => {
      const yearA = parseInt(a.Date.split('_')[0]);
      const yearB = parseInt(b.Date.split('_')[0]);
      return yearA - yearB;
    });

    const labels = sortedData.map(item => item.Date);

    // 不同指标 => 生成多个 dataset
    const colors = [
      'rgba(75, 192, 192, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(201, 203, 207, 0.6)',
      'rgba(255, 99, 71, 0.6)',
      'rgba(0, 128, 128, 0.6)',
      'rgba(106, 90, 205, 0.6)'
    ];

    const datasets = selectedMetrics.map((metric, index) => ({
      // 使用翻译对象获取指标名称，如果没有对应翻译则使用原始名称
      label: t[metric.trim().toLowerCase().replace(/\s+/g, '_')] || metric,
      data: sortedData.map(item => parseValue(item[metric])),
      backgroundColor: colors[index % colors.length],
      borderColor: colors[index % colors.length].replace('0.6', '1'),
      borderWidth: 1,
    }));

    return { labels, datasets };
  };

  // 2) 对比模式 => X轴为所选指标, 多条/多柱 => 分别代表选中的年份
  const prepareCompareChartData = () => {
    // X轴为选中的指标
    const labels = selectedMetrics.map(metric => t[metric.trim().toLowerCase().replace(/\s+/g, '_')] || metric);

    // 使用与单一模式相同的颜色数组
    const colors = [
      'rgba(75, 192, 192, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(201, 203, 207, 0.6)',
      'rgba(255, 99, 71, 0.6)',
      'rgba(0, 128, 128, 0.6)',
      'rgba(106, 90, 205, 0.6)'
    ];

    // 为每个选中的年份创建一个数据集
    const datasets = compareIndices.map((index, i) => {
      const yearData = laborTrends[index];
      return {
        // 使用翻译对象获取"数据"一词，如果没有对应翻译则使用默认值
        label: `${yearData.Date} ${t.data || 'Data'}`,
        data: selectedMetrics.map(metric => parseValue(yearData[metric])),
        backgroundColor: colors[i % colors.length],
        borderColor: colors[i % colors.length].replace('0.6', '1'),
        borderWidth: 1,
      };
    });

    return { labels, datasets };
  };

  // 根据模式生成不同的 chartData
  const chartData = compareMode ? prepareCompareChartData() : prepareSingleChartData();

  // 图表配置
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'top' },
      title: {
        display: true,
        text: compareMode ? (t.laborDataComparison || 'Labor Data Comparison') : (t.laborTrendAnalysis || 'Labor Trend Analysis'),
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat('zh-CN').format(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return new Intl.NumberFormat('zh-CN').format(value);
          }
        }
      }
    }
  };

  // 一键全选/全部取消
  const handleSelectAll = () => {
    setSelectedMetrics(categories[selectedCategory]);
  };
  const handleClearAll = () => {
    setSelectedMetrics([]);
  };

  return (
    <div
      className="labor-trend-data-panel"
      style={{
        marginTop: '20px',
        backgroundColor: '#ffffff',
        borderRadius: '12px',
        padding: '24px'
      }}
    >
      <div style={{ 
        marginTop: 0, 
        marginBottom: '20px', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: '16px'
      }}>
        <h2 style={{
          margin: 0,
          fontSize: '18px',
          fontWeight: '600',
          color: '#333'
        }}>
          {compareMode ? (t.laborDataComparison || 'Labor Data Comparison') : (t.laborTrendAnalysis || 'Labor Trend Analysis')}
        </h2>
        <button
          onClick={toggleCompareMode}
          style={{
            padding: '8px 16px',
            borderRadius: '20px',
            backgroundColor: compareMode ? 'rgba(245, 34, 45, 0.05)' : 'rgba(24, 144, 255, 0.05)',
            color: compareMode ? '#f5222d' : '#1890ff',
            border: `1px solid ${compareMode ? '#ffccc7' : '#91d5ff'}`,
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}
        >
          {compareMode ? (t.switchToTrendMode || 'Switch to Trend Mode') : (t.switchToCompareMode || 'Switch to Compare Mode')}
        </button>
      </div>

      {/* 对比模式下：选择对比年份 - 现代化设计 */}
      {compareMode && (
        <div style={{ 
          marginBottom: '24px',
          background: 'linear-gradient(to right, rgba(24, 144, 255, 0.02), rgba(24, 144, 255, 0.05))',
          padding: '16px',
          borderRadius: '10px',
          border: '1px solid rgba(24, 144, 255, 0.1)'
        }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            marginBottom: '12px'
          }}>
            <div style={{ 
              fontSize: '15px', 
              fontWeight: '600',
              color: '#333',
              display: 'flex',
              alignItems: 'center',
              gap: '6px'
            }}>
              <span style={{ 
                display: 'inline-block',
                width: '14px',
                height: '14px',
                borderRadius: '50%',
                background: 'rgba(24, 144, 255, 0.2)'
              }}></span>
              {t.selectCompareYears || 'Select Compare Years'}
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button 
                onClick={handleSelectAllYears}
                style={{ 
                  padding: '6px 12px', 
                  borderRadius: '18px', 
                  background: 'rgba(24, 144, 255, 0.1)', 
                  border: '1px solid rgba(24, 144, 255, 0.3)',
                  color: '#1890ff',
                  fontSize: '13px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                <span style={{ fontSize: '14px' }}>✓</span>
                {t.selectAll || 'Select All'}
              </button>
              <button 
                onClick={handleClearAllYears}
                style={{ 
                  padding: '6px 12px', 
                  borderRadius: '18px', 
                  background: 'rgba(245, 34, 45, 0.05)', 
                  border: '1px solid rgba(245, 34, 45, 0.2)',
                  color: '#f5222d',
                  fontSize: '13px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '4px'
                }}
              >
                <span style={{ fontSize: '14px' }}>×</span>
                {t.clearAll || 'Clear All'}
              </button>
            </div>
          </div>
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: '10px',
            marginTop: '10px' 
          }}>
            {laborTrends.map((item, idx) => (
              <label key={idx} style={{ 
                padding: '6px 12px',
                backgroundColor: compareIndices.includes(idx) ? 'rgba(24, 144, 255, 0.1)' : 'white',
                border: `1px solid ${compareIndices.includes(idx) ? 'rgba(24, 144, 255, 0.3)' : '#e8e8e8'}`,
                borderRadius: '18px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                fontSize: '13px',
                fontWeight: compareIndices.includes(idx) ? '500' : 'normal',
                color: compareIndices.includes(idx) ? '#1890ff' : '#555',
                transition: 'all 0.3s ease',
                boxShadow: compareIndices.includes(idx) ? '0 2px 6px rgba(24, 144, 255, 0.1)' : 'none'
              }}>
                <input
                  type="checkbox"
                  checked={compareIndices.includes(idx)}
                  onChange={() => handleCompareChange(idx)}
                  style={{ 
                    marginRight: '6px',
                    accentColor: '#1890ff'
                  }}
                />
                {item.Date}
              </label>
            ))}
          </div>
          {compareIndices.length === 0 && (
            <div style={{ 
              marginTop: '12px', 
              padding: '10px',
              background: 'rgba(245, 34, 45, 0.05)',
              borderRadius: '6px',
              color: '#f5222d', 
              fontSize: '13px',
              textAlign: 'center',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '6px'
            }}>
              <span style={{ fontSize: '16px' }}>⚠️</span>
              {t.pleaseSelectYears || 'Please select at least one year for comparison'}
            </div>
          )}
        </div>
      )}

      {/* 图表类型 + 数据类别 - 现代化设计 */}
      <div style={{ 
        marginBottom: '24px', 
        display: 'flex', 
        flexDirection: 'column',
        gap: '16px',
        background: 'linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(240, 249, 255, 0.9))',
        padding: '20px',
        borderRadius: '12px'
      }}>
        {/* 图表类型选择 */}
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          gap: '12px'
        }}>
          <div style={{ 
            fontSize: '15px', 
            fontWeight: '600',
            color: '#333',
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          }}>
            <span style={{ 
              display: 'inline-block',
              width: '14px',
              height: '14px',
              borderRadius: '50%',
              background: 'rgba(24, 144, 255, 0.2)'
            }}></span>
            {t.chartType || 'Chart Type'}
          </div>
          <div style={{ 
            display: 'flex', 
            gap: '10px',
            flexWrap: 'wrap'
          }}>
            {[
              { value: 'line', label: t.lineChart || 'Line Chart', icon: '📈' },
              { value: 'bar', label: t.barChart || 'Bar Chart', icon: '📊' }
            ].map(type => (
              <button
                key={type.value}
                onClick={() => setChartType(type.value)}
                style={{
                  padding: '8px 16px',
                  borderRadius: '20px',
                  border: `1px solid ${chartType === type.value ? 'rgba(24, 144, 255, 0.5)' : 'rgba(0, 0, 0, 0.1)'}`,
                  backgroundColor: chartType === type.value ? 'rgba(24, 144, 255, 0.1)' : 'white',
                  color: chartType === type.value ? '#1890ff' : '#555',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: chartType === type.value ? '500' : 'normal',
                  boxShadow: chartType === type.value ? '0 2px 6px rgba(24, 144, 255, 0.1)' : 'none',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px'
                }}
              >
                <span>{type.icon}</span>
                {type.label}
              </button>
            ))}
          </div>
        </div>
        
        {/* 数据类别选择 */}
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          gap: '12px'
        }}>
          <div style={{ 
            fontSize: '15px', 
            fontWeight: '600',
            color: '#333',
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          }}>
            <span style={{ 
              display: 'inline-block',
              width: '14px',
              height: '14px',
              borderRadius: '50%',
              background: 'rgba(24, 144, 255, 0.2)'
            }}></span>
            {t.dataCategory || 'Data Category'}
          </div>
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: '10px'
          }}>
            {[
              { value: 'education', label: t.education || 'Education Level', icon: '🎓' },
              { value: 'employment', label: t.employment || 'Employment Status', icon: '👥' },
              { value: 'occupation', label: t.occupation || 'Occupation Distribution', icon: '👷' },
              { value: 'industry', label: t.industry || 'Industry Distribution', icon: '🏭' },
              { value: 'workingHours', label: t.workingHours || 'Working Hours', icon: '⏰' }
            ].map(category => (
              <button
                key={category.value}
                onClick={() => handleCategoryChange(category.value)}
                style={{
                  padding: '8px 16px',
                  borderRadius: '20px',
                  border: `1px solid ${selectedCategory === category.value ? 'rgba(24, 144, 255, 0.5)' : 'rgba(0, 0, 0, 0.1)'}`,
                  backgroundColor: selectedCategory === category.value ? 'rgba(24, 144, 255, 0.1)' : 'white',
                  color: selectedCategory === category.value ? '#1890ff' : '#555',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: selectedCategory === category.value ? '500' : 'normal',
                  boxShadow: selectedCategory === category.value ? '0 2px 6px rgba(24, 144, 255, 0.1)' : 'none',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px'
                }}
              >
                <span>{category.icon}</span>
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* 选择指标 + 全选/全不选 - 现代化设计 */}
      <div style={{ 
        marginBottom: '24px',
        background: 'linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(240, 249, 255, 0.9))',
        padding: '20px',
        borderRadius: '12px'
      }}>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          marginBottom: '16px'
        }}>
          <div style={{ 
            fontSize: '15px', 
            fontWeight: '600',
            color: '#333',
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          }}>
            <span style={{ 
              display: 'inline-block',
              width: '14px',
              height: '14px',
              borderRadius: '50%',
              background: 'rgba(24, 144, 255, 0.2)'
            }}></span>
            {t.selectMetrics || 'Select Metrics'}
          </div>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button 
              onClick={handleSelectAll}
              style={{ 
                padding: '6px 14px', 
                borderRadius: '18px', 
                background: 'rgba(24, 144, 255, 0.1)', 
                border: '1px solid rgba(24, 144, 255, 0.3)',
                color: '#1890ff',
                fontSize: '13px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              <span style={{ fontSize: '14px' }}>✓</span>
              {t.selectAll || 'Select All'}
            </button>
            <button 
              onClick={handleClearAll}
              style={{ 
                padding: '6px 14px', 
                borderRadius: '18px', 
                background: 'rgba(245, 34, 45, 0.05)', 
                border: '1px solid rgba(245, 34, 45, 0.2)',
                color: '#f5222d',
                fontSize: '13px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}
            >
              <span style={{ fontSize: '14px' }}>×</span>
              {t.clearAll || 'Clear All'}
            </button>
          </div>
        </div>
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: '10px',
          marginTop: '10px' 
        }}>
          {categories[selectedCategory].map(metric => (
            <label key={metric} style={{ 
              padding: '8px 14px',
              backgroundColor: selectedMetrics.includes(metric) ? 'rgba(24, 144, 255, 0.1)' : 'white',
              border: `1px solid ${selectedMetrics.includes(metric) ? 'rgba(24, 144, 255, 0.3)' : '#e8e8e8'}`,
              borderRadius: '20px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              fontSize: '13px',
              fontWeight: selectedMetrics.includes(metric) ? '500' : 'normal',
              color: selectedMetrics.includes(metric) ? '#1890ff' : '#555',
              transition: 'all 0.3s ease',
              boxShadow: selectedMetrics.includes(metric) ? '0 2px 6px rgba(24, 144, 255, 0.1)' : 'none'
            }}>
              <input
                type="checkbox"
                checked={selectedMetrics.includes(metric)}
                onChange={() => {
                  if (selectedMetrics.includes(metric)) {
                    setSelectedMetrics(selectedMetrics.filter(m => m !== metric));
                  } else {
                    setSelectedMetrics([...selectedMetrics, metric]);
                  }
                }}
                style={{ 
                  marginRight: '8px',
                  accentColor: '#1890ff'
                }}
              />
              {/* 使用翻译对象获取指标名称，如果没有对应翻译则使用原始名称 */}
              {t[metric.trim().toLowerCase().replace(/\s+/g, '_')] || metric}
            </label>
          ))}
        </div>
      </div>

      {/* 图表展示区 - 现代化设计 */}
      <div style={{ 
        height: '400px', 
        marginBottom: '24px',
        background: 'white',
        borderRadius: '12px',
        padding: '20px',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {selectedMetrics.length > 0 ? (
          <>
            <div style={{ 
              position: 'absolute', 
              top: '10px', 
              right: '10px', 
              zIndex: 10,
              display: 'flex',
              gap: '6px'
            }}>
              <div style={{ 
                fontSize: '12px', 
                padding: '4px 10px', 
                borderRadius: '16px',
                backgroundColor: 'rgba(0, 0, 0, 0.03)',
                color: '#666',
                display: 'flex',
                alignItems: 'center',
                gap: '4px'
              }}>
                <span style={{ 
                  width: '8px', 
                  height: '8px', 
                  borderRadius: '50%', 
                  backgroundColor: compareMode ? '#f5222d' : '#1890ff' 
                }}></span>
                {compareMode ? (t.compareMode || 'Compare Mode') : (t.trendMode || 'Trend Mode')}
              </div>
            </div>
            <div style={{ height: '100%', width: '100%' }}>
              {chartType === 'line' ? (
                <Line data={chartData} options={chartOptions} />
              ) : (
                <Bar data={chartData} options={chartOptions} />
              )}
            </div>
          </>
        ) : (
          <div style={{ 
            height: '100%', 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center',
            border: '1px dashed rgba(0, 0, 0, 0.1)',
            borderRadius: '8px',
            backgroundColor: 'rgba(0, 0, 0, 0.01)',
            gap: '12px'
          }}>
            <div style={{ 
              fontSize: '48px', 
              opacity: 0.2 
            }}>
              {chartType === 'line' ? '📈' : '📊'}
            </div>
            <div style={{ 
              color: '#666', 
              fontSize: '15px',
              textAlign: 'center',
              maxWidth: '80%',
              lineHeight: '1.5'
            }}>
              {compareMode && compareIndices.length === 0 
                ? (t.pleaseSelectYears || 'Please select at least one year for comparison')
                : (t.pleaseSelectMetrics || 'Please select at least one metric to display chart')}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LaborTrendDataPanel;

import React, { useState, useEffect } from 'react';
import { Radar, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
} from 'chart.js';
import '../../../styles/LaborEconomicsAnalysis.css';

// 注册 Chart.js 组件
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement
);

const LaborEconomicsAnalysis = ({ parkName, t }) => {
  const [laborData, setLaborData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [annotations, setAnnotations] = useState([]);

  useEffect(() => {
    fetch('/data/Thai_labor_economics.json')
      .then(response => {
        if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
        return response.json();
      })
      .then(data => {
        setLaborData(data);
        setAnnotations(data.Annotations || []);
        setLoading(false);
      })
      .catch(error => {
        console.error("Error loading labor economics data:", error);
        setLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="labor-economics-loading">
        <div className="loading-spinner"></div>
        <p>{t?.loading || 'Loading...'}</p>
      </div>
    );
  }

  if (!laborData || !laborData['Raw Data'] || laborData['Raw Data'].length === 0) {
    return <div className="labor-economics-error">{t?.dataError || 'Unable to load labor economics data'}</div>;
  }

  // 查找特定省份数据（Prachin Buri）和区域总数据
  const prachinburiData = laborData['Raw Data'].find(item => item.Province_and_Gender === 'Prachin Buri');
  const regionalData = laborData['Raw Data'].find(item => item.Province_and_Gender === 'Regional Total');

  if (!prachinburiData || !regionalData) {
    return <div className="labor-economics-error">{t?.dataError || 'Unable to find relevant provincial data'}</div>;
  }

  // 雷达图数据
  const radarData = {
    labels: [
      t?.manufacturingShare || 'Manufacturing Share',
      t?.skilledWorker || 'Skilled Workers',
      t?.privateEmployment || 'Private Employment',
      t?.fullTimeRate || 'Full-time Rate',
      t?.underemploymentRate || 'Underemployment Rate',
    ],
    datasets: [
      {
        label: t?.prachinBuri || 'Prachinburi Province',
        data: [
          prachinburiData['Manufacturing Share (%)'],
          prachinburiData['Skilled Worker %'],
          prachinburiData['Private Sector %'],
          prachinburiData['Full-time Rate (%)'],
          prachinburiData['Underemployment Rate (%)'],
        ],
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      },
      {
        label: t?.regionalAverage || 'Regional Average',
        data: [
          regionalData['Manufacturing Share (%)'],
          regionalData['Skilled Worker %'],
          regionalData['Private Sector %'],
          regionalData['Full-time Rate (%)'],
          regionalData['Underemployment Rate (%)'],
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  // 雷达图选项
  const radarOptions = {
    scales: {
      r: {
        angleLines: {
          display: true,
        },
        suggestedMin: 0,
        suggestedMax: 100,
      },
    },
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.raw.toFixed(1)}%`;
          }
        }
      }
    },
    maintainAspectRatio: false,
  };

  // 就业类型数据（饼图）
  const employmentTypeData = {
    labels: [
      t?.governmentEmployee || 'Government Employee',
      t?.privateEmployee || 'Private Employee',
      t?.selfEmployed || 'Self-Employed',
      t?.familyBusiness || 'Family Business',
    ],
    datasets: [
      {
        data: [
          prachinburiData['Government %'],
          prachinburiData['Private Sector %'],
          prachinburiData['Self-Employed %'],
          prachinburiData['Family Business %'],
        ],
        backgroundColor: [
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // 饼图选项
  const doughnutOptions = {
    plugins: {
      legend: {
        position: 'right',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.label}: ${context.raw.toFixed(1)}%`;
          }
        }
      }
    },
    maintainAspectRatio: false,
  };

  // 劳动力对比数据（条形图）
  const workforceComparisonData = {
    labels: [
      t?.totalWorkforce || 'Total Workforce',
      t?.employedPersons || 'Employed Persons',
      t?.manufacturingWorkforce || 'Manufacturing Workforce',
      t?.skilledWorkforce || 'Skilled Workforce',
    ],
    datasets: [
      {
        label: t?.prachinBuri || 'Prachinburi Province',
        data: [
          prachinburiData['Total Labor Force'],
          prachinburiData['Employed Persons'],
          prachinburiData['Manufacturing Workforce'],
          prachinburiData['Higher Education'] + prachinburiData['Vocational Training'],
        ],
        backgroundColor: 'rgba(255, 99, 132, 0.7)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
      },
      {
        label: t?.regionalTotal || 'Regional Total (scaled down 100x)',
        data: [
          regionalData['Total Labor Force'] / 100,
          regionalData['Employed Persons'] / 100,
          regionalData['Manufacturing Workforce'] / 100,
          (regionalData['Higher Education'] + regionalData['Vocational Training']) / 100,
        ],
        backgroundColor: 'rgba(54, 162, 235, 0.7)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
      },
    ],
  };

  // 条形图选项
  const barOptions = {
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: t?.peopleCount || 'People Count',
        },
      },
    },
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const datasetLabel = context.dataset.label;
            const value = context.raw;
            if (datasetLabel.includes('区域总数')) {
              return `${datasetLabel}: ${(value * 100).toLocaleString()}`;
            }
            return `${datasetLabel}: ${value.toLocaleString()}`;
          }
        }
      }
    },
    maintainAspectRatio: false,
  };

  // 关键指标数据
  const keyMetrics = [
    {
      name: t?.availableWorkforce || 'Available Workforce',
      value: prachinburiData['Available Workforce'].toLocaleString(),
      description: annotations.find(a => a.Metric === 'Available Workforce')?.Explanation || '',
      icon: '👥',
    },
    {
      name: t?.hiringCapacity || 'Hiring Capacity',
      value: prachinburiData['Hiring Capacity'].toLocaleString(),
      description: annotations.find(a => a.Metric === 'Hiring Capacity')?.Explanation || '',
      icon: '🔍',
    },
    {
      name: t?.manufacturingDependency || 'Manufacturing Dependency',
      value: prachinburiData['Manufacturing Dependency'].toFixed(1) + '%',
      description: annotations.find(a => a.Metric === 'Manufacturing Dependency')?.Explanation || '',
      icon: '🏭',
    },
    {
      name: t?.skillGap || 'Skill Gap',
      value: prachinburiData['Skill Gap (%)'].toFixed(1) + '%',
      description: annotations.find(a => a.Metric === 'Skill Gap (%)')?.Explanation || '',
      icon: '📊',
    },
  ];

  return (
    <div>
      <div className="labor-economics-header">
        <p className="labor-economics-subtitle">
          {t?.prachinBuriProvince || 'Prachinburi Province'} - {parkName}
        </p>
      </div>

      <div className="labor-economics-tabs">
        <button 
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          {t?.overview || 'Overview'}
        </button>
        <button 
          className={`tab-button ${activeTab === 'employment' ? 'active' : ''}`}
          onClick={() => setActiveTab('employment')}
        >
          {t?.employmentTypes || 'Employment Types'}
        </button>
        <button 
          className={`tab-button ${activeTab === 'workforce' ? 'active' : ''}`}
          onClick={() => setActiveTab('workforce')}
        >
          {t?.workforceComparison || 'Workforce Comparison'}
        </button>
      </div>

      <div className="labor-economics-content">
        {activeTab === 'overview' && (
          <>
            <div className="key-metrics-grid">
              {keyMetrics.map((metric, index) => (
                <div className="key-metric-card" key={index}>
                  <div className="metric-icon">{metric.icon}</div>
                  <div className="metric-details">
                    <h3>{metric.name}</h3>
                    <div className="metric-value">{metric.value}</div>
                    <p className="metric-description">{metric.description}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="radar-chart-container">
              <h3>{t?.laborMarketRadar || 'Labor Market Radar'}</h3>
              <div className="chart-wrapper">
                <Radar data={radarData} options={radarOptions} />
              </div>
            </div>
          </>
        )}

        {activeTab === 'employment' && (
          <div className="employment-types-container">
            <div className="employment-chart-container">
              <h3>{t?.employmentDistribution || 'Employment Distribution'}</h3>
              <div className="chart-wrapper">
                <Doughnut data={employmentTypeData} options={doughnutOptions} />
              </div>
            </div>
            <div className="employment-stats">
              <h3>{t?.employmentStatistics || 'Employment Statistics'}</h3>
              <div className="stats-grid">
                <div className="stat-item">
                  <span className="stat-label">{t?.fullTimeRate || 'Full-time Rate'}</span>
                  <span className="stat-value">{prachinburiData['Full-time Rate (%)'].toFixed(1)}%</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">{t?.partTimeRate || 'Part-time Rate'}</span>
                  <span className="stat-value">{prachinburiData['Part-time Rate (%)'].toFixed(1)}%</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">{t?.underemploymentRate || 'Underemployment Rate'}</span>
                  <span className="stat-value">{prachinburiData['Underemployment Rate (%)'].toFixed(1)}%</span>
                </div>
                <div className="stat-item">
                  <span className="stat-label">{t?.manufacturingShare || 'Manufacturing Share'}</span>
                  <span className="stat-value">{prachinburiData['Manufacturing Share (%)'].toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'workforce' && (
          <div className="workforce-comparison-container">
            <h3>{t?.workforceComparison || 'Workforce Comparison'}</h3>
            <p className="comparison-note">
              {t?.comparisonNote || 'Note: Regional total data scaled down 100x for comparison'}
            </p>
            <div className="chart-wrapper">
              <Bar data={workforceComparisonData} options={barOptions} />
            </div>
            <div className="workforce-highlights">
              <div className="highlight-item">
                <h4>{t?.manufacturingHighlight || 'Manufacturing Highlight'}</h4>
                <p>
                  {t?.manufacturingHighlightDesc || 
                    `Prachinburi Province manufacturing share is ${prachinburiData['Manufacturing Share (%)'].toFixed(1)}%, 
                    higher than the regional average of ${regionalData['Manufacturing Share (%)'].toFixed(1)}%. 
                    This indicates the area has strong competitiveness and development potential in manufacturing.`
                  }
                </p>
              </div>
              <div className="highlight-item">
                <h4>{t?.skillHighlight || 'Skilled Worker Highlight'}</h4>
                <p>
                  {t?.skillHighlightDesc || 
                    `Prachinburi Province skilled worker percentage is ${prachinburiData['Skilled Worker %'].toFixed(1)}%, 
                    with a skill gap of ${prachinburiData['Skill Gap (%)'].toFixed(1)}%. 
                    This indicates significant room for skill training and education improvement in the area.`
                  }
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LaborEconomicsAnalysis;
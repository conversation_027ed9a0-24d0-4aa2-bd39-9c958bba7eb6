import React, { useState, useEffect } from 'react';
import { Bar, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import translations from '../../../utils/translations'; // 确保正确导入

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const LaborDataCharts = ({ parkName, language }) => {
  const [laborData, setLaborData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('');
  const [availableRegions, setAvailableRegions] = useState([]);
  
  // 添加错误处理
  const t = (() => {
    try {
      return translations[language] || translations['en'];
    } catch (err) {
      console.error('Translation object access error:', err);
      return { loading: 'Loading...', dataLoadError: 'Cannot load labor data' };
    }
  })();

  useEffect(() => {
    fetch('/data/Thai_labor_economics.json')
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((data) => {
        console.log('Loaded labor data:', data);
        // 转换新数据结构为兼容格式
        const convertedData = {};
        if (data['Raw Data']) {
          data['Raw Data'].forEach(regionData => {
            const regionName = regionData['Province_and_Gender'];
            convertedData[regionName] = {
              laborEconomics: regionData
            };
          });
        }
        setLaborData(convertedData);

        const regions = Object.keys(convertedData);
        const sortedRegions = regions.sort((a, b) => {
          if (a === 'Regional Total') return -1;
          if (b === 'Regional Total') return 1;
          if (a === 'Prachin Buri') return -1;
          if (b === 'Prachin Buri') return 1;
          return a.localeCompare(b);
        });
        setAvailableRegions(sortedRegions);

        if (parkName === '304 Industrial Park (Prachinburi）' && sortedRegions.includes('Prachin Buri')) {
          setSelectedRegion('Prachin Buri');
        } else if (sortedRegions.includes('Regional Total')) {
          setSelectedRegion('Regional Total');
        } else if (sortedRegions.length > 0) {
          setSelectedRegion(sortedRegions[0]);
        }
        setLoading(false);
      })
      .catch((error) => {
        console.error('Error loading labor data:', error);
        setLaborData(null);
        setLoading(false);
      });
  }, [parkName]);

  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>{t.loading || '加载中...'}</p>
      </div>
    );
  }

  if (!laborData || !selectedRegion) {
    return (
      <div className="error-message">
        <p>{t.dataLoadError || '无法加载劳动力数据'}</p>
      </div>
    );
  }

  const regionData = laborData[selectedRegion];

  const prepareEmploymentByIndustryData = () => {
    if (!regionData || !regionData.laborEconomics) return null;
    const economics = regionData.laborEconomics;
    
    // 使用新数据结构中的字段
    const industries = ['制造业', '政府部门', '私营企业', '个体经营', '家族企业'];
    const data = [
      economics['Manufacturing Workforce'] || 0,
      economics['Government Employee'] || 0,
      economics['Private Employee'] || 0,
      economics['Self-Employed'] || 0,
      economics['Family Business Worker'] || 0,
    ];
    
    return {
      labels: industries,
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareEmploymentByOccupationData = () => {
    if (!regionData || !regionData.laborEconomics) return null;
    const economics = regionData.laborEconomics;
    
    // 使用新数据结构中的教育和技能相关字段
    const categories = ['高等教育', '职业培训', '技能工人', '其他劳动力'];
    const data = [
      economics['Higher Education'] || 0,
      economics['Vocational Training'] || 0,
      (economics['Higher Education'] || 0) + (economics['Vocational Training'] || 0),
      (economics['Total Labor Force'] || 0) - ((economics['Higher Education'] || 0) + (economics['Vocational Training'] || 0))
    ];
    
    return {
      labels: categories,
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareLaborForceParticipationData = () => {
    if (!regionData || !regionData.laborEconomics) return null;
    const economics = regionData.laborEconomics;
    
    const laborForce = economics['Total Labor Force'] || 0;
    const notInLaborForce = (economics['Aged 15 and Over'] || 0) - laborForce;
    
    return {
      labels: [t.inLaborForce || '劳动力人口', t.notInLaborForce || '非劳动力人口'],
      datasets: [
        {
          data: [Math.round(laborForce), Math.round(notInLaborForce)],
          backgroundColor: ['rgba(54, 162, 235, 0.6)', 'rgba(255, 99, 132, 0.6)'],
          borderColor: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)'],
          borderWidth: 1,
        },
      ],
    };
  };

  // 删除 prepareDetailedLaborForceData 函数（约第160行开始）
  
  // 新增就业人数数据准备函数
  const prepareEmploymentData = () => {
    if (!regionData || !regionData.laborEconomics) return null;
    const economics = regionData.laborEconomics;
    
    return {
      labels: [t.employedPersons || '就业人数'],
      datasets: [
        {
          label: t.inLaborForce || 'In Labor Force',
          data: [Math.round(economics['Employed Persons'] || 0)],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // 新增其他劳动力类别数据准备函数
  const prepareOtherLaborForceData = () => {
    if (!regionData || !regionData.laborEconomics) return null;
    const economics = regionData.laborEconomics;
    
    const laborForceCategories = [
      { key: 'Unemployed Persons', label: t.unemployedPersons || '失业人口' },
      { key: 'Seasonally Unemployed', label: t.seasonallyUnemployed || '季节性失业' }
    ];
    
    const laborForceData = [
      Math.round(economics['Unemployed Persons'] || 0),
      Math.round(economics['Seasonally Unemployed'] || 0)
    ];
    
    // 简化非劳动力类别，因为新数据源没有详细分类
    const nonLaborForceData = [
      Math.round(economics['Underemployed'] || 0), // 就业不充分
      Math.round(((economics['Aged 15 and Over'] || 0) - (economics['Total Labor Force'] || 0)) / 2) // 其他非劳动力
    ];
    
    return {
      labels: [
        ...laborForceCategories.map(c => c.label),
        '就业不充分', '其他非劳动力'
      ],
      datasets: [
        {
          label: t.inLaborForce || 'In Labor Force',
          data: [...laborForceData, ...Array(2).fill(0)],
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
          stack: 'Stack 0',
        },
        {
          label: t.notInLaborForce || 'Not in Labor Force',
          data: [...Array(2).fill(0), ...nonLaborForceData],
          backgroundColor: 'rgba(255, 99, 132, 0.6)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
          stack: 'Stack 0',
        }
      ]
    };
  };

  // 修改农业与非农业数据准备函数
  const prepareAgriNonAgriData = () => {
    if (!regionData || !regionData.Table8 || !regionData.Table8.Total) return null;
    
    const agriSector = regionData.Table8.Total['Economic Activity - Agricultural Sector'];
    const nonAgriSector = regionData.Table8.Total['Economic Activity - Non-Agricultural Sector'];
    
    const validAgri = agriSector !== 'n.a.' ? Math.round(parseFloat(agriSector) || 0) : 0;
    const validNonAgri = nonAgriSector !== 'n.a.' ? Math.round(parseFloat(nonAgriSector) || 0) : 0;
    
    if (validAgri === 0 && validNonAgri === 0 && regionData.Table4 && regionData.Table4.Total) {
      const agriValue = regionData.Table4.Total['Agriculture, Forestry, and Fishing'];
      const agriEstimate = agriValue !== 'n.a.' ? Math.round(parseFloat(agriValue) || 0) : 0;
      
      const totalEmployed = regionData.Table1.Total['Employed Persons '];
      const totalEstimate = totalEmployed !== 'n.a.' ? Math.round(parseFloat(totalEmployed) || 0) : 0;
      const nonAgriEstimate = totalEstimate - agriEstimate;
      
      return {
        labels: [t.agricultural_sector || '农业部门', t.non_agricultural_sector || '非农业部门'],
        datasets: [
          {
            label: t.employment_by_sector || '按部门划分的就业情况',
            data: [agriEstimate, nonAgriEstimate],
            backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(153, 102, 255, 0.6)'],
            borderColor: ['rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],
            borderWidth: 1,
          }
        ]
      };
    }
    
    return {
      labels: [t.agricultural_sector || '农业部门', t.non_agricultural_sector || '非农业部门'],
      datasets: [
        {
          label: t.employment_by_sector || '按部门划分的就业情况',
          data: [validAgri, validNonAgri],
          backgroundColor: ['rgba(75, 192, 192, 0.6)', 'rgba(153, 102, 255, 0.6)'],
          borderColor: ['rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],
          borderWidth: 1,
        }
      ]
    };
  };

  const prepareEducationLevelData = () => {
    if (!regionData || !regionData.Table7 || !regionData.Table7.Total) return null;
    const educationLevels = [
      'No Education',
      'Primary Education',
      'lower Secondary Education',
      'Upper Secondary Education - Academic Track',
      'Upper Secondary Education - Vocational Track',
      'Higher Education - Professional Track',
      'Higher Education - Education Track',
    ];
    const data = educationLevels.map((level) => {
      const value = regionData.Table7.Total[level];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    return {
      labels: educationLevels.map((level) =>
        t[level.toLowerCase().replace(/\s+/g, '_').replace(/-/g, '_')] || level
      ),
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(201, 203, 207, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(201, 203, 207, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareEmploymentStatusData = () => {
    if (!regionData || !regionData.Table5 || !regionData.Table5.Total) return null;
    const statuses = [
      'Employer',
      ' Government Employee',
      'Private Employee',
      ' Self-Employed',
      'Family Business Worker',
    ];
    const data = statuses.map((status) => {
      const value = regionData.Table5.Total[status];
      return value === 'n.a.' ? 0 : Math.round(parseFloat(value) || 0);
    });
    return {
      labels: statuses.map((status) => t[status.trim().toLowerCase().replace(/\s+/g, '_')] || status.trim()),
      datasets: [
        {
          label: t.employedPersons || '就业人数',
          data,
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // 修改图表标题和标签的显示方式，确保使用翻译对象
  const barOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      title: { display: true, text: t.employmentDistribution || '就业分布' },
    },
  };

  // 新增：堆叠柱状图配置
  const stackedBarOptions = {
    responsive: true,
    scales: {
      x: {
        stacked: true,
      },
      y: {
        stacked: true,
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: { position: 'top' },
      title: { 
        display: true, 
        text: t.laborForceDetailedDistribution || 'Detailed Labor Force Distribution' 
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            if (value === 0) return null; // 不显示数值为0的提示
            return `${context.dataset.label}: ${value.toLocaleString()} ${t.persons || 'persons'}`;
          }
        }
      }
    },
  };

  // 修改饼图配置，确保使用翻译对象
  const pieOptions = {
    responsive: true,
    plugins: {
      legend: { position: 'top' },
      title: { display: true, text: t.laborForceParticipation || '劳动力参与情况' },
    },
  };

  const industryData = prepareEmploymentByIndustryData();
  const occupationData = prepareEmploymentByOccupationData();
  const laborForceData = prepareLaborForceParticipationData();
  const educationData = prepareEducationLevelData();
  const statusData = prepareEmploymentStatusData();
  // 替换为新的数据获取
  const employmentData = prepareEmploymentData();
  const otherLaborForceData = prepareOtherLaborForceData();
  const agriNonAgriData = prepareAgriNonAgriData();

  return (
    <div className="labor-data-charts">
      <h2>{t.laborMarketAnalysis || '劳动力市场分析'}</h2>
      <div className="region-selector">
        <label htmlFor="region-select">{t.selectRegion || '选择地区'}:</label>
        <select id="region-select" value={selectedRegion} onChange={handleRegionChange} className="region-select">
          {availableRegions.map((region) => (
            <option key={region} value={region}>
              {region === 'Regional Total'
                ? t.regional_total || '区域总计'
                : region === 'Prachin Buri'
                ? `${region} (${t.currentLocation || '当前位置'})`
                : `${region} (${t.nearbyArea || '周边地区'})`}
            </option>
          ))}
        </select>
      </div>
      <div className="region-info">
        <p>
          {selectedRegion === 'Regional Total'
            ? t.regionalTotalDescription || '显示泰国中部地区的总体劳动力数据'
            : selectedRegion === 'Prachin Buri'
            ? t.prachinBuriDescription || '304工业园所在地区的劳动力数据'
            : t.nearbyAreaDescription || '周边地区的劳动力数据，可作为参考'}
        </p>
      </div>
      <div className="labor-stats-summary">
        <div className="stat-card">
          <h3>{t.totalPopulation || '总人口 (15岁以上)'}</h3>
          <p className="stat-value">{Math.round(regionData.Table1.Total['Aged 15 and Over']).toLocaleString()}</p>
        </div>
        <div className="stat-card">
          <h3>{t.employedPersons || '就业人数'}</h3>
          <p className="stat-value">{Math.round(regionData.Table1.Total['Employed Persons ']).toLocaleString()}</p>
        </div>
        <div className="stat-card">
          <h3>{t.unemploymentRate || '失业率'}</h3>
          {/* 修复：不再乘以100，直接显示数据集中的失业率 */}
          <p className="stat-value">{regionData.Table1.Total['Unemployment Rate'].toFixed(2)}%</p>
        </div>
        <div className="stat-card">
          <h3>{t.laborForceParticipationRate || '劳动参与率'}</h3>
          <p className="stat-value">
            {(
              (regionData.Table1.Total[
                'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
              ] /
                regionData.Table1.Total['Aged 15 and Over']) *
              100
            ).toFixed(2)}
            %
          </p>
        </div>
        
        {/* 移除失业人数和制造业就业人数，保留其他两个统计卡片 */}
        <div className="stat-card">
          <h3>{t.manufacturingRatio || '制造业就业占比'}</h3>
          <p className="stat-value">
            {(
              (regionData.Table4.Total['Manufacturing'] / regionData.Table1.Total['Employed Persons ']) *
              100
            ).toFixed(2)}%
          </p>
        </div>
        <div className="stat-card">
          <h3>{t.higherEducationRatio || '高等教育比例'}</h3>
          <p className="stat-value">
            {(
              ((regionData.Table7.Total['Higher Education - Professional Track'] + 
                regionData.Table7.Total['Higher Education - Education Track']) / 
                regionData.Table1.Total['Employed Persons ']) *
              100
            ).toFixed(2)}%
          </p>
        </div>
      </div>
      <div className="charts-grid">
        <div className="chart-container">
          <h3>{t.employmentByIndustry || '按行业分类的就业情况'}</h3>
          {industryData ? <Bar data={industryData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.employmentByIndustry || '按行业分类的就业情况' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.employmentByOccupation || '按职业分类的就业情况'}</h3>
          {occupationData ? <Bar data={occupationData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.employmentByOccupation || '按职业分类的就业情况' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>

        {/* 新增三个图表 */}
        <div className="chart-container">
          <h3>{t.employedPersons || '就业人数'}</h3>
          {employmentData ? <Bar data={employmentData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.employedPersons || '就业人数' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.otherLaborCategories || '其他劳动力类别'}</h3>
          {otherLaborForceData ? <Bar data={otherLaborForceData} options={stackedBarOptions} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.agriNonAgriRatio || '农业与非农业就业比例'}</h3>
          {agriNonAgriData ? <Pie data={agriNonAgriData} options={{
            ...pieOptions,
            plugins: {
              ...pieOptions.plugins,
              title: { display: true, text: t.agriNonAgriRatio || '农业与非农业就业比例' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>

        {/* 原教育水平图表 */}
        <div className="chart-container">
          <h3>{t.educationLevel || '就业人口教育水平'}</h3>
          {educationData ? <Bar data={educationData} options={{
            ...barOptions,
            plugins: {
              ...barOptions.plugins,
              title: { display: true, text: t.educationLevel || '就业人口教育水平' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>

        {/* 保留原有饼图 */}
        <div className="chart-container">
          <h3>{t.laborForceParticipation || '劳动力参与情况'}</h3>
          {laborForceData ? <Pie data={laborForceData} options={pieOptions} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
        <div className="chart-container">
          <h3>{t.employmentStatus || '就业状态分布'}</h3>
          {statusData ? <Pie data={statusData} options={{
            ...pieOptions,
            plugins: {
              ...pieOptions.plugins,
              title: { display: true, text: t.employmentStatus || '就业状态分布' }
            }
          }} /> : <p>{t.noData || '暂无数据'}</p>}
        </div>
      </div>
      <div className="labor-data-insights">
        <h3>{t.marketInsights || 'Market Insights'}</h3>
        <div className="insights-content">
          <p>
            {t.laborMarketInsight1 ||
              `The manufacturing sector employs ${regionData.Table4.Total[
                'Manufacturing'
              ].toLocaleString()} people in ${selectedRegion}, accounting for ${(
                (regionData.Table4.Total['Manufacturing'] / regionData.Table1.Total['Employed Persons ']) *
                100
              ).toFixed(2)}% of total employment, making it the main employment industry in this region.`}
          </p>
          <p>
            {t.laborMarketInsight2 ||
              `The region has a labor force participation rate of ${(
                (regionData.Table1.Total[
                  'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
                ] /
                  regionData.Table1.Total['Aged 15 and Over']) *
                100
              ).toFixed(2)}% and an unemployment rate of ${(regionData.Table1.Total['Unemployment Rate'] * 100).toFixed(2)}%.`}
          </p>
          <p>
            {t.laborMarketInsight3 ||
              `In terms of education, ${(
                ((regionData.Table7.Total['lower Secondary Education'] +
                  regionData.Table7.Total['Upper Secondary Education - Academic Track'] +
                  regionData.Table7.Total['Upper Secondary Education - Vocational Track'] +
                  regionData.Table7.Total['Higher Education - Professional Track'] +
                  regionData.Table7.Total['Higher Education - Education Track']) /
                  regionData.Table1.Total['Employed Persons ']) *
                100
              ).toFixed(2)}% of workers have secondary education or higher, indicating a relatively high-quality workforce in this region.`}
          </p>
        </div>
      </div>
      <div className="gender-comparison">
        <h3>{t.genderComparison || '性别对比分析'}</h3>
        <div className="gender-stats">
          <div className="gender-stat-card male">
            <h4>{t.male || '男性'}</h4>
            <p>
              {t.employedPersons || '就业人数'}:{' '}
              {Math.round(regionData.Table1.Male['Employed Persons ']).toLocaleString()}
            </p>
            <p>
              {t.unemploymentRate || '失业率'}:{' '}
              {(regionData.Table1.Male['Unemployment Rate'] * 100).toFixed(2)}%
            </p>
            <p>
              {t.laborForceParticipationRate || '劳动参与率'}:{' '}
              {(
                (regionData.Table1.Male[
                  'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
                ] /
                  regionData.Table1.Male['Aged 15 and Over']) *
                100
              ).toFixed(2)}
              %
            </p>
          </div>
          <div className="gender-stat-card female">
            <h4>{t.female || '女性'}</h4>
            <p>
              {t.employedPersons || '就业人数'}:{' '}
              {Math.round(regionData.Table1.Female['Employed Persons ']).toLocaleString()}
            </p>
            <p>
              {t.unemploymentRate || '失业率'}:{' '}
              {(regionData.Table1.Female['Unemployment Rate'] * 100).toFixed(2)}%
            </p>
            <p>
              {t.laborForceParticipationRate || '劳动参与率'}:{' '}
              {(
                (regionData.Table1.Female[
                  'Total （Total Labor Force， employed+unemployed+seasonally unemployed）'
                ] /
                  regionData.Table1.Female['Aged 15 and Over']) *
                100
              ).toFixed(2)}
              %
            </p>
          </div>
        </div>
      </div>
      <div className="data-source-info">
        <p className="data-source">
          {t.dataSource || '数据来源'}: {t.laborDataSource || '泰国国家统计局，2024年第四季度'}
        </p>
      </div>
    </div>
  );
};

export default LaborDataCharts;

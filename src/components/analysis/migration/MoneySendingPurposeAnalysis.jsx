import React, { useState, useEffect } from 'react';
import '../../../styles/MoneyPurposeAnalysis.css'; // Reuse the existing CSS
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MoneySendingPurposeAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    moneySendingPurposeAnalysis: 'Money Sending Purpose Analysis',
    moneySendingPurposeDesc: 'This analysis shows migration numbers by money sending purpose, gender, and region, helping with economic modeling and forecasting.',
    region: 'Region',
    gender: 'Gender',
    purpose: 'Purpose',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    totalMoney: 'Total Money Sent',
    forDailyLiving: 'For daily living expenses',
    forEducation: 'For education',
    toPayOffDebt: 'To pay off debt',
    unknown: 'Unknown',
    purposeDistribution: 'Purpose Distribution',
    genderComparison: 'Gender Comparison',
    regionalBreakdown: 'Regional Breakdown',
    purposePatterns: 'Purpose Patterns',
    economicImpact: 'Economic Impact',
    regionalDistribution: 'Regional Distribution',
    all: 'All',
    male: 'Male',
    female: 'Female',
    total: 'Total',
    nationwide: 'Nationwide',
    bangkok: 'Bangkok',
    central: 'Central',
    north: 'North',
    northeast: 'Northeast',
    south: 'South',
    dataInsights: 'Data Insights',
    ...t
  };

  // Check if language is Chinese and use Chinese translations
  const isChineseLanguage = language === 'zh';

  // Get the appropriate translation
  const getTranslation = (key) => {
    if (isChineseLanguage && defaultT.zh && defaultT.zh[key]) {
      return defaultT.zh[key];
    }
    return defaultT[key];
  };

  const [purposeData, setPurposeData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedPurpose, setSelectedPurpose] = useState('All');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Available purposes for filtering
  const purposes = ['All', 'For daily living expenses', 'For education', 'To pay off debt', 'Unknown'];

  // Load money purpose data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t20.csv', {
      download: true,
      header: true,
      complete: (results) => {
        if (results.data && results.data.length > 0) {
          // Transform the data into a more usable format
          const transformedData = transformPurposeData(results.data);
          setPurposeData(transformedData);
          setLoading(false);
        } else {
          setError('No data found in the CSV file');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformPurposeData = (data) => {
    // Filter out empty rows
    const filteredData = data.filter(row => row.population_label && row.population_label.trim() !== '');

    // Initialize result array
    const result = [];

    // Process each row
    filteredData.forEach(row => {
      const label = row.population_label.trim();

      // Skip empty rows
      if (!label) return;

      // Create a data object for this purpose
      result.push({
        purpose: label,
        nationwide: {
          total: parseFloat(row.total_nationwide) || 0,
          male: parseFloat(row.male_nationwide) || 0,
          female: parseFloat(row.female_nationwide) || 0
        },
        bangkok: {
          total: parseFloat(row.total_bangkok) || 0,
          male: parseFloat(row.male_bangkok) || 0,
          female: parseFloat(row.female_bangkok) || 0
        },
        central: {
          total: parseFloat(row.total_central) || 0,
          male: parseFloat(row.male_central) || 0,
          female: parseFloat(row.female_central) || 0
        },
        north: {
          total: parseFloat(row.total_north) || 0,
          male: parseFloat(row.male_north) || 0,
          female: parseFloat(row.female_north) || 0
        },
        northeast: {
          total: parseFloat(row.total_northeast) || 0,
          male: parseFloat(row.male_northeast) || 0,
          female: parseFloat(row.female_northeast) || 0
        },
        south: {
          total: parseFloat(row.total_south) || 0,
          male: parseFloat(row.male_south) || 0,
          female: parseFloat(row.female_south) || 0
        }
      });
    });

    return result;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!purposeData || purposeData.length === 0) {
      return {
        totalMoney: 0,
        dailyLiving: 0,
        education: 0,
        debtPayment: 0,
        unknown: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the total money row (Grand Total)
    const totalRow = purposeData.find(item => item.purpose === 'Grand Total');

    // Find specific purpose rows
    const dailyLivingRow = purposeData.find(item => item.purpose === 'For daily living expenses');
    const educationRow = purposeData.find(item => item.purpose === 'For education');
    const debtPaymentRow = purposeData.find(item => item.purpose === 'To pay off debt');
    const unknownRow = purposeData.find(item => item.purpose === 'Unknown');

    // Calculate values based on filters
    const totalMoney = totalRow ? totalRow[region][gender] : 0;
    const dailyLiving = dailyLivingRow ? dailyLivingRow[region][gender] : 0;
    const education = educationRow ? educationRow[region][gender] : 0;
    const debtPayment = debtPaymentRow ? debtPaymentRow[region][gender] : 0;
    const unknown = unknownRow ? unknownRow[region][gender] : 0;

    return {
      totalMoney,
      dailyLiving,
      education,
      debtPayment,
      unknown
    };
  };

  // Prepare data for purpose distribution pie chart
  const preparePurposePieData = () => {
    if (!purposeData || purposeData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define purposes to include in the chart (exclude Grand Total)
    const purposeLabels = [
      'For daily living expenses',
      'For education',
      'To pay off debt',
      'Unknown'
    ];

    // Create a structured dataset with all purposes
    const purposeValues = {};

    // Initialize with zeros
    purposeLabels.forEach(purpose => {
      purposeValues[purpose] = 0;
    });

    // Fill in actual values from the data
    purposeData.forEach(item => {
      if (purposeLabels.includes(item.purpose)) {
        purposeValues[item.purpose] = item[region][gender];
      }
    });

    // Filter out zero values
    const nonZeroPurposes = purposeLabels.filter(purpose => purposeValues[purpose] > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroPurposes.map(purpose => {
        // Map purpose to translation or use the original
        switch(purpose) {
          case 'For daily living expenses': return getTranslation('forDailyLiving');
          case 'For education': return getTranslation('forEducation');
          case 'To pay off debt': return getTranslation('toPayOffDebt');
          case 'Unknown': return getTranslation('unknown');
          default: return purpose;
        }
      }),
      datasets: [
        {
          data: nonZeroPurposes.map(purpose => purposeValues[purpose]),
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ].slice(0, nonZeroPurposes.length),
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(75, 192, 192, 1)'
          ].slice(0, nonZeroPurposes.length),
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!purposeData || purposeData.length === 0) return null;

    // Define purposes to include in the chart
    const purposeLabels = [
      'For daily living expenses',
      'For education',
      'To pay off debt',
      'Unknown'
    ];

    // Filter purposes based on selection
    const filteredPurposes = selectedPurpose === 'All'
      ? purposeLabels
      : purposeLabels.filter(purpose => purpose === selectedPurpose);

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Create datasets for male and female
    const maleData = [];
    const femaleData = [];
    const labels = [];

    // Fill in data for each purpose
    filteredPurposes.forEach(purpose => {
      const purposeItem = purposeData.find(item => item.purpose === purpose);
      if (purposeItem) {
        labels.push(
          purpose === 'For daily living expenses' ? getTranslation('forDailyLiving') :
          purpose === 'For education' ? getTranslation('forEducation') :
          purpose === 'To pay off debt' ? getTranslation('toPayOffDebt') :
          purpose === 'Unknown' ? getTranslation('unknown') : purpose
        );
        maleData.push(purposeItem[region].male);
        femaleData.push(purposeItem[region].female);
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: labels,
      datasets: [
        {
          label: getTranslation('male'),
          data: maleData,
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: getTranslation('female'),
          data: femaleData,
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional breakdown bar chart
  const prepareRegionalBreakdownData = () => {
    if (!purposeData || purposeData.length === 0) return null;

    // Define regions to include in the chart
    const chartRegions = ['bangkok', 'central', 'north', 'northeast', 'south'];

    // Get the purpose to display
    let purposeToShow = selectedPurpose;
    if (purposeToShow === 'All') {
      purposeToShow = 'For daily living expenses'; // Default to showing daily living if 'All' is selected
    }

    // Get gender to lowercase
    const gender = selectedGender.toLowerCase();

    // Find the data for the selected purpose
    const purposeItem = purposeData.find(item =>
      item.purpose === purposeToShow
    );

    if (!purposeItem) return null;

    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => getTranslation(region)),
      datasets: [
        {
          label: purposeToShow === 'For daily living expenses' ? getTranslation('forDailyLiving') :
                 purposeToShow === 'For education' ? getTranslation('forEducation') :
                 purposeToShow === 'To pay off debt' ? getTranslation('toPayOffDebt') :
                 purposeToShow === 'Unknown' ? getTranslation('unknown') : purposeToShow,
          data: chartRegions.map(region => purposeItem[region][gender]),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.raw.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  const handlePurposeChange = (e) => {
    setSelectedPurpose(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) return <div className="loading-container">{getTranslation('loading')}</div>;

  if (error) return <div className="error-message">{getTranslation('loadError')}: {error}</div>;

  return (
    <div className="money-purpose-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-money-bill-wave" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {getTranslation('moneySendingPurposeAnalysis')}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {getTranslation('moneySendingPurposeDesc')}
        </span>
      </p>

      <div className="money-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {getTranslation('region')}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {getTranslation(region.toLowerCase())}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {getTranslation('gender')}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {getTranslation(gender.toLowerCase())}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-tasks" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {getTranslation('purpose')}:
            </label>
            <select
              value={selectedPurpose}
              onChange={handlePurposeChange}
            >
              {purposes.map(purpose => (
                <option key={purpose} value={purpose}>
                  {purpose === 'All' ? getTranslation('all') :
                   purpose === 'For daily living expenses' ? getTranslation('forDailyLiving') :
                   purpose === 'For education' ? getTranslation('forEducation') :
                   purpose === 'To pay off debt' ? getTranslation('toPayOffDebt') :
                   purpose === 'Unknown' ? getTranslation('unknown') : purpose}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-money">
              <div className="stat-content">
                <h3>{getTranslation('totalMoney')}</h3>
                <div className="stat-value">{summaryStats.totalMoney.toLocaleString()}</div>
                <div className="stat-note">(15+ years)</div>
              </div>
            </div>

            <div className="stat-tile daily-living">
              <div className="stat-content">
                <h3>{getTranslation('forDailyLiving')}</h3>
                <div className="stat-value">{summaryStats.dailyLiving.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMoney > 0 ? ((summaryStats.dailyLiving / summaryStats.totalMoney) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>

            <div className="stat-tile education">
              <div className="stat-content">
                <h3>{getTranslation('forEducation')}</h3>
                <div className="stat-value">{summaryStats.education.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMoney > 0 ? ((summaryStats.education / summaryStats.totalMoney) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>

            <div className="stat-tile debt-payment">
              <div className="stat-content">
                <h3>{getTranslation('toPayOffDebt')}</h3>
                <div className="stat-value">{summaryStats.debtPayment.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMoney > 0 ? ((summaryStats.debtPayment / summaryStats.totalMoney) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{getTranslation('purposeDistribution')}</h3>
                <div className="chart-container">
                  <Pie
                    data={preparePurposePieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{getTranslation('genderComparison')}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{getTranslation('regionalBreakdown')}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalBreakdownData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {getTranslation('dataInsights')}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-pie"></i>
                  <h4>{getTranslation('purposePatterns')}</h4>
                </div>
                <p>{isChineseLanguage ?
                   "了解移民如何使用汇款有助于预测经济影响和消费模式。" :
                   "Understanding how migrants use remittances helps predict economic impacts and consumption patterns."}</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-line"></i>
                  <h4>{getTranslation('economicImpact')}</h4>
                </div>
                <p>{isChineseLanguage ?
                   "用于日常生活开支的资金直接影响当地经济和家庭消费。" :
                   "Money sent for daily living expenses directly impacts local economies and household consumption."}</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-globe-asia"></i>
                  <h4>{getTranslation('regionalDistribution')}</h4>
                </div>
                <p>{isChineseLanguage ?
                   "汇款用途的地区差异反映了不同的经济需求和发展水平。" :
                   "Regional differences in remittance purposes reflect varying economic needs and development levels."}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoneySendingPurposeAnalysis;

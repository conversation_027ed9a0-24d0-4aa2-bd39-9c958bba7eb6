import React, { useState, useEffect } from 'react';
import '../../../styles/MigrationFrequencyAnalysis.css';
import { Bar, Line, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, PointElement, LineElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MigrationFrequencyAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    migrationFrequencyAnalysis: 'Migration Frequency Analysis',
    migrationFrequencyDesc: 'This analysis shows migration population by migration frequency, gender, and region, helping you understand migration patterns and predict trends.',
    region: 'Region',
    gender: 'Gender',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalPopulation: 'Total Population',
    didNotMigrate: 'Did Not Migrate',
    migratedOnce: 'Migrated Once',
    migratedTwice: 'Migrated Twice',
    migratedMoreThanThree: 'Migrated More Than 3 Times',
    dataInsights: 'Data Insights',
    migrationPatterns: 'Migration Patterns',
    frequencyDistribution: 'Frequency Distribution',
    genderComparison: 'Gender Comparison',
    regionalTrends: 'Regional Trends',
    detailedBreakdown: 'Detailed Breakdown',
    ...t
  };

  // State variables
  const [migrationData, setMigrationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [summaryStats, setSummaryStats] = useState({
    totalPopulation: 0,
    didNotMigrate: 0,
    migratedOnce: 0,
    migratedTwice: 0,
    migratedMoreThanThree: 0
  });

  // Define regions and genders
  const regions = ['Nationwide', 'Bangkok', 'Central', 'Northern', 'Northeastern', 'Southern'];
  const genders = ['Total', 'Male', 'Female'];

  // Load migration frequency data
  useEffect(() => {
    setLoading(true);
    setError(null);

    Papa.parse('/data/t12.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          if (results.data && results.data.length > 0) {
            // Process the data
            const processedData = processRawData(results.data);
            setMigrationData(processedData);
          } else {
            throw new Error('No data found in CSV file');
          }
        } catch (err) {
          console.error('Error processing migration frequency data:', err);
          setError('Failed to process migration frequency data');
        } finally {
          setLoading(false);
        }
      },
      error: (err) => {
        console.error('Error loading migration frequency data:', err);
        setError('Failed to load migration frequency data');
        setLoading(false);
      }
    });
  }, []);

  // Process raw CSV data
  const processRawData = (rawData) => {
    // Map the data to a more usable format
    return rawData.map(row => {
      // Extract the migration frequency label
      const frequencyLabel = row.population_label;
      
      // Skip the Grand Total row for detailed data
      if (frequencyLabel === 'Grand Total') {
        return {
          frequency: 'Total',
          nationwide: {
            total: parseFloat(row.total_nationwide_total_nationwide) || 0,
            male: parseFloat(row.male__male_nationwide) || 0,
            female: parseFloat(row.female__female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok_total_bangkok) || 0,
            male: parseFloat(row.male__male_bangkok) || 0,
            female: parseFloat(row.female__female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central_region_total_central) || 0,
            male: parseFloat(row.male__male_central) || 0,
            female: parseFloat(row.female__female_central) || 0
          },
          northern: {
            total: parseFloat(row.total_northern_region_total_north) || 0,
            male: parseFloat(row.male__male_north) || 0,
            female: parseFloat(row.female__female_north) || 0
          },
          northeastern: {
            total: parseFloat(row.total_northeastern_region_total_northeast) || 0,
            male: parseFloat(row.male__male_northeast) || 0,
            female: parseFloat(row.female__female_northeast) || 0
          },
          southern: {
            total: parseFloat(row.total_southern_region_total_south) || 0,
            male: parseFloat(row.male__male_south) || 0,
            female: parseFloat(row.female__female_south) || 0
          }
        };
      }
      
      // For other rows, map to specific migration frequency
      let frequency = '';
      if (frequencyLabel === 'Did Not Migrate') frequency = 'Did Not Migrate';
      else if (frequencyLabel === '1 Time') frequency = '1 Time';
      else if (frequencyLabel === '2 Times') frequency = '2 Times';
      else if (frequencyLabel === 'More Than 3 Times') frequency = 'More Than 3 Times';
      else frequency = frequencyLabel;
      
      return {
        frequency,
        nationwide: {
          total: parseFloat(row.total_nationwide_total_nationwide) || 0,
          male: parseFloat(row.male__male_nationwide) || 0,
          female: parseFloat(row.female__female_nationwide) || 0
        },
        bangkok: {
          total: parseFloat(row.total_bangkok_total_bangkok) || 0,
          male: parseFloat(row.male__male_bangkok) || 0,
          female: parseFloat(row.female__female_bangkok) || 0
        },
        central: {
          total: parseFloat(row.total_central_region_total_central) || 0,
          male: parseFloat(row.male__male_central) || 0,
          female: parseFloat(row.female__female_central) || 0
        },
        northern: {
          total: parseFloat(row.total_northern_region_total_north) || 0,
          male: parseFloat(row.male__male_north) || 0,
          female: parseFloat(row.female__female_north) || 0
        },
        northeastern: {
          total: parseFloat(row.total_northeastern_region_total_northeast) || 0,
          male: parseFloat(row.male__male_northeast) || 0,
          female: parseFloat(row.female__female_northeast) || 0
        },
        southern: {
          total: parseFloat(row.total_southern_region_total_south) || 0,
          male: parseFloat(row.male__male_south) || 0,
          female: parseFloat(row.female__female_south) || 0
        }
      };
    });
  };

  // Update summary statistics when data, region, or gender changes
  useEffect(() => {
    if (migrationData) {
      calculateSummaryStats();
    }
  }, [migrationData, selectedRegion, selectedGender]);

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!migrationData || migrationData.length === 0) {
      return;
    }

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the total population row
    const totalRow = migrationData.find(item => item.frequency === 'Total');
    
    // Find specific frequency rows
    const didNotMigrateRow = migrationData.find(item => item.frequency === 'Did Not Migrate');
    const migratedOnceRow = migrationData.find(item => item.frequency === '1 Time');
    const migratedTwiceRow = migrationData.find(item => item.frequency === '2 Times');
    const migratedMoreThanThreeRow = migrationData.find(item => item.frequency === 'More Than 3 Times');

    // Calculate statistics
    const totalPopulation = totalRow ? totalRow[region][gender] : 0;
    const didNotMigrate = didNotMigrateRow ? didNotMigrateRow[region][gender] : 0;
    const migratedOnce = migratedOnceRow ? migratedOnceRow[region][gender] : 0;
    const migratedTwice = migratedTwiceRow ? migratedTwiceRow[region][gender] : 0;
    const migratedMoreThanThree = migratedMoreThanThreeRow ? migratedMoreThanThreeRow[region][gender] : 0;

    // Update state
    setSummaryStats({
      totalPopulation,
      didNotMigrate,
      migratedOnce,
      migratedTwice,
      migratedMoreThanThree
    });
  };

  // Prepare data for migration frequency distribution pie chart
  const prepareFrequencyPieData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define frequencies to include in the chart
    const frequencies = [
      'Did Not Migrate',
      '1 Time',
      '2 Times',
      'More Than 3 Times'
    ];

    // Create a structured dataset with all frequencies
    const frequencyValues = {};

    // Initialize with zeros
    frequencies.forEach(freq => {
      frequencyValues[freq] = 0;
    });

    // Populate with actual values
    migrationData.forEach(item => {
      if (frequencies.includes(item.frequency)) {
        frequencyValues[item.frequency] = item[region][gender];
      }
    });

    // Filter out zero values
    const nonZeroFrequencies = frequencies.filter(freq => frequencyValues[freq] > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroFrequencies.map(freq => {
        if (freq === 'Did Not Migrate') return defaultT.didNotMigrate;
        if (freq === '1 Time') return defaultT.migratedOnce;
        if (freq === '2 Times') return defaultT.migratedTwice;
        if (freq === 'More Than 3 Times') return defaultT.migratedMoreThanThree;
        return freq;
      }),
      datasets: [
        {
          data: nonZeroFrequencies.map(freq => frequencyValues[freq]),
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Define frequencies to include in the chart
    const frequencies = [
      'Did Not Migrate',
      '1 Time',
      '2 Times',
      'More Than 3 Times'
    ];

    // Create datasets for male and female
    const maleValues = {};
    const femaleValues = {};

    // Initialize with zeros
    frequencies.forEach(freq => {
      maleValues[freq] = 0;
      femaleValues[freq] = 0;
    });

    // Populate with actual values
    migrationData.forEach(item => {
      if (frequencies.includes(item.frequency)) {
        maleValues[item.frequency] = item[region].male;
        femaleValues[item.frequency] = item[region].female;
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: frequencies.map(freq => {
        if (freq === 'Did Not Migrate') return defaultT.didNotMigrate;
        if (freq === '1 Time') return defaultT.migratedOnce;
        if (freq === '2 Times') return defaultT.migratedTwice;
        if (freq === 'More Than 3 Times') return defaultT.migratedMoreThanThree;
        return freq;
      }),
      datasets: [
        {
          label: defaultT.male || 'Male',
          data: frequencies.map(freq => maleValues[freq]),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.female || 'Female',
          data: frequencies.map(freq => femaleValues[freq]),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional trends line chart
  const prepareRegionalTrendsData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Define regions to include in the chart
    const chartRegions = ['nationwide', 'bangkok', 'central', 'northern', 'northeastern', 'southern'];
    
    // Define the frequency we want to track across regions (using '2 Times' as it has the most data)
    const frequency = '2 Times';
    
    // Get the gender to display
    const gender = selectedGender.toLowerCase();
    
    // Find the data for the selected frequency
    const frequencyData = migrationData.find(item => item.frequency === frequency);
    
    if (!frequencyData) return null;
    
    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'nationwide') return defaultT.nationwide || 'Nationwide';
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'northern') return defaultT.northern || 'Northern';
        if (region === 'northeastern') return defaultT.northeastern || 'Northeastern';
        if (region === 'southern') return defaultT.southern || 'Southern';
        return region;
      }),
      datasets: [
        {
          label: defaultT.migratedTwice || 'Migrated Twice',
          data: chartRegions.map(region => frequencyData[region][gender]),
          borderColor: 'rgba(75, 192, 192, 1)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.4,
          fill: true
        }
      ]
    };

    return chartData;
  };

  // Prepare data for detailed breakdown bar chart
  const prepareDetailedBreakdownData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Define frequencies to include in the chart
    const frequencies = [
      'Did Not Migrate',
      '1 Time',
      '2 Times',
      'More Than 3 Times'
    ];

    // Define regions to include
    const chartRegions = ['nationwide', 'bangkok', 'central', 'northern', 'northeastern', 'southern'];
    
    // Get the gender to display
    const gender = selectedGender.toLowerCase();
    
    // Create datasets for each frequency
    const datasets = frequencies.map((freq, index) => {
      const frequencyData = migrationData.find(item => item.frequency === freq);
      
      // Skip if no data found for this frequency
      if (!frequencyData) return null;
      
      // Define colors for each frequency
      const colors = [
        { bg: 'rgba(54, 162, 235, 0.7)', border: 'rgba(54, 162, 235, 1)' },
        { bg: 'rgba(255, 99, 132, 0.7)', border: 'rgba(255, 99, 132, 1)' },
        { bg: 'rgba(255, 206, 86, 0.7)', border: 'rgba(255, 206, 86, 1)' },
        { bg: 'rgba(75, 192, 192, 0.7)', border: 'rgba(75, 192, 192, 1)' }
      ];
      
      // Get label based on frequency
      let label = freq;
      if (freq === 'Did Not Migrate') label = defaultT.didNotMigrate;
      if (freq === '1 Time') label = defaultT.migratedOnce;
      if (freq === '2 Times') label = defaultT.migratedTwice;
      if (freq === 'More Than 3 Times') label = defaultT.migratedMoreThanThree;
      
      return {
        label,
        data: chartRegions.map(region => frequencyData[region][gender]),
        backgroundColor: colors[index % colors.length].bg,
        borderColor: colors[index % colors.length].border,
        borderWidth: 1
      };
    }).filter(dataset => dataset !== null);
    
    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'nationwide') return defaultT.nationwide || 'Nationwide';
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'northern') return defaultT.northern || 'Northern';
        if (region === 'northeastern') return defaultT.northeastern || 'Northeastern';
        if (region === 'southern') return defaultT.southern || 'Southern';
        return region;
      }),
      datasets
    };

    return chartData;
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Chart options for line charts
  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="migration-frequency-container">
        <div className="loading-container">
          <i className="fas fa-spinner fa-spin" style={{ marginRight: '10px' }}></i>
          {defaultT.loading}
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="migration-frequency-container">
        <div className="error-message">
          <i className="fas fa-exclamation-triangle" style={{ marginRight: '10px' }}></i>
          {defaultT.loadError}: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="migration-frequency-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-exchange-alt" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.migrationFrequencyAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.migrationFrequencyDesc}
        </span>
      </p>

      <div className="migration-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile population">
              <div className="stat-content">
                <h3>{defaultT.totalPopulation}</h3>
                <div className="stat-value">{summaryStats.totalPopulation.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile migrated-once">
              <div className="stat-content">
                <h3>{defaultT.migratedOnce}</h3>
                <div className="stat-value">{summaryStats.migratedOnce.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.migratedOnce / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile migrated-twice">
              <div className="stat-content">
                <h3>{defaultT.migratedTwice}</h3>
                <div className="stat-value">{summaryStats.migratedTwice.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.migratedTwice / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile migrated-more">
              <div className="stat-content">
                <h3>{defaultT.migratedMoreThanThree}</h3>
                <div className="stat-value">{summaryStats.migratedMoreThanThree.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.migratedMoreThanThree / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.frequencyDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareFrequencyPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.genderComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.regionalTrends}</h3>
                <div className="chart-container">
                  <Line
                    data={prepareRegionalTrendsData()}
                    options={lineOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.detailedBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareDetailedBreakdownData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          position: 'top'
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-exchange-alt"></i>
                  <h4>{defaultT.migrationPatterns}</h4>
                </div>
                <p>Migration frequency analysis helps understand how often people migrate, which can inform infrastructure and service planning.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{defaultT.genderComparison}</h4>
                </div>
                <p>Gender differences in migration frequency can reveal social and economic factors influencing mobility patterns.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalTrends}</h4>
                </div>
                <p>Regional variations in migration frequency help identify areas with high population turnover or stability.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-line"></i>
                  <h4>{defaultT.frequencyDistribution}</h4>
                </div>
                <p>Understanding migration frequency distribution helps predict future migration trends and resource needs.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MigrationFrequencyAnalysis;

import React, { useState, useEffect } from 'react';
import '../../../styles/MoneyGoodsReceiptAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MoneyGoodsReceiptAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    moneyGoodsReceiptAnalysis: 'Money & Goods Receipt Analysis',
    moneyGoodsReceiptDesc: 'This analysis shows migration numbers by received money and goods, gender, and region, helping you understand financial impacts of migration.',
    region: 'Region',
    gender: 'Gender',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMigrants: 'Total Migrants',
    receivedMoney: 'Received Money',
    receivedGoods: 'Received Goods',
    receivedBoth: 'Received Both',
    didNotReceive: 'Did Not Receive',
    dataInsights: 'Data Insights',
    financialImpact: 'Financial Impact',
    remittancePatterns: 'Remittance Patterns',
    genderDifferences: 'Gender Differences',
    regionalVariations: 'Regional Variations',
    receiptDistribution: 'Receipt Distribution',
    genderComparison: 'Gender Comparison',
    regionalBreakdown: 'Regional Breakdown',
    fundFlowAnalysis: 'Fund Flow Analysis',
    ...t
  };

  const [receiptData, setReceiptData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Load data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t15.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          if (results.data && results.data.length > 0) {
            // Transform the data into a more usable format
            const transformedData = transformReceiptData(results.data);
            setReceiptData(transformedData);
            setLoading(false);
          } else {
            setError('No data found in the CSV file');
            setLoading(false);
          }
        } catch (err) {
          console.error('Error processing money/goods receipt data:', err);
          setError('Failed to process money/goods receipt data');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformReceiptData = (data) => {
    // Create a structured object to hold the data
    const transformedData = {};

    // Process each row in the CSV
    data.forEach(row => {
      const label = row['Population Label'];
      
      // Skip empty rows or rows without a label
      if (!label) return;

      // Initialize the label entry if it doesn't exist
      if (!transformedData[label]) {
        transformedData[label] = {};
      }

      // Process each region and gender combination
      regions.forEach(region => {
        if (!transformedData[label][region.toLowerCase()]) {
          transformedData[label][region.toLowerCase()] = {};
        }

        genders.forEach(gender => {
          const columnName = `${gender}_${region}`;
          const value = parseFloat(row[columnName]) || 0;
          transformedData[label][region.toLowerCase()][gender.toLowerCase()] = value;
        });
      });
    });

    return transformedData;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!receiptData || Object.keys(receiptData).length === 0) {
      return {
        totalMigrants: 0,
        receivedMoney: 0,
        receivedGoods: 0,
        receivedBoth: 0,
        didNotReceive: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Get the total migrants
    const totalMigrants = receiptData['Grand Total']?.[region]?.[gender] || 0;

    // Get the migrants who received only money
    const receivedMoney = receiptData['Received Only Money']?.[region]?.[gender] || 0;

    // Get the migrants who received only goods
    const receivedGoods = receiptData['Received Only Goods']?.[region]?.[gender] || 0;

    // Get the migrants who received both money and goods
    const receivedBoth = receiptData['Received Both Money and Goods']?.[region]?.[gender] || 0;

    // Get the migrants who did not receive anything
    const didNotReceive = receiptData['Did Not Receive']?.[region]?.[gender] || 0;

    return {
      totalMigrants,
      receivedMoney,
      receivedGoods,
      receivedBoth,
      didNotReceive
    };
  };

  // Prepare data for receipt distribution pie chart
  const prepareReceiptPieData = () => {
    if (!receiptData || Object.keys(receiptData).length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define receipt categories to include in the chart
    const receiptCategories = [
      'Received Only Money',
      'Received Only Goods',
      'Received Both Money and Goods',
      'Did Not Receive'
    ];

    // Create a structured dataset with all receipt categories
    const receiptValues = {};

    // Initialize with zeros
    receiptCategories.forEach(category => {
      receiptValues[category] = 0;
    });

    // Fill in the values from the data
    receiptCategories.forEach(category => {
      if (receiptData[category] && receiptData[category][region] && receiptData[category][region][gender]) {
        receiptValues[category] = receiptData[category][region][gender];
      }
    });

    // Filter out categories with zero values
    const nonZeroCategories = receiptCategories.filter(category => receiptValues[category] > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroCategories.map(category => {
        if (category === 'Received Only Money') return defaultT.receivedMoney;
        if (category === 'Received Only Goods') return defaultT.receivedGoods;
        if (category === 'Received Both Money and Goods') return defaultT.receivedBoth;
        if (category === 'Did Not Receive') return defaultT.didNotReceive;
        return category;
      }),
      datasets: [
        {
          data: nonZeroCategories.map(category => receiptValues[category]),
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!receiptData || Object.keys(receiptData).length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Define receipt categories to include in the chart
    const receiptCategories = [
      'Received Only Money',
      'Received Only Goods',
      'Received Both Money and Goods',
      'Did Not Receive'
    ];

    // Create structured datasets for male and female
    const maleValues = {};
    const femaleValues = {};

    // Initialize with zeros
    receiptCategories.forEach(category => {
      maleValues[category] = 0;
      femaleValues[category] = 0;
    });

    // Fill in the values from the data
    receiptCategories.forEach(category => {
      if (receiptData[category] && receiptData[category][region]) {
        maleValues[category] = receiptData[category][region]['male'] || 0;
        femaleValues[category] = receiptData[category][region]['female'] || 0;
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: receiptCategories.map(category => {
        if (category === 'Received Only Money') return defaultT.receivedMoney;
        if (category === 'Received Only Goods') return defaultT.receivedGoods;
        if (category === 'Received Both Money and Goods') return defaultT.receivedBoth;
        if (category === 'Did Not Receive') return defaultT.didNotReceive;
        return category;
      }),
      datasets: [
        {
          label: defaultT.male || 'Male',
          data: receiptCategories.map(category => maleValues[category]),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.female || 'Female',
          data: receiptCategories.map(category => femaleValues[category]),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional breakdown stacked bar chart
  const prepareRegionalBreakdownData = () => {
    if (!receiptData || Object.keys(receiptData).length === 0) return null;

    // Convert gender to lowercase for data access
    const gender = selectedGender.toLowerCase();

    // Define receipt categories to include in the chart
    const receiptCategories = [
      'Received Only Money',
      'Received Only Goods',
      'Received Both Money and Goods'
    ];

    // Define regions to include
    const chartRegions = ['bangkok', 'central', 'north', 'northeast', 'south'];
    
    // Create a structured dataset for each receipt category
    const datasets = receiptCategories.map((category, index) => {
      const colors = [
        { bg: 'rgba(54, 162, 235, 0.7)', border: 'rgba(54, 162, 235, 1)' },
        { bg: 'rgba(255, 99, 132, 0.7)', border: 'rgba(255, 99, 132, 1)' },
        { bg: 'rgba(255, 206, 86, 0.7)', border: 'rgba(255, 206, 86, 1)' }
      ];
      
      // Get label based on category
      let label = category;
      if (category === 'Received Only Money') label = defaultT.receivedMoney;
      if (category === 'Received Only Goods') label = defaultT.receivedGoods;
      if (category === 'Received Both Money and Goods') label = defaultT.receivedBoth;
      
      return {
        label,
        data: chartRegions.map(region => 
          receiptData[category]?.[region]?.[gender] || 0
        ),
        backgroundColor: colors[index % colors.length].bg,
        borderColor: colors[index % colors.length].border,
        borderWidth: 1
      };
    });

    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'north') return defaultT.northern || 'Northern';
        if (region === 'northeast') return defaultT.northeastern || 'Northeastern';
        if (region === 'south') return defaultT.southern || 'Southern';
        return region;
      }),
      datasets
    };

    return chartData;
  };

  // Prepare data for fund flow analysis chart
  const prepareFundFlowData = () => {
    if (!receiptData || Object.keys(receiptData).length === 0) return null;

    // Define regions to include
    const chartRegions = ['nationwide', 'bangkok', 'central', 'north', 'northeast', 'south'];
    
    // Create datasets for money and both categories
    const moneyDataset = {
      label: defaultT.receivedMoney || 'Received Money',
      data: chartRegions.map(region => 
        receiptData['Received Only Money']?.[region]?.['total'] || 0
      ),
      backgroundColor: 'rgba(54, 162, 235, 0.7)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    };
    
    const bothDataset = {
      label: defaultT.receivedBoth || 'Received Both',
      data: chartRegions.map(region => 
        receiptData['Received Both Money and Goods']?.[region]?.['total'] || 0
      ),
      backgroundColor: 'rgba(255, 206, 86, 0.7)',
      borderColor: 'rgba(255, 206, 86, 1)',
      borderWidth: 1
    };

    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'nationwide') return defaultT.nationwide || 'Nationwide';
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'north') return defaultT.northern || 'Northern';
        if (region === 'northeast') return defaultT.northeastern || 'Northeastern';
        if (region === 'south') return defaultT.southern || 'Southern';
        return region;
      }),
      datasets: [moneyDataset, bothDataset]
    };

    return chartData;
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Chart options for stacked bar charts
  const stackedBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        stacked: true,
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  // If loading, show loading message
  if (loading) {
    return (
      <div className="money-goods-receipt-container">
        <div className="loading-container">
          <i className="fas fa-spinner fa-spin" style={{ marginRight: '10px' }}></i>
          {defaultT.loading}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="money-goods-receipt-container">
        <div className="error-message">
          <i className="fas fa-exclamation-triangle" style={{ marginRight: '10px' }}></i>
          {defaultT.loadError}: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="money-goods-receipt-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-money-bill-wave" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsReceiptAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsReceiptDesc}
        </span>
      </p>

      <div className="money-goods-receipt-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-migrants">
              <div className="stat-content">
                <h3>{defaultT.totalMigrants}</h3>
                <div className="stat-value">{summaryStats.totalMigrants.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile received-money">
              <div className="stat-content">
                <h3>{defaultT.receivedMoney}</h3>
                <div className="stat-value">{summaryStats.receivedMoney.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.receivedMoney / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile received-both">
              <div className="stat-content">
                <h3>{defaultT.receivedBoth}</h3>
                <div className="stat-value">{summaryStats.receivedBoth.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.receivedBoth / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile did-not-receive">
              <div className="stat-content">
                <h3>{defaultT.didNotReceive}</h3>
                <div className="stat-value">{summaryStats.didNotReceive.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.didNotReceive / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.receiptDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareReceiptPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.genderComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.regionalBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalBreakdownData()}
                    options={stackedBarOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.fundFlowAnalysis}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareFundFlowData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-money-bill-wave"></i>
                  <h4>{defaultT.financialImpact}</h4>
                </div>
                <p>Analysis of money and goods receipt patterns helps understand the financial impact of migration on households.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-exchange-alt"></i>
                  <h4>{defaultT.remittancePatterns}</h4>
                </div>
                <p>Remittance patterns reveal how migrants support their families and contribute to local economies.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{defaultT.genderDifferences}</h4>
                </div>
                <p>Gender differences in money and goods receipt highlight varying economic roles and responsibilities.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalVariations}</h4>
                </div>
                <p>Regional variations in remittance patterns can inform targeted economic development policies.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoneyGoodsReceiptAnalysis;

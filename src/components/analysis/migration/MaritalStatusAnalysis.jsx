import React, { useState, useEffect } from 'react';
import '../../../styles/MaritalStatusAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MaritalStatusAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    maritalStatusAnalysis: 'Marital Status Analysis',
    maritalStatusDesc: 'This analysis shows marital status by migration status, gender, and region, helping you understand population marital patterns.',
    region: 'Region',
    gender: 'Gender',
    migrationStatus: 'Migration Status',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalPopulation: 'Total Population',
    migrantPopulation: 'Migrant Population',
    nonMigrantPopulation: 'Non-Migrant Population',
    marriageRate: 'Marriage Rate',
    dataInsights: 'Data Insights',
    maritalPatterns: 'Marital Status Patterns',
    migrantMaritalStatus: 'Migrant Marital Status',
    nonMigrantMaritalStatus: 'Non-Migrant Marital Status',
    maritalDistribution: 'Marital Status Distribution',
    maritalComparison: 'Marital Status Comparison',
    detailedBreakdown: 'Detailed Breakdown',
    single: 'Single',
    married: 'Married',
    widowed: 'Widowed',
    divorced: 'Divorced',
    separated: 'Separated',
    ...t
  };

  const [maritalData, setMaritalData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedMigrationStatus, setSelectedMigrationStatus] = useState('Total');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Available migration statuses for filtering
  const migrationStatuses = ['Total', 'Migrant', 'Non-Migrant'];

  // Load marital status data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t4.csv', {
      download: true,
      header: true,
      complete: (results) => {
        if (results.data && results.data.length > 0) {
          // Transform the data into a more usable format
          const transformedData = transformMaritalData(results.data);
          setMaritalData(transformedData);
          setLoading(false);
        } else {
          setError('No data found in the CSV file');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformMaritalData = (data) => {
    // Filter out empty rows
    const filteredData = data.filter(row => row.population_label && row.population_label.trim() !== '');

    // Initialize result array
    const result = [];

    // Track current migration status
    let currentMigrationStatus = 'Total';

    // Process each row
    filteredData.forEach(row => {
      const label = row.population_label.trim();

      // Handle migration status rows
      if (label === 'Migrant' || label === 'Non-Migrant') {
        currentMigrationStatus = label;

        // Add the total row for this migration status
        result.push({
          maritalStatus: 'Total',
          migrationStatus: currentMigrationStatus,
          nationwide: {
            total: parseFloat(row.total_nationwide) || 0,
            male: parseFloat(row.male_nationwide) || 0,
            female: parseFloat(row.female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok) || 0,
            male: parseFloat(row.male_bangkok) || 0,
            female: parseFloat(row.female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central) || 0,
            male: parseFloat(row.male_central) || 0,
            female: parseFloat(row.female_central) || 0
          },
          north: {
            total: parseFloat(row.total_north) || 0,
            male: parseFloat(row.male_north) || 0,
            female: parseFloat(row.female_north) || 0
          },
          northeast: {
            total: parseFloat(row.total_northeast) || 0,
            male: parseFloat(row.male_northeast) || 0,
            female: parseFloat(row.female_northeast) || 0
          },
          south: {
            total: parseFloat(row.total_south) || 0,
            male: parseFloat(row.male_south) || 0,
            female: parseFloat(row.female_south) || 0
          }
        });
      }
      // Handle Grand Total row
      else if (label === 'Grand Total') {
        result.push({
          maritalStatus: 'Total',
          migrationStatus: 'Total',
          nationwide: {
            total: parseFloat(row.total_nationwide) || 0,
            male: parseFloat(row.male_nationwide) || 0,
            female: parseFloat(row.female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok) || 0,
            male: parseFloat(row.male_bangkok) || 0,
            female: parseFloat(row.female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central) || 0,
            male: parseFloat(row.male_central) || 0,
            female: parseFloat(row.female_central) || 0
          },
          north: {
            total: parseFloat(row.total_north) || 0,
            male: parseFloat(row.male_north) || 0,
            female: parseFloat(row.female_north) || 0
          },
          northeast: {
            total: parseFloat(row.total_northeast) || 0,
            male: parseFloat(row.male_northeast) || 0,
            female: parseFloat(row.female_northeast) || 0
          },
          south: {
            total: parseFloat(row.total_south) || 0,
            male: parseFloat(row.male_south) || 0,
            female: parseFloat(row.female_south) || 0
          }
        });
      }
      // Handle marital status rows
      else if (['Single', 'Married', 'Widowed', 'Divorced', 'Separated'].includes(label)) {
        result.push({
          maritalStatus: label,
          migrationStatus: currentMigrationStatus,
          nationwide: {
            total: parseFloat(row.total_nationwide) || 0,
            male: parseFloat(row.male_nationwide) || 0,
            female: parseFloat(row.female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok) || 0,
            male: parseFloat(row.male_bangkok) || 0,
            female: parseFloat(row.female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central) || 0,
            male: parseFloat(row.male_central) || 0,
            female: parseFloat(row.female_central) || 0
          },
          north: {
            total: parseFloat(row.total_north) || 0,
            male: parseFloat(row.male_north) || 0,
            female: parseFloat(row.female_north) || 0
          },
          northeast: {
            total: parseFloat(row.total_northeast) || 0,
            male: parseFloat(row.male_northeast) || 0,
            female: parseFloat(row.female_northeast) || 0
          },
          south: {
            total: parseFloat(row.total_south) || 0,
            male: parseFloat(row.male_south) || 0,
            female: parseFloat(row.female_south) || 0
          }
        });
      }
    });

    return result;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!maritalData || maritalData.length === 0) {
      return {
        totalPopulation: 0,
        migrantPopulation: 0,
        nonMigrantPopulation: 0,
        marriageRate: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the total population row
    const totalRow = maritalData.find(item =>
      item.maritalStatus === 'Total' &&
      item.migrationStatus === 'Total'
    );

    // Find the migrant population row
    const migrantRow = maritalData.find(item =>
      item.maritalStatus === 'Total' &&
      item.migrationStatus === 'Migrant'
    );

    // Find the non-migrant population row
    const nonMigrantRow = maritalData.find(item =>
      item.maritalStatus === 'Total' &&
      item.migrationStatus === 'Non-Migrant'
    );

    // Find married population row
    const marriedRow = maritalData.find(item =>
      item.maritalStatus === 'Married' &&
      (selectedMigrationStatus === 'Total' ?
        item.migrationStatus === 'Total' :
        item.migrationStatus === selectedMigrationStatus)
    );

    // Calculate total population based on filters
    const totalPopulation = totalRow ? totalRow[region][gender] : 0;

    // Calculate migrant population based on filters
    const migrantPopulation = migrantRow ? migrantRow[region][gender] : 0;

    // Calculate non-migrant population based on filters
    const nonMigrantPopulation = nonMigrantRow ? nonMigrantRow[region][gender] : 0;

    // Calculate marriage rate
    const marriedPopulation = marriedRow ? marriedRow[region][gender] : 0;
    const marriageRate = totalPopulation > 0 ? (marriedPopulation / totalPopulation) * 100 : 0;

    return {
      totalPopulation,
      migrantPopulation,
      nonMigrantPopulation,
      marriageRate
    };
  };

  // Prepare data for marital status distribution pie chart
  const prepareMaritalPieData = () => {
    if (!maritalData || maritalData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define marital statuses to include in the chart
    const maritalStatuses = [
      'Single',
      'Married',
      'Widowed',
      'Divorced',
      'Separated'
    ];

    // Create a structured dataset with all marital statuses
    const maritalValues = {};

    // Initialize with zeros
    maritalStatuses.forEach(status => {
      maritalValues[status] = 0;
    });

    // Fill in actual values from the data
    maritalData.forEach(item => {
      if (maritalStatuses.includes(item.maritalStatus)) {
        if (selectedMigrationStatus === 'Total' && item.migrationStatus === 'Total') {
          maritalValues[item.maritalStatus] = item[region][gender];
        } else if (item.migrationStatus === selectedMigrationStatus) {
          maritalValues[item.maritalStatus] = item[region][gender];
        }
      }
    });

    // Filter out zero values
    const nonZeroStatuses = maritalStatuses.filter(status => maritalValues[status] > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroStatuses.map(status => defaultT[status.toLowerCase()] || status),
      datasets: [
        {
          data: nonZeroStatuses.map(status => maritalValues[status]),
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for marital status comparison between migrant and non-migrant
  const prepareMaritalComparisonData = () => {
    if (!maritalData || maritalData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define marital statuses to include in the chart
    const maritalStatuses = [
      'Single',
      'Married',
      'Widowed',
      'Divorced',
      'Separated'
    ];

    // Create a structured dataset with all marital statuses
    const migrantValues = {};
    const nonMigrantValues = {};

    // Initialize with zeros
    maritalStatuses.forEach(status => {
      migrantValues[status] = 0;
      nonMigrantValues[status] = 0;
    });

    // Fill in actual values from the data
    maritalData.forEach(item => {
      if (maritalStatuses.includes(item.maritalStatus)) {
        if (item.migrationStatus === 'Migrant') {
          migrantValues[item.maritalStatus] = item[region][gender];
        } else if (item.migrationStatus === 'Non-Migrant') {
          nonMigrantValues[item.maritalStatus] = item[region][gender];
        }
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: maritalStatuses.map(status => defaultT[status.toLowerCase()] || status),
      datasets: [
        {
          label: defaultT.migrantMaritalStatus,
          data: maritalStatuses.map(status => migrantValues[status]),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.nonMigrantMaritalStatus,
          data: maritalStatuses.map(status => nonMigrantValues[status]),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for detailed marital status breakdown
  const prepareDetailedMaritalData = () => {
    if (!maritalData || maritalData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define marital statuses to include in the chart
    const maritalStatuses = [
      'Single',
      'Married',
      'Widowed',
      'Divorced',
      'Separated'
    ];

    // Create a structured dataset with all marital statuses
    const maritalValues = {};

    // Initialize with zeros
    maritalStatuses.forEach(status => {
      maritalValues[status] = 0;
    });

    // Fill in actual values from the data
    maritalData.forEach(item => {
      if (maritalStatuses.includes(item.maritalStatus)) {
        if (selectedMigrationStatus === 'Total' && item.migrationStatus === 'Total') {
          maritalValues[item.maritalStatus] = item[region][gender];
        } else if (item.migrationStatus === selectedMigrationStatus) {
          maritalValues[item.maritalStatus] = item[region][gender];
        }
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: maritalStatuses.map(status => defaultT[status.toLowerCase()] || status),
      datasets: [
        {
          label: defaultT[selectedMigrationStatus.toLowerCase() + 'Population'] || selectedMigrationStatus,
          data: maritalStatuses.map(status => maritalValues[status]),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        },
        // Only show one legend item per label
        filter: (legendItem, data) => {
          const index = data.labels.indexOf(legendItem.text);
          return index === data.labels.lastIndexOf(legendItem.text);
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  const handleMigrationStatusChange = (e) => {
    setSelectedMigrationStatus(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) return <div className="loading-container">{defaultT.loading}</div>;

  if (error) return <div className="error-message">{defaultT.loadError}: {error}</div>;

  return (
    <div className="marital-status-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-ring" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.maritalStatusAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.maritalStatusDesc}
        </span>
      </p>

      <div className="marital-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-users" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.migrationStatus}:
            </label>
            <select
              value={selectedMigrationStatus}
              onChange={handleMigrationStatusChange}
            >
              {migrationStatuses.map(status => (
                <option key={status} value={status}>
                  {defaultT[status.toLowerCase() + 'Population'] || status}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile population">
              <div className="stat-content">
                <h3>{defaultT.totalPopulation}</h3>
                <div className="stat-value">{summaryStats.totalPopulation.toLocaleString()}</div>
                <div className="stat-note">(15+ years)</div>
              </div>
            </div>

            <div className="stat-tile migrant">
              <div className="stat-content">
                <h3>{defaultT.migrantPopulation}</h3>
                <div className="stat-value">{summaryStats.migrantPopulation.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.migrantPopulation / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile non-migrant">
              <div className="stat-content">
                <h3>{defaultT.nonMigrantPopulation}</h3>
                <div className="stat-value">{summaryStats.nonMigrantPopulation.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.nonMigrantPopulation / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile marriage-rate">
              <div className="stat-content">
                <h3>{defaultT.marriageRate}</h3>
                <div className="stat-value">{summaryStats.marriageRate.toFixed(1)}%</div>
                <div className="stat-note">{defaultT.married}</div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.maritalDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareMaritalPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.maritalComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareMaritalComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.detailedBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareDetailedMaritalData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-ring"></i>
                  <h4>{defaultT.maritalPatterns}</h4>
                </div>
                <p>Marital status analysis helps understand population marital patterns and social structure.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-plane-arrival"></i>
                  <h4>{defaultT.migrantMaritalStatus}</h4>
                </div>
                <p>Migrant marital patterns reveal how marital status influences migration decisions and family mobility.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-line"></i>
                  <h4>{defaultT.maritalComparison}</h4>
                </div>
                <p>Comparing marital status between migrants and non-migrants helps identify social patterns and inform policy decisions.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaritalStatusAnalysis;

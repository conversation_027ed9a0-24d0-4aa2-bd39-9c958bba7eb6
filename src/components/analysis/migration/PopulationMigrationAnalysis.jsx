import React, { useState, useEffect } from 'react';
import '../../../styles/PopulationMigrationAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const PopulationMigrationAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    populationMigrationAnalysis: 'Population Migration Analysis',
    populationMigrationDesc: 'This analysis shows population migration patterns by region, age group, and gender, helping you understand labor mobility trends.',
    region: 'Region',
    ageGroup: 'Age Group',
    gender: 'Gender',
    all: 'All',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalPopulation: 'Total Population',
    migrantPopulation: 'Migrant Population',
    localPopulation: 'Local Population',
    migrationRate: 'Migration Rate',
    totalPopulationDesc: 'Total population in the selected area',
    migrantPopulationDesc: 'Population that migrated from other regions',
    localPopulationDesc: 'Population born and living in the local area',
    migrationRateDesc: 'Percentage of migrants in total population',
    dataInsights: 'Data Insights',
    ageDistribution: 'Age Distribution',
    migrationPatterns: 'Migration Patterns',
    genderRatio: 'Gender Ratio',
    migrationRatio: 'Migration to Local Population Ratio',
    populationByMigrationStatus: 'Population by Migration Status',
    ...t
  };

  const [populationData, setPopulationData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedAgeGroup, setSelectedAgeGroup] = useState('All');
  const [selectedGender, setSelectedGender] = useState('Total');

  // Load CSV data
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t1.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        setPopulationData(results.data);
        setLoading(false);
      },
      error: (error) => {
        setError(error.message);
        setLoading(false);
      }
    });
  }, []);

  // 提取可用区域列表
  const regions = [...new Set(populationData.filter(item => item.Region).map(item => item.Region))];

  // 提取年龄组列表
  const ageGroups = ['All', ...new Set(populationData.filter(item => item['Age Group']).map(item => item['Age Group']))];

  // 提取性别列表
  const genders = [...new Set(populationData.filter(item => item.Gender).map(item => item.Gender))];

  // 根据选择筛选数据
  const filteredData = populationData.filter(item => {
    const regionMatch = selectedRegion === 'All' || item.Region === selectedRegion;
    const ageGroupMatch = selectedAgeGroup === 'All' || item['Age Group'] === selectedAgeGroup;
    const genderMatch = selectedGender === 'All' || item.Gender === selectedGender;
    return regionMatch && ageGroupMatch && genderMatch;
  });

  // Group by migration status
  const migrantData = filteredData.filter(item => item['Migration Status'] === 'Migrant');
  const localData = filteredData.filter(item => item['Migration Status'] === 'Local total');
  const totalData = filteredData.filter(item => item['Migration Status'] === 'Grand total');

  // 为柱状图准备数据
  const prepareBarChartData = () => {
    let labels = [];
    let migrantValues = [];
    let localValues = [];
    let totalValues = [];

    if (selectedAgeGroup === 'All') {
      // 如果选择了所有年龄组，则按年龄组分组
      labels = [...new Set(filteredData.map(item => item['Age Group']))].filter(Boolean);

      labels.forEach(ageGroup => {
        const migrantItem = migrantData.find(item => item['Age Group'] === ageGroup);
        const localItem = localData.find(item => item['Age Group'] === ageGroup);
        const totalItem = totalData.find(item => item['Age Group'] === ageGroup);

        migrantValues.push(migrantItem ? parseFloat(migrantItem.Population) : 0);
        localValues.push(localItem ? parseFloat(localItem.Population) : 0);
        totalValues.push(totalItem ? parseFloat(totalItem.Population) : 0);
      });
    } else {
      // Otherwise group by gender
      labels = [...new Set(filteredData.map(item => item.Gender))].filter(Boolean);

      labels.forEach(gender => {
        const migrantItem = migrantData.find(item => item.Gender === gender);
        const localItem = localData.find(item => item.Gender === gender);
        const totalItem = totalData.find(item => item.Gender === gender);

        migrantValues.push(migrantItem ? parseFloat(migrantItem.Population) : 0);
        localValues.push(localItem ? parseFloat(localItem.Population) : 0);
        totalValues.push(totalItem ? parseFloat(totalItem.Population) : 0);
      });
    }

    return {
      labels,
      datasets: [
        {
          label: t.migrant || 'Migrant',
          data: migrantValues,
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1,
        },
        {
          label: t.localPopulation || 'Local Population',
          data: localValues,
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1,
        }
      ]
    };
  };

  // 为饼图准备数据
  const preparePieChartData = () => {
    // 计算总数
    const migrantTotal = migrantData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0);
    const localTotal = localData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0);

    return {
      labels: [t.migrant || 'Migrant', t.localPopulation || 'Local Population'],
      datasets: [
        {
          data: [migrantTotal, localTotal],
          backgroundColor: ['rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)'],
          borderColor: ['rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)'],
          borderWidth: 1,
        }
      ]
    };
  };

  // 图表选项
  const barChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 13,
            family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
          }
        }
      },
      title: {
        display: false, // Don't display title, we use custom title
        text: '',
        font: {
          size: 16,
          weight: 'bold',
          family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: 'rgba(0, 0, 0, 0.1)',
        borderWidth: 1,
        cornerRadius: 6,
        padding: 12,
        boxPadding: 6,
        usePointStyle: true,
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += new Intl.NumberFormat().format(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: t.population || 'Population (thousands)',
          font: {
            size: 13,
            weight: 'bold'
          },
          padding: {top: 0, bottom: 10}
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          drawBorder: false
        },
        ticks: {
          padding: 8,
          callback: function(value) {
            return new Intl.NumberFormat().format(value);
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          padding: 8,
          font: {
            size: 12
          }
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart'
    },
    layout: {
      padding: {
        top: 5,
        right: 15,
        bottom: 5,
        left: 15
      }
    },
    elements: {
      bar: {
        borderRadius: 4
      }
    }
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false, // Don't display title, we use custom title
        text: ''
      },
      legend: {
        position: 'right',
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 13,
            family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
          },
          generateLabels: function(chart) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map(function(label, i) {
                const meta = chart.getDatasetMeta(0);
                const style = meta.controller.getStyle(i);
                const value = chart.data.datasets[0].data[i];
                const total = chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);

                return {
                  text: `${label}: ${percentage}%`,
                  fillStyle: style.backgroundColor,
                  strokeStyle: style.borderColor,
                  lineWidth: style.borderWidth,
                  pointStyle: 'circle',
                  hidden: !chart.getDataVisibility(i),
                  index: i
                };
              });
            }
            return [];
          }
        }
      },
      title: {
        display: false, // Don't display duplicate title, we use custom title
        text: t.migrationRatio || 'Migration to Local Population Ratio',
        font: {
          size: 16,
          weight: 'bold',
          family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#666',
        borderColor: 'rgba(0, 0, 0, 0.1)',
        borderWidth: 1,
        cornerRadius: 6,
        padding: 12,
        boxPadding: 6,
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
            const percentage = Math.round((value / total) * 100);
            return `${context.label}: ${new Intl.NumberFormat().format(value)} (${percentage}%)`;
          }
        }
      },
      datalabels: {
        formatter: (value, ctx) => {
          const total = ctx.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
          const percentage = Math.round((value / total) * 100);
          return percentage > 5 ? `${percentage}%` : '';
        },
        color: '#fff',
        font: {
          weight: 'bold',
          size: 14
        }
      }
    },
    cutout: '40%',
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1000,
      easing: 'easeOutQuart'
    },
    elements: {
      arc: {
        borderWidth: 2
      }
    }
  };

  if (loading) return <div className="loading-container">{defaultT.loading}</div>;

  if (error) return <div className="error-message">{defaultT.loadError}: {error}</div>;

  return (
    <div className="population-migration-container">
      <h2>
        <i className="fas fa-users" style={{ marginRight: '10px', color: '#3498db' }}></i>
        {defaultT.populationMigrationAnalysis}
      </h2>
      <p className="analysis-description">
        <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
        {defaultT.populationMigrationDesc}
      </p>

      <div className="filters-container">
        <div className="filter-group">
          <label>
            <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
            {defaultT.region}:
          </label>
          <select value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value)}>
            <option value="All">{defaultT.all}</option>
            {regions.map(region => (
              <option key={region} value={region}>{region}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>
            <i className="fas fa-birthday-cake" style={{ marginRight: '5px', color: '#3498db' }}></i>
            {defaultT.ageGroup}:
          </label>
          <select value={selectedAgeGroup} onChange={(e) => setSelectedAgeGroup(e.target.value)}>
            {ageGroups.map(age => (
              <option key={age} value={age}>{age}</option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>
            <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
            {defaultT.gender}:
          </label>
          <select value={selectedGender} onChange={(e) => setSelectedGender(e.target.value)}>
            {genders.map(gender => (
              <option key={gender} value={gender}>{gender}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="charts-container">
        <div className="chart-wrapper bar-chart">
          <div className="chart-title-badge">{defaultT.populationByMigrationStatus || 'Population by Migration Status'}</div>
          <Bar data={prepareBarChartData()} options={barChartOptions} height={300} />
        </div>

        <div className="chart-wrapper pie-chart">
          <div className="chart-title-badge">{defaultT.migrationRatio || 'Migration to Local Population Ratio'}</div>
          <Pie data={preparePieChartData()} options={pieChartOptions} height={300} />
        </div>
      </div>

      <div className="data-summary">
        <h3>{defaultT.keySummary}</h3>
        <div className="summary-cards">
          <div className="summary-card">
            <div className="card-icon blue">
              <i className="fas fa-users"></i>
            </div>
            <div className="card-content">
              <div className="card-title">{defaultT.totalPopulation}</div>
              <div className="card-value">{totalData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0).toLocaleString()}</div>
            </div>
          </div>

          <div className="summary-card">
            <div className="card-icon red">
              <i className="fas fa-plane-arrival"></i>
            </div>
            <div className="card-content">
              <div className="card-title">{defaultT.migrantPopulation}</div>
              <div className="card-value">{migrantData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0).toLocaleString()}</div>
            </div>
          </div>

          <div className="summary-card">
            <div className="card-icon green">
              <i className="fas fa-home"></i>
            </div>
            <div className="card-content">
              <div className="card-title">{defaultT.localPopulation}</div>
              <div className="card-value">{localData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0).toLocaleString()}</div>
            </div>
          </div>

          <div className="summary-card">
            <div className="card-icon orange">
              <i className="fas fa-percentage"></i>
            </div>
            <div className="card-content">
              <div className="card-title">{defaultT.migrationRate}</div>
              <div className="card-value">{(migrantData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0) /
                totalData.reduce((sum, item) => sum + parseFloat(item.Population || 0), 0) * 100).toFixed(2)}%</div>
            </div>
          </div>
        </div>
      </div>

      <div className="insights-section">
        <h3>
          <i className="fas fa-lightbulb" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.dataInsights}
        </h3>
        <div className="insights-grid">
          <div className="insight-card">
            <div className="insight-icon">
              <i className="fas fa-chart-line"></i>
            </div>
            <h4>{defaultT.ageDistribution}</h4>
            <p>The age distribution shows the population distribution across different age groups, helping to understand population structure.</p>
          </div>
          <div className="insight-card">
            <div className="insight-icon">
              <i className="fas fa-exchange-alt"></i>
            </div>
            <h4>{defaultT.migrationPatterns}</h4>
            <p>Migration patterns show the proportion of migrant population in different regions, helping to understand population mobility trends.</p>
          </div>
          <div className="insight-card">
            <div className="insight-icon">
              <i className="fas fa-venus-mars"></i>
            </div>
            <h4>{defaultT.genderRatio}</h4>
            <p>Gender ratio analysis helps understand gender distribution differences across regions and age groups.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopulationMigrationAnalysis;
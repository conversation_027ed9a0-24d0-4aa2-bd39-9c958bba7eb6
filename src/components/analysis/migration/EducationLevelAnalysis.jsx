import React, { useState, useEffect } from 'react';
import '../../../styles/EducationLevelAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const EducationLevelAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    educationLevelAnalysis: 'Education Level Analysis',
    educationLevelDesc: 'This analysis shows education levels by migration status, gender, and region, helping you understand population education patterns.',
    region: 'Region',
    gender: 'Gender',
    migrationStatus: 'Migration Status',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalPopulation: 'Total Population',
    migrantPopulation: 'Migrant Population',
    nonMigrantPopulation: 'Non-Migrant Population',
    educationRate: 'Higher Education Rate',
    dataInsights: 'Data Insights',
    educationPatterns: 'Education Patterns',
    migrantEducation: 'Migrant Education Levels',
    nonMigrantEducation: 'Non-Migrant Education Levels',
    educationDistribution: 'Education Distribution',
    educationComparison: 'Education Comparison',
    detailedBreakdown: 'Detailed Breakdown',
    noSchooling: 'No Schooling',
    lessThanPrimary: 'Less Than Primary',
    primaryEducation: 'Primary Education',
    lowerSecondaryEducation: 'Lower Secondary Education',
    upperSecondaryEducation: 'Upper Secondary Education',
    tertiaryEducation: 'Tertiary Education',
    otherEducation: 'Other Education',
    unknown: 'Unknown',
    ...t
  };

  const [educationData, setEducationData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedMigrationStatus, setSelectedMigrationStatus] = useState('Total');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Available migration statuses for filtering
  const migrationStatuses = ['Total', 'Migrant', 'Non-Migrant'];

  // Load education data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t3.csv', {
      download: true,
      header: true,
      complete: (results) => {
        if (results.data && results.data.length > 0) {
          // Transform the data into a more usable format
          const transformedData = transformEducationData(results.data);
          setEducationData(transformedData);
          setLoading(false);
        } else {
          setError('No data found in the CSV file');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformEducationData = (data) => {
    // Filter out empty rows
    const filteredData = data.filter(row => row.population_label && row.population_label.trim() !== '');

    // Initialize result array
    const result = [];

    // Track current migration status
    let currentMigrationStatus = 'Total';

    // Process each row
    filteredData.forEach(row => {
      const label = row.population_label.trim();

      // Handle migration status rows
      if (label === 'Migrant' || label === 'Non-Migrant') {
        currentMigrationStatus = label;

        // Add the total row for this migration status
        result.push({
          educationLevel: 'Total',
          migrationStatus: currentMigrationStatus,
          nationwide: {
            total: parseFloat(row.total_nationwide) || 0,
            male: parseFloat(row.male_nationwide) || 0,
            female: parseFloat(row.female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok) || 0,
            male: parseFloat(row.male_bangkok) || 0,
            female: parseFloat(row.female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central) || 0,
            male: parseFloat(row.male_central) || 0,
            female: parseFloat(row.female_central) || 0
          },
          north: {
            total: parseFloat(row.total_north) || 0,
            male: parseFloat(row.male_north) || 0,
            female: parseFloat(row.female_north) || 0
          },
          northeast: {
            total: parseFloat(row.total_northeast) || 0,
            male: parseFloat(row.male_northeast) || 0,
            female: parseFloat(row.female_northeast) || 0
          },
          south: {
            total: parseFloat(row.total_south) || 0,
            male: parseFloat(row.male_south) || 0,
            female: parseFloat(row.female_south) || 0
          }
        });
      }
      // Handle Grand Total row
      else if (label === 'Grand Total') {
        result.push({
          educationLevel: 'Total',
          migrationStatus: 'Total',
          nationwide: {
            total: parseFloat(row.total_nationwide) || 0,
            male: parseFloat(row.male_nationwide) || 0,
            female: parseFloat(row.female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok) || 0,
            male: parseFloat(row.male_bangkok) || 0,
            female: parseFloat(row.female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central) || 0,
            male: parseFloat(row.male_central) || 0,
            female: parseFloat(row.female_central) || 0
          },
          north: {
            total: parseFloat(row.total_north) || 0,
            male: parseFloat(row.male_north) || 0,
            female: parseFloat(row.female_north) || 0
          },
          northeast: {
            total: parseFloat(row.total_northeast) || 0,
            male: parseFloat(row.male_northeast) || 0,
            female: parseFloat(row.female_northeast) || 0
          },
          south: {
            total: parseFloat(row.total_south) || 0,
            male: parseFloat(row.male_south) || 0,
            female: parseFloat(row.female_south) || 0
          }
        });
      }
      // Handle education level rows
      else {
        result.push({
          educationLevel: label,
          migrationStatus: currentMigrationStatus,
          nationwide: {
            total: parseFloat(row.total_nationwide) || 0,
            male: parseFloat(row.male_nationwide) || 0,
            female: parseFloat(row.female_nationwide) || 0
          },
          bangkok: {
            total: parseFloat(row.total_bangkok) || 0,
            male: parseFloat(row.male_bangkok) || 0,
            female: parseFloat(row.female_bangkok) || 0
          },
          central: {
            total: parseFloat(row.total_central) || 0,
            male: parseFloat(row.male_central) || 0,
            female: parseFloat(row.female_central) || 0
          },
          north: {
            total: parseFloat(row.total_north) || 0,
            male: parseFloat(row.male_north) || 0,
            female: parseFloat(row.female_north) || 0
          },
          northeast: {
            total: parseFloat(row.total_northeast) || 0,
            male: parseFloat(row.male_northeast) || 0,
            female: parseFloat(row.female_northeast) || 0
          },
          south: {
            total: parseFloat(row.total_south) || 0,
            male: parseFloat(row.male_south) || 0,
            female: parseFloat(row.female_south) || 0
          }
        });
      }
    });

    return result;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!educationData || educationData.length === 0) {
      return {
        totalPopulation: 0,
        migrantPopulation: 0,
        nonMigrantPopulation: 0,
        higherEducationRate: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the total population row
    const totalRow = educationData.find(item =>
      item.educationLevel === 'Total' &&
      item.migrationStatus === 'Total'
    );

    // Find the migrant population row
    const migrantRow = educationData.find(item =>
      item.educationLevel === 'Total' &&
      item.migrationStatus === 'Migrant'
    );

    // Find the non-migrant population row
    const nonMigrantRow = educationData.find(item =>
      item.educationLevel === 'Total' &&
      item.migrationStatus === 'Non-Migrant'
    );

    // Find higher education rows (Tertiary Education)
    const higherEducationRow = educationData.find(item =>
      item.educationLevel === 'Tertiary Education' &&
      (selectedMigrationStatus === 'Total' ?
        item.migrationStatus === 'Total' :
        item.migrationStatus === selectedMigrationStatus)
    );

    // Calculate total population based on filters
    const totalPopulation = totalRow ? totalRow[region][gender] : 0;

    // Calculate migrant population based on filters
    const migrantPopulation = migrantRow ? migrantRow[region][gender] : 0;

    // Calculate non-migrant population based on filters
    const nonMigrantPopulation = nonMigrantRow ? nonMigrantRow[region][gender] : 0;

    // Calculate higher education rate
    const higherEducation = higherEducationRow ? higherEducationRow[region][gender] : 0;
    const higherEducationRate = totalPopulation > 0 ? (higherEducation / totalPopulation) * 100 : 0;

    return {
      totalPopulation,
      migrantPopulation,
      nonMigrantPopulation,
      higherEducationRate
    };
  };

  // Prepare data for education distribution pie chart
  const prepareEducationPieData = () => {
    if (!educationData || educationData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define education levels to include in the chart
    const educationLevels = [
      'No Schooling',
      'Less Than Primary',
      'Primary Education',
      'Lower Secondary Education',
      'Upper Secondary Education',
      'Tertiary Education',
      'Other Education 1/'
    ];

    // Create a structured dataset with all education levels
    const educationValues = {};

    // Initialize with zeros
    educationLevels.forEach(level => {
      educationValues[level] = 0;
    });

    // Fill in actual values from the data
    educationData.forEach(item => {
      if (educationLevels.includes(item.educationLevel)) {
        if (selectedMigrationStatus === 'Total' && item.migrationStatus === 'Total') {
          educationValues[item.educationLevel] = item[region][gender];
        } else if (item.migrationStatus === selectedMigrationStatus) {
          educationValues[item.educationLevel] = item[region][gender];
        }
      }
    });

    // Filter out zero values
    const nonZeroLevels = educationLevels.filter(level => educationValues[level] > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroLevels.map(level => defaultT[level.toLowerCase().replace(/\s+/g, '_').replace(/\//g, '')] || level),
      datasets: [
        {
          data: nonZeroLevels.map(level => educationValues[level]),
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(201, 203, 207, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(201, 203, 207, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for education comparison between migrant and non-migrant
  const prepareEducationComparisonData = () => {
    if (!educationData || educationData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define education levels to include in the chart
    const educationLevels = [
      'No Schooling',
      'Less Than Primary',
      'Primary Education',
      'Lower Secondary Education',
      'Upper Secondary Education',
      'Tertiary Education'
    ];

    // Create a structured dataset with all education levels
    const migrantValues = {};
    const nonMigrantValues = {};

    // Initialize with zeros
    educationLevels.forEach(level => {
      migrantValues[level] = 0;
      nonMigrantValues[level] = 0;
    });

    // Fill in actual values from the data
    educationData.forEach(item => {
      if (educationLevels.includes(item.educationLevel)) {
        if (item.migrationStatus === 'Migrant') {
          migrantValues[item.educationLevel] = item[region][gender];
        } else if (item.migrationStatus === 'Non-Migrant') {
          nonMigrantValues[item.educationLevel] = item[region][gender];
        }
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: educationLevels.map(level => defaultT[level.toLowerCase().replace(/\s+/g, '_')] || level),
      datasets: [
        {
          label: defaultT.migrantEducation,
          data: educationLevels.map(level => migrantValues[level]),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.nonMigrantEducation,
          data: educationLevels.map(level => nonMigrantValues[level]),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for detailed education breakdown
  const prepareDetailedEducationData = () => {
    if (!educationData || educationData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define education levels to include in the chart
    const educationLevels = [
      'No Schooling',
      'Less Than Primary',
      'Primary Education',
      'Lower Secondary Education',
      'Upper Secondary Education',
      'Tertiary Education'
    ];

    // Create a structured dataset with all education levels
    const educationValues = {};

    // Initialize with zeros
    educationLevels.forEach(level => {
      educationValues[level] = 0;
    });

    // Fill in actual values from the data
    educationData.forEach(item => {
      if (educationLevels.includes(item.educationLevel)) {
        if (selectedMigrationStatus === 'Total' && item.migrationStatus === 'Total') {
          educationValues[item.educationLevel] = item[region][gender];
        } else if (item.migrationStatus === selectedMigrationStatus) {
          educationValues[item.educationLevel] = item[region][gender];
        }
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: educationLevels.map(level => defaultT[level.toLowerCase().replace(/\s+/g, '_')] || level),
      datasets: [
        {
          label: defaultT[selectedMigrationStatus.toLowerCase() + 'Population'] || selectedMigrationStatus,
          data: educationLevels.map(level => educationValues[level]),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        },
        // Only show one legend item per label
        filter: (legendItem, data) => {
          const index = data.labels.indexOf(legendItem.text);
          return index === data.labels.lastIndexOf(legendItem.text);
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  const handleMigrationStatusChange = (e) => {
    setSelectedMigrationStatus(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) return <div className="loading-container">{defaultT.loading}</div>;

  if (error) return <div className="error-message">{defaultT.loadError}: {error}</div>;

  return (
    <div className="education-level-container">
      <h2>
        <i className="fas fa-graduation-cap" style={{ marginRight: '10px', color: '#3498db' }}></i>
        {defaultT.educationLevelAnalysis}
      </h2>
      <p className="analysis-description">
        <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
        {defaultT.educationLevelDesc}
      </p>

      <div className="education-analysis-wrapper">
        <div className="filters-container">
        <div className="filter-group">
          <label>
            <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
            {defaultT.region}:
          </label>
          <select
            value={selectedRegion}
            onChange={handleRegionChange}
          >
            {regions.map(region => (
              <option key={region} value={region}>
                {defaultT[region.toLowerCase()] || region}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>
            <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
            {defaultT.gender}:
          </label>
          <select
            value={selectedGender}
            onChange={handleGenderChange}
          >
            {genders.map(gender => (
              <option key={gender} value={gender}>
                {defaultT[gender.toLowerCase()] || gender}
              </option>
            ))}
          </select>
        </div>

        <div className="filter-group">
          <label>
            <i className="fas fa-users" style={{ marginRight: '5px', color: '#3498db' }}></i>
            {defaultT.migrationStatus}:
          </label>
          <select
            value={selectedMigrationStatus}
            onChange={handleMigrationStatusChange}
          >
            {migrationStatuses.map(status => (
              <option key={status} value={status}>
                {defaultT[status.toLowerCase() + 'Population'] || status}
              </option>
            ))}
          </select>
        </div>
      </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile population">
              <div className="stat-content">
                <h3>{defaultT.totalPopulation}</h3>
                <div className="stat-value">{summaryStats.totalPopulation.toLocaleString()}</div>
                <div className="stat-note">(6+ years)</div>
              </div>
            </div>

            <div className="stat-tile migrant">
              <div className="stat-content">
                <h3>{defaultT.migrantPopulation}</h3>
                <div className="stat-value">{summaryStats.migrantPopulation.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.migrantPopulation / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile non-migrant">
              <div className="stat-content">
                <h3>{defaultT.nonMigrantPopulation}</h3>
                <div className="stat-value">{summaryStats.nonMigrantPopulation.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.nonMigrantPopulation / summaryStats.totalPopulation) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile education-rate">
              <div className="stat-content">
                <h3>{defaultT.educationRate}</h3>
                <div className="stat-value">{summaryStats.higherEducationRate.toFixed(1)}%</div>
                <div className="stat-note">Tertiary Education</div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.educationDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareEducationPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.educationComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareEducationComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.detailedBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareDetailedEducationData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                </div>
                <div className="chart-note">Includes short-term vocational training</div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-graduation-cap"></i>
                  <h4>{defaultT.educationPatterns}</h4>
                </div>
                <p>Education level analysis helps understand population education patterns and workforce quality.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-user-graduate"></i>
                  <h4>{defaultT.migrantEducation}</h4>
                </div>
                <p>Migrant education patterns reveal how education levels influence migration decisions and opportunities.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-line"></i>
                  <h4>{defaultT.educationComparison}</h4>
                </div>
                <p>Comparing education levels between migrants and non-migrants helps identify educational disparities and inform policy decisions.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EducationLevelAnalysis;

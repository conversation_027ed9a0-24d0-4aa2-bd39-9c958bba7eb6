import React, { useState, useEffect } from 'react';
import '../../../styles/MoneyGoodsRecipientAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MoneyGoodsRecipientAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    moneyGoodsRecipientAnalysis: 'Money & Goods Sending by Recipient Analysis',
    moneyGoodsRecipientDesc: 'This analysis shows migration numbers by recipient of sent money and goods, gender, and region, helping explore how migrants send funds and goods.',
    region: 'Region',
    gender: 'Gender',
    recipientType: 'Recipient Type',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMigrants: 'Total Migrants',
    sentToParents: 'Sent to Parents/Mother',
    sentToSpouse: 'Sent to Husband/Wife',
    sentToChildren: 'Sent to Children',
    sentToRelatives: 'Sent to Other Relatives',
    sentToOthers: 'Sent to Others',
    dataInsights: 'Data Insights',
    recipientPatterns: 'Recipient Patterns',
    recipientDistribution: 'Recipient Distribution',
    recipientFlowAnalysis: 'Recipient Flow Analysis',
    genderDifferences: 'Gender Differences',
    regionalVariations: 'Regional Variations',
    regionalBreakdown: 'Regional Breakdown',
    ...t
  };

  // State variables
  const [recipientData, setRecipientData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Load data from CSV
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);

      try {
        // Directly fetch the CSV file
        const response = await fetch('/data/t19.csv');
        if (!response.ok) {
          throw new Error(`Failed to fetch CSV: ${response.status} ${response.statusText}`);
        }

        const csvText = await response.text();
        console.log('Raw CSV text:', csvText);

        // Parse the CSV manually to ensure correct data handling
        Papa.parse(csvText, {
          header: true,
          skipEmptyLines: true,
          complete: (results) => {
            try {
              if (results.data && results.data.length > 0) {
                console.log('Parsed CSV data:', results.data);

                // Process the data
                const processedData = processCSVData(results.data);
                console.log('Processed data:', processedData);

                setRecipientData(processedData);
                setLoading(false);
              } else {
                setError('No data found in the CSV file');
                setLoading(false);
              }
            } catch (err) {
              console.error('Error processing CSV data:', err);
              setError(`Failed to process data: ${err.message}`);
              setLoading(false);
            }
          },
          error: (error) => {
            console.error('Papa parse error:', error);
            setError(`Error parsing CSV: ${error.message}`);
            setLoading(false);
          }
        });
      } catch (err) {
        console.error('Fetch error:', err);
        setError(`Failed to fetch CSV file: ${err.message}`);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Process CSV data into a structured format
  const processCSVData = (data) => {
    const processedData = [];

    // Helper function to safely parse float values
    const parseFloatSafe = (value) => {
      if (value === undefined || value === null || value === '') return 0;
      // Replace any commas and ensure it's a valid number
      const cleanValue = String(value).replace(/,/g, '');
      const parsed = parseFloat(cleanValue);
      return isNaN(parsed) ? 0 : parsed;
    };

    // Log the raw data for debugging
    console.log('Raw data first row:', data[0]);

    // Process each row
    data.forEach(row => {
      // Skip empty rows or note rows
      if (!row.population_label || row.population_label.includes('--')) return;

      const label = row.population_label.trim();

      // Create a data object for this row
      const rowData = {
        recipientType: label,
        nationwide: {
          total: parseFloatSafe(row.total_nationwide),
          male: parseFloatSafe(row.male_nationwide),
          female: parseFloatSafe(row.female_nationwide)
        },
        bangkok: {
          total: parseFloatSafe(row.total_bangkok),
          male: parseFloatSafe(row.male_bangkok),
          female: parseFloatSafe(row.female_bangkok)
        },
        central: {
          total: parseFloatSafe(row.total_central),
          male: parseFloatSafe(row.male_central),
          female: parseFloatSafe(row.female_central)
        },
        north: {
          total: parseFloatSafe(row.total_north),
          male: parseFloatSafe(row.male_north),
          female: parseFloatSafe(row.female_north)
        },
        northeast: {
          total: parseFloatSafe(row.total_northeast),
          male: parseFloatSafe(row.male_northeast),
          female: parseFloatSafe(row.female_northeast)
        },
        south: {
          total: parseFloatSafe(row.total_south),
          male: parseFloatSafe(row.male_south),
          female: parseFloatSafe(row.female_south)
        }
      };

      // Log for debugging
      console.log(`Processed row "${label}":`, {
        nationwide: rowData.nationwide,
        bangkok: rowData.bangkok
      });

      processedData.push(rowData);
    });

    // Log the processed data for debugging
    if (processedData.length > 0) {
      console.log('Grand Total row data:', processedData.find(item => item.recipientType === 'Grand Total'));
      console.log('Bangkok data for Grand Total:', processedData.find(item => item.recipientType === 'Grand Total')?.bangkok);
    }

    return processedData;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!recipientData || recipientData.length === 0) {
      return {
        totalMigrants: 0,
        sentToParents: 0,
        sentToSpouse: 0,
        sentToChildren: 0,
        sentToRelatives: 0,
        sentToOthers: 0
      };
    }

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the rows for each recipient type
    const totalRow = recipientData.find(item => item.recipientType === 'Grand Total');
    const parentsRow = recipientData.find(item => item.recipientType === 'Father / Mother');
    const spouseRow = recipientData.find(item => item.recipientType === 'Husband / Wife');
    const childrenRow = recipientData.find(item => item.recipientType === 'Children');
    const relativesRow = recipientData.find(item => item.recipientType === 'Other Relatives');
    const othersRow = recipientData.find(item => item.recipientType === 'Others');

    // Get the values based on selected filters
    let totalMigrants = 0;
    let sentToParents = 0;
    let sentToSpouse = 0;
    let sentToChildren = 0;
    let sentToRelatives = 0;
    let sentToOthers = 0;

    if (totalRow && totalRow[region]) {
      totalMigrants = totalRow[region][gender];
    }

    if (parentsRow && parentsRow[region]) {
      sentToParents = parentsRow[region][gender];
    }

    if (spouseRow && spouseRow[region]) {
      sentToSpouse = spouseRow[region][gender];
    }

    if (childrenRow && childrenRow[region]) {
      sentToChildren = childrenRow[region][gender];
    }

    if (relativesRow && relativesRow[region]) {
      sentToRelatives = relativesRow[region][gender];
    }

    if (othersRow && othersRow[region]) {
      sentToOthers = othersRow[region][gender];
    }

    // Log the calculated values for debugging
    console.log('Summary stats:', {
      region,
      gender,
      totalMigrants,
      sentToParents,
      sentToSpouse,
      sentToChildren,
      sentToRelatives,
      sentToOthers,
      totalRow: totalRow ? totalRow[region] : null,
      parentsRow: parentsRow ? parentsRow[region] : null,
      spouseRow: spouseRow ? spouseRow[region] : null,
      childrenRow: childrenRow ? childrenRow[region] : null
    });

    return {
      totalMigrants,
      sentToParents,
      sentToSpouse,
      sentToChildren,
      sentToRelatives,
      sentToOthers
    };
  };

  // Prepare data for recipient distribution pie chart
  const prepareRecipientPieData = () => {
    if (!recipientData || recipientData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define recipient types to include in the chart
    const recipientTypes = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Other Relatives',
      'Others'
    ];

    // Create arrays to store values and labels
    const values = [];
    const labels = [];

    // Process each recipient type
    recipientTypes.forEach(type => {
      const row = recipientData.find(item => item.recipientType === type);
      if (row && row[region] && typeof row[region][gender] === 'number') {
        const value = row[region][gender];

        // Only include non-zero values
        if (value > 0) {
          values.push(value);

          // Get translated label
          let label;
          switch (type) {
            case 'Father / Mother': label = defaultT.sentToParents; break;
            case 'Husband / Wife': label = defaultT.sentToSpouse; break;
            case 'Children': label = defaultT.sentToChildren; break;
            case 'Other Relatives': label = defaultT.sentToRelatives; break;
            case 'Others': label = defaultT.sentToOthers; break;
            default: label = type;
          }

          labels.push(label);
        }
      }
    });

    // Log for debugging
    console.log('Pie chart data:', { region, gender, labels, values });

    // Prepare the chart data
    const chartData = {
      labels: labels,
      datasets: [
        {
          data: values,
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!recipientData || recipientData.length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Define recipient types to include in the chart
    const recipientTypes = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Other Relatives',
      'Others'
    ];

    // Create arrays to store male and female values
    const maleValues = [];
    const femaleValues = [];
    const labels = [];
    const validTypes = [];

    // Find the corresponding rows and update values
    recipientTypes.forEach(type => {
      const row = recipientData.find(item => item.recipientType === type);
      if (row && row[region]) {
        // Only include types that have data
        const male = row[region].male || 0;
        const female = row[region].female || 0;

        if (male > 0 || female > 0) {
          maleValues.push(male);
          femaleValues.push(female);
          validTypes.push(type);

          // Translate the label
          let translatedLabel;
          switch (type) {
            case 'Father / Mother': translatedLabel = defaultT.sentToParents; break;
            case 'Husband / Wife': translatedLabel = defaultT.sentToSpouse; break;
            case 'Children': translatedLabel = defaultT.sentToChildren; break;
            case 'Other Relatives': translatedLabel = defaultT.sentToRelatives; break;
            case 'Others': translatedLabel = defaultT.sentToOthers; break;
            default: translatedLabel = type;
          }
          labels.push(translatedLabel);
        }
      }
    });

    // Log for debugging
    console.log('Gender comparison data:', {
      region,
      validTypes,
      maleValues,
      femaleValues
    });

    // Prepare the chart data
    const chartData = {
      labels: labels,
      datasets: [
        {
          label: defaultT.male,
          data: maleValues,
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.female,
          data: femaleValues,
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional breakdown stacked bar chart
  const prepareRegionalBreakdownData = () => {
    if (!recipientData || recipientData.length === 0) return null;

    // Define regions to include in the chart
    const chartRegions = ['nationwide', 'bangkok', 'central', 'north', 'northeast', 'south'];

    // Define recipient types to include in the chart
    const recipientTypes = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Other Relatives',
      'Others'
    ];

    // Create arrays to store values for each recipient type
    const datasets = [];
    const colors = [
      'rgba(255, 99, 132, 0.7)',
      'rgba(54, 162, 235, 0.7)',
      'rgba(255, 206, 86, 0.7)',
      'rgba(75, 192, 192, 0.7)',
      'rgba(153, 102, 255, 0.7)'
    ];
    const borderColors = [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)',
      'rgba(255, 206, 86, 1)',
      'rgba(75, 192, 192, 1)',
      'rgba(153, 102, 255, 1)'
    ];

    // Create a dataset for each recipient type
    recipientTypes.forEach((type, index) => {
      const row = recipientData.find(item => item.recipientType === type);
      if (row) {
        const values = chartRegions.map(region => row[region][selectedGender.toLowerCase()]);

        // Translate the label
        let translatedLabel;
        switch (type) {
          case 'Father / Mother': translatedLabel = defaultT.sentToParents; break;
          case 'Husband / Wife': translatedLabel = defaultT.sentToSpouse; break;
          case 'Children': translatedLabel = defaultT.sentToChildren; break;
          case 'Other Relatives': translatedLabel = defaultT.sentToRelatives; break;
          case 'Others': translatedLabel = defaultT.sentToOthers; break;
          default: translatedLabel = type;
        }

        datasets.push({
          label: translatedLabel,
          data: values,
          backgroundColor: colors[index],
          borderColor: borderColors[index],
          borderWidth: 1,
          stack: 'Stack 0' // 添加stack属性，确保所有数据集堆叠在一起
        });
      }
    });

    // Prepare the chart data
    const chartData = {
      labels: chartRegions.map(region => {
        switch (region) {
          case 'nationwide': return defaultT.nationwide || 'Nationwide';
          case 'bangkok': return defaultT.bangkok || 'Bangkok';
          case 'central': return defaultT.central || 'Central';
          case 'north': return defaultT.north || 'North';
          case 'northeast': return defaultT.northeast || 'Northeast';
          case 'south': return defaultT.south || 'South';
          default: return region.charAt(0).toUpperCase() + region.slice(1);
        }
      }),
      datasets: datasets
    };

    return chartData;
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString('en-US', {maximumFractionDigits: 3})} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString('en-US', {maximumFractionDigits: 3})}`;
          }
        }
      }
    }
  };

  // Chart options for stacked bar charts
  const stackedBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        stacked: true,
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString('en-US', {maximumFractionDigits: 3})}`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  // If loading, show loading message
  if (loading) {
    return (
      <div className="money-goods-recipient-container">
        <div className="loading-container">
          <i className="fas fa-spinner fa-spin" style={{ marginRight: '10px' }}></i>
          {defaultT.loading}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="money-goods-recipient-container">
        <div className="error-message">
          <i className="fas fa-exclamation-triangle" style={{ marginRight: '10px' }}></i>
          {defaultT.loadError}: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="money-goods-recipient-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-people-arrows" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsRecipientAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsRecipientDesc}
        </span>
      </p>

      <div className="recipient-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-migrants">
              <div className="stat-content">
                <h3>{defaultT.totalMigrants}</h3>
                <div className="stat-value">{summaryStats.totalMigrants.toLocaleString('en-US', {maximumFractionDigits: 3})}</div>
              </div>
            </div>

            <div className="stat-tile parents-recipient">
              <div className="stat-content">
                <h3>{defaultT.sentToParents}</h3>
                <div className="stat-value">{summaryStats.sentToParents.toLocaleString('en-US', {maximumFractionDigits: 3})}</div>
                <div className="stat-percentage">
                  {((summaryStats.sentToParents / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile spouse-recipient">
              <div className="stat-content">
                <h3>{defaultT.sentToSpouse}</h3>
                <div className="stat-value">{summaryStats.sentToSpouse.toLocaleString('en-US', {maximumFractionDigits: 3})}</div>
                <div className="stat-percentage">
                  {((summaryStats.sentToSpouse / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile children-recipient">
              <div className="stat-content">
                <h3>{defaultT.sentToChildren}</h3>
                <div className="stat-value">{summaryStats.sentToChildren.toLocaleString('en-US', {maximumFractionDigits: 3})}</div>
                <div className="stat-percentage">
                  {((summaryStats.sentToChildren / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.recipientDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareRecipientPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.genderDifferences}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.regionalBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalBreakdownData()}
                    options={stackedBarOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-people-arrows"></i>
                  <h4>{defaultT.recipientPatterns}</h4>
                </div>
                <p>Analysis of money and goods sending by recipient type helps understand how migrants maintain connections with different family members.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-exchange-alt"></i>
                  <h4>{defaultT.recipientFlowAnalysis}</h4>
                </div>
                <p>Recipient flow analysis reveals the direction of financial support within migrant families and communities.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{defaultT.genderDifferences}</h4>
                </div>
                <p>Gender differences in sending patterns highlight varying family responsibilities and financial support roles among male and female migrants.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalVariations}</h4>
                </div>
                <p>Regional variations in recipient patterns can inform targeted economic development and social support policies.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoneyGoodsRecipientAnalysis;

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Pie } from 'react-chartjs-2';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import '../../../styles/NationwideMigrationSummary.css';

const MigrationPopup = ({
  region,
  isVisible,
  showChart: parentShowChart,
  onToggleChart,
  getMigrationTypeColor,
  getMigrationTypeName,
  t
}) => {
  // Use internal state to manage display options
  const [showChart, setShowChart] = useState(false); // Default to data view instead of chart
  const [showThaiName, setShowThaiName] = useState(false);
  const [activeTab, setActiveTab] = useState('types'); // 'types' or 'reasons'
  const chartRef = useRef(null);
  // Pagination state for migration reasons
  const [reasonsPage, setReasonsPage] = useState(0);
  const reasonsPerPage = 3; // Number of reasons to show per page

  // Prevent events from propagating to the map
  const handleEvent = useCallback((e) => {
    e.stopPropagation();
  }, [])

  if (!region || !isVisible) return null;

  // 当父组件的 showChart 变化时，同步内部状态
  useEffect(() => {
    if (parentShowChart !== undefined) {
      setShowChart(parentShowChart);
    }
  }, [parentShowChart]);

  // Calculate total migration by type
  const totalMigration = Object.values(region.migrationTypes || {}).reduce((sum, val) => sum + (val || 0), 0);

  // Calculate total migration by reason
  const totalMigrationByReason = Object.values(region.migrationReasons || {}).reduce((sum, val) => sum + (val || 0), 0);

  // 调试日志：显示 South 区域的 Female 数据
  if (region.name === 'South') {
    console.log('MigrationPopup - South region migration reasons:', region.migrationReasons);
    console.log('MigrationPopup - South region total migration by reason:', totalMigrationByReason);
  }

  // Prepare migration types chart data
  const typesChartData = {
    labels: Object.keys(region.migrationTypes || {}).map(type => getMigrationTypeName(type, t)),
    datasets: [
      {
        data: Object.values(region.migrationTypes || {}),
        backgroundColor: Object.keys(region.migrationTypes || {}).map(type => getMigrationTypeColor(type)),
        borderColor: Object.keys(region.migrationTypes || {}).map(type => getMigrationTypeColor(type).replace('0.7', '1')),
        borderWidth: 1,
      },
    ],
  };

  // Prepare migration reasons chart data
  const reasonsChartData = {
    labels: Object.keys(region.migrationReasons || {}),
    datasets: [
      {
        data: Object.values(region.migrationReasons || {}),
        backgroundColor: [
          'rgba(255, 99, 132, 0.7)',   // Red
          'rgba(54, 162, 235, 0.7)',   // Blue
          'rgba(255, 206, 86, 0.7)',   // Yellow
          'rgba(75, 192, 192, 0.7)',   // Green
          'rgba(153, 102, 255, 0.7)',  // Purple
          'rgba(255, 159, 64, 0.7)',   // Orange
          'rgba(199, 199, 199, 0.7)',  // Gray
          'rgba(83, 102, 255, 0.7)',   // Indigo
          'rgba(255, 99, 255, 0.7)',   // Pink
          'rgba(99, 255, 132, 0.7)',   // Light green
          'rgba(255, 178, 102, 0.7)',  // Light orange
          'rgba(102, 178, 255, 0.7)',  // Light blue
          'rgba(178, 102, 255, 0.7)',  // Light purple
          'rgba(255, 102, 178, 0.7)',  // Light pink
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
          'rgba(199, 199, 199, 1)',
          'rgba(83, 102, 255, 1)',
          'rgba(255, 99, 255, 1)',
          'rgba(99, 255, 132, 1)',
          'rgba(255, 178, 102, 1)',
          'rgba(102, 178, 255, 1)',
          'rgba(178, 102, 255, 1)',
          'rgba(255, 102, 178, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Use the appropriate chart data based on active tab
  const chartData = activeTab === 'types' ? typesChartData : reasonsChartData;

  // Chart configuration
  const pieOptions = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = activeTab === 'types' ? totalMigration : totalMigrationByReason;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    },
    maintainAspectRatio: false,
  };

  // No longer needed as we use inline functions for toggling chart display

  return (
    <div
      className="nationwide-migration-summary"
      style={{ position: 'static', animation: 'none' }}
    >
      <div className="nationwide-header">
        <div className="header-title-container">
          <h3 className="migration-title">
            {showThaiName ? (region.nameTH || region.name || region.Group) : (region.name || region.Group)} Migration Data
          </h3>
        </div>
        <div className="header-actions">
          <button
            className="language-toggle-btn"
            onClick={() => setShowThaiName(prev => !prev)}
            title={showThaiName ? t.showEnglishName || "Show English Name" : t.showThaiName || "Show Thai Name"}
          >
            {showThaiName ? "EN" : "TH"}
          </button>
        </div>
        <div className="nationwide-total">
          <div className="total-label">{t.totalMigration || 'Total Migration'}</div>
          <div className="total-value">{totalMigration.toLocaleString()}</div>
        </div>
      </div>

      {/* Tabs for switching between migration types and reasons */}
      <div className="migration-tabs">
        <button
          className={`migration-tab ${activeTab === 'types' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('types');
            setReasonsPage(0); // Reset page when changing tabs
          }}
        >
          {t.migrationTypes || 'Migration Types'}
        </button>
        <button
          className={`migration-tab ${activeTab === 'reasons' ? 'active' : ''}`}
          onClick={() => {
            setActiveTab('reasons');
            setReasonsPage(0); // Reset page when changing tabs
          }}
        >
          {t.migrationReasons || 'Migration Reasons'}
        </button>
      </div>

      {!showChart ? (
        <div className="nationwide-data-view">
          <div
            className="nationwide-data-blocks"
            onWheel={handleEvent}
            onClick={handleEvent}
            onMouseDown={handleEvent}
          >
            {activeTab === 'types' ? (
              // Migration Types Data View
              Object.entries(region.migrationTypes || {}).map(([type, value]) => {
                const percentage = totalMigration > 0 ? ((value / totalMigration) * 100).toFixed(1) : '0.0';
                const numValue = value || 0;
                return (
                  <div key={type} className="nationwide-data-block">
                    <div className="migration-data-label">
                      <span
                        className="color-dot"
                        style={{ backgroundColor: getMigrationTypeColor(type) }}
                      ></span>
                      {getMigrationTypeName(type, t)}
                    </div>
                    <div className="migration-data-values">
                      <div className="migration-data-value">
                        {numValue.toLocaleString()}
                      </div>
                      <div className="migration-data-percentage">
                        {percentage}%
                      </div>
                    </div>
                    <div className="migration-data-bar-container">
                      <div
                        className="migration-data-bar"
                        style={{
                          width: `${percentage}%`,
                          backgroundColor: getMigrationTypeColor(type)
                        }}
                      ></div>
                    </div>
                  </div>
                );
              })
            ) : (
              // Migration Reasons Data View - with pagination
              <div className="reasons-slider-container">
                <div className="reasons-slider-content">
                  {Object.entries(region.migrationReasons || {})
                    .slice(reasonsPage * reasonsPerPage, (reasonsPage + 1) * reasonsPerPage)
                    .map(([reason, value], index) => {
                      const percentage = totalMigrationByReason > 0 ? ((value / totalMigrationByReason) * 100).toFixed(1) : '0.0';
                      const numValue = value || 0;
                      // Get a color based on the index
                      const colors = [
                        'rgba(255, 99, 132, 0.7)',   // Red
                        'rgba(54, 162, 235, 0.7)',   // Blue
                        'rgba(255, 206, 86, 0.7)',   // Yellow
                        'rgba(75, 192, 192, 0.7)',   // Green
                        'rgba(153, 102, 255, 0.7)',  // Purple
                        'rgba(255, 159, 64, 0.7)',   // Orange
                        'rgba(199, 199, 199, 0.7)',  // Gray
                        'rgba(83, 102, 255, 0.7)',   // Indigo
                        'rgba(255, 99, 255, 0.7)',   // Pink
                        'rgba(99, 255, 132, 0.7)',   // Light green
                        'rgba(255, 178, 102, 0.7)',  // Light orange
                        'rgba(102, 178, 255, 0.7)',  // Light blue
                        'rgba(178, 102, 255, 0.7)',  // Light purple
                        'rgba(255, 102, 178, 0.7)',  // Light pink
                      ];
                      // Calculate the actual index in the full list
                      const actualIndex = reasonsPage * reasonsPerPage + index;
                      const color = colors[actualIndex % colors.length];

                      return (
                        <div key={reason} className="nationwide-data-block">
                          <div className="migration-data-label">
                            <span
                              className="color-dot"
                              style={{ backgroundColor: color }}
                            ></span>
                            {reason}
                          </div>
                          <div className="migration-data-values">
                            <div className="migration-data-value">
                              {numValue.toLocaleString()}
                            </div>
                            <div className="migration-data-percentage">
                              {percentage}%
                            </div>
                          </div>
                          <div className="migration-data-bar-container">
                            <div
                              className="migration-data-bar"
                              style={{
                                width: `${percentage}%`,
                                backgroundColor: color
                              }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                </div>

                {/* Pagination controls */}
                {Object.keys(region.migrationReasons || {}).length > reasonsPerPage && (
                  <div className="reasons-pagination-controls">
                    <button
                      className="pagination-btn"
                      onClick={() => setReasonsPage(prev => Math.max(0, prev - 1))}
                      disabled={reasonsPage === 0}
                      title={t.previousPage || "Previous page"}
                    >
                      <FaChevronLeft />
                    </button>
                    <div className="pagination-indicator">
                      {reasonsPage + 1} / {Math.ceil(Object.keys(region.migrationReasons || {}).length / reasonsPerPage)}
                    </div>
                    <button
                      className="pagination-btn"
                      onClick={() => setReasonsPage(prev => {
                        const maxPage = Math.ceil(Object.keys(region.migrationReasons || {}).length / reasonsPerPage) - 1;
                        return Math.min(maxPage, prev + 1);
                      })}
                      disabled={reasonsPage >= Math.ceil(Object.keys(region.migrationReasons || {}).length / reasonsPerPage) - 1}
                      title={t.nextPage || "Next page"}
                    >
                      <FaChevronRight />
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>

          <button
            className="toggle-chart-btn"
            onClick={() => {
              setShowChart(true);
              setReasonsPage(0); // Reset page when changing view
              if (onToggleChart) onToggleChart();
            }}
          >
            {t.showChart || 'Show as Chart'} ▼
          </button>
        </div>
      ) : (
        <div className="nationwide-chart-view">
          <div className="nationwide-chart-container">
            <h5 className="chart-title">
              {activeTab === 'types'
                ? (t.migrationTypeDistribution || 'Migration Type Distribution')
                : (t.migrationReasonDistribution || 'Migration Reason Distribution')
              }
            </h5>
            {chartData.labels && chartData.labels.length > 0 ? (
              <div className="nationwide-pie-chart-wrapper">
                <Pie data={chartData} options={pieOptions} ref={chartRef} />
              </div>
            ) : (
              <div className="no-chart-data">{t.noDataAvailable || 'No data for chart'}</div>
            )}
            <div className="chart-legend-container">
              <div
                className="chart-legend scrollable"
                onWheel={handleEvent}
                onClick={handleEvent}
                onMouseDown={handleEvent}
              >
                {activeTab === 'types' ? (
                  // Migration Types Legend
                  Object.keys(region.migrationTypes || {}).map(type => (
                    <div key={type} className="legend-item">
                      <span
                        className="legend-color"
                        style={{ backgroundColor: getMigrationTypeColor(type) }}
                      ></span>
                      <span className="legend-label">
                        {getMigrationTypeName(type, t)}
                      </span>
                    </div>
                  ))
                ) : (
                  // Migration Reasons Legend
                  Object.keys(region.migrationReasons || {}).map((reason, index) => {
                    const colors = [
                      'rgba(255, 99, 132, 0.7)',   // Red
                      'rgba(54, 162, 235, 0.7)',   // Blue
                      'rgba(255, 206, 86, 0.7)',   // Yellow
                      'rgba(75, 192, 192, 0.7)',   // Green
                      'rgba(153, 102, 255, 0.7)',  // Purple
                      'rgba(255, 159, 64, 0.7)',   // Orange
                      'rgba(199, 199, 199, 0.7)',  // Gray
                      'rgba(83, 102, 255, 0.7)',   // Indigo
                      'rgba(255, 99, 255, 0.7)',   // Pink
                      'rgba(99, 255, 132, 0.7)',   // Light green
                      'rgba(255, 178, 102, 0.7)',  // Light orange
                      'rgba(102, 178, 255, 0.7)',  // Light blue
                      'rgba(178, 102, 255, 0.7)',  // Light purple
                      'rgba(255, 102, 178, 0.7)',  // Light pink
                    ];
                    const color = colors[index % colors.length];

                    return (
                      <div key={reason} className="legend-item">
                        <span
                          className="legend-color"
                          style={{ backgroundColor: color }}
                        ></span>
                        <span className="legend-label">
                          {reason}
                        </span>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>

          <button
            className="toggle-chart-btn"
            onClick={() => {
              setShowChart(false);
              setReasonsPage(0); // Reset page when changing view
              if (onToggleChart) onToggleChart();
            }}
          >
            {t.hideChart || 'Show as Bars'} ▲
          </button>
        </div>
      )}
    </div>
  );
};

export default MigrationPopup;

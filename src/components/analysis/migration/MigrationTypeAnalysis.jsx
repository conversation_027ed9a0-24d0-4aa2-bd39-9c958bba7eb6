import React, { useState, useEffect } from 'react';
import '../../../styles/MigrationTypeAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MigrationTypeAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    migrationTypeAnalysis: 'Migration Type Analysis',
    migrationTypeDesc: 'This analysis shows migration population by migration type, region, and administrative area, helping you understand regional migration patterns.',
    region: 'Region',
    areaType: 'Area Type',
    migrationType: 'Migration Type',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMigration: 'Total Migration',
    withinRegion: 'Within Region',
    betweenRegions: 'Between Regions',
    fromAbroad: 'From Abroad',
    dataInsights: 'Data Insights',
    migrationPatterns: 'Migration Patterns',
    municipality: 'Municipality',
    nonMunicipality: 'Non-Municipality',
    total: 'Total',
    nationwide: 'Nationwide',
    bangkok: 'Bangkok',
    central: 'Central',
    north: 'North',
    northeast: 'Northeast',
    south: 'South',
    ...t
  };

  const [migrationData, setMigrationData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedAreaType, setSelectedAreaType] = useState('Total');
  const [selectedMigrationType, setSelectedMigrationType] = useState('All');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available area types for filtering
  const areaTypes = ['Total', 'Municipality', 'NonMunicipality'];

  // Available migration types for filtering
  const migrationTypes = [
    'All',
    '1. Migrated within the same region',
    '1.1 Migrated between provinces',
    '1.2 Migrated within the same province',
    '2. Migrated between regions',
    '2.1 From Bangkok',
    '2.2 From Central Region',
    '2.3 From Northern Region',
    '2.4 From Northeastern Region',
    '2.5 From Southern Region',
    '3. From Abroad'
  ];

  // Load migration data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t8.csv', {
      download: true,
      header: true,
      complete: (results) => {
        if (results.data && results.data.length > 0) {
          setMigrationData(results.data);
          setLoading(false);
        } else {
          setError('No data found in the CSV file');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Handle region change
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  // Handle area type change
  const handleAreaTypeChange = (e) => {
    setSelectedAreaType(e.target.value);
  };

  // Handle migration type change
  const handleMigrationTypeChange = (e) => {
    setSelectedMigrationType(e.target.value);
  };

  // Filter data based on selected filters
  const getFilteredData = () => {
    return migrationData.filter(item => {
      const regionMatch = selectedRegion === 'All' || item.Region === selectedRegion;
      const areaTypeMatch = selectedAreaType === 'All' || item['Area Type'] === selectedAreaType;
      const migrationTypeMatch = selectedMigrationType === 'All' || item['Migration Label'] === selectedMigrationType;
      return regionMatch && areaTypeMatch && migrationTypeMatch;
    });
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!migrationData || migrationData.length === 0) {
      return {
        totalMigration: 0,
        withinRegion: 0,
        betweenRegions: 0,
        fromAbroad: 0
      };
    }

    // Filter data based on selected region and area type
    const filteredData = migrationData.filter(item =>
      (selectedRegion === 'All' || item.Region === selectedRegion) &&
      (selectedAreaType === 'All' || item['Area Type'] === selectedAreaType)
    );

    // Calculate total migration
    const totalMigration = filteredData.find(item =>
      item['Migration Label'] === 'Grand Total'
    );

    // Calculate migration within the same region
    const withinRegion = filteredData.find(item =>
      item['Migration Label'] === '1. Migrated within the same region'
    );

    // Calculate migration between regions
    const betweenRegions = filteredData.find(item =>
      item['Migration Label'] === '2. Migrated between regions'
    );

    // Calculate migration from abroad
    const fromAbroad = filteredData.find(item =>
      item['Migration Label'] === '3. From Abroad'
    );

    return {
      totalMigration: totalMigration ? parseFloat(totalMigration.Population) : 0,
      withinRegion: withinRegion ? parseFloat(withinRegion.Population) : 0,
      betweenRegions: betweenRegions ? parseFloat(betweenRegions.Population) : 0,
      fromAbroad: fromAbroad ? parseFloat(fromAbroad.Population) : 0
    };
  };

  // Prepare data for migration type distribution pie chart
  const prepareMigrationPieData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Filter data based on selected region and area type
    const filteredData = migrationData.filter(item =>
      (selectedRegion === 'All' || item.Region === selectedRegion) &&
      (selectedAreaType === 'All' || item['Area Type'] === selectedAreaType)
    );

    // Define migration types to include in the chart
    const mainMigrationTypes = [
      '1. Migrated within the same region',
      '2. Migrated between regions',
      '3. From Abroad'
    ];

    // Create a structured dataset with all migration types
    const migrationValues = {};

    // Initialize with zeros
    mainMigrationTypes.forEach(type => {
      migrationValues[type] = 0;
    });

    // Fill in actual values from the data
    filteredData.forEach(item => {
      if (mainMigrationTypes.includes(item['Migration Label'])) {
        migrationValues[item['Migration Label']] = parseFloat(item.Population) || 0;
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: mainMigrationTypes.map(type => {
        switch (type) {
          case '1. Migrated within the same region':
            return defaultT.withinRegion;
          case '2. Migrated between regions':
            return defaultT.betweenRegions;
          case '3. From Abroad':
            return defaultT.fromAbroad;
          default:
            return type;
        }
      }),
      datasets: [
        {
          data: mainMigrationTypes.map(type => migrationValues[type]),
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for detailed migration type breakdown
  const prepareDetailedMigrationData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Filter data based on selected region and area type
    const filteredData = migrationData.filter(item =>
      (selectedRegion === 'All' || item.Region === selectedRegion) &&
      (selectedAreaType === 'All' || item['Area Type'] === selectedAreaType)
    );

    // Define migration types to include in the chart
    const detailedMigrationTypes = [
      '1.1 Migrated between provinces',
      '1.2 Migrated within the same province',
      '2.1 From Bangkok',
      '2.2 From Central Region',
      '2.3 From Northern Region',
      '2.4 From Northeastern Region',
      '2.5 From Southern Region',
      '3. From Abroad'
    ];

    // Create a structured dataset with all migration types
    const migrationValues = {};

    // Initialize with zeros
    detailedMigrationTypes.forEach(type => {
      migrationValues[type] = 0;
    });

    // Fill in actual values from the data
    filteredData.forEach(item => {
      if (detailedMigrationTypes.includes(item['Migration Label'])) {
        migrationValues[item['Migration Label']] = parseFloat(item.Population) || 0;
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: detailedMigrationTypes.map(type => {
        // Simplify labels by removing the numbering
        return type.split(' ').slice(1).join(' ');
      }),
      datasets: [
        {
          label: defaultT.migrationPopulation || 'Migration Population',
          data: detailedMigrationTypes.map(type => migrationValues[type]),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for region comparison
  const prepareRegionComparisonData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Filter data based on selected area type and migration type
    const filteredData = migrationData.filter(item =>
      (selectedAreaType === 'All' || item['Area Type'] === selectedAreaType) &&
      (selectedMigrationType === 'All' || item['Migration Label'] === selectedMigrationType)
    );

    // Define regions to include in the chart
    const regionsToInclude = ['Bangkok', 'Central', 'North', 'Northeast', 'South'];

    // Create a structured dataset with all regions
    const regionValues = {};

    // Initialize with zeros
    regionsToInclude.forEach(region => {
      regionValues[region] = 0;
    });

    // Fill in actual values from the data
    filteredData.forEach(item => {
      if (regionsToInclude.includes(item.Region)) {
        if (selectedMigrationType === 'All') {
          // If 'All' is selected, use 'Grand Total' rows
          if (item['Migration Label'] === 'Grand Total') {
            regionValues[item.Region] = parseFloat(item.Population) || 0;
          }
        } else {
          // Otherwise use the selected migration type
          if (item['Migration Label'] === selectedMigrationType) {
            regionValues[item.Region] = parseFloat(item.Population) || 0;
          }
        }
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: regionsToInclude.map(region => defaultT[region.toLowerCase()] || region),
      datasets: [
        {
          label: selectedMigrationType === 'All'
            ? (defaultT.totalMigration || 'Total Migration')
            : (selectedMigrationType.split(' ').slice(1).join(' ')),
          data: regionsToInclude.map(region => regionValues[region]),
          backgroundColor: 'rgba(153, 102, 255, 0.7)',
          borderColor: 'rgba(153, 102, 255, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Chart options
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          boxWidth: 12,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            return `${context.dataset.label}: ${context.raw.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) return <div className="loading-container">{defaultT.loading}</div>;

  if (error) return <div className="error-message">{defaultT.loadError}: {error}</div>;

  return (
    <div className="migration-type-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-exchange-alt" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.migrationTypeAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.migrationTypeDesc}
        </span>
      </p>

      <div className="migration-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-city" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.areaType}:
            </label>
            <select
              value={selectedAreaType}
              onChange={handleAreaTypeChange}
            >
              {areaTypes.map(areaType => (
                <option key={areaType} value={areaType}>
                  {defaultT[areaType.toLowerCase()] || areaType}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-route" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.migrationType}:
            </label>
            <select
              value={selectedMigrationType}
              onChange={handleMigrationTypeChange}
            >
              {migrationTypes.map(type => (
                <option key={type} value={type}>
                  {type === 'All' ? defaultT.all : type}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-migration">
              <div className="stat-content">
                <h3>{defaultT.totalMigration}</h3>
                <div className="stat-value">{summaryStats.totalMigration.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile within-region">
              <div className="stat-content">
                <h3>{defaultT.withinRegion}</h3>
                <div className="stat-value">{summaryStats.withinRegion.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMigration > 0
                    ? ((summaryStats.withinRegion / summaryStats.totalMigration) * 100).toFixed(1)
                    : 0}%
                </div>
              </div>
            </div>

            <div className="stat-tile between-regions">
              <div className="stat-content">
                <h3>{defaultT.betweenRegions}</h3>
                <div className="stat-value">{summaryStats.betweenRegions.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMigration > 0
                    ? ((summaryStats.betweenRegions / summaryStats.totalMigration) * 100).toFixed(1)
                    : 0}%
                </div>
              </div>
            </div>

            <div className="stat-tile from-abroad">
              <div className="stat-content">
                <h3>{defaultT.fromAbroad}</h3>
                <div className="stat-value">{summaryStats.fromAbroad.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMigration > 0
                    ? ((summaryStats.fromAbroad / summaryStats.totalMigration) * 100).toFixed(1)
                    : 0}%
                </div>
              </div>
            </div>
          </div>



          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.migrationDistribution || 'Migration Distribution'}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareMigrationPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.regionComparison || 'Region Comparison'}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.detailedBreakdown || 'Detailed Breakdown'}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareDetailedMigrationData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights || 'Data Insights'}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-exchange-alt"></i>
                  <h4>{defaultT.migrationPatterns || 'Migration Patterns'}</h4>
                </div>
                <p>Migration type analysis helps understand population movement patterns and regional mobility.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalMigration || 'Regional Migration'}</h4>
                </div>
                <p>Regional migration patterns reveal how population moves between different administrative areas.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-line"></i>
                  <h4>{defaultT.policyImplications || 'Policy Implications'}</h4>
                </div>
                <p>Understanding migration types by administrative area helps inform regional development policies.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MigrationTypeAnalysis;

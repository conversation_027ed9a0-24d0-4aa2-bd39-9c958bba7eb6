import React, { useState, useEffect } from 'react';
import '../../../styles/MoneyGoodsSenderAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MoneyGoodsSenderAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    moneyGoodsSenderAnalysis: 'Money & Goods Receipt by Sender Analysis',
    moneyGoodsSenderDesc: 'This analysis shows migration numbers by received money and goods by sender type, gender, and region, helping identify key financial contributors to migrants.',
    region: 'Region',
    gender: 'Gender',
    senderType: 'Sender Type',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMigrants: 'Total Migrants',
    receivedFromParents: 'Received from Parents',
    receivedFromSpouse: 'Received from Spouse',
    receivedFromChildren: 'Received from Children',
    receivedFromRelatives: 'Received from Relatives',
    receivedFromOthers: 'Received from Others',
    dataInsights: 'Data Insights',
    senderPatterns: 'Sender Patterns',
    familySupport: 'Family Support',
    genderDifferences: 'Gender Differences',
    regionalVariations: 'Regional Variations',
    senderDistribution: 'Sender Distribution',
    genderComparison: 'Gender Comparison',
    regionalBreakdown: 'Regional Breakdown',
    senderFlowAnalysis: 'Sender Flow Analysis',
    ...t
  };

  const [senderData, setSenderData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedSenderType, setSelectedSenderType] = useState('All');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Available sender types for filtering
  const senderTypes = ['All', 'Father / Mother', 'Husband / Wife', 'Children', 'Relatives', 'Others'];

  // Load data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t16.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          if (results.data && results.data.length > 0) {
            // Transform the data into a more usable format
            const transformedData = transformSenderData(results.data);
            setSenderData(transformedData);
            setLoading(false);
          } else {
            setError('No data found in the CSV file');
            setLoading(false);
          }
        } catch (err) {
          console.error('Error processing money/goods sender data:', err);
          setError('Failed to process money/goods sender data');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformSenderData = (data) => {
    // Create a structured object to hold the data
    const transformedData = {};

    // Process each row in the CSV
    data.forEach(row => {
      const label = row['Population Label'];
      
      // Skip empty rows or rows without a label
      if (!label) return;

      // Initialize the label entry if it doesn't exist
      if (!transformedData[label]) {
        transformedData[label] = {};
      }

      // Process each region and gender combination
      regions.forEach(region => {
        const regionKey = region.toLowerCase();
        if (!transformedData[label][regionKey]) {
          transformedData[label][regionKey] = {};
        }

        genders.forEach(gender => {
          const genderKey = gender.toLowerCase();
          const columnName = `${gender}_${region}`;
          const value = parseFloat(row[columnName]) || 0;
          transformedData[label][regionKey][genderKey] = value;
        });
      });
    });

    return transformedData;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!senderData || Object.keys(senderData).length === 0) {
      return {
        totalMigrants: 0,
        receivedFromParents: 0,
        receivedFromSpouse: 0,
        receivedFromChildren: 0,
        receivedFromRelatives: 0,
        receivedFromOthers: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Get the total migrants
    const totalMigrants = senderData['Grand Total']?.[region]?.[gender] || 0;

    // Get the migrants who received from parents
    const receivedFromParents = senderData['Father / Mother']?.[region]?.[gender] || 0;

    // Get the migrants who received from spouse
    const receivedFromSpouse = senderData['Husband / Wife']?.[region]?.[gender] || 0;

    // Get the migrants who received from children
    const receivedFromChildren = senderData['Children']?.[region]?.[gender] || 0;

    // Get the migrants who received from relatives
    const receivedFromRelatives = senderData['Relatives']?.[region]?.[gender] || 0;

    // Get the migrants who received from others
    const receivedFromOthers = senderData['Others']?.[region]?.[gender] || 0;

    return {
      totalMigrants,
      receivedFromParents,
      receivedFromSpouse,
      receivedFromChildren,
      receivedFromRelatives,
      receivedFromOthers
    };
  };

  // Prepare data for sender distribution pie chart
  const prepareSenderPieData = () => {
    if (!senderData || Object.keys(senderData).length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define sender categories to include in the chart
    const senderCategories = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Relatives',
      'Others'
    ];

    // Create a structured dataset with all sender categories
    const senderValues = {};

    // Initialize with zeros
    senderCategories.forEach(category => {
      senderValues[category] = 0;
    });

    // Fill in the values from the data
    senderCategories.forEach(category => {
      if (senderData[category] && senderData[category][region] && senderData[category][region][gender]) {
        senderValues[category] = senderData[category][region][gender];
      }
    });

    // Filter out categories with zero values
    const nonZeroCategories = senderCategories.filter(category => senderValues[category] > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroCategories.map(category => {
        if (category === 'Father / Mother') return defaultT.receivedFromParents;
        if (category === 'Husband / Wife') return defaultT.receivedFromSpouse;
        if (category === 'Children') return defaultT.receivedFromChildren;
        if (category === 'Relatives') return defaultT.receivedFromRelatives;
        if (category === 'Others') return defaultT.receivedFromOthers;
        return category;
      }),
      datasets: [
        {
          data: nonZeroCategories.map(category => senderValues[category]),
          backgroundColor: [
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 99, 132, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!senderData || Object.keys(senderData).length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Define sender categories to include in the chart
    let senderCategories = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Relatives',
      'Others'
    ];

    // If a specific sender type is selected, filter the categories
    if (selectedSenderType !== 'All') {
      senderCategories = senderCategories.filter(category => category === selectedSenderType);
    }

    // Create structured datasets for male and female
    const maleValues = {};
    const femaleValues = {};

    // Initialize with zeros
    senderCategories.forEach(category => {
      maleValues[category] = 0;
      femaleValues[category] = 0;
    });

    // Fill in the values from the data
    senderCategories.forEach(category => {
      if (senderData[category] && senderData[category][region]) {
        maleValues[category] = senderData[category][region]['male'] || 0;
        femaleValues[category] = senderData[category][region]['female'] || 0;
      }
    });

    // Map the data to chart format
    const chartData = {
      labels: senderCategories.map(category => {
        if (category === 'Father / Mother') return defaultT.receivedFromParents;
        if (category === 'Husband / Wife') return defaultT.receivedFromSpouse;
        if (category === 'Children') return defaultT.receivedFromChildren;
        if (category === 'Relatives') return defaultT.receivedFromRelatives;
        if (category === 'Others') return defaultT.receivedFromOthers;
        return category;
      }),
      datasets: [
        {
          label: defaultT.male || 'Male',
          data: senderCategories.map(category => maleValues[category]),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.female || 'Female',
          data: senderCategories.map(category => femaleValues[category]),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional breakdown stacked bar chart
  const prepareRegionalBreakdownData = () => {
    if (!senderData || Object.keys(senderData).length === 0) return null;

    // Convert gender to lowercase for data access
    const gender = selectedGender.toLowerCase();

    // Define sender categories to include in the chart
    let senderCategories = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Relatives',
      'Others'
    ];

    // If a specific sender type is selected, filter the categories
    if (selectedSenderType !== 'All') {
      senderCategories = senderCategories.filter(category => category === selectedSenderType);
    }

    // Define regions to include
    const chartRegions = ['bangkok', 'central', 'north', 'northeast', 'south'];
    
    // Create a structured dataset for each sender category
    const datasets = senderCategories.map((category, index) => {
      const colors = [
        { bg: 'rgba(54, 162, 235, 0.7)', border: 'rgba(54, 162, 235, 1)' },
        { bg: 'rgba(255, 99, 132, 0.7)', border: 'rgba(255, 99, 132, 1)' },
        { bg: 'rgba(255, 206, 86, 0.7)', border: 'rgba(255, 206, 86, 1)' },
        { bg: 'rgba(75, 192, 192, 0.7)', border: 'rgba(75, 192, 192, 1)' },
        { bg: 'rgba(153, 102, 255, 0.7)', border: 'rgba(153, 102, 255, 1)' }
      ];
      
      // Get label based on category
      let label = category;
      if (category === 'Father / Mother') label = defaultT.receivedFromParents;
      if (category === 'Husband / Wife') label = defaultT.receivedFromSpouse;
      if (category === 'Children') label = defaultT.receivedFromChildren;
      if (category === 'Relatives') label = defaultT.receivedFromRelatives;
      if (category === 'Others') label = defaultT.receivedFromOthers;
      
      return {
        label,
        data: chartRegions.map(region => 
          senderData[category]?.[region]?.[gender] || 0
        ),
        backgroundColor: colors[index % colors.length].bg,
        borderColor: colors[index % colors.length].border,
        borderWidth: 1
      };
    });

    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'north') return defaultT.northern || 'Northern';
        if (region === 'northeast') return defaultT.northeastern || 'Northeastern';
        if (region === 'south') return defaultT.southern || 'Southern';
        return region;
      }),
      datasets
    };

    return chartData;
  };

  // Prepare data for sender flow analysis chart
  const prepareSenderFlowData = () => {
    if (!senderData || Object.keys(senderData).length === 0) return null;

    // Define regions to include
    const chartRegions = ['nationwide', 'bangkok', 'central', 'north', 'northeast', 'south'];
    
    // Define sender categories to include in the chart
    let senderCategories = [
      'Father / Mother',
      'Husband / Wife',
      'Children',
      'Relatives',
      'Others'
    ];

    // If a specific sender type is selected, filter the categories
    if (selectedSenderType !== 'All') {
      senderCategories = senderCategories.filter(category => category === selectedSenderType);
    }

    // Create datasets for each sender category
    const datasets = senderCategories.map((category, index) => {
      const colors = [
        { bg: 'rgba(54, 162, 235, 0.7)', border: 'rgba(54, 162, 235, 1)' },
        { bg: 'rgba(255, 99, 132, 0.7)', border: 'rgba(255, 99, 132, 1)' },
        { bg: 'rgba(255, 206, 86, 0.7)', border: 'rgba(255, 206, 86, 1)' },
        { bg: 'rgba(75, 192, 192, 0.7)', border: 'rgba(75, 192, 192, 1)' },
        { bg: 'rgba(153, 102, 255, 0.7)', border: 'rgba(153, 102, 255, 1)' }
      ];

      // Get label based on category
      let label = category;
      if (category === 'Father / Mother') label = defaultT.receivedFromParents;
      if (category === 'Husband / Wife') label = defaultT.receivedFromSpouse;
      if (category === 'Children') label = defaultT.receivedFromChildren;
      if (category === 'Relatives') label = defaultT.receivedFromRelatives;
      if (category === 'Others') label = defaultT.receivedFromOthers;
      
      return {
        label,
        data: chartRegions.map(region => 
          senderData[category]?.[region]?.['total'] || 0
        ),
        backgroundColor: colors[index % colors.length].bg,
        borderColor: colors[index % colors.length].border,
        borderWidth: 1
      };
    });
    
    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'nationwide') return defaultT.nationwide || 'Nationwide';
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'north') return defaultT.northern || 'Northern';
        if (region === 'northeast') return defaultT.northeastern || 'Northeastern';
        if (region === 'south') return defaultT.southern || 'Southern';
        return region;
      }),
      datasets
    };

    return chartData;
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Chart options for stacked bar charts
  const stackedBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        stacked: true,
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  const handleSenderTypeChange = (e) => {
    setSelectedSenderType(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  // If loading, show loading message
  if (loading) {
    return (
      <div className="money-goods-sender-container">
        <div className="loading-container">
          <i className="fas fa-spinner fa-spin" style={{ marginRight: '10px' }}></i>
          {defaultT.loading}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="money-goods-sender-container">
        <div className="error-message">
          <i className="fas fa-exclamation-triangle" style={{ marginRight: '10px' }}></i>
          {defaultT.loadError}: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="money-goods-sender-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-people-arrows" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsSenderAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsSenderDesc}
        </span>
      </p>

      <div className="money-goods-sender-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-user-friends" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.senderType}:
            </label>
            <select
              value={selectedSenderType}
              onChange={handleSenderTypeChange}
            >
              {senderTypes.map(type => (
                <option key={type} value={type}>
                  {type === 'All' ? (defaultT.all || 'All') :
                   type === 'Father / Mother' ? defaultT.receivedFromParents :
                   type === 'Husband / Wife' ? defaultT.receivedFromSpouse :
                   type === 'Children' ? defaultT.receivedFromChildren :
                   type === 'Relatives' ? defaultT.receivedFromRelatives :
                   type === 'Others' ? defaultT.receivedFromOthers : type}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-migrants">
              <div className="stat-content">
                <h3>{defaultT.totalMigrants}</h3>
                <div className="stat-value">{summaryStats.totalMigrants.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile received-from-parents">
              <div className="stat-content">
                <h3>{defaultT.receivedFromParents}</h3>
                <div className="stat-value">{summaryStats.receivedFromParents.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.receivedFromParents / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile received-from-spouse">
              <div className="stat-content">
                <h3>{defaultT.receivedFromSpouse}</h3>
                <div className="stat-value">{summaryStats.receivedFromSpouse.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.receivedFromSpouse / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile received-from-children">
              <div className="stat-content">
                <h3>{defaultT.receivedFromChildren}</h3>
                <div className="stat-value">{summaryStats.receivedFromChildren.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.receivedFromChildren / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.senderDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareSenderPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.genderComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.regionalBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalBreakdownData()}
                    options={stackedBarOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.senderFlowAnalysis}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareSenderFlowData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-people-arrows"></i>
                  <h4>{defaultT.senderPatterns}</h4>
                </div>
                <p>Analysis of money and goods receipt by sender type helps identify key financial contributors to migrants.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-home"></i>
                  <h4>{defaultT.familySupport}</h4>
                </div>
                <p>Family support patterns reveal how different family members contribute to migrant financial well-being.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{defaultT.genderDifferences}</h4>
                </div>
                <p>Gender differences in sender patterns highlight varying support networks for male and female migrants.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalVariations}</h4>
                </div>
                <p>Regional variations in sender patterns can inform targeted economic and social support policies.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoneyGoodsSenderAnalysis;

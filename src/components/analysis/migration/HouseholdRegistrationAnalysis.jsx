import React, { useState, useEffect } from 'react';
import '../../../styles/HouseholdRegistrationAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement);

const HouseholdRegistrationAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    householdRegistrationAnalysis: 'Household Registration Analysis',
    householdRegistrationDesc: 'This analysis shows household registration status by region, gender, and migration status, helping you understand population mobility and settlement patterns.',
    region: 'Region',
    gender: 'Gender',
    migrationStatus: 'Migration Status',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalPopulation: 'Total Population',
    registeredInResidence: 'Registered in Residence',
    registeredElsewhere: 'Registered Elsewhere',
    notRegistered: 'Not Registered Anywhere',
    registrationRate: 'Local Registration Rate',
    dataInsights: 'Data Insights',
    registrationPatterns: 'Registration Patterns',
    migrantRegistration: 'Migrant Registration Status',
    nonMigrantRegistration: 'Non-Migrant Registration Status',
    registrationDistribution: 'Registration Distribution',
    registrationLocation: 'Registration Location',
    detailedBreakdown: 'Detailed Breakdown',
    ...t
  };

  const [registrationData, setRegistrationData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedMigrationStatus, setSelectedMigrationStatus] = useState('Total');

  // Helper function to get registration data for a specific migration status
  const getRegistrationDataForMigrationStatus = (migrationStatus, gender, region) => {
    // Find the index of the migration status row
    const migrationStatusIndex = registrationData.findIndex(item => {
      return item.Label === migrationStatus &&
             item.Group === gender &&
             (region === 'All' || item.Region === region);
    });

    if (migrationStatusIndex === -1) {
      console.log(`Migration status ${migrationStatus} not found for ${gender} in ${region}`);
      return {
        'Registered in the Residence': 0,
        'Registered Elsewhere': 0,
        'Not Registered Anywhere': 0
      };
    }

    // Look for registration data in the rows following the migration status row
    const registeredInResidenceRow = registrationData.find((item, index) => {
      return index > migrationStatusIndex &&
             item.Label === 'Registered in the Residence' &&
             item.Group === gender &&
             (region === 'All' || item.Region === region);
    });

    const registeredElsewhereRow = registrationData.find((item, index) => {
      return index > migrationStatusIndex &&
             item.Label === 'Registered Elsewhere' &&
             item.Group === gender &&
             (region === 'All' || item.Region === region);
    });

    const notRegisteredRow = registrationData.find((item, index) => {
      return index > migrationStatusIndex &&
             item.Label === 'Not Registered Anywhere' &&
             item.Group === gender &&
             (region === 'All' || item.Region === region);
    });

    // Calculate the total population for this migration status
    const totalPopulation = parseFloat(registrationData[migrationStatusIndex].Population) || 0;

    // Get the values for each category, defaulting to 0 if not found
    const registeredInResidence = registeredInResidenceRow ? parseFloat(registeredInResidenceRow.Population) || 0 : 0;
    const registeredElsewhere = registeredElsewhereRow ? parseFloat(registeredElsewhereRow.Population) || 0 : 0;
    const notRegistered = notRegisteredRow ? parseFloat(notRegisteredRow.Population) || 0 : 0;

    // If Not Registered Anywhere is missing, calculate it from the total
    // Only do this if the total is greater than the sum of the other two categories
    let notRegisteredValue = notRegistered;
    if (notRegisteredValue === 0) {
      const calculatedNotRegistered = totalPopulation - registeredInResidence - registeredElsewhere;
      if (calculatedNotRegistered > 0) {
        notRegisteredValue = calculatedNotRegistered;
        console.log(`Calculated Not Registered Anywhere for ${migrationStatus} ${gender}: ${notRegisteredValue}`);
      }
    }

    return {
      'Registered in the Residence': registeredInResidence,
      'Registered Elsewhere': registeredElsewhere,
      'Not Registered Anywhere': notRegisteredValue
    };
  };

  // Effect to update migration data when filters change
  useEffect(() => {
    if (registrationData.length > 0) {
      // Call the function to prepare migration data
      const updateMigrationData = () => {
        console.log('Updating migration data with filters:', { selectedRegion, selectedGender, selectedMigrationStatus });

        // Get the gender to use for data lookup
        const genderToUse = selectedGender === 'All' ? 'Total' : selectedGender;

        // Get registration data for Migrant and Non-Migrant
        const migrantData = getRegistrationDataForMigrationStatus('Migrant', genderToUse, selectedRegion);
        const nonMigrantData = getRegistrationDataForMigrationStatus('Non-Migrant', genderToUse, selectedRegion);

        console.log('Retrieved migration data:', { migrantData, nonMigrantData });

        // Create a new object to avoid mutating state directly
        const updatedRegistrationData = {
          'Migrant': migrantData,
          'Non-Migrant': nonMigrantData
        };

        // Update the state
        setRegistrationByMigration(updatedRegistrationData);

        console.log('Migration data updated based on filters');
      };

      updateMigrationData();
    }
  }, [selectedRegion, selectedGender, selectedMigrationStatus, registrationData]);

  // Load CSV data
  useEffect(() => {
    setLoading(true);
    console.log('Loading data from CSV...');
    Papa.parse('/data/t2.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        console.log('T2 data loaded:', results.data.length, 'rows');
        console.log('Sample data:', results.data.slice(0, 3));

        // Process the data to ensure all numeric values are properly parsed
        const processedData = results.data
          .filter(item => item.Label && item.Population) // Filter out any rows with missing data
          .map(item => ({
            ...item,
            Population: parseFloat(item.Population) || 0
          }));

        // Log some key data points for debugging
        const totalNationwide = processedData.find(item =>
          item.Label === 'total' && item.Region === 'Nationwide' && item.Group === 'Total'
        );

        const registeredInResidenceNationwide = processedData.find(item =>
          item.Label === 'Registered in the Residence' && item.Region === 'Nationwide' && item.Group === 'Total'
        );

        const registeredElsewhereNationwide = processedData.find(item =>
          item.Label === 'Registered Elsewhere' && item.Region === 'Nationwide' && item.Group === 'Total'
        );

        console.log('Key data points:', {
          totalNationwide: totalNationwide?.Population,
          registeredInResidenceNationwide: registeredInResidenceNationwide?.Population,
          registeredElsewhereNationwide: registeredElsewhereNationwide?.Population
        });

        setRegistrationData(processedData);
        setLoading(false);
      },
      error: (error) => {
        console.error('Error loading T2 data:', error);
        setError(error.message);
        setLoading(false);
      }
    });
  }, []);

  // Extract available regions
  const regions = [...new Set(registrationData.filter(item => item.Region).map(item => item.Region))];

  // Extract available genders - remove 'All' since we already have 'Total'
  const genders = [...new Set(registrationData.filter(item => item.Group).map(item => item.Group))]
    .filter(gender => gender !== 'All');

  // Extract available migration statuses
  const migrationStatuses = ['Total', 'Migrant', 'Non-Migrant'];

  // Prepare data for registration status pie chart
  const prepareRegistrationPieData = () => {
    console.log('Preparing pie chart data');
    // Group data by registration status
    const registrationGroups = {
      'Registered in the Residence': 0,
      'Registered Elsewhere': 0,
      'Not Registered Anywhere': 0
    };

    try {
      // Get the gender to use for data lookup
      const genderToUse = selectedGender === 'All' ? 'Total' : selectedGender;

      // If a specific migration status is selected, use that data
      if (selectedMigrationStatus !== 'Total') {
        // Get registration data for the selected migration status
        const migrationData = getRegistrationDataForMigrationStatus(
          selectedMigrationStatus,
          genderToUse,
          selectedRegion
        );

        registrationGroups['Registered in the Residence'] = migrationData['Registered in the Residence'];
        registrationGroups['Registered Elsewhere'] = migrationData['Registered Elsewhere'];
        registrationGroups['Not Registered Anywhere'] = migrationData['Not Registered Anywhere'];
      } else {
        // For Total migration status, get the overall data
        // Find registration status rows
        const registeredInResidenceRow = registrationData.find(item =>
          item.Label === 'Registered in the Residence' &&
          item.Group === genderToUse &&
          (selectedRegion === 'Nationwide' || item.Region === selectedRegion) &&
          // Make sure we're getting the overall data, not migration-specific
          registrationData.findIndex(row => row.Label === 'total' && row.Group === genderToUse) <
          registrationData.findIndex(row => row === item)
        );

        if (registeredInResidenceRow) {
          registrationGroups['Registered in the Residence'] = parseFloat(registeredInResidenceRow.Population) || 0;
        }

        const registeredElsewhereRow = registrationData.find(item =>
          item.Label === 'Registered Elsewhere' &&
          item.Group === genderToUse &&
          (selectedRegion === 'Nationwide' || item.Region === selectedRegion) &&
          // Make sure we're getting the overall data, not migration-specific
          registrationData.findIndex(row => row.Label === 'total' && row.Group === genderToUse) <
          registrationData.findIndex(row => row === item)
        );

        if (registeredElsewhereRow) {
          registrationGroups['Registered Elsewhere'] = parseFloat(registeredElsewhereRow.Population) || 0;
        }

        const notRegisteredRow = registrationData.find(item =>
          item.Label === 'Not Registered Anywhere' &&
          item.Group === genderToUse &&
          (selectedRegion === 'Nationwide' || item.Region === selectedRegion) &&
          // Make sure we're getting the overall data, not migration-specific
          registrationData.findIndex(row => row.Label === 'total' && row.Group === genderToUse) <
          registrationData.findIndex(row => row === item)
        );

        if (notRegisteredRow) {
          registrationGroups['Not Registered Anywhere'] = parseFloat(notRegisteredRow.Population) || 0;
        }
      }

      console.log('Registration groups calculated directly from rows:', registrationGroups);

      // Calculate total and percentages for better visualization
      const total = Object.values(registrationGroups).reduce((sum, val) => sum + val, 0);
      const percentages = {};

      Object.keys(registrationGroups).forEach(key => {
        const value = registrationGroups[key];
        percentages[key] = total > 0 ? (value / total) * 100 : 0;
      });

      console.log('Registration percentages:', percentages);

      // Create a special dataset for small values
      // If Not Registered Anywhere is less than 1%, we'll use a minimum size to make it visible
      const notRegisteredPct = percentages['Not Registered Anywhere'];
      let adjustedData = [...Object.values(registrationGroups)];

      // Adjust the data to make small values more visible
      if (notRegisteredPct > 0 && notRegisteredPct < 1) {
        // Make the Not Registered Anywhere slice at least 5% for visibility
        // while preserving the ratio between the other two slices
        const notRegisteredIndex = 2; // Index of Not Registered Anywhere
        const minVisiblePercentage = 5;

        // Calculate how much we need to reduce from the other slices
        const remainingPercentage = 100 - minVisiblePercentage;

        // Calculate scaling factor for the other slices
        const scalingFactor = remainingPercentage / (100 - notRegisteredPct);

        // Adjust each slice
        adjustedData = adjustedData.map((value, index) => {
          if (index === notRegisteredIndex) {
            // Make the small slice more visible
            return (total * minVisiblePercentage) / 100;
          } else {
            // Scale down the other slices proportionally
            return value * scalingFactor;
          }
        });

        console.log('Adjusted data to make small values visible:', adjustedData);
      }

      // Store original values for tooltips
      const originalValues = [...Object.values(registrationGroups)];

      return {
        labels: Object.keys(registrationGroups),
        datasets: [
          {
            data: adjustedData,
            backgroundColor: ['#36A2EB', '#FFCE56', '#FF6384'],
            hoverBackgroundColor: ['#36A2EB', '#FFCE56', '#FF6384'],
            originalValues: originalValues
          },
        ],
      };
    } catch (err) {
      console.error('Error preparing pie chart data:', err);
      return {
        labels: Object.keys(registrationGroups),
        datasets: [
          {
            data: [0, 0, 0],
            backgroundColor: ['#36A2EB', '#FFCE56', '#FF6384'],
            hoverBackgroundColor: ['#36A2EB', '#FFCE56', '#FF6384'],
          },
        ],
      };
    }
  };

  // Prepare data for registration status by migration status bar chart
  const prepareRegistrationByMigrationData = () => {
    console.log('Preparing migration data chart from state');

    try {
      // If a specific migration status is selected, only return data for that status
      let datasets = [];

      if (selectedMigrationStatus === 'Total') {
        // Show both migrant and non-migrant data
        datasets = [
          {
            label: 'Migrant',
            data: [
              registrationByMigration['Migrant']['Registered in the Residence'],
              registrationByMigration['Migrant']['Registered Elsewhere'],
              registrationByMigration['Migrant']['Not Registered Anywhere']
            ],
            backgroundColor: '#36A2EB',
          },
          {
            label: 'Non-Migrant',
            data: [
              registrationByMigration['Non-Migrant']['Registered in the Residence'],
              registrationByMigration['Non-Migrant']['Registered Elsewhere'],
              registrationByMigration['Non-Migrant']['Not Registered Anywhere']
            ],
            backgroundColor: '#FFCE56',
          },
        ];
      } else if (selectedMigrationStatus === 'Migrant') {
        // Only show migrant data
        datasets = [
          {
            label: 'Migrant',
            data: [
              registrationByMigration['Migrant']['Registered in the Residence'],
              registrationByMigration['Migrant']['Registered Elsewhere'],
              registrationByMigration['Migrant']['Not Registered Anywhere']
            ],
            backgroundColor: '#36A2EB',
          }
        ];
      } else if (selectedMigrationStatus === 'Non-Migrant') {
        // Only show non-migrant data
        datasets = [
          {
            label: 'Non-Migrant',
            data: [
              registrationByMigration['Non-Migrant']['Registered in the Residence'],
              registrationByMigration['Non-Migrant']['Registered Elsewhere'],
              registrationByMigration['Non-Migrant']['Not Registered Anywhere']
            ],
            backgroundColor: '#FFCE56',
          }
        ];
      }

      return {
        labels: ['Registered in the Residence', 'Registered Elsewhere', 'Not Registered Anywhere'],
        datasets: datasets,
      };
    } catch (err) {
      console.error('Error preparing migration data:', err);
      // Return empty data structure in case of error
      return {
        labels: ['Registered in the Residence', 'Registered Elsewhere', 'Not Registered Anywhere'],
        datasets: [],
      };
    }
  };

  // Helper function to get detailed registration data for a specific migration status
  const getDetailedRegistrationDataForMigrationStatus = (migrationStatus, gender, region) => {
    // Find the index of the migration status row
    const migrationStatusIndex = registrationData.findIndex(item => {
      return item.Label === migrationStatus &&
             item.Group === gender &&
             (region === 'All' || item.Region === region);
    });

    if (migrationStatusIndex === -1) {
      console.log(`Migration status ${migrationStatus} not found for ${gender} in ${region}`);
      return {
        'In This Province': 0,
        'In Another Province': 0,
        'In Another Country': 0
      };
    }

    // Look for detailed registration data in the rows following the migration status row
    const inThisProvinceRow = registrationData.find((item, index) => {
      return index > migrationStatusIndex &&
             item.Label === 'In This Province' &&
             item.Group === gender &&
             (region === 'All' || item.Region === region) &&
             // Stop when we reach the next migration status or total
             !(['Migrant', 'Non-Migrant', 'total'].includes(registrationData[index + 1]?.Label));
    });

    const inAnotherProvinceRow = registrationData.find((item, index) => {
      return index > migrationStatusIndex &&
             item.Label === 'In Another Province' &&
             item.Group === gender &&
             (region === 'All' || item.Region === region) &&
             // Stop when we reach the next migration status or total
             !(['Migrant', 'Non-Migrant', 'total'].includes(registrationData[index + 1]?.Label));
    });

    const inAnotherCountryRow = registrationData.find((item, index) => {
      return index > migrationStatusIndex &&
             item.Label === 'In Another Country' &&
             item.Group === gender &&
             (region === 'All' || item.Region === region) &&
             // Stop when we reach the next migration status or total
             !(['Migrant', 'Non-Migrant', 'total'].includes(registrationData[index + 1]?.Label));
    });

    return {
      'In This Province': inThisProvinceRow ? parseFloat(inThisProvinceRow.Population) || 0 : 0,
      'In Another Province': inAnotherProvinceRow ? parseFloat(inAnotherProvinceRow.Population) || 0 : 0,
      'In Another Country': inAnotherCountryRow ? parseFloat(inAnotherCountryRow.Population) || 0 : 0
    };
  };

  // Prepare data for detailed registration status bar chart
  const prepareDetailedRegistrationData = () => {
    console.log('Preparing detailed registration data');

    // Create structure for the data
    const detailedRegistration = {
      'In This Province': 0,
      'In Another Province': 0,
      'In Another Country': 0
    };

    try {
      // Get the gender to use for data lookup
      const genderToUse = selectedGender === 'All' ? 'Total' : selectedGender;

      // If a specific migration status is selected, use that data
      if (selectedMigrationStatus !== 'Total') {
        // Get detailed registration data for the selected migration status
        const detailedData = getDetailedRegistrationDataForMigrationStatus(
          selectedMigrationStatus,
          genderToUse,
          selectedRegion
        );

        detailedRegistration['In This Province'] = detailedData['In This Province'];
        detailedRegistration['In Another Province'] = detailedData['In Another Province'];
        detailedRegistration['In Another Country'] = detailedData['In Another Country'];
      } else {
        // For Total migration status, get the overall data
        // Find the total row index
        const totalRowIndex = registrationData.findIndex(item =>
          item.Label === 'total' &&
          item.Group === genderToUse &&
          (selectedRegion === 'All' || item.Region === selectedRegion)
        );

        if (totalRowIndex !== -1) {
          // Find detailed registration rows after the total row
          const inThisProvinceRow = registrationData.find((item, index) =>
            index > totalRowIndex &&
            item.Label === 'In This Province' &&
            item.Group === genderToUse &&
            (selectedRegion === 'All' || item.Region === selectedRegion) &&
            // Make sure we're getting the overall data, not migration-specific
            !(['Migrant', 'Non-Migrant'].includes(registrationData[index - 1]?.Label))
          );

          if (inThisProvinceRow) {
            detailedRegistration['In This Province'] = parseFloat(inThisProvinceRow.Population) || 0;
          }

          const inAnotherProvinceRow = registrationData.find((item, index) =>
            index > totalRowIndex &&
            item.Label === 'In Another Province' &&
            item.Group === genderToUse &&
            (selectedRegion === 'All' || item.Region === selectedRegion) &&
            // Make sure we're getting the overall data, not migration-specific
            !(['Migrant', 'Non-Migrant'].includes(registrationData[index - 1]?.Label))
          );

          if (inAnotherProvinceRow) {
            detailedRegistration['In Another Province'] = parseFloat(inAnotherProvinceRow.Population) || 0;
          }

          const inAnotherCountryRow = registrationData.find((item, index) =>
            index > totalRowIndex &&
            item.Label === 'In Another Country' &&
            item.Group === genderToUse &&
            (selectedRegion === 'All' || item.Region === selectedRegion) &&
            // Make sure we're getting the overall data, not migration-specific
            !(['Migrant', 'Non-Migrant'].includes(registrationData[index - 1]?.Label))
          );

          if (inAnotherCountryRow) {
            detailedRegistration['In Another Country'] = parseFloat(inAnotherCountryRow.Population) || 0;
          }
        }
      }

      console.log('Detailed registration data calculated directly from rows:', detailedRegistration);

      return {
        labels: Object.keys(detailedRegistration),
        datasets: [
          {
            label: 'Population',
            data: Object.values(detailedRegistration),
            backgroundColor: ['#4BC0C0', '#FF9F40', '#9966FF'],
          },
        ],
      };
    } catch (err) {
      console.error('Error preparing detailed registration data:', err);
      // Return empty data structure in case of error
      return {
        labels: ['In This Province', 'In Another Province', 'In Another Country'],
        datasets: [{
          label: 'Population',
          data: [0, 0, 0],
          backgroundColor: ['#4BC0C0', '#FF9F40', '#9966FF'],
        }],
      };
    }
  };

  // Create a state variable for registration by migration data
  const [registrationByMigration, setRegistrationByMigration] = useState({
    'Migrant': {
      'Registered in the Residence': 0,
      'Registered Elsewhere': 0,
      'Not Registered Anywhere': 0
    },
    'Non-Migrant': {
      'Registered in the Residence': 0,
      'Registered Elsewhere': 0,
      'Not Registered Anywhere': 0
    }
  });

  // Calculate summary statistics
  const calculateSummaryStats = () => {
    console.log('Calculating summary stats with data length:', registrationData.length);
    let totalPopulation = 0;
    let registeredInResidence = 0;
    let registeredElsewhere = 0;
    let notRegistered = 0;

    // Get the gender to use for data lookup
    const genderToUse = selectedGender === 'All' ? 'Total' : selectedGender;

    // If a specific migration status is selected, use that data
    if (selectedMigrationStatus !== 'Total') {
      // Find the migration status row
      const migrationStatusRow = registrationData.find(item =>
        item.Label === selectedMigrationStatus &&
        item.Group === genderToUse &&
        (selectedRegion === 'All' || item.Region === selectedRegion)
      );

      if (migrationStatusRow) {
        totalPopulation = parseFloat(migrationStatusRow.Population) || 0;
      }

      // Get registration data for the selected migration status
      const migrationData = getRegistrationDataForMigrationStatus(
        selectedMigrationStatus,
        genderToUse,
        selectedRegion
      );

      registeredInResidence = migrationData['Registered in the Residence'];
      registeredElsewhere = migrationData['Registered Elsewhere'];
      notRegistered = migrationData['Not Registered Anywhere'];
    } else {
      // For Total migration status, get the overall data
      // Find the total population row
      const totalPopulationRow = registrationData.find(item =>
        item.Label === 'total' &&
        item.Group === genderToUse &&
        (selectedRegion === 'All' || item.Region === selectedRegion)
      );

      if (totalPopulationRow) {
        totalPopulation = parseFloat(totalPopulationRow.Population) || 0;
      }

      // Find registration status rows
      const registeredInResidenceRow = registrationData.find(item =>
        item.Label === 'Registered in the Residence' &&
        item.Group === genderToUse &&
        (selectedRegion === 'All' || item.Region === selectedRegion) &&
        // Make sure we're getting the overall data, not migration-specific
        registrationData.findIndex(row => row.Label === 'total' && row.Group === genderToUse) <
        registrationData.findIndex(row => row === item)
      );

      if (registeredInResidenceRow) {
        registeredInResidence = parseFloat(registeredInResidenceRow.Population) || 0;
      }

      const registeredElsewhereRow = registrationData.find(item =>
        item.Label === 'Registered Elsewhere' &&
        item.Group === genderToUse &&
        (selectedRegion === 'All' || item.Region === selectedRegion) &&
        // Make sure we're getting the overall data, not migration-specific
        registrationData.findIndex(row => row.Label === 'total' && row.Group === genderToUse) <
        registrationData.findIndex(row => row === item)
      );

      if (registeredElsewhereRow) {
        registeredElsewhere = parseFloat(registeredElsewhereRow.Population) || 0;
      }

      const notRegisteredRow = registrationData.find(item =>
        item.Label === 'Not Registered Anywhere' &&
        item.Group === genderToUse &&
        (selectedRegion === 'All' || item.Region === selectedRegion) &&
        // Make sure we're getting the overall data, not migration-specific
        registrationData.findIndex(row => row.Label === 'total' && row.Group === genderToUse) <
        registrationData.findIndex(row => row === item)
      );

      if (notRegisteredRow) {
        notRegistered = parseFloat(notRegisteredRow.Population) || 0;
      }
    }

    // Debug the data
    console.log('Summary stats found:', {
      totalPopulation,
      registeredInResidence,
      registeredElsewhere,
      notRegistered
    });

    // If we still don't have a total population, calculate it from the components
    if (totalPopulation === 0) {
      // Sum the registration categories
      totalPopulation = registeredInResidence + registeredElsewhere + notRegistered;
    }

    // If we don't have registered elsewhere value, we can try to calculate it from the detailed breakdown
    // But we'll only do this if we really need to, as we should have the direct value from the data
    if (registeredElsewhere === 0) {
      // Find the individual detailed rows
      const inThisProvinceRow = registrationData.find(item => {
        // Match the label
        const labelMatch = item.Label === 'In This Province';
        // Match the region
        const regionMatch = selectedRegion === 'All' || item.Region === selectedRegion;

        // Match gender
        let genderMatch = false;
        if (selectedGender === 'Total') {
          genderMatch = item.Group === 'Total';
        } else if (selectedGender === 'Male' || selectedGender === 'Female') {
          genderMatch = item.Group === selectedGender;
        } else {
          genderMatch = true;
        }

        // Match migration status
        let migrationMatch = false;
        if (selectedMigrationStatus === 'Total') {
          if (selectedGender === 'Total') {
            migrationMatch = item.Group === 'Total';
          } else {
            migrationMatch = item.Group === selectedGender;
          }
        } else {
          // If specific migration status is selected (Migrant or Non-Migrant)
          if (selectedGender === 'Total') {
            // If gender is Total, find rows with Label = 'In This Province' for the selected migration status
            migrationMatch = item.Label === selectedMigrationStatus && item.Group === 'Total';
          } else {
            // If specific gender is selected, find rows with Label = selectedMigrationStatus and Group = selectedGender
            migrationMatch = item.Label === selectedMigrationStatus && item.Group === selectedGender;
          }
        }

        return labelMatch && regionMatch && (genderMatch || migrationMatch);
      });

      const inAnotherProvinceRow = registrationData.find(item => {
        // Match the label
        const labelMatch = item.Label === 'In Another Province';
        // Match the region
        const regionMatch = selectedRegion === 'All' || item.Region === selectedRegion;

        // Match gender
        let genderMatch = false;
        if (selectedGender === 'Total') {
          genderMatch = item.Group === 'Total';
        } else if (selectedGender === 'Male' || selectedGender === 'Female') {
          genderMatch = item.Group === selectedGender;
        } else {
          genderMatch = true;
        }

        // Match migration status
        let migrationMatch = false;
        if (selectedMigrationStatus === 'Total') {
          if (selectedGender === 'Total') {
            migrationMatch = item.Group === 'Total';
          } else {
            migrationMatch = item.Group === selectedGender;
          }
        } else {
          // If specific migration status is selected (Migrant or Non-Migrant)
          if (selectedGender === 'Total') {
            // If gender is Total, find rows with Label = 'In Another Province' for the selected migration status
            migrationMatch = item.Label === selectedMigrationStatus && item.Group === 'Total';
          } else {
            // If specific gender is selected, find rows with Label = selectedMigrationStatus and Group = selectedGender
            migrationMatch = item.Label === selectedMigrationStatus && item.Group === selectedGender;
          }
        }

        return labelMatch && regionMatch && (genderMatch || migrationMatch);
      });

      const inAnotherCountryRow = registrationData.find(item => {
        // Match the label
        const labelMatch = item.Label === 'In Another Country';
        // Match the region
        const regionMatch = selectedRegion === 'All' || item.Region === selectedRegion;

        // Match gender
        let genderMatch = false;
        if (selectedGender === 'Total') {
          genderMatch = item.Group === 'Total';
        } else if (selectedGender === 'Male' || selectedGender === 'Female') {
          genderMatch = item.Group === selectedGender;
        } else {
          genderMatch = true;
        }

        // Match migration status
        let migrationMatch = false;
        if (selectedMigrationStatus === 'Total') {
          if (selectedGender === 'Total') {
            migrationMatch = item.Group === 'Total';
          } else {
            migrationMatch = item.Group === selectedGender;
          }
        } else {
          // If specific migration status is selected (Migrant or Non-Migrant)
          if (selectedGender === 'Total') {
            // If gender is Total, find rows with Label = 'In Another Country' for the selected migration status
            migrationMatch = item.Label === selectedMigrationStatus && item.Group === 'Total';
          } else {
            // If specific gender is selected, find rows with Label = selectedMigrationStatus and Group = selectedGender
            migrationMatch = item.Label === selectedMigrationStatus && item.Group === selectedGender;
          }
        }

        return labelMatch && regionMatch && (genderMatch || migrationMatch);
      });

      // Calculate the sum
      const inThisProvince = inThisProvinceRow ? parseFloat(inThisProvinceRow.Population) || 0 : 0;
      const inAnotherProvince = inAnotherProvinceRow ? parseFloat(inAnotherProvinceRow.Population) || 0 : 0;
      const inAnotherCountry = inAnotherCountryRow ? parseFloat(inAnotherCountryRow.Population) || 0 : 0;

      const calculatedElsewhere = inThisProvince + inAnotherProvince + inAnotherCountry;
      if (calculatedElsewhere > 0) {
        registeredElsewhere = calculatedElsewhere;
      }
    }

    const registrationRate = totalPopulation > 0 ? (registeredInResidence / totalPopulation) * 100 : 0;

    return {
      totalPopulation,
      registeredInResidence,
      registeredElsewhere,
      notRegistered,
      registrationRate
    };
  };

  // Chart options
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          boxWidth: 12,
          padding: 20,
          font: {
            size: 13,
            family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
          },
          usePointStyle: true
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        padding: 10,
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        },
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            // Use original values for tooltips if available
            const dataset = context.dataset;
            const originalValues = dataset.originalValues || null;
            let value = context.raw || 0;

            // If we have original values, use those instead of the adjusted ones
            if (originalValues && originalValues.length > context.dataIndex) {
              value = originalValues[context.dataIndex];
            }

            const total = originalValues ?
              originalValues.reduce((sum, val) => sum + val, 0) :
              context.chart.getDatasetMeta(0).total;

            const percentage = total > 0 ? ((value / total) * 100).toFixed(2) : 0;
            return `${label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      },
      title: {
        display: false,
      },
    },
    elements: {
      arc: {
        borderWidth: 1,
        borderColor: '#fff'
      }
    },
    layout: {
      padding: 10
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        stacked: true,
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        },
        ticks: {
          font: {
            size: 12
          },
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      },
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          boxWidth: 12,
          padding: 20,
          font: {
            size: 13,
            family: "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif"
          },
          usePointStyle: true
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        padding: 10,
        titleFont: {
          size: 14
        },
        bodyFont: {
          size: 13
        },
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.raw || 0;
            return `${label}: ${value.toLocaleString()}`;
          }
        }
      },
      title: {
        display: false,
      },
    },
    elements: {
      bar: {
        borderRadius: 4
      }
    },
    layout: {
      padding: 10
    }
  };

  // Add debugging logs
  console.log('Rendering with state:', {
    selectedRegion,
    selectedGender,
    selectedMigrationStatus,
    dataLength: registrationData.length,
    regions,
    genders,
    migrationStatuses
  });

  // Wrap in try-catch to catch any errors during calculation
  let summaryStats;
  try {
    summaryStats = calculateSummaryStats();
    console.log('Summary stats calculated:', summaryStats);
  } catch (err) {
    console.error('Error calculating summary stats:', err);
    return <div className="error-container">Error calculating statistics: {err.message}</div>;
  }

  if (loading) {
    return <div className="loading-container">{defaultT.loading}</div>;
  }

  if (error) {
    return <div className="error-container">{defaultT.loadError}: {error}</div>;
  }

  return (
    <div className="household-registration-container">
      <h2>
        <i className="fas fa-home" style={{ marginRight: '10px', color: '#3498db' }}></i>
        {defaultT.householdRegistrationAnalysis}
      </h2>

      <div className="registration-analysis-wrapper">
        <div className="info-banner">
          <div className="info-icon">
            <i className="fas fa-info-circle"></i>
          </div>
          <p>{defaultT.householdRegistrationDesc}</p>
        </div>

        <div className="control-panel">
          <div className="control-filters">
            <div className="filter-control">
              <label htmlFor="region-select">{defaultT.region}</label>
              <div className="select-wrapper">
                <select
                  id="region-select"
                  value={selectedRegion}
                  onChange={(e) => setSelectedRegion(e.target.value)}
                >
                  {regions.map((region) => (
                    <option key={region} value={region}>{region}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="filter-control">
              <label htmlFor="gender-select">{defaultT.gender}</label>
              <div className="select-wrapper">
                <select
                  id="gender-select"
                  value={selectedGender}
                  onChange={(e) => setSelectedGender(e.target.value)}
                >
                  {genders.map((gender) => (
                    <option key={gender} value={gender}>{gender}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="filter-control">
              <label htmlFor="migration-status-select">{defaultT.migrationStatus}</label>
              <div className="select-wrapper">
                <select
                  id="migration-status-select"
                  value={selectedMigrationStatus}
                  onChange={(e) => setSelectedMigrationStatus(e.target.value)}
                >
                  {migrationStatuses.map((status) => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile population">
              <div className="stat-content">
                <h3>{defaultT.totalPopulation}</h3>
                <div className="stat-value">{summaryStats.totalPopulation.toLocaleString()}</div>
                <div className="stat-note">(15+ years)</div>
              </div>
            </div>

            <div className="stat-tile residence">
              <div className="stat-content">
                <h3>{defaultT.registeredInResidence}</h3>
                <div className="stat-value">{summaryStats.registeredInResidence.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile elsewhere">
              <div className="stat-content">
                <h3>{defaultT.registeredElsewhere}</h3>
                <div className="stat-value">{summaryStats.registeredElsewhere.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile rate">
              <div className="stat-content">
                <h3>{defaultT.registrationRate}</h3>
                <div className="stat-value">{summaryStats.registrationRate.toFixed(2)}%</div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.registrationDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareRegistrationPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.registrationPatterns}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegistrationByMigrationData()}
                    options={{
                      ...barOptions,
                      scales: {
                        ...barOptions.scales,
                        y: {
                          ...barOptions.scales.y,
                          // Adjust the y-axis to better show the data
                          max: 1000 // Fixed scale to ensure data is visible
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.registeredElsewhere} - {defaultT.detailedBreakdown || 'Detailed Breakdown'}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareDetailedRegistrationData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-home"></i>
                  <h4>{defaultT.registrationPatterns}</h4>
                </div>
                <p>Registration status analysis helps understand population mobility and settlement patterns.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-user-friends"></i>
                  <h4>{defaultT.migrantRegistration}</h4>
                </div>
                <p>Migrant registration patterns reveal how migrants integrate into local administrative systems.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marker-alt"></i>
                  <h4>{defaultT.registrationLocation}</h4>
                </div>
                <p>Registration location analysis shows where people maintain their official residence relative to their current location.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HouseholdRegistrationAnalysis;

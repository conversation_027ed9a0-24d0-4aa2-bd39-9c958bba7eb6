import React, { useState, useEffect } from 'react';
import { Pie, Bar, Line } from 'react-chartjs-2';
import <PERSON> from 'papaparse';
import '../../../styles/ExpectedStayAnalysis.css';

const ExpectedStayAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    expectedStayAnalysis: 'Expected Stay Duration Analysis',
    expectedStayDesc: 'This analysis shows migrant population by expected stay duration, gender, and region, helping you predict infrastructure needs.',
    region: 'Region',
    gender: 'Gender',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMigrants: 'Total Migrants',
    shortTermMigrants: 'Short-term Migrants (<12 months)',
    longTermMigrants: 'Long-term Migrants (≥12 months)',
    permanentMigrants: 'Permanent Migrants',
    dataInsights: 'Data Insights',
    stayPatterns: 'Stay Duration Patterns',
    genderComparison: 'Gender Comparison',
    regionalDifferences: 'Regional Differences',
    stayDistribution: 'Stay Duration Distribution',
    stayComparison: 'Gender Comparison by Duration',
    detailedBreakdown: 'Detailed Regional Breakdown',
    shortTerm: 'Short-term',
    mediumTerm: 'Medium-term',
    longTerm: 'Long-term',
    permanent: 'Permanent',
    ...t
  };

  const [stayData, setStayData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Load expected stay duration data
  useEffect(() => {
    setLoading(true);
    setError(null);

    Papa.parse('/data/t13.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          if (results.data && results.data.length > 0) {
            // Log the first row to understand the data structure
            console.log('First row of t13.csv:', results.data[0]);

            // Log column names to understand the format
            console.log('Column names:', Object.keys(results.data[0]));

            // Log the raw data for the first row to see actual values
            console.log('Raw data values for first row:');
            Object.entries(results.data[0]).forEach(([key, value]) => {
              console.log(`  ${key}: ${value}`);
            });

            // Process the data
            const processedData = processRawData(results.data);
            console.log('Processed data first row:', processedData[0]);

            setStayData(processedData);
          } else {
            throw new Error('No data found in CSV file');
          }
        } catch (err) {
          console.error('Error processing expected stay duration data:', err);
          setError('Failed to process expected stay duration data');
        } finally {
          setLoading(false);
        }
      },
      error: (err) => {
        console.error('Error loading expected stay duration data:', err);
        setError('Failed to load expected stay duration data');
        setLoading(false);
      }
    });
  }, []);

  // Process raw CSV data into a more usable format
  const processRawData = (rawData) => {
    if (rawData.length === 0) return [];

    // First, let's examine the first row to understand the data structure
    const firstRow = rawData[0];
    console.log('First row keys:', Object.keys(firstRow));

    // Create a mapping for our regions and genders
    const regionMapping = {
      'nationwide': 'nationwide',
      'bangkok': 'bangkok',
      'central': 'central',
      'north': 'north',
      'northeast': 'northeast',
      'south': 'south'
    };

    const genderMapping = {
      'total': 'total',
      'male': 'male',
      'female': 'female'
    };

    // Extract the header information and the actual values from the column names
    // The header row contains column names like "12.155_798.749_total_nationwide"
    const headerInfo = {};
    const headerValues = {};

    Object.keys(firstRow).forEach(columnName => {
      if (columnName === 'population_label') return;

      const parts = columnName.split('_');
      if (parts.length >= 3) { // 至少需要3部分：数值_性别_地区
        let percentage = 0;
        let absoluteCount = 0;
        let gender = '';
        let region = '';

        // 处理特殊格式：47.784_total_bangkok (只有一个数值)
        if (parts.length === 3) {
          // 检查第二部分是否是性别标识
          if (['total', 'male', 'female'].includes(parts[1])) {
            percentage = parseFloat(parts[0]) || 0; // e.g., "47.784"
            absoluteCount = percentage; // 使用百分比作为绝对值
            gender = parts[1]; // e.g., "total"
            region = parts[2]; // e.g., "bangkok"
            console.log(`Special format detected: ${columnName} -> ${gender}_${region} = ${absoluteCount}`);
          } else {
            // 不符合已知格式，跳过
            return;
          }
        }
        // 处理标准格式：12.155_798.749_total_nationwide (有两个数值)
        else if (parts.length >= 4) {
          percentage = parseFloat(parts[0]) || 0; // e.g., "12.155"
          absoluteCount = parseFloat(parts[1]) || 0; // e.g., "798.749"
          gender = parts[2]; // e.g., "total"
          region = parts[3]; // e.g., "nationwide"
        }
        else {
          // 不符合任何已知格式，跳过
          return;
        }

        if (regionMapping[region] && genderMapping[gender]) {
          const key = `${gender}_${region}`;
          headerInfo[key] = {
            columnName,
            percentage,
            absoluteCount
          };

          // Store the absolute count for the header row
          headerValues[key] = absoluteCount;

          // 为 Bangkok 添加调试信息
          if (region === 'bangkok') {
            console.log(`Bangkok data: ${key}, value: ${absoluteCount}, original: ${columnName}`);
          }
        }
      }
    });

    console.log('Header info:', headerInfo);
    console.log('Header values:', headerValues);

    // Create a header row with the total values from the column names
    const headerRow = {
      stayDuration: 'Total',
      ...headerValues
    };

    console.log('Created header row with values from column names:', headerRow);

    // Process each data row
    const processedRows = rawData.map(row => {
      const processedRow = {
        stayDuration: row.population_label,
      };

      // Initialize all possible combinations with 0
      regions.forEach(region => {
        const regionLower = region.toLowerCase();
        genders.forEach(gender => {
          const genderLower = gender.toLowerCase();
          processedRow[`${genderLower}_${regionLower}`] = 0;
        });
      });

      // Process each region and gender combination using our mapping
      Object.entries(headerInfo).forEach(([key, info]) => {
        // Get the value directly from the row using the column name
        const value = row[info.columnName];

        // If the value exists and is not empty, use it
        if (value !== undefined && value !== '') {
          processedRow[key] = parseFloat(value) || 0;
        }
      });

      return processedRow;
    });

    // Add the header row at the beginning
    return [headerRow, ...processedRows];
  };

  // Handle region change
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  // Handle gender change
  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!stayData || stayData.length === 0) {
      return {
        totalMigrants: 0,
        shortTermMigrants: 0,
        longTermMigrants: 0,
        permanentMigrants: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();
    const key = `${gender}_${region}`;

    console.log(`Looking for data with key: ${key}`);
    console.log('Available keys in first row:', Object.keys(stayData[0]));
    console.log('First row:', stayData[0]);

    // 特别检查 Bangkok 数据
    if (region === 'bangkok') {
      console.log('Bangkok data check:');
      stayData.forEach(item => {
        console.log(`  ${item.stayDuration}: ${item[key]}`);
      });
    }

    // Check if we have a header row with the total value
    // The header row should have stayDuration === 'Total'
    const headerRow = stayData.find(item => item.stayDuration === 'Total');

    let totalMigrants = 0;

    if (headerRow && headerRow[key]) {
      // Use the value from the header row (which comes from the column name)
      totalMigrants = headerRow[key];
      console.log(`Using total from header row: ${totalMigrants}`);
    } else {
      // Calculate total by summing all stay durations
      totalMigrants = stayData.reduce((sum, item) => {
        if (item.stayDuration === 'Total') return sum; // Skip header row in sum
        const value = item[key] || 0;
        console.log(`${item.stayDuration}: ${value}`);
        return sum + value;
      }, 0);
      console.log(`Calculated total by summing: ${totalMigrants}`);
    }

    // Short-term migrants (< 12 months)
    const shortTermMigrants = stayData
      .filter(item =>
        item.stayDuration === '3 - 5.9 months' ||
        item.stayDuration === '6 - 11.9 months'
      )
      .reduce((sum, item) => sum + (item[key] || 0), 0);

    // Long-term migrants (≥ 12 months, excluding permanent)
    const longTermMigrants = stayData
      .filter(item =>
        item.stayDuration === '12 - 23.9 months' ||
        item.stayDuration === '24 months or more'
      )
      .reduce((sum, item) => sum + (item[key] || 0), 0);

    // Permanent migrants
    const permanentMigrants = stayData
      .filter(item => item.stayDuration === 'Plan to stay permanently')
      .reduce((sum, item) => sum + (item[key] || 0), 0);

    // Verify that our totals add up correctly
    const calculatedTotal = shortTermMigrants + longTermMigrants + permanentMigrants;
    console.log(`Calculated total: ${calculatedTotal}, Direct total: ${totalMigrants}`);

    // If there's a significant difference, use the calculated total instead
    // But if we have a header row, prefer that value
    const finalTotal = headerRow ? totalMigrants :
      (Math.abs(calculatedTotal - totalMigrants) < 0.1 ? totalMigrants : calculatedTotal);

    const result = {
      totalMigrants: finalTotal,
      shortTermMigrants,
      longTermMigrants,
      permanentMigrants
    };

    console.log('Summary stats:', result);

    return result;
  };

  // Prepare data for stay duration distribution pie chart
  const prepareStayPieData = () => {
    if (!stayData || stayData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();
    const key = `${gender}_${region}`;

    // Define all stay durations as separate categories
    const stayDurations = [
      '3 - 5.9 months',
      '6 - 11.9 months',
      '12 - 23.9 months',
      '24 months or more',
      'Plan to stay permanently'
    ];

    // Calculate values for each duration
    const durationValues = stayDurations.map(duration => {
      const item = stayData.find(d => d.stayDuration === duration && d.stayDuration !== 'Total');
      const value = item ? (item[key] || 0) : 0;
      console.log(`Pie chart - ${duration}: ${value}`);

      return {
        label: duration,
        value
      };
    });

    console.log('Pie chart duration values:', durationValues);

    // Prepare chart data
    return {
      labels: durationValues.map(item => item.label),
      datasets: [
        {
          data: durationValues.map(item => item.value),
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare data for gender comparison by stay duration
  const prepareGenderComparisonData = () => {
    if (!stayData || stayData.length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Define stay durations to include in the chart
    const stayDurations = [
      '3 - 5.9 months',
      '6 - 11.9 months',
      '12 - 23.9 months',
      '24 months or more',
      'Plan to stay permanently'
    ];

    // Get male and female data for each duration
    const maleData = stayDurations.map(duration => {
      // Skip the header row (Total)
      const item = stayData.find(d => d.stayDuration === duration && d.stayDuration !== 'Total');
      const value = item ? (item[`male_${region}`] || 0) : 0;
      console.log(`Gender comparison - Male, ${duration}: ${value}`);
      return value;
    });

    const femaleData = stayDurations.map(duration => {
      // Skip the header row (Total)
      const item = stayData.find(d => d.stayDuration === duration && d.stayDuration !== 'Total');
      const value = item ? (item[`female_${region}`] || 0) : 0;
      console.log(`Gender comparison - Female, ${duration}: ${value}`);
      return value;
    });

    console.log('Gender comparison data:', { maleData, femaleData });

    // Prepare chart data
    return {
      labels: stayDurations,
      datasets: [
        {
          label: 'Male',
          data: maleData,
          backgroundColor: 'rgba(54, 162, 235, 0.5)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: 'Female',
          data: femaleData,
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };
  };

  // Prepare data for detailed regional breakdown
  const prepareRegionalBreakdownData = () => {
    if (!stayData || stayData.length === 0) return null;

    // Convert gender to lowercase for data access
    const gender = selectedGender.toLowerCase();

    // Define stay durations to include in the chart
    const stayDurations = [
      '3 - 5.9 months',
      '6 - 11.9 months',
      '12 - 23.9 months',
      '24 months or more',
      'Plan to stay permanently'
    ];

    // Define regions to include (excluding Nationwide)
    const regionsToInclude = regions.filter(r => r !== 'Nationwide');

    // Colors for each region
    const colors = [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)',
      'rgba(255, 206, 86, 1)',
      'rgba(75, 192, 192, 1)',
      'rgba(153, 102, 255, 1)'
    ];

    // Prepare datasets for each region
    const datasets = regionsToInclude.map((region, index) => {
      const regionLower = region.toLowerCase();

      // Get data for this region
      const regionData = stayDurations.map(duration => {
        // Skip the header row (Total)
        const item = stayData.find(d => d.stayDuration === duration && d.stayDuration !== 'Total');
        const value = item ? (item[`${gender}_${regionLower}`] || 0) : 0;
        console.log(`Regional breakdown - ${region}, ${duration}: ${value}`);
        return value;
      });

      return {
        label: region,
        data: regionData,
        backgroundColor: colors[index % colors.length].replace('1)', '0.5)'),
        borderColor: colors[index % colors.length],
        borderWidth: 1
      };
    });

    console.log('Regional breakdown datasets:', datasets);

    // Prepare chart data
    return {
      labels: stayDurations,
      datasets: datasets
    };
  };

  // Prepare data for time-based line chart
  const prepareTimeSeriesData = () => {
    if (!stayData || stayData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();
    const key = `${gender}_${region}`;

    // Define stay durations in chronological order
    const stayDurations = [
      '3 - 5.9 months',
      '6 - 11.9 months',
      '12 - 23.9 months',
      '24 months or more',
      'Plan to stay permanently'
    ];

    // Get data for each duration
    const timeSeriesData = stayDurations.map(duration => {
      // Skip the header row (Total)
      const item = stayData.find(d => d.stayDuration === duration && d.stayDuration !== 'Total');
      const value = item ? (item[key] || 0) : 0;
      console.log(`Time series - ${duration}: ${value}`);
      return value;
    });

    console.log('Time series data:', timeSeriesData);

    // Prepare chart data
    return {
      labels: stayDurations,
      datasets: [
        {
          label: defaultT.expectedStayAnalysis,
          data: timeSeriesData,
          fill: false,
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
          tension: 0.4,
          pointBackgroundColor: 'rgba(75, 192, 192, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(75, 192, 192, 1)'
        }
      ]
    };
  };

  // Chart options
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          boxWidth: 15,
          padding: 15,
          font: {
            size: 11 // 减小字体大小以适应更长的标签
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      }
    }
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    plugins: {
      legend: {
        display: false
      }
    }
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) {
    return <div className="loading-container">{defaultT.loading}</div>;
  }

  if (error) {
    return <div className="error-message">{error}</div>;
  }

  return (
    <div className="expected-stay-container">
      <h2>{defaultT.expectedStayAnalysis}</h2>
      <p className="analysis-description">{defaultT.expectedStayDesc}</p>

      <div className="stay-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#e74c3c' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-migrants">
              <div className="stat-content">
                <h3>{defaultT.totalMigrants}</h3>
                <div className="stat-value">{summaryStats.totalMigrants.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile short-term">
              <div className="stat-content">
                <h3>{defaultT.shortTermMigrants}</h3>
                <div className="stat-value">{summaryStats.shortTermMigrants.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.shortTermMigrants / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile long-term">
              <div className="stat-content">
                <h3>{defaultT.longTermMigrants}</h3>
                <div className="stat-value">{summaryStats.longTermMigrants.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.longTermMigrants / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile permanent">
              <div className="stat-content">
                <h3>{defaultT.permanentMigrants}</h3>
                <div className="stat-value">{summaryStats.permanentMigrants.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.permanentMigrants / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.stayDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareStayPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.stayComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.detailedBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalBreakdownData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.expectedStayAnalysis}</h3>
                <div className="chart-container">
                  <Line
                    data={prepareTimeSeriesData()}
                    options={lineOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-clock"></i>
                  <h4>{defaultT.stayPatterns}</h4>
                </div>
                <p>Expected stay duration analysis helps predict infrastructure needs and service planning for migrant populations.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{defaultT.genderComparison}</h4>
                </div>
                <p>Gender differences in expected stay duration can reveal social and economic factors influencing migration patterns.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalDifferences}</h4>
                </div>
                <p>Regional variations in expected stay duration help identify areas with different types of migration flows.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExpectedStayAnalysis;

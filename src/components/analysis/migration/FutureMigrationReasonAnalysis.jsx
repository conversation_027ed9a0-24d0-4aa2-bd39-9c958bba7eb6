import React, { useState, useEffect } from 'react';
import '../../../styles/FutureMigrationReasonAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const FutureMigrationReasonAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    futureMigrationReasonAnalysis: 'Future Migration Reason Analysis',
    futureMigrationReasonDesc: 'This analysis shows population by expected future migration reason, gender, and region, helping you predict future migration drivers and plan ahead.',
    region: 'Region',
    gender: 'Gender',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalPopulation: 'Total Population',
    lookingForJob: 'Looking for a job',
    wantToChangeJob: 'Want to change job',
    wantToIncreaseIncome: 'Want to increase income',
    jobAssignment: 'Job assignment',
    furtherEducation: 'Further education',
    relocation: 'Relocation',
    returnToHometown: 'Return to hometown',
    followFamilyMember: 'Follow family member',
    others: 'Others',
    dataInsights: 'Data Insights',
    reasonDistribution: 'Reason Distribution',
    genderComparison: 'Gender Comparison',
    topReasons: 'Top Migration Reasons',
    regionalComparison: 'Regional Comparison',
    economicFactors: 'Economic Factors',
    familyFactors: 'Family Factors',
    educationFactors: 'Education Factors',
    ...t
  };

  // State variables
  const [migrationData, setMigrationData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedReason, setSelectedReason] = useState('Return to hometown');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Load migration reason data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t14.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          if (results.data && results.data.length > 0) {
            // Transform the data into a more usable format
            const transformedData = transformMigrationData(results.data);
            setMigrationData(transformedData);
            setLoading(false);
          } else {
            setError('No data found in the CSV file');
            setLoading(false);
          }
        } catch (err) {
          console.error('Error processing migration reason data:', err);
          setError('Failed to process migration reason data');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformMigrationData = (data) => {
    // Filter out empty rows
    const filteredData = data.filter(row => row.population_label && row.population_label.trim() !== '');

    // Map the data to a more structured format
    return filteredData.map(row => {
      const reasonLabel = row.population_label.trim();

      return {
        reason: reasonLabel,
        nationwide: {
          total: parseFloat(row.total_nationwide) || 0,
          male: parseFloat(row.male_nationwide) || 0,
          female: parseFloat(row.female_nationwide) || 0
        },
        bangkok: {
          total: parseFloat(row.total_bangkok) || 0,
          male: parseFloat(row.male_bangkok) || 0,
          female: parseFloat(row.female_bangkok) || 0
        },
        central: {
          total: parseFloat(row.total_central) || 0,
          male: parseFloat(row.male_central) || 0,
          female: parseFloat(row.female_central) || 0
        },
        north: {
          total: parseFloat(row.total_north) || 0,
          male: parseFloat(row.male_north) || 0,
          female: parseFloat(row.female_north) || 0
        },
        northeast: {
          total: parseFloat(row.total_northeast) || 0,
          male: parseFloat(row.male_northeast) || 0,
          female: parseFloat(row.female_northeast) || 0
        },
        south: {
          total: parseFloat(row.total_south) || 0,
          male: parseFloat(row.male_south) || 0,
          female: parseFloat(row.female_south) || 0
        }
      };
    });
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!migrationData || migrationData.length === 0) {
      return {
        totalPopulation: 0,
        topReasons: []
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Calculate total population (sum of all reasons)
    const totalPopulation = migrationData.reduce((sum, item) => sum + item[region][gender], 0);

    // Get all reasons with their values and percentages
    const reasonsWithValues = migrationData.map(item => ({
      reason: item.reason,
      value: item[region][gender],
      percentage: (item[region][gender] / totalPopulation) * 100
    }));

    // Sort by value in descending order
    reasonsWithValues.sort((a, b) => b.value - a.value);

    // Get top 3 reasons
    const topReasons = reasonsWithValues.slice(0, 3);

    return {
      totalPopulation,
      topReasons
    };
  };

  // Prepare data for reason distribution pie chart
  const prepareReasonPieData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Create a structured dataset with all reasons
    const reasonValues = migrationData.map(item => ({
      reason: item.reason,
      value: item[region][gender]
    }));

    // Sort by value in descending order
    reasonValues.sort((a, b) => b.value - a.value);

    // Filter out zero values
    const nonZeroReasons = reasonValues.filter(item => item.value > 0);

    // Map the data to chart format
    const chartData = {
      labels: nonZeroReasons.map(item => {
        // Map reason labels to translations if available
        const key = item.reason.toLowerCase().replace(/\s+/g, '');
        return defaultT[key] || item.reason;
      }),
      datasets: [
        {
          data: nonZeroReasons.map(item => item.value),
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)',
            'rgba(153, 102, 255, 0.7)',
            'rgba(255, 159, 64, 0.7)',
            'rgba(199, 199, 199, 0.7)',
            'rgba(83, 102, 255, 0.7)',
            'rgba(78, 205, 196, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(199, 199, 199, 1)',
            'rgba(83, 102, 255, 1)',
            'rgba(78, 205, 196, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Sort reasons by total value in descending order
    const sortedReasons = [...migrationData].sort((a, b) =>
      (b[region].male + b[region].female) - (a[region].male + a[region].female)
    );

    // Use all reasons for complete visualization
    const allReasons = sortedReasons;

    // Map the data to chart format
    const chartData = {
      labels: allReasons.map(item => {
        // Map reason labels to translations if available
        const key = item.reason.toLowerCase().replace(/\s+/g, '');
        return defaultT[key] || item.reason;
      }),
      datasets: [
        {
          label: defaultT.male || 'Male',
          data: allReasons.map(item => item[region].male),
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.female || 'Female',
          data: allReasons.map(item => item[region].female),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for top reasons bar chart
  const prepareTopReasonsData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Sort reasons by value in descending order
    const sortedReasons = [...migrationData].sort((a, b) =>
      b[region][gender] - a[region][gender]
    );

    // Take all reasons for visualization
    const chartData = {
      labels: sortedReasons.map(item => {
        // Map reason labels to translations if available
        const key = item.reason.toLowerCase().replace(/\s+/g, '');
        return defaultT[key] || item.reason;
      }),
      datasets: [
        {
          label: defaultT[selectedGender.toLowerCase()] || selectedGender,
          data: sortedReasons.map(item => item[region][gender]),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional comparison bar chart
  const prepareRegionalComparisonData = () => {
    if (!migrationData || migrationData.length === 0) return null;

    // Get the gender to display
    const gender = selectedGender.toLowerCase();

    // Find the selected reason data
    const reasonData = migrationData.find(item => item.reason === selectedReason);

    if (!reasonData) return null;

    // Define regions to include in the chart
    const chartRegions = ['nationwide', 'bangkok', 'central', 'north', 'northeast', 'south'];

    // Map the data to chart format
    const chartData = {
      labels: chartRegions.map(region => {
        if (region === 'nationwide') return defaultT.nationwide || 'Nationwide';
        if (region === 'bangkok') return defaultT.bangkok || 'Bangkok';
        if (region === 'central') return defaultT.central || 'Central';
        if (region === 'north') return defaultT.north || 'North';
        if (region === 'northeast') return defaultT.northeast || 'Northeast';
        if (region === 'south') return defaultT.south || 'South';
        return region;
      }),
      datasets: [
        {
          label: defaultT[selectedReason.toLowerCase().replace(/\s+/g, '')] || selectedReason,
          data: chartRegions.map(region => reasonData[region][gender]),
          backgroundColor: 'rgba(255, 159, 64, 0.7)',
          borderColor: 'rgba(255, 159, 64, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  const handleReasonChange = (e) => {
    setSelectedReason(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) return <div className="loading-container">{defaultT.loading}</div>;

  if (error) return <div className="error-message">{defaultT.loadError}: {error}</div>;

  return (
    <div className="future-migration-reason-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-globe" style={{ marginRight: '10px', color: '#9b59b6' }}></i>
          {defaultT.futureMigrationReasonAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.futureMigrationReasonDesc}
        </span>
      </p>

      <div className="migration-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>


        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-population">
              <div className="stat-content">
                <h3>{defaultT.totalPopulation}</h3>
                <div className="stat-value">{summaryStats.totalPopulation.toLocaleString()}</div>
                <div className="stat-note">({defaultT.futureMigrants || 'Future Migrants'})</div>
              </div>
            </div>

            {summaryStats.topReasons.map((reason, index) => (
              <div key={reason.reason} className={`stat-tile ${index === 0 ? 'job-search' : index === 1 ? 'job-change' : 'return-hometown'}`}>
                <div className="stat-content">
                  <h3>{defaultT[reason.reason.toLowerCase().replace(/\s+/g, '')] || reason.reason}</h3>
                  <div className="stat-value">{reason.value.toLocaleString()}</div>
                  <div className="stat-percentage">
                    {reason.percentage.toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.reasonDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareReasonPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.topReasons}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareTopReasonsData()}
                    options={{
                      ...barOptions,
                      indexAxis: 'y',
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.genderComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins
                      }
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <h3 className="chart-heading" style={{ margin: 0 }}>{defaultT.regionalComparison}</h3>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <label style={{ marginRight: '10px', fontSize: '14px' }}>
                      <i className="fas fa-exchange-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
                      {defaultT.reason || 'Reason'}:
                    </label>
                    <select
                      value={selectedReason}
                      onChange={handleReasonChange}
                      style={{ padding: '5px', borderRadius: '4px', border: '1px solid #ddd' }}
                    >
                      {migrationData.map(item => (
                        <option key={item.reason} value={item.reason}>
                          {defaultT[item.reason.toLowerCase().replace(/\s+/g, '')] || item.reason}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalComparisonData()}
                    options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        legend: {
                          display: false
                        }
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-briefcase"></i>
                  <h4>{defaultT.economicFactors}</h4>
                </div>
                <p>Economic factors like job opportunities and income improvement are major drivers for future migration, helping predict labor market needs.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-home"></i>
                  <h4>{defaultT.familyFactors}</h4>
                </div>
                <p>Family-related reasons such as returning to hometown and following family members significantly influence migration decisions.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-graduation-cap"></i>
                  <h4>{defaultT.educationFactors}</h4>
                </div>
                <p>Education-driven migration helps identify future skilled workforce distribution and educational infrastructure needs.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FutureMigrationReasonAnalysis;

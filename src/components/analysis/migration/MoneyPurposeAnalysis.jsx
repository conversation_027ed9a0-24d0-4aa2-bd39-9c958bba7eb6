import React, { useState, useEffect } from 'react';
import '../../../styles/MoneyPurposeAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MoneyPurposeAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    moneyPurposeAnalysis: 'Money Usage Purpose Analysis',
    moneyPurposeDesc: 'This analysis shows how migrants use received money by purpose, gender, and region, helping understand financial patterns.',
    region: 'Region',
    gender: 'Gender',
    purpose: 'Purpose',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMoney: 'Total Money Received',
    dailyLiving: 'Daily Living Expenses',
    education: 'Educational Expenses',
    investment: 'Investment in Business',
    others: 'Others',
    all: 'All',
    male: 'Male',
    female: 'Female',
    total: 'Total',
    nationwide: 'Nationwide',
    bangkok: 'Bangkok',
    central: 'Central',
    north: 'North',
    northeast: 'Northeast',
    south: 'South',
    dataInsights: 'Data Insights',
    moneyUsagePatterns: 'Money Usage Patterns',
    genderComparison: 'Gender Comparison',
    regionalDistribution: 'Regional Distribution',
    purposeDistribution: 'Purpose Distribution',
    purposeComparison: 'Purpose Comparison by Region',

    // Chinese translations
    zh: {
      moneyPurposeAnalysis: '汇款用途分析',
      moneyPurposeDesc: '此分析显示移民如何按用途、性别和地区使用收到的资金，帮助理解财务模式。',
      region: '地区',
      gender: '性别',
      purpose: '用途',
      filters: '筛选器',
      loading: '加载中...',
      loadError: '加载数据错误',
      keySummary: '关键摘要',
      totalMoney: '收到的总资金',
      dailyLiving: '日常生活开支',
      education: '教育开支',
      investment: '商业投资',
      others: '其他',
      all: '全部',
      male: '男性',
      female: '女性',
      total: '总计',
      nationwide: '全国',
      bangkok: '曼谷',
      central: '中部',
      north: '北部',
      northeast: '东北部',
      south: '南部',
      dataInsights: '数据洞察',
      moneyUsagePatterns: '资金使用模式',
      genderComparison: '性别比较',
      regionalDistribution: '地区分布',
      purposeDistribution: '用途分布',
      purposeComparison: '各地区用途比较'
    },

    ...t
  };

  // Check if language is Chinese and use Chinese translations
  const isChineseLanguage = language === 'zh';

  // Get the appropriate translation
  const getTranslation = (key) => {
    if (isChineseLanguage && defaultT.zh && defaultT.zh[key]) {
      return defaultT.zh[key];
    }
    return defaultT[key];
  };

  const [moneyData, setMoneyData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');
  const [selectedPurpose, setSelectedPurpose] = useState('All');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Available purposes for filtering
  const purposes = ['All', 'Investment in Business', 'Daily Living Expenses', 'Educational Expenses', 'Others'];

  // Load money purpose data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t17.csv', {
      download: true,
      header: true,
      complete: (results) => {
        if (results.data && results.data.length > 0) {
          // Transform the data into a more usable format
          const transformedData = transformMoneyData(results.data);
          setMoneyData(transformedData);
          setLoading(false);
        } else {
          setError('No data found in the CSV file');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Transform the CSV data into a more usable format
  const transformMoneyData = (data) => {
    // Filter out empty rows
    const filteredData = data.filter(row => row.population_label && row.population_label.trim() !== '');

    // Initialize result array
    const result = [];

    // Process each row
    filteredData.forEach(row => {
      const label = row.population_label.trim();

      // Skip empty rows
      if (!label) return;

      // Create a data object for this purpose
      result.push({
        purpose: label,
        nationwide: {
          total: parseFloat(row.total_nationwide) || 0,
          male: parseFloat(row.male_nationwide) || 0,
          female: parseFloat(row.female_nationwide) || 0
        },
        bangkok: {
          total: parseFloat(row.total_bangkok) || 0,
          male: parseFloat(row.male_bangkok) || 0,
          female: parseFloat(row.female_bangkok) || 0
        },
        central: {
          total: parseFloat(row.total_central) || 0,
          male: parseFloat(row.male_central) || 0,
          female: parseFloat(row.female_central) || 0
        },
        north: {
          total: parseFloat(row.total_north) || 0,
          male: parseFloat(row.male_north) || 0,
          female: parseFloat(row.female_north) || 0
        },
        northeast: {
          total: parseFloat(row.total_northeast) || 0,
          male: parseFloat(row.male_northeast) || 0,
          female: parseFloat(row.female_northeast) || 0
        },
        south: {
          total: parseFloat(row.total_south) || 0,
          male: parseFloat(row.male_south) || 0,
          female: parseFloat(row.female_south) || 0
        }
      });
    });

    return result;
  };

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!moneyData || moneyData.length === 0) {
      return {
        totalMoney: 0,
        dailyLiving: 0,
        education: 0,
        investment: 0,
        others: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the total money row (Grand Total)
    const totalRow = moneyData.find(item => item.purpose === 'Grand Total');

    // Find specific purpose rows
    const dailyLivingRow = moneyData.find(item => item.purpose === 'Daily Living Expenses');
    const educationRow = moneyData.find(item => item.purpose === 'Educational Expenses');
    const investmentRow = moneyData.find(item => item.purpose === 'Investment in Business');
    const othersRow = moneyData.find(item => item.purpose === 'Others');

    // Calculate values based on filters
    const totalMoney = totalRow ? totalRow[region][gender] : 0;
    const dailyLiving = dailyLivingRow ? dailyLivingRow[region][gender] : 0;
    const education = educationRow ? educationRow[region][gender] : 0;
    const investment = investmentRow ? investmentRow[region][gender] : 0;
    const others = othersRow ? othersRow[region][gender] : 0;

    return {
      totalMoney,
      dailyLiving,
      education,
      investment,
      others
    };
  };

  // Prepare data for purpose distribution pie chart
  const preparePurposePieData = () => {
    if (!moneyData || moneyData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define purposes to include in the chart (exclude Grand Total)
    const purposeLabels = [
      'Daily Living Expenses',
      'Educational Expenses',
      'Investment in Business',
      'Others'
    ];

    // If a specific purpose is selected (not 'All'), only show that purpose
    if (selectedPurpose !== 'All') {
      const filteredPurposeLabels = purposeLabels.filter(purpose => purpose === selectedPurpose);

      if (filteredPurposeLabels.length === 0) {
        // If no matching purpose found, return empty chart
        return {
          labels: [],
          datasets: [{
            data: [],
            backgroundColor: [],
            borderColor: [],
            borderWidth: 1
          }]
        };
      }

      // Create a structured dataset with the selected purpose
      const purposeValues = {};

      // Initialize with zeros
      filteredPurposeLabels.forEach(purpose => {
        purposeValues[purpose] = 0;
      });

      // Fill in actual values from the data
      moneyData.forEach(item => {
        if (filteredPurposeLabels.includes(item.purpose)) {
          purposeValues[item.purpose] = item[region][gender];
        }
      });

      // Filter out zero values
      const nonZeroPurposes = filteredPurposeLabels.filter(purpose => purposeValues[purpose] > 0);

      // Map the data to chart format
      const chartData = {
        labels: nonZeroPurposes.map(purpose => {
          // Map purpose to translation or use the original
          switch(purpose) {
            case 'Daily Living Expenses': return getTranslation('dailyLiving');
            case 'Educational Expenses': return getTranslation('education');
            case 'Investment in Business': return getTranslation('investment');
            case 'Others': return getTranslation('others');
            default: return purpose;
          }
        }),
        datasets: [
          {
            data: nonZeroPurposes.map(purpose => purposeValues[purpose]),
            backgroundColor: [
              'rgba(54, 162, 235, 0.7)',
              'rgba(255, 206, 86, 0.7)',
              'rgba(75, 192, 192, 0.7)',
              'rgba(153, 102, 255, 0.7)'
            ].slice(0, nonZeroPurposes.length),
            borderColor: [
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)'
            ].slice(0, nonZeroPurposes.length),
            borderWidth: 1
          }
        ]
      };

      return chartData;
    } else {
      // Create a structured dataset with all purposes
      const purposeValues = {};

      // Initialize with zeros
      purposeLabels.forEach(purpose => {
        purposeValues[purpose] = 0;
      });

      // Fill in actual values from the data
      moneyData.forEach(item => {
        if (purposeLabels.includes(item.purpose)) {
          purposeValues[item.purpose] = item[region][gender];
        }
      });

      // Filter out zero values
      const nonZeroPurposes = purposeLabels.filter(purpose => purposeValues[purpose] > 0);

      // Map the data to chart format
      const chartData = {
        labels: nonZeroPurposes.map(purpose => {
          // Map purpose to translation or use the original
          switch(purpose) {
            case 'Daily Living Expenses': return getTranslation('dailyLiving');
            case 'Educational Expenses': return getTranslation('education');
            case 'Investment in Business': return getTranslation('investment');
            case 'Others': return getTranslation('others');
            default: return purpose;
          }
        }),
        datasets: [
          {
            data: nonZeroPurposes.map(purpose => purposeValues[purpose]),
            backgroundColor: [
              'rgba(54, 162, 235, 0.7)',
              'rgba(255, 206, 86, 0.7)',
              'rgba(75, 192, 192, 0.7)',
              'rgba(153, 102, 255, 0.7)'
            ].slice(0, nonZeroPurposes.length),
            borderColor: [
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)',
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)'
            ].slice(0, nonZeroPurposes.length),
            borderWidth: 1
          }
        ]
      };

      return chartData;
    }
  };

  // Prepare data for regional comparison bar chart
  const prepareRegionalComparisonData = () => {
    if (!moneyData || moneyData.length === 0) return null;

    // Get gender for data access
    const gender = selectedGender.toLowerCase();

    // Define regions to include in the chart
    const regionLabels = ['nationwide', 'bangkok', 'central', 'north', 'northeast', 'south'];

    // Get the purpose to display
    let purposeToShow = selectedPurpose;
    if (purposeToShow === 'All') {
      purposeToShow = 'Grand Total';
    }

    // Find the data for the selected purpose
    const purposeData = moneyData.find(item =>
      item.purpose === purposeToShow
    );

    if (!purposeData) return null;

    // Map the data to chart format
    const chartData = {
      labels: regionLabels.map(region => {
        // Map region to translation or capitalize first letter
        switch(region) {
          case 'nationwide': return getTranslation('nationwide');
          case 'bangkok': return getTranslation('bangkok');
          case 'central': return getTranslation('central');
          case 'north': return getTranslation('north');
          case 'northeast': return getTranslation('northeast');
          case 'south': return getTranslation('south');
          default: return region.charAt(0).toUpperCase() + region.slice(1);
        }
      }),
      datasets: [
        {
          label: selectedPurpose === 'All' ?
                 (gender === 'total' ? getTranslation('totalMoney') :
                  gender === 'male' ? getTranslation('male') :
                  getTranslation('female')) :
                 (() => {
                   // Use the selected purpose as the label
                   switch(selectedPurpose) {
                     case 'Daily Living Expenses': return getTranslation('dailyLiving');
                     case 'Educational Expenses': return getTranslation('education');
                     case 'Investment in Business': return getTranslation('investment');
                     case 'Others': return getTranslation('others');
                     default: return selectedPurpose;
                   }
                 })(),
          data: regionLabels.map(region => purposeData[region][gender]),
          backgroundColor: 'rgba(75, 192, 192, 0.7)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for purpose comparison by region
  const preparePurposeComparisonData = () => {
    if (!moneyData || moneyData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define purposes to include in the chart (exclude Grand Total)
    let purposeLabels = [
      'Daily Living Expenses',
      'Educational Expenses',
      'Investment in Business',
      'Others'
    ];

    // If a specific purpose is selected (not 'All'), only show that purpose
    if (selectedPurpose !== 'All') {
      purposeLabels = purposeLabels.filter(purpose => purpose === selectedPurpose);
    }

    // Filter out purposes with zero values for the selected region and gender
    const filteredPurposes = purposeLabels.filter(purpose => {
      const purposeData = moneyData.find(item => item.purpose === purpose);
      return purposeData && purposeData[region][gender] > 0;
    });

    // If no purposes to display, return empty chart
    if (filteredPurposes.length === 0) {
      return {
        labels: [],
        datasets: [{
          label: defaultT[selectedRegion.toLowerCase()] || selectedRegion,
          data: [],
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }]
      };
    }

    // Map the data to chart format
    const chartData = {
      labels: filteredPurposes.map(purpose => {
        // Map purpose to translation or use the original
        switch(purpose) {
          case 'Daily Living Expenses': return getTranslation('dailyLiving');
          case 'Educational Expenses': return getTranslation('education');
          case 'Investment in Business': return getTranslation('investment');
          case 'Others': return getTranslation('others');
          default: return purpose;
        }
      }),
      datasets: [
        {
          label: getTranslation(selectedRegion.toLowerCase()),
          data: filteredPurposes.map(purpose => {
            const purposeData = moneyData.find(item => item.purpose === purpose);
            return purposeData ? purposeData[region][gender] : 0;
          }),
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            return `${context.dataset.label}: ${value.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    }
  };

  // Chart options for pie charts
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        },
        // Only show one legend item per label
        filter: (legendItem, data) => {
          const index = data.labels.indexOf(legendItem.text);
          return index === data.labels.lastIndexOf(legendItem.text);
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Handle filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  const handlePurposeChange = (e) => {
    setSelectedPurpose(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  if (loading) return <div className="loading-container">{getTranslation('loading')}</div>;

  if (error) return <div className="error-message">{getTranslation('loadError')}: {error}</div>;

  return (
    <div className="money-purpose-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-money-bill-wave" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {getTranslation('moneyPurposeAnalysis')}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {getTranslation('moneyPurposeDesc')}
        </span>
      </p>

      <div className="money-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {getTranslation('region')}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {getTranslation(region.toLowerCase())}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {getTranslation('gender')}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {getTranslation(gender.toLowerCase())}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-tags" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {getTranslation('purpose')}:
            </label>
            <select
              value={selectedPurpose}
              onChange={handlePurposeChange}
            >
              {purposes.map(purpose => (
                <option key={purpose} value={purpose}>
                  {purpose === 'All' ? getTranslation('all') :
                   purpose === 'Daily Living Expenses' ? getTranslation('dailyLiving') :
                   purpose === 'Educational Expenses' ? getTranslation('education') :
                   purpose === 'Investment in Business' ? getTranslation('investment') :
                   purpose === 'Others' ? getTranslation('others') : purpose}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-money">
              <div className="stat-content">
                <h3>{getTranslation('totalMoney')}</h3>
                <div className="stat-value">{summaryStats.totalMoney.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile daily-living">
              <div className="stat-content">
                <h3>{getTranslation('dailyLiving')}</h3>
                <div className="stat-value">{summaryStats.dailyLiving.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMoney > 0 ? ((summaryStats.dailyLiving / summaryStats.totalMoney) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>

            <div className="stat-tile education">
              <div className="stat-content">
                <h3>{getTranslation('education')}</h3>
                <div className="stat-value">{summaryStats.education.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMoney > 0 ? ((summaryStats.education / summaryStats.totalMoney) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>

            <div className="stat-tile investment">
              <div className="stat-content">
                <h3>{getTranslation('investment')}</h3>
                <div className="stat-value">{summaryStats.investment.toLocaleString()}</div>
                <div className="stat-percentage">
                  {summaryStats.totalMoney > 0 ? ((summaryStats.investment / summaryStats.totalMoney) * 100).toFixed(1) : 0}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{getTranslation('purposeDistribution')}</h3>
                <div className="chart-container">
                  <Pie
                    data={preparePurposePieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{getTranslation('purposeComparison')}</h3>
                <div className="chart-container">
                  <Bar
                    data={preparePurposeComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{getTranslation('regionalDistribution')}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {getTranslation('dataInsights')}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-money-bill-wave"></i>
                  <h4>{getTranslation('moneyUsagePatterns')}</h4>
                </div>
                <p>{isChineseLanguage ?
                   "资金使用模式揭示了移民如何优先考虑他们的财务资源并支持家庭。" :
                   "Money usage patterns reveal how migrants prioritize their financial resources and support their families."}</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{getTranslation('genderComparison')}</h4>
                </div>
                <p>{isChineseLanguage ?
                   "资金使用的性别差异突显了男性和女性移民之间不同的优先事项和责任。" :
                   "Gender differences in money usage highlight varying priorities and responsibilities between male and female migrants."}</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-chart-pie"></i>
                  <h4>{getTranslation('regionalDistribution')}</h4>
                </div>
                <p>{isChineseLanguage ?
                   "资金使用目的的地区差异可以为有针对性的金融服务和政策制定提供信息。" :
                   "Regional variations in money usage purposes can inform targeted financial services and policy development."}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoneyPurposeAnalysis;

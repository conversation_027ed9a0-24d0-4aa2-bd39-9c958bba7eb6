import React, { useState, useEffect } from 'react';
import '../../../styles/MoneyGoodsSendingAnalysis.css';
import { Bar, Pie } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import <PERSON> from 'papaparse';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const MoneyGoodsSendingAnalysis = ({ parkName, language, t = {} }) => {
  // Ensure t has default values to prevent undefined errors
  const defaultT = {
    moneyGoodsSendingAnalysis: 'Money & Goods Sending Analysis',
    moneyGoodsSendingDesc: 'This analysis shows migration numbers by sending money and goods, gender, and region, helping analyze reverse fund flow and support trade planning.',
    region: 'Region',
    gender: 'Gender',
    filters: 'Filters',
    loading: 'Loading...',
    loadError: 'Error loading data',
    keySummary: 'Key Summary',
    totalMigrants: 'Total Migrants',
    onlyMoney: 'Only Money',
    onlyGoods: 'Only Goods',
    bothMoneyAndGoods: 'Both Money and Goods',
    didNotSend: 'Did Not Send',
    dataInsights: 'Data Insights',
    financialImpact: 'Financial Impact',
    remittancePatterns: 'Remittance Patterns',
    genderDifferences: 'Gender Differences',
    regionalVariations: 'Regional Variations',
    sendingDistribution: 'Sending Distribution',
    genderComparison: 'Gender Comparison',
    regionalBreakdown: 'Regional Breakdown',
    fundFlowAnalysis: 'Fund Flow Analysis',
    ...t
  };

  // State variables
  const [sendingData, setSendingData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('Nationwide');
  const [selectedGender, setSelectedGender] = useState('Total');

  // Available regions for filtering
  const regions = ['Nationwide', 'Bangkok', 'Central', 'North', 'Northeast', 'South'];

  // Available genders for filtering
  const genders = ['Total', 'Male', 'Female'];

  // Function to transform CSV data into a more usable format
  const transformSendingData = (data) => {
    // Create a structured object to store the transformed data
    const transformedData = [];

    // Process each row in the CSV
    data.forEach(row => {
      if (!row.population_label) return; // Skip rows without a label

      // Extract the label (sending type)
      const label = row.population_label;

      // Create an entry for this sending type
      const entry = {
        sendingType: label,
        nationwide: {
          total: parseFloat(row.total_nationwide) || 0,
          male: parseFloat(row.male_nationwide) || 0,
          female: parseFloat(row.female_nationwide) || 0
        },
        bangkok: {
          total: parseFloat(row.total_bangkok) || 0,
          male: parseFloat(row.male_bangkok) || 0,
          female: parseFloat(row.female_bangkok) || 0
        },
        central: {
          total: parseFloat(row.total_central) || 0,
          male: parseFloat(row.male_central) || 0,
          female: parseFloat(row.female_central) || 0
        },
        north: {
          total: parseFloat(row.total_north) || 0,
          male: parseFloat(row.male_north) || 0,
          female: parseFloat(row.female_north) || 0
        },
        northeast: {
          total: parseFloat(row.total_northeast) || 0,
          male: parseFloat(row.male_northeast) || 0,
          female: parseFloat(row.female_northeast) || 0
        },
        south: {
          total: parseFloat(row.total_south) || 0,
          male: parseFloat(row.male_south) || 0,
          female: parseFloat(row.female_south) || 0
        }
      };

      transformedData.push(entry);
    });

    return transformedData;
  };

  // Load data from CSV
  useEffect(() => {
    setLoading(true);
    Papa.parse('/data/t18.csv', {
      download: true,
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          if (results.data && results.data.length > 0) {
            // Transform the data into a more usable format
            const transformedData = transformSendingData(results.data);
            setSendingData(transformedData);
            setLoading(false);
          } else {
            setError('No data found in the CSV file');
            setLoading(false);
          }
        } catch (err) {
          console.error('Error processing money/goods sending data:', err);
          setError('Failed to process money/goods sending data');
          setLoading(false);
        }
      },
      error: (error) => {
        setError(`Error parsing CSV: ${error.message}`);
        setLoading(false);
      }
    });
  }, []);

  // Calculate summary statistics based on filters
  const calculateSummaryStats = () => {
    if (!sendingData || sendingData.length === 0) {
      return {
        totalMigrants: 0,
        onlyMoney: 0,
        onlyGoods: 0,
        bothMoneyAndGoods: 0,
        didNotSend: 0
      };
    }

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Find the total migrants row
    const totalRow = sendingData.find(item => item.sendingType === 'Grand Total');

    // Find the only money row
    const onlyMoneyRow = sendingData.find(item => item.sendingType === 'Only Money');

    // Find the only goods row
    const onlyGoodsRow = sendingData.find(item => item.sendingType === 'Only Goods');

    // Find the both money and goods row
    const bothRow = sendingData.find(item => item.sendingType === 'Both Money and Goods');

    // Find the did not send row
    const didNotSendRow = sendingData.find(item => item.sendingType === 'Did Not Send');

    // Calculate total migrants based on filters
    const totalMigrants = totalRow ? totalRow[region][gender] : 0;

    // Calculate only money migrants based on filters
    const onlyMoney = onlyMoneyRow ? onlyMoneyRow[region][gender] : 0;

    // Calculate only goods migrants based on filters
    const onlyGoods = onlyGoodsRow ? onlyGoodsRow[region][gender] : 0;

    // Calculate both money and goods migrants based on filters
    const bothMoneyAndGoods = bothRow ? bothRow[region][gender] : 0;

    // Calculate did not send migrants based on filters
    const didNotSend = didNotSendRow ? didNotSendRow[region][gender] : 0;

    return {
      totalMigrants,
      onlyMoney,
      onlyGoods,
      bothMoneyAndGoods,
      didNotSend
    };
  };

  // Prepare data for sending distribution pie chart
  const prepareSendingPieData = () => {
    if (!sendingData || sendingData.length === 0) return null;

    // Convert region and gender to lowercase for data access
    const region = selectedRegion.toLowerCase();
    const gender = selectedGender.toLowerCase();

    // Define sending types to include in the chart
    const sendingTypes = [
      'Only Money',
      'Only Goods',
      'Both Money and Goods',
      'Did Not Send'
    ];

    // Create a structured dataset with all sending types
    const sendingValues = {};

    // Initialize with zeros
    sendingTypes.forEach(type => {
      sendingValues[type] = 0;
    });

    // Find the corresponding rows and update values
    sendingData.forEach(item => {
      if (sendingTypes.includes(item.sendingType)) {
        sendingValues[item.sendingType] = item[region][gender];
      }
    });

    // Prepare the chart data
    const chartData = {
      labels: sendingTypes.map(type => {
        switch (type) {
          case 'Only Money': return defaultT.onlyMoney;
          case 'Only Goods': return defaultT.onlyGoods;
          case 'Both Money and Goods': return defaultT.bothMoneyAndGoods;
          case 'Did Not Send': return defaultT.didNotSend;
          default: return type;
        }
      }),
      datasets: [
        {
          data: Object.values(sendingValues),
          backgroundColor: [
            'rgba(255, 99, 132, 0.7)',
            'rgba(54, 162, 235, 0.7)',
            'rgba(255, 206, 86, 0.7)',
            'rgba(75, 192, 192, 0.7)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for gender comparison bar chart
  const prepareGenderComparisonData = () => {
    if (!sendingData || sendingData.length === 0) return null;

    // Convert region to lowercase for data access
    const region = selectedRegion.toLowerCase();

    // Define sending types to include in the chart
    const sendingTypes = [
      'Only Money',
      'Only Goods',
      'Both Money and Goods',
      'Did Not Send'
    ];

    // Create arrays to store male and female values
    const maleValues = [];
    const femaleValues = [];
    const labels = [];

    // Find the corresponding rows and update values
    sendingTypes.forEach(type => {
      const row = sendingData.find(item => item.sendingType === type);
      if (row) {
        maleValues.push(row[region].male);
        femaleValues.push(row[region].female);
        
        // Translate the label
        let translatedLabel;
        switch (type) {
          case 'Only Money': translatedLabel = defaultT.onlyMoney; break;
          case 'Only Goods': translatedLabel = defaultT.onlyGoods; break;
          case 'Both Money and Goods': translatedLabel = defaultT.bothMoneyAndGoods; break;
          case 'Did Not Send': translatedLabel = defaultT.didNotSend; break;
          default: translatedLabel = type;
        }
        labels.push(translatedLabel);
      }
    });

    // Prepare the chart data
    const chartData = {
      labels: labels,
      datasets: [
        {
          label: defaultT.male,
          data: maleValues,
          backgroundColor: 'rgba(54, 162, 235, 0.7)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        },
        {
          label: defaultT.female,
          data: femaleValues,
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderColor: 'rgba(255, 99, 132, 1)',
          borderWidth: 1
        }
      ]
    };

    return chartData;
  };

  // Prepare data for regional breakdown stacked bar chart
  const prepareRegionalBreakdownData = () => {
    if (!sendingData || sendingData.length === 0) return null;

    // Define regions to include in the chart
    const chartRegions = ['nationwide', 'bangkok', 'central', 'north', 'northeast', 'south'];

    // Define sending types to include in the chart
    const sendingTypes = [
      'Only Money',
      'Only Goods',
      'Both Money and Goods',
      'Did Not Send'
    ];

    // Create arrays to store values for each sending type
    const datasets = [];
    const colors = [
      'rgba(255, 99, 132, 0.7)',
      'rgba(54, 162, 235, 0.7)',
      'rgba(255, 206, 86, 0.7)',
      'rgba(75, 192, 192, 0.7)'
    ];
    const borderColors = [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)',
      'rgba(255, 206, 86, 1)',
      'rgba(75, 192, 192, 1)'
    ];

    // Convert gender to lowercase for data access
    const gender = selectedGender.toLowerCase();

    // Create a dataset for each sending type
    sendingTypes.forEach((type, index) => {
      const row = sendingData.find(item => item.sendingType === type);
      if (row) {
        const values = chartRegions.map(region => row[region][gender]);
        
        // Translate the label
        let translatedLabel;
        switch (type) {
          case 'Only Money': translatedLabel = defaultT.onlyMoney; break;
          case 'Only Goods': translatedLabel = defaultT.onlyGoods; break;
          case 'Both Money and Goods': translatedLabel = defaultT.bothMoneyAndGoods; break;
          case 'Did Not Send': translatedLabel = defaultT.didNotSend; break;
          default: translatedLabel = type;
        }
        
        datasets.push({
          label: translatedLabel,
          data: values,
          backgroundColor: colors[index],
          borderColor: borderColors[index],
          borderWidth: 1
        });
      }
    });

    // Prepare the chart data
    const chartData = {
      labels: chartRegions.map(region => {
        switch (region) {
          case 'nationwide': return defaultT.nationwide || 'Nationwide';
          case 'bangkok': return defaultT.bangkok || 'Bangkok';
          case 'central': return defaultT.central || 'Central';
          case 'north': return defaultT.north || 'North';
          case 'northeast': return defaultT.northeast || 'Northeast';
          case 'south': return defaultT.south || 'South';
          default: return region;
        }
      }),
      datasets: datasets
    };

    return chartData;
  };

  // Chart options for pie chart
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
            return `${label}: ${value.toLocaleString()} (${percentage}%)`;
          }
        }
      }
    }
  };

  // Chart options for bar charts
  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: false,
        grid: {
          display: false
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y || 0;
            return `${label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Chart options for stacked bar chart
  const stackedBarOptions = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        stacked: true,
        grid: {
          display: false
        }
      },
      y: {
        stacked: true,
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          boxWidth: 15,
          padding: 15
        }
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y || 0;
            return `${label}: ${value.toLocaleString()}`;
          }
        }
      }
    }
  };

  // Event handlers for filter changes
  const handleRegionChange = (e) => {
    setSelectedRegion(e.target.value);
  };

  const handleGenderChange = (e) => {
    setSelectedGender(e.target.value);
  };

  // Calculate summary statistics
  const summaryStats = calculateSummaryStats();

  // If loading, show loading message
  if (loading) {
    return (
      <div className="money-goods-sending-container">
        <div className="loading-container">
          <i className="fas fa-spinner fa-spin" style={{ marginRight: '10px' }}></i>
          {defaultT.loading}
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="money-goods-sending-container">
        <div className="error-message">
          <i className="fas fa-exclamation-triangle" style={{ marginRight: '10px' }}></i>
          {defaultT.loadError}: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="money-goods-sending-container">
      <h2>
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-exchange-alt" style={{ marginRight: '10px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsSendingAnalysis}
        </span>
      </h2>
      <p className="analysis-description">
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <i className="fas fa-info-circle" style={{ marginRight: '8px', color: '#3498db' }}></i>
          {defaultT.moneyGoodsSendingDesc}
        </span>
      </p>

      <div className="money-sending-analysis-wrapper">
        <div className="filters-container">
          <div className="filter-group">
            <label>
              <i className="fas fa-map-marker-alt" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.region}:
            </label>
            <select
              value={selectedRegion}
              onChange={handleRegionChange}
            >
              {regions.map(region => (
                <option key={region} value={region}>
                  {defaultT[region.toLowerCase()] || region}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label>
              <i className="fas fa-venus-mars" style={{ marginRight: '5px', color: '#3498db' }}></i>
              {defaultT.gender}:
            </label>
            <select
              value={selectedGender}
              onChange={handleGenderChange}
            >
              {genders.map(gender => (
                <option key={gender} value={gender}>
                  {defaultT[gender.toLowerCase()] || gender}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="data-dashboard">
          <div className="stat-tiles">
            <div className="stat-tile total-migrants">
              <div className="stat-content">
                <h3>{defaultT.totalMigrants}</h3>
                <div className="stat-value">{summaryStats.totalMigrants.toLocaleString()}</div>
              </div>
            </div>

            <div className="stat-tile only-money">
              <div className="stat-content">
                <h3>{defaultT.onlyMoney}</h3>
                <div className="stat-value">{summaryStats.onlyMoney.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.onlyMoney / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile only-goods">
              <div className="stat-content">
                <h3>{defaultT.onlyGoods}</h3>
                <div className="stat-value">{summaryStats.onlyGoods.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.onlyGoods / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>

            <div className="stat-tile both-money-goods">
              <div className="stat-content">
                <h3>{defaultT.bothMoneyAndGoods}</h3>
                <div className="stat-value">{summaryStats.bothMoneyAndGoods.toLocaleString()}</div>
                <div className="stat-percentage">
                  {((summaryStats.bothMoneyAndGoods / summaryStats.totalMigrants) * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          </div>

          <div className="chart-section">
            <div className="chart-row">
              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.sendingDistribution}</h3>
                <div className="chart-container">
                  <Pie
                    data={prepareSendingPieData()}
                    options={pieOptions}
                  />
                </div>
              </div>

              <div className="chart-box">
                <h3 className="chart-heading">{defaultT.genderComparison}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareGenderComparisonData()}
                    options={barOptions}
                  />
                </div>
              </div>
            </div>

            <div className="chart-row">
              <div className="chart-box full-width">
                <h3 className="chart-heading">{defaultT.regionalBreakdown}</h3>
                <div className="chart-container">
                  <Bar
                    data={prepareRegionalBreakdownData()}
                    options={stackedBarOptions}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="insights-panel">
            <h3 className="insights-heading">
              <i className="fas fa-lightbulb"></i>
              {defaultT.dataInsights}
            </h3>
            <div className="insights-grid">
              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-money-bill-wave"></i>
                  <h4>{defaultT.financialImpact}</h4>
                </div>
                <p>Analysis of money and goods sending patterns helps understand the financial impact of migration on source communities.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-exchange-alt"></i>
                  <h4>{defaultT.remittancePatterns}</h4>
                </div>
                <p>Remittance patterns reveal how migrants maintain connections with their home communities and support economic development.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-venus-mars"></i>
                  <h4>{defaultT.genderDifferences}</h4>
                </div>
                <p>Gender differences in sending patterns highlight varying economic roles and responsibilities among male and female migrants.</p>
              </div>

              <div className="insight-card">
                <div className="insight-header">
                  <i className="fas fa-map-marked-alt"></i>
                  <h4>{defaultT.regionalVariations}</h4>
                </div>
                <p>Regional variations in sending patterns can inform targeted economic development and trade planning policies.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MoneyGoodsSendingAnalysis;

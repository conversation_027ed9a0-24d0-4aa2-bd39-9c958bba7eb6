import React, { useState, useEffect } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import '../../styles/TimeSeriesPanel.css';

// 注册 Chart.js 组件
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend);

// 格式化日期显示，使其更简洁
const formatDate = (dateStr) => {
  // 假设日期格式为 "2023_Q4" 这样的形式
  if (!dateStr) return '';

  // 提取年份和季度
  const parts = dateStr.split('_');
  if (parts.length < 2) return dateStr;

  const year = parts[0];
  const quarter = parts[1];

  // 简化显示，只保留年份的后两位
  const shortYear = year.slice(2);
  return `${shortYear}'${quarter}`;
};

const TimeSeriesPanel = ({ show, onClose, laborTrends = [], selectedIndex = 0, onChangeIndex, t = {} }) => {
  // 图表类型（折线图/柱状图）
  const [chartType, setChartType] = useState('line');
  // 数据类别
  const [selectedCategory, setSelectedCategory] = useState('education');
  // 当前选择的指标
  const [selectedMetrics, setSelectedMetrics] = useState([]);

  // 预定义的各类别下的指标字段
  const categories = {
    education: [
      'No Education', 'Below Primary  Education', 'Primary Education', 'lower Secondary Education',
      'General Education Track ', 'Vocational Track ', 'Education Track (upper secondary)',
      'Academic Track', 'Professional Track', 'Education Track'
    ],
    employment: ['Employed Persons ', 'Unemployed Persons'],
    occupation: [
      'Technicians and Associate Professionals', 'Clerks',
      'Craftsmen and Related Workers', 'Plant and Machine Operators and Assemblers'
    ],
    industry: ['Manufacturing', 'Transportation and Warehousing'],
    workingHours: [
      'Less than 1 Hour', ' 1 - 9', ' 10 - 19', ' 20 - 29',
      ' 30 - 34', ' 35 - 39', ' 40 - 49', '50 hours'
    ]
  };

  // 初始化：默认选中 "education" 类别的前3个指标
  useEffect(() => {
    if (categories[selectedCategory]) {
      setSelectedMetrics(categories[selectedCategory].slice(0, 3));
    }
  }, []);

  // 切换数据类别 => 默认选中该类别的前3个指标
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
    setSelectedMetrics(categories[category].slice(0, 3));
  };

  // 一键全选/全部取消
  const handleSelectAll = () => {
    setSelectedMetrics(categories[selectedCategory]);
  };
  const handleClearAll = () => {
    setSelectedMetrics([]);
  };

  // 工具函数：去除逗号、空格，转成数字
  const parseValue = (val) => {
    if (!val) return 0;
    return parseFloat(val.replace(/,|\s/g, '')) || 0;
  };

  // 处理时间轴变化
  const handleTimeSliderChange = (e) => {
    onChangeIndex(parseInt(e.target.value, 10));
  };

  // 根据当前选中的时间索引获取数据
  const currentRecord = laborTrends[selectedIndex] || {};

  // 准备图表数据 - 基于当前选中的时间点
  const chartData = {
    labels: selectedMetrics.map(metric => t[metric.trim().toLowerCase().replace(/\s+/g, '_')] || metric),
    datasets: [{
      label: currentRecord.Date || t.data || '数据',
      data: selectedMetrics.map(metric => parseValue(currentRecord[metric])),
      backgroundColor: [
        'rgba(75, 192, 192, 0.6)',
        'rgba(255, 99, 132, 0.6)',
        'rgba(54, 162, 235, 0.6)',
        'rgba(255, 206, 86, 0.6)',
        'rgba(153, 102, 255, 0.6)',
      ],
      borderColor: [
        'rgba(75, 192, 192, 1)',
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 206, 86, 1)',
        'rgba(153, 102, 255, 1)',
      ],
      borderWidth: 1,
    }]
  };

  // 图表配置
  const chartOptions = {
    indexAxis: 'y',  // 水平柱状图，更好地利用右侧面板空间
    responsive: true,
    maintainAspectRatio: false,
    // 添加动画配置，确保数据变化时平滑过渡
    animation: {
      duration: 500, // 动画持续时间
      easing: 'easeOutQuad', // 缓动函数
    },
    // 确保图表不会从0开始重绘
    transitions: {
      active: {
        animation: {
          duration: 500
        }
      }
    },
    plugins: {
      legend: { position: 'top' },
      title: {
        display: true,
        text: currentRecord.Date ? `${t.timeSeriesAnalysis || '时间序列分析'} - ${currentRecord.Date}` : (t.timeSeriesAnalysis || '时间序列分析'),
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.x !== null) {
              label += new Intl.NumberFormat('zh-CN').format(context.parsed.x);
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return new Intl.NumberFormat('zh-CN').format(value);
          }
        }
      }
    }
  };

  return (
    <div className={`time-series-panel ${show ? 'show' : ''}`}>
      <div className="panel-header">
        <h2>{t.timeSeriesAnalysis || '时间序列分析'}</h2>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="panel-content">
        {/* 图表类型 + 数据类别选择 - 改为统一风格 */}
        <div className="unified-control-section">
          <div className="unified-control-row">
            <div className="unified-section-title">{t.chartType || '图表类型'}</div>
            <div className="chart-type-container">
              <button
                onClick={() => setChartType('bar')}
                className={`chart-type-button ${chartType === 'bar' ? 'active' : ''}`}
              >
                <span className="button-icon">📊</span>
                {t.barChart || '柱状图'}
              </button>
              <button
                onClick={() => setChartType('line')}
                className={`chart-type-button ${chartType === 'line' ? 'active' : ''}`}
              >
                <span className="button-icon">📈</span>
                {t.lineChart || '折线图'}
              </button>
            </div>
          </div>

          <div className="unified-control-row">
            <div className="unified-section-title">{t.dataCategory || '数据类别'}</div>
            <div className="category-grid">
              <button
                onClick={() => handleCategoryChange('education')}
                className={`category-button ${selectedCategory === 'education' ? 'active' : ''}`}
              >
                <span className="category-icon">🎓</span>
                <span className="category-label">{t.education || '教育水平'}</span>
              </button>
              <button
                onClick={() => handleCategoryChange('employment')}
                className={`category-button ${selectedCategory === 'employment' ? 'active' : ''}`}
              >
                <span className="category-icon">👥</span>
                <span className="category-label">{t.employment || '就业状态'}</span>
              </button>
              <button
                onClick={() => handleCategoryChange('occupation')}
                className={`category-button ${selectedCategory === 'occupation' ? 'active' : ''}`}
              >
                <span className="category-icon">👷</span>
                <span className="category-label">{t.occupation || '职业分布'}</span>
              </button>
              <button
                onClick={() => handleCategoryChange('industry')}
                className={`category-button ${selectedCategory === 'industry' ? 'active' : ''}`}
              >
                <span className="category-icon">🏭</span>
                <span className="category-label">{t.industry || '行业分布'}</span>
              </button>
              <button
                onClick={() => handleCategoryChange('workingHours')}
                className={`category-button ${selectedCategory === 'workingHours' ? 'active' : ''}`}
              >
                <span className="category-icon">⏰</span>
                <span className="category-label">{t.workingHours || '工作时间'}</span>
              </button>
            </div>
          </div>
        </div>

        {/* 指标选择 - 改进的UI */}
        <div className="metrics-section">
          <div className="metrics-header">
            <div className="section-title">{t.selectMetrics || '选择指标'}</div>
            <div className="metrics-actions">
              <button onClick={handleSelectAll} className="action-button">
                <span className="action-icon">✓</span>
                {t.selectAll || '全选'}
              </button>
              <button onClick={handleClearAll} className="action-button clear">
                <span className="action-icon">×</span>
                {t.clearAll || '清空'}
              </button>
            </div>
          </div>
          <div className="metrics-grid">
            {categories[selectedCategory].map(metric => (
              <label key={metric} className={`metric-item ${selectedMetrics.includes(metric) ? 'active' : ''}`}>
                <input
                  type="checkbox"
                  checked={selectedMetrics.includes(metric)}
                  onChange={() => {
                    if (selectedMetrics.includes(metric)) {
                      setSelectedMetrics(selectedMetrics.filter(m => m !== metric));
                    } else {
                      setSelectedMetrics([...selectedMetrics, metric]);
                    }
                  }}
                />
                {t[metric.trim().toLowerCase().replace(/\s+/g, '_')] || metric}
              </label>
            ))}
          </div>
        </div>

        {/* 时间轴滑块 - 改进的UI */}
        {laborTrends && laborTrends.length > 0 && (
          <div className="time-slider-container">
            <div className="section-title">{t.selectTimeRange || '选择时间范围'}</div>
            <div className="time-slider-wrapper">
              <input
                type="range"
                min="0"
                max={laborTrends.length - 1}
                value={selectedIndex}
                onChange={handleTimeSliderChange}
                className="time-slider"
              />

              {/* 将所有时间点显示为带标签的小球 */}
              <div className="time-ticks">
                {laborTrends.map((trend, idx) => {
                  const isPassed = idx <= selectedIndex;
                  const isActive = idx === selectedIndex;
                  const position = (idx / (laborTrends.length - 1)) * 100;

                  // 改进标签显示逻辑，减少标签数量
                  const totalLabels = 5; // 只显示大约5个标签
                  const step = Math.max(1, Math.floor(laborTrends.length / totalLabels));

                  // 只显示第一个、最后一个、当前选中的和按步长间隔的标签
                  const shouldShowLabel =
                    isActive ||
                    idx === 0 ||
                    idx === laborTrends.length - 1 ||
                    (idx % step === 0 && idx !== 0 && idx !== laborTrends.length - 1);

                  // 简化日期显示
                  const formattedDate = formatDate(trend.Date);

                  return (
                    <div key={idx} className="time-tick-with-label">
                      <div
                        className={`time-tick ${isPassed ? 'passed' : ''} ${isActive ? 'active' : ''}`}
                        style={{ left: `${position}%` }}
                        onClick={() => onChangeIndex(idx)}
                      />
                      {shouldShowLabel && (
                        <div
                          className={`time-tick-label ${isActive ? 'active' : ''}`}
                          style={{ left: `${position}%` }} /* 使用CSS中的top属性控制位置 */
                        >
                          {formattedDate}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* 图表展示区 - 改进的UI */}
        <div className="chart-section">
          <div className="chart-title">
            {currentRecord.Date ? `${t.timeSeriesAnalysis || '时间序列分析'} - ${currentRecord.Date}` : (t.timeSeriesAnalysis || '时间序列分析')}
          </div>
          {selectedMetrics.length > 0 && laborTrends.length > 0 ? (
            <div className="chart-container">
              {chartType === 'line' ? (
                <Line
                  data={chartData}
                  options={{...chartOptions, indexAxis: 'x', plugins: {...chartOptions.plugins, title: {display: false}}}}
                />
              ) : (
                <Bar
                  data={chartData}
                  options={{...chartOptions, plugins: {...chartOptions.plugins, title: {display: false}}}}
                />
              )}
            </div>
          ) : (
            <div className="empty-chart">
              <div className="empty-icon">
                {chartType === 'line' ? '📈' : '📊'}
              </div>
              <div className="empty-text">
                {laborTrends.length === 0
                  ? (t.noDataAvailable || '没有可用数据')
                  : (t.pleaseSelectMetrics || '请选择至少一个指标以显示图表')}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimeSeriesPanel;
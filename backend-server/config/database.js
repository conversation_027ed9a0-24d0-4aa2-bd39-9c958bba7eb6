const mysql = require('mysql2/promise');
require('dotenv').config();

// AWS RDS MySQL 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  database: process.env.DB_NAME || 'platform_core_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  // AWS RDS 优化配置
  acquireTimeout: 60000, // 60秒连接超时
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

// 只有当密码存在且不为空时才添加密码字段
if (process.env.DB_PASSWORD && process.env.DB_PASSWORD.trim() !== '') {
  dbConfig.password = process.env.DB_PASSWORD;
}

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ AWS RDS MySQL 数据库连接成功');
    console.log(`📍 连接到: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    
    // 执行简单查询测试
    const [result] = await connection.execute('SELECT 1 as test');
    console.log('🔍 数据库响应测试:', result[0]);
    
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ AWS RDS 数据库连接失败:', error.message);
    console.error('🔧 请检查网络连接和数据库配置');
    return false;
  }
};

// 执行查询的通用函数（带错误重试机制）
const executeQuery = async (query, params = [], retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const [rows] = await pool.execute(query, params);
      return rows;
    } catch (error) {
      console.error(`数据库查询错误 (尝试 ${attempt}/${retries}):`, error.message);
      
      if (attempt === retries) {
        throw error;
      }
      
      // 如果是连接错误，等待后重试
      if (error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') {
        console.log('⏳ 等待重试...');
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      } else {
        throw error; // 非连接错误直接抛出
      }
    }
  }
};

// 获取单个结果（带错误重试机制）
const executeQuerySingle = async (query, params = [], retries = 3) => {
  try {
    const rows = await executeQuery(query, params, retries);
    return rows[0] || null;
  } catch (error) {
    console.error('数据库单查询错误:', error);
    throw error;
  }
};

// 检查数据库表是否存在
const checkTablesExist = async () => {
  try {
    const tables = await executeQuery('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);
    console.log('📋 现有数据表:', tableNames.join(', '));
    
    // 检查必需的表
    const requiredTables = ['users', 'projects'];
    const missingTables = requiredTables.filter(table => !tableNames.includes(table));
    
    if (missingTables.length > 0) {
      console.warn('⚠️  缺少数据表:', missingTables.join(', '));
      return false;
    }
    
    console.log('✅ 所有必需的数据表都存在');
    return true;
  } catch (error) {
    console.error('❌ 检查数据表失败:', error.message);
    return false;
  }
};

module.exports = {
  pool,
  testConnection,
  executeQuery,
  executeQuerySingle,
  checkTablesExist
};

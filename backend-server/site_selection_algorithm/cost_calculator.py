"""
成本计算器
实现工业站点选择中的各种成本计算逻辑
包括电费、水费、地税、人工费等运营成本计算
"""

import random
from typing import Dict, Any, Optional


class CostCalculator:
    """
    成本计算器类
    提供各种成本计算方法
    """
    
    def __init__(self):
        """初始化成本计算器"""
        # 默认成本参数 (基于德克萨斯州达拉斯地区)
        self.default_params = {
            # 电价 (美分/kWh)
            "electric_rate_cents_kwh": 14.2,
            
            # 水价 (美分/加仑)
            "water_rate_cents_gallon": 0.4,
            
            # 房产税厘税率 (千分比)
            "millage_rate": 22.5,  # 德克萨斯州约2.25%
            
            # 平均时薪 (美元/小时)
            "avg_hourly_wage": 20.0,
            
            # 默认年工作时数
            "annual_work_hours": 2080,  # 52周 × 40小时
            
            # 交易杂费率
            "closing_fee_rate": 0.02,  # 2%
            
            # 其他系数
            "maintenance_rate": 0.01,  # 维护费用占资产价值的1%
            "insurance_rate": 0.005,   # 保险费用占资产价值的0.5%
        }
    
    def get_cost_parameters(self, region: str = "dallas_tx") -> Dict[str, float]:
        """
        获取成本参数
        
        Args:
            region: 地区代码，用于获取地区化的成本参数
            
        Returns:
            Dict: 成本参数字典
        """
        # 根据地区调整参数 (目前使用模拟数据)
        if region == "dallas_tx":
            return {
                "electric_rate_cents_kwh": random.uniform(12.0, 16.5),
                "water_rate_cents_gallon": random.uniform(0.35, 0.45),
                "millage_rate": random.uniform(20.0, 25.0),
                "avg_hourly_wage": random.uniform(18.0, 25.0),
                "annual_work_hours": 2080,
                "closing_fee_rate": 0.02
            }
        else:
            return self.default_params.copy()
    
    def calculate_electric_cost(self, annual_kwh: float, 
                              rate_cents_kwh: Optional[float] = None) -> float:
        """
        计算年度电费
        
        Args:
            annual_kwh: 年度用电量 (千瓦时)
            rate_cents_kwh: 电价 (美分/千瓦时)，如果为None则使用默认值
            
        Returns:
            float: 年度电费 (美元)
        """
        if rate_cents_kwh is None:
            rate_cents_kwh = self.default_params["electric_rate_cents_kwh"]
        
        # 电费 = 用电量 × 电价 ÷ 100 (转换美分为美元)
        electric_cost = annual_kwh * (rate_cents_kwh / 100.0)
        
        return round(electric_cost, 2)
    
    def calculate_water_cost(self, annual_gallons: float, 
                           rate_cents_gallon: Optional[float] = None) -> float:
        """
        计算年度水费
        
        Args:
            annual_gallons: 年度用水量 (加仑)
            rate_cents_gallon: 水价 (美分/加仑)，如果为None则使用默认值
            
        Returns:
            float: 年度水费 (美元)
        """
        if rate_cents_gallon is None:
            rate_cents_gallon = self.default_params["water_rate_cents_gallon"]
        
        # 水费 = 用水量 × 水价 ÷ 100 (转换美分为美元)
        water_cost = annual_gallons * (rate_cents_gallon / 100.0)
        
        return round(water_cost, 2)
    
    def calculate_property_tax(self, assessed_value: float, 
                             millage_rate: Optional[float] = None) -> float:
        """
        计算年度房产税
        
        Args:
            assessed_value: 评估价值 (美元)
            millage_rate: 厘税率 (千分比)，如果为None则使用默认值
            
        Returns:
            float: 年度房产税 (美元)
        """
        if millage_rate is None:
            millage_rate = self.default_params["millage_rate"]
        
        # 房产税 = 评估价值 × 厘税率 ÷ 1000
        property_tax = assessed_value * (millage_rate / 1000.0)
        
        return round(property_tax, 2)
    
    def calculate_labor_cost(self, fte_count: int, 
                           avg_hourly_wage: Optional[float] = None,
                           annual_hours: Optional[float] = None) -> float:
        """
        计算年度人工费用
        
        Args:
            fte_count: 全职员工数量
            avg_hourly_wage: 平均时薪 (美元/小时)，如果为None则使用默认值
            annual_hours: 年度工作时数，如果为None则使用默认值
            
        Returns:
            float: 年度人工费用 (美元)
        """
        if avg_hourly_wage is None:
            avg_hourly_wage = self.default_params["avg_hourly_wage"]
        
        if annual_hours is None:
            annual_hours = self.default_params["annual_work_hours"]
        
        # 人工费 = 员工数 × 时薪 × 年工作时数
        labor_cost = fte_count * avg_hourly_wage * annual_hours
        
        return round(labor_cost, 2)
    
    def calculate_closing_costs(self, land_price: float, 
                              closing_rate: Optional[float] = None) -> float:
        """
        计算交易杂费
        
        Args:
            land_price: 土地价格 (美元)
            closing_rate: 杂费率，如果为None则使用默认值
            
        Returns:
            float: 交易杂费 (美元)
        """
        if closing_rate is None:
            closing_rate = self.default_params["closing_fee_rate"]
        
        # 交易杂费 = 土地价格 × 杂费率
        closing_costs = land_price * closing_rate
        
        return round(closing_costs, 2)
    
    def calculate_maintenance_cost(self, asset_value: float, 
                                 maintenance_rate: Optional[float] = None) -> float:
        """
        计算年度维护费用
        
        Args:
            asset_value: 资产价值 (美元)
            maintenance_rate: 维护费率，如果为None则使用默认值
            
        Returns:
            float: 年度维护费用 (美元)
        """
        if maintenance_rate is None:
            maintenance_rate = self.default_params["maintenance_rate"]
        
        # 维护费用 = 资产价值 × 维护费率
        maintenance_cost = asset_value * maintenance_rate
        
        return round(maintenance_cost, 2)
    
    def calculate_insurance_cost(self, asset_value: float, 
                               insurance_rate: Optional[float] = None) -> float:
        """
        计算年度保险费用
        
        Args:
            asset_value: 资产价值 (美元)
            insurance_rate: 保险费率，如果为None则使用默认值
            
        Returns:
            float: 年度保险费用 (美元)
        """
        if insurance_rate is None:
            insurance_rate = self.default_params["insurance_rate"]
        
        # 保险费用 = 资产价值 × 保险费率
        insurance_cost = asset_value * insurance_rate
        
        return round(insurance_cost, 2)
    
    def calculate_transportation_cost(self, distance_to_port: float, 
                                    annual_shipments: int = 100,
                                    cost_per_mile: float = 1.50) -> float:
        """
        计算年度运输费用 (可选功能)
        
        Args:
            distance_to_port: 到港口的距离 (英里)
            annual_shipments: 年度货运次数
            cost_per_mile: 每英里运输成本 (美元)
            
        Returns:
            float: 年度运输费用 (美元)
        """
        # 运输费用 = 距离 × 货运次数 × 每英里成本 × 2 (往返)
        transportation_cost = distance_to_port * annual_shipments * cost_per_mile * 2
        
        return round(transportation_cost, 2)
    
    def calculate_total_capex(self, land_price: float, 
                            include_closing_costs: bool = True,
                            additional_costs: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """
        计算总的一次性投资成本 (CAPEX)
        
        Args:
            land_price: 土地价格
            include_closing_costs: 是否包含交易杂费
            additional_costs: 其他一次性成本
            
        Returns:
            Dict: CAPEX分解
        """
        capex_breakdown = {
            "land_price": land_price,
            "closing_costs": 0,
            "additional_costs": 0,
            "total_capex": land_price
        }
        
        if include_closing_costs:
            closing_costs = self.calculate_closing_costs(land_price)
            capex_breakdown["closing_costs"] = closing_costs
            capex_breakdown["total_capex"] += closing_costs
        
        if additional_costs:
            total_additional = sum(additional_costs.values())
            capex_breakdown["additional_costs"] = total_additional
            capex_breakdown["total_capex"] += total_additional
            capex_breakdown["additional_breakdown"] = additional_costs
        
        return capex_breakdown
    
    def calculate_total_opex(self, 
                           annual_kwh: float = 1800000,
                           annual_gallons: float = 3000000,
                           assessed_value: float = 1000000,
                           fte_count: int = 100,
                           region: str = "dallas_tx",
                           include_maintenance: bool = False,
                           include_insurance: bool = False) -> Dict[str, float]:
        """
        计算总的年度运营费用 (OPEX)
        
        Args:
            annual_kwh: 年度用电量
            annual_gallons: 年度用水量
            assessed_value: 评估价值
            fte_count: 员工数量
            region: 地区
            include_maintenance: 是否包含维护费用
            include_insurance: 是否包含保险费用
            
        Returns:
            Dict: OPEX分解
        """
        cost_params = self.get_cost_parameters(region)
        
        # 计算各项成本
        electric_cost = self.calculate_electric_cost(annual_kwh, cost_params["electric_rate_cents_kwh"])
        water_cost = self.calculate_water_cost(annual_gallons, cost_params["water_rate_cents_gallon"])
        property_tax = self.calculate_property_tax(assessed_value, cost_params["millage_rate"])
        labor_cost = self.calculate_labor_cost(fte_count, cost_params["avg_hourly_wage"])
        
        opex_breakdown = {
            "electric_cost": electric_cost,
            "water_cost": water_cost,
            "property_tax": property_tax,
            "labor_cost": labor_cost,
            "maintenance_cost": 0,
            "insurance_cost": 0,
            "total_opex": electric_cost + water_cost + property_tax + labor_cost
        }
        
        if include_maintenance:
            maintenance_cost = self.calculate_maintenance_cost(assessed_value)
            opex_breakdown["maintenance_cost"] = maintenance_cost
            opex_breakdown["total_opex"] += maintenance_cost
        
        if include_insurance:
            insurance_cost = self.calculate_insurance_cost(assessed_value)
            opex_breakdown["insurance_cost"] = insurance_cost
            opex_breakdown["total_opex"] += insurance_cost
        
        return opex_breakdown
    
    def calculate_5yr_total_cost(self, capex: float, annual_opex: float, 
                               years: int = 5, discount_rate: float = 0) -> Dict[str, float]:
        """
        计算多年总成本
        
        Args:
            capex: 一次性投资
            annual_opex: 年度运营费用
            years: 年数
            discount_rate: 折现率 (如果为0则不折现)
            
        Returns:
            Dict: 总成本分解
        """
        if discount_rate == 0:
            # 不折现的简单计算
            total_opex = annual_opex * years
            total_cost = capex + total_opex
        else:
            # 折现计算
            total_opex = 0
            for year in range(1, years + 1):
                discounted_opex = annual_opex / ((1 + discount_rate) ** year)
                total_opex += discounted_opex
            total_cost = capex + total_opex
        
        return {
            "capex": capex,
            "total_opex": round(total_opex, 2),
            "total_cost": round(total_cost, 2),
            "years": years,
            "discount_rate": discount_rate,
            "annual_opex": annual_opex
        } 
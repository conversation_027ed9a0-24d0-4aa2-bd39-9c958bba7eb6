"""
站点选择算法核心实现
实现6步工业站点选择算法的主要逻辑
"""

import sys
import os
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# 添加父目录到路径以导入regard_api和当前目录的其他模块
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, parent_dir)
sys.path.insert(0, current_dir)

# 导入regard_api模块
import regard_api.regrid_client as regrid_client_module
import regard_api.data_models as data_models_module
RegridClient = regrid_client_module.RegridClient
ParcelData = data_models_module.ParcelData
SearchFilter = data_models_module.SearchFilter
from cost_calculator import CostCalculator
from scoring_system import ScoringSystem
from utils import AlgorithmUtils


@dataclass
class SiteSelectionParams:
    """站点选择参数"""
    # 地理条件
    search_area: Dict[str, Any]  # {"type": "city", "value": "Dallas, TX"}
    
    # 面积过滤
    min_area_sqft: float = 50000
    max_area_sqft: float = 200000
    
    # 分区要求
    zoning: str = "industrial"
    
    # 预算限制
    max_budget: Optional[float] = None
    
    # 距离限制
    max_distance_to_port_miles: float = 100
    
    # 运营参数
    workers: int = 100
    annual_kwh: float = 1800000  # 年度用电量
    annual_gallons: float = 3000000  # 年度用水量
    
    # 分析参数
    max_candidates: int = 10
    operating_years: int = 5


@dataclass
class SiteAnalysisResult:
    """站点分析结果"""
    parcel_id: str
    rank: int
    
    # 基本信息
    area_sqft: float
    address: str
    owner: str
    zoning: str
    
    # 成本分解
    capex: float  # 一次性投资
    land_price: float
    closing_fee: float
    
    # 年度运营成本
    opex_annual: float
    electric_cost_annual: float
    water_cost_annual: float
    tax_cost_annual: float
    labor_cost_annual: float
    
    # 总成本
    total_5yr_cost: float
    
    # 地理信息
    geometry: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, float]] = None  # {"lat": xx, "lng": xx}
    
    # 额外信息
    metadata: Optional[Dict[str, Any]] = None


class SiteSelectionAlgorithm:
    """
    站点选择算法主类
    实现完整的6步算法流程
    """
    
    def __init__(self, regrid_client: Optional[RegridClient] = None):
        """
        初始化算法
        
        Args:
            regrid_client: Regrid API客户端，如果为None则创建默认实例
        """
        self.regrid_client = regrid_client or RegridClient(use_mock_data=True)
        self.cost_calculator = CostCalculator()
        self.scoring_system = ScoringSystem()
        self.utils = AlgorithmUtils()
        
    def run_site_selection(self, params: SiteSelectionParams) -> Dict[str, Any]:
        """
        运行完整的站点选择算法
        
        Args:
            params: 站点选择参数
            
        Returns:
            Dict: 包含排序后的站点列表和分析结果
        """
        try:
            print("🔍 开始运行6步站点选择算法...", file=sys.stderr)
            
            # 第1步：预筛选
            print("📋 第1步：预筛选地块数据...", file=sys.stderr)
            raw_parcels = self._step1_pre_filter(params)
            print(f"✅ 预筛选完成，获得 {len(raw_parcels)} 个合格地块", file=sys.stderr)
            
            if not raw_parcels:
                return {
                    "success": False,
                    "message": "未找到符合条件的地块",
                    "parcels": [],
                    "summary": {
                        "total_found": 0,
                        "algorithm_steps": 1
                    }
                }
            
            # 第2步：计算CAPEX
            print("💰 第2步：计算一次性买地成本(CAPEX)...", file=sys.stderr)
            parcels_with_capex = self._step2_calculate_capex(raw_parcels)
            
            # 第3步：计算年度OPEX
            print("📊 第3步：计算年度运营费用(OPEX)...", file=sys.stderr)
            parcels_with_opex = self._step3_calculate_opex(parcels_with_capex, params)
            
            # 第4步：计算5年总成本
            print("🧮 第4步：折算五年总成本...", file=sys.stderr)
            parcels_with_total = self._step4_calculate_total_cost(parcels_with_opex, params.operating_years)
            
            # 第5步：排序与排名
            print("🏆 第5步：排序与排名...", file=sys.stderr)
            ranked_parcels = self._step5_rank_parcels(parcels_with_total)
            
            # 第6步：准备前端展示数据
            print("🎨 第6步：准备前端展示数据...", file=sys.stderr)
            display_results = self._step6_prepare_display(ranked_parcels, params.max_candidates)
            
            print(f"✅ 算法执行完成！返回前 {len(display_results)} 个最优站点", file=sys.stderr)
            
            return {
                "success": True,
                "message": f"成功分析了 {len(raw_parcels)} 个地块，返回前 {len(display_results)} 个最优选择",
                "parcels": display_results,
                "summary": {
                    "total_found": len(raw_parcels),
                    "total_ranked": len(ranked_parcels),
                    "top_returned": len(display_results),
                    "algorithm_steps": 6,
                    "search_area": params.search_area,
                    "area_range": {
                        "min_sqft": params.min_area_sqft,
                        "max_sqft": params.max_area_sqft
                    },
                    "operating_years": params.operating_years
                }
            }
            
        except Exception as e:
            print(f"❌ 算法执行失败: {str(e)}", file=sys.stderr)
            return {
                "success": False,
                "message": f"算法执行错误: {str(e)}",
                "parcels": [],
                "error": str(e)
            }
    
    def _step1_pre_filter(self, params: SiteSelectionParams) -> List[ParcelData]:
        """
        第1步：预筛选
        根据用户硬性条件过滤地块
        """
        # 构建搜索过滤条件
        search_filter = SearchFilter(
            state="TX",  # 目前专注于德克萨斯州
            min_area_sqft=params.min_area_sqft,
            max_area_sqft=params.max_area_sqft,
            zoning=["I-1", "I-2", "I-3", "IM", "IL", "IH", "M-1", "M-2"],  # 工业分区
            vacancy_only=None,  # 不限制空置状态
            max_sale_price=params.max_budget if params.max_budget else None
        )
        
        # 从搜索区域提取地理信息
        if params.search_area.get("type") == "city":
            city_name = params.search_area.get("value", "Dallas, TX")
            # 这里可以扩展为更复杂的地理查询
            # 目前使用模拟数据，默认查询德克萨斯州
            search_filter.state = "TX"
        
        # 调用Regrid API搜索
        parcels = self.regrid_client.search_parcels(
            search_filter, 
            limit=params.max_candidates * 3  # 获取更多数据以便筛选
        )
        
        # 额外的硬性过滤条件
        filtered_parcels = []
        for parcel in parcels:
            # 面积过滤
            if parcel.ll_gissqft:
                if parcel.ll_gissqft < params.min_area_sqft or parcel.ll_gissqft > params.max_area_sqft:
                    continue
            
            # 预算过滤
            if params.max_budget:
                estimated_price = parcel.saleprice or parcel.totalval or parcel.landval
                if estimated_price and estimated_price > params.max_budget:
                    continue
            
            # 分区过滤
            if parcel.zoning and not any(zone in parcel.zoning for zone in ["I-", "M-", "IM", "IL", "IH"]):
                continue
            
            filtered_parcels.append(parcel)
        
        return filtered_parcels[:params.max_candidates * 2]  # 返回足够的候选项
    
    def _step2_calculate_capex(self, parcels: List[ParcelData]) -> List[Dict[str, Any]]:
        """
        第2步：计算一次性买地成本(CAPEX)
        """
        results = []
        
        for parcel in parcels:
            # 计算土地价格：优先使用销售价格，否则使用评估价值
            land_price = 0
            if parcel.saleprice and parcel.saleprice > 0:
                land_price = parcel.saleprice
            elif parcel.totalval and parcel.totalval > 0:
                land_price = parcel.totalval
            elif parcel.landval and parcel.landval > 0:
                land_price = parcel.landval
            else:
                # 如果没有价格信息，基于面积估算
                area_sqft = parcel.ll_gissqft or 100000
                land_price = area_sqft * 15  # $15/sqft 作为默认估计
            
            # 计算交易杂费 (2%)
            closing_fee = land_price * 0.02
            
            # 计算总的一次性成本
            capex = land_price + closing_fee
            
            result = {
                "parcel": parcel,
                "land_price": land_price,
                "closing_fee": closing_fee,
                "capex": capex
            }
            
            results.append(result)
        
        return results
    
    def _step3_calculate_opex(self, parcels_data: List[Dict[str, Any]], 
                             params: SiteSelectionParams) -> List[Dict[str, Any]]:
        """
        第3步：计算年度运营费用(OPEX)
        """
        # 获取成本参数
        cost_params = self.cost_calculator.get_cost_parameters()
        
        for data in parcels_data:
            parcel = data["parcel"]
            
            # 计算电费
            electric_cost = self.cost_calculator.calculate_electric_cost(
                annual_kwh=params.annual_kwh,
                rate_cents_kwh=cost_params["electric_rate_cents_kwh"]
            )
            
            # 计算水费
            water_cost = self.cost_calculator.calculate_water_cost(
                annual_gallons=params.annual_gallons,
                rate_cents_gallon=cost_params["water_rate_cents_gallon"]
            )
            
            # 计算地税
            assessed_value = parcel.totalval or parcel.landval or data["land_price"]
            tax_cost = self.cost_calculator.calculate_property_tax(
                assessed_value=assessed_value,
                millage_rate=cost_params["millage_rate"]
            )
            
            # 计算人工费
            labor_cost = self.cost_calculator.calculate_labor_cost(
                fte_count=params.workers,
                avg_hourly_wage=cost_params["avg_hourly_wage"]
            )
            
            # 汇总年度运营费
            opex_annual = electric_cost + water_cost + tax_cost + labor_cost
            
            # 添加到数据中
            data.update({
                "electric_cost_annual": electric_cost,
                "water_cost_annual": water_cost,
                "tax_cost_annual": tax_cost,
                "labor_cost_annual": labor_cost,
                "opex_annual": opex_annual
            })
        
        return parcels_data
    
    def _step4_calculate_total_cost(self, parcels_data: List[Dict[str, Any]], 
                                  operating_years: int) -> List[Dict[str, Any]]:
        """
        第4步：折算五年总成本
        """
        for data in parcels_data:
            # 计算总成本：一次性成本 + (年度运营成本 × 年数)
            total_5yr_cost = data["capex"] + (data["opex_annual"] * operating_years)
            data["total_5yr_cost"] = total_5yr_cost
        
        return parcels_data
    
    def _step5_rank_parcels(self, parcels_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        第5步：排序与排名
        按五年总成本从低到高排序
        """
        # 按总成本排序
        sorted_parcels = sorted(parcels_data, key=lambda x: x["total_5yr_cost"])
        
        # 添加排名
        for i, data in enumerate(sorted_parcels):
            data["rank"] = i + 1
        
        return sorted_parcels
    
    def _step6_prepare_display(self, ranked_parcels: List[Dict[str, Any]], 
                              max_candidates: int) -> List[Dict[str, Any]]:
        """
        第6步：准备前端展示数据
        返回前N个地块的详细信息
        """
        display_results = []
        
        for data in ranked_parcels[:max_candidates]:
            parcel = data["parcel"]
            
            # 计算地块中心坐标
            location = None
            if parcel.geometry and parcel.geometry.coordinates:
                try:
                    coords = parcel.geometry.coordinates[0]  # 获取外环坐标
                    # 计算多边形中心点
                    lats = [coord[1] for coord in coords[:-1]]  # 排除闭合点
                    lngs = [coord[0] for coord in coords[:-1]]
                    center_lat = sum(lats) / len(lats)
                    center_lng = sum(lngs) / len(lngs)
                    location = {"lat": center_lat, "lng": center_lng}
                except:
                    location = None
            
            # 构建前端需要的数据结构
            result = {
                "parcel_id": parcel.ll_uuid,
                "rank": data["rank"],
                
                # 基本信息
                "area_sqft": parcel.ll_gissqft or 0,
                "address": parcel.address or f"{parcel.parcelnumb}, TX",
                "owner": parcel.owner or "未知业主",
                "zoning": parcel.zoning or "工业用地",
                
                # 成本信息
                "total_cost": data["total_5yr_cost"],
                "capex": data["capex"],
                "land_cost": data["land_price"],
                "closing_fee": data["closing_fee"],
                
                # 年度运营成本
                "opex_annual": data["opex_annual"],
                "electric_cost": data["electric_cost_annual"],
                "water_cost": data["water_cost_annual"],
                "tax_cost": data["tax_cost_annual"],
                "labor_cost": data["labor_cost_annual"],
                
                # 地理信息
                "geometry": parcel.geometry.to_dict() if parcel.geometry else None,
                "location": location,
                
                # 元数据
                "metadata": {
                    "ll_uuid": parcel.ll_uuid,
                    "geoid": parcel.geoid,
                    "parcelnumb": parcel.parcelnumb,
                    "county": parcel.county,
                    "state": parcel.state2,
                    "vacancy_status": parcel.usps_vacancy,
                    "year_built": parcel.yearbuilt,
                    "building_count": parcel.ll_bldg_count,
                    "qoz": parcel.qoz,
                    "sale_date": parcel.saledate,
                    "assessed_value": parcel.totalval,
                    "land_value": parcel.landval,
                    "improvement_value": parcel.improvval,
                    "annual_tax": parcel.taxamt,
                    "labor_cost": data["labor_cost_annual"],
                    "utility_cost": data["electric_cost_annual"] + data["water_cost_annual"],
                    "transportation_cost": 0  # 可以后续添加物流成本计算
                }
            }
            
            display_results.append(result)
        
        return display_results 
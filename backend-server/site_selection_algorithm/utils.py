"""
算法工具函数
提供站点选择算法中需要的各种辅助功能
"""

import json
import math
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime


class AlgorithmUtils:
    """
    算法工具类
    提供各种辅助计算和数据处理功能
    """
    
    @staticmethod
    def format_currency(amount: float, currency_symbol: str = "$") -> str:
        """
        格式化货币显示
        
        Args:
            amount: 金额
            currency_symbol: 货币符号
            
        Returns:
            str: 格式化的货币字符串
        """
        if amount >= 1_000_000:
            return f"{currency_symbol}{amount / 1_000_000:.1f}M"
        elif amount >= 1_000:
            return f"{currency_symbol}{amount / 1_000:.1f}K"
        else:
            return f"{currency_symbol}{amount:,.0f}"
    
    @staticmethod
    def format_area(area_sqft: float, unit: str = "sqft") -> str:
        """
        格式化面积显示
        
        Args:
            area_sqft: 面积(平方英尺)
            unit: 单位 ("sqft", "acres", "both")
            
        Returns:
            str: 格式化的面积字符串
        """
        if unit == "acres":
            acres = area_sqft / 43560
            return f"{acres:.1f} acres"
        elif unit == "both":
            acres = area_sqft / 43560
            return f"{area_sqft:,.0f} sqft ({acres:.1f} acres)"
        else:
            return f"{area_sqft:,.0f} sqft"
    
    @staticmethod
    def calculate_distance_miles(lat1: float, lng1: float, 
                               lat2: float, lng2: float) -> float:
        """
        计算两点间的距离(英里)
        使用Haversine公式
        
        Args:
            lat1, lng1: 第一个点的经纬度
            lat2, lng2: 第二个点的经纬度
            
        Returns:
            float: 距离(英里)
        """
        # 转换为弧度
        lat1_rad = math.radians(lat1)
        lng1_rad = math.radians(lng1)
        lat2_rad = math.radians(lat2)
        lng2_rad = math.radians(lng2)
        
        # Haversine公式
        dlat = lat2_rad - lat1_rad
        dlng = lng2_rad - lng1_rad
        
        a = (math.sin(dlat/2)**2 + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2)
        c = 2 * math.asin(math.sqrt(a))
        
        # 地球半径 (英里)
        radius_miles = 3959.0
        
        return radius_miles * c
    
    @staticmethod
    def calculate_polygon_centroid(coordinates: List[List[float]]) -> Tuple[float, float]:
        """
        计算多边形重心
        
        Args:
            coordinates: 多边形坐标 [[lng, lat], ...]
            
        Returns:
            Tuple[float, float]: (纬度, 经度)
        """
        if not coordinates or len(coordinates) < 3:
            return (0.0, 0.0)
        
        # 移除闭合点(如果存在)
        coords = coordinates[:-1] if coordinates[0] == coordinates[-1] else coordinates
        
        if len(coords) < 3:
            return (0.0, 0.0)
        
        # 计算重心
        area = 0.0
        cx = 0.0
        cy = 0.0
        
        for i in range(len(coords)):
            j = (i + 1) % len(coords)
            cross = coords[i][0] * coords[j][1] - coords[j][0] * coords[i][1]
            area += cross
            cx += (coords[i][0] + coords[j][0]) * cross
            cy += (coords[i][1] + coords[j][1]) * cross
        
        if area == 0:
            # 如果面积为0，返回几何中心
            avg_lng = sum(coord[0] for coord in coords) / len(coords)
            avg_lat = sum(coord[1] for coord in coords) / len(coords)
            return (avg_lat, avg_lng)
        
        area *= 0.5
        factor = 1.0 / (6.0 * area)
        
        centroid_lng = cx * factor
        centroid_lat = cy * factor
        
        return (centroid_lat, centroid_lng)
    
    @staticmethod
    def validate_coordinates(lat: float, lng: float) -> bool:
        """
        验证经纬度坐标是否有效
        
        Args:
            lat: 纬度
            lng: 经度
            
        Returns:
            bool: 是否有效
        """
        return (-90 <= lat <= 90) and (-180 <= lng <= 180)
    
    @staticmethod
    def normalize_state_code(state_input: str) -> str:
        """
        规范化州代码
        
        Args:
            state_input: 输入的州名或州代码
            
        Returns:
            str: 标准的2字符州代码
        """
        # 州名到州代码的映射
        state_mapping = {
            "texas": "TX",
            "california": "CA",
            "illinois": "IL",
            "new york": "NY",
            "florida": "FL",
            "georgia": "GA",
            "north carolina": "NC",
            "ohio": "OH",
            "pennsylvania": "PA",
            "michigan": "MI"
        }
        
        state_clean = state_input.strip().lower()
        
        # 如果已经是2字符代码
        if len(state_clean) == 2 and state_clean.upper().isalpha():
            return state_clean.upper()
        
        # 查找州名映射
        return state_mapping.get(state_clean, "TX")  # 默认返回TX
    
    @staticmethod
    def parse_city_state(location_string: str) -> Tuple[str, str]:
        """
        解析城市和州信息
        
        Args:
            location_string: 位置字符串 "City, State" 或 "City, ST"
            
        Returns:
            Tuple[str, str]: (城市, 州代码)
        """
        try:
            parts = location_string.split(",")
            if len(parts) >= 2:
                city = parts[0].strip()
                state = AlgorithmUtils.normalize_state_code(parts[1].strip())
                return (city, state)
            else:
                return (location_string.strip(), "TX")
        except:
            return ("Dallas", "TX")
    
    @staticmethod
    def calculate_cost_per_sqft(total_cost: float, area_sqft: float) -> float:
        """
        计算每平方英尺成本
        
        Args:
            total_cost: 总成本
            area_sqft: 面积(平方英尺)
            
        Returns:
            float: 每平方英尺成本
        """
        if area_sqft <= 0:
            return 0.0
        return total_cost / area_sqft
    
    @staticmethod
    def calculate_annual_roi(annual_revenue: float, total_investment: float) -> float:
        """
        计算年度投资回报率
        
        Args:
            annual_revenue: 年度收入
            total_investment: 总投资
            
        Returns:
            float: ROI百分比
        """
        if total_investment <= 0:
            return 0.0
        return (annual_revenue / total_investment) * 100
    
    @staticmethod
    def estimate_construction_time(area_sqft: float, building_type: str = "industrial") -> int:
        """
        估算建设时间(月)
        
        Args:
            area_sqft: 建筑面积
            building_type: 建筑类型
            
        Returns:
            int: 预计建设时间(月)
        """
        base_months = {
            "industrial": 8,
            "warehouse": 6,
            "office": 10,
            "mixed": 12
        }
        
        base = base_months.get(building_type, 8)
        
        # 根据面积调整时间
        if area_sqft > 200000:
            return base + 6
        elif area_sqft > 100000:
            return base + 3
        else:
            return base
    
    @staticmethod
    def generate_site_summary(site_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成站点摘要信息
        
        Args:
            site_data: 站点数据
            
        Returns:
            Dict: 摘要信息
        """
        total_cost = site_data.get("total_cost", 0)
        area_sqft = site_data.get("area_sqft", 0)
        
        summary = {
            "parcel_id": site_data.get("parcel_id", ""),
            "rank": site_data.get("rank", 0),
            "total_cost_formatted": AlgorithmUtils.format_currency(total_cost),
            "area_formatted": AlgorithmUtils.format_area(area_sqft, "both"),
            "cost_per_sqft": AlgorithmUtils.calculate_cost_per_sqft(total_cost, area_sqft),
            "cost_per_sqft_formatted": AlgorithmUtils.format_currency(
                AlgorithmUtils.calculate_cost_per_sqft(total_cost, area_sqft)
            ),
            "address": site_data.get("address", ""),
            "zoning": site_data.get("zoning", ""),
            
            # 成本分解
            "cost_breakdown": {
                "capex": {
                    "amount": site_data.get("capex", 0),
                    "formatted": AlgorithmUtils.format_currency(site_data.get("capex", 0)),
                    "percentage": (site_data.get("capex", 0) / total_cost * 100) if total_cost > 0 else 0
                },
                "annual_opex": {
                    "amount": site_data.get("opex_annual", 0),
                    "formatted": AlgorithmUtils.format_currency(site_data.get("opex_annual", 0))
                }
            },
            
            # 地理信息
            "location": site_data.get("location", {}),
            "geometry_available": bool(site_data.get("geometry"))
        }
        
        return summary
    
    @staticmethod
    def export_results_to_csv(results: List[Dict[str, Any]], 
                            filename: Optional[str] = None) -> str:
        """
        导出结果为CSV格式
        
        Args:
            results: 结果数据
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: CSV内容
        """
        if not results:
            return ""
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"site_selection_results_{timestamp}.csv"
        
        # CSV头部
        headers = [
            "Rank", "Parcel ID", "Address", "Total Cost", "Area (sqft)", 
            "Cost per sqft", "CAPEX", "Annual OPEX", "Land Cost", 
            "Labor Cost", "Electric Cost", "Tax Cost", "Zoning"
        ]
        
        csv_lines = [",".join(headers)]
        
        # 数据行
        for result in results:
            row = [
                str(result.get("rank", "")),
                result.get("parcel_id", ""),
                f'"{result.get("address", "")}"',  # 引号包围地址
                str(result.get("total_cost", "")),
                str(result.get("area_sqft", "")),
                str(AlgorithmUtils.calculate_cost_per_sqft(
                    result.get("total_cost", 0), 
                    result.get("area_sqft", 1)
                )),
                str(result.get("capex", "")),
                str(result.get("opex_annual", "")),
                str(result.get("land_cost", "")),
                str(result.get("labor_cost", "")),
                str(result.get("electric_cost", "")),
                str(result.get("tax_cost", "")),
                result.get("zoning", "")
            ]
            csv_lines.append(",".join(row))
        
        return "\n".join(csv_lines)
    
    @staticmethod
    def validate_search_params(params: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证搜索参数
        
        Args:
            params: 搜索参数
            
        Returns:
            Dict: 验证结果 {"valid": bool, "errors": List[str], "normalized_params": Dict}
        """
        errors = []
        normalized_params = params.copy()
        
        # 验证面积范围
        min_area = params.get("min_area_sqft", 0)
        max_area = params.get("max_area_sqft", 0)
        
        if min_area <= 0:
            errors.append("最小面积必须大于0")
        if max_area <= 0:
            errors.append("最大面积必须大于0")
        if min_area >= max_area:
            errors.append("最小面积必须小于最大面积")
        
        # 验证员工数量
        workers = params.get("workers", 0)
        if workers < 0:
            errors.append("员工数量不能为负数")
        
        # 验证用电量
        annual_kwh = params.get("annual_kwh", 0)
        if annual_kwh < 0:
            errors.append("年度用电量不能为负数")
        
        # 验证搜索区域
        search_area = params.get("search_area", {})
        if not search_area.get("value"):
            errors.append("必须指定搜索区域")
        
        # 规范化参数
        if "search_area" in normalized_params and normalized_params["search_area"].get("value"):
            city, state = AlgorithmUtils.parse_city_state(normalized_params["search_area"]["value"])
            normalized_params["search_area"]["city"] = city
            normalized_params["search_area"]["state"] = state
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "normalized_params": normalized_params
        }
    
    @staticmethod
    def calculate_summary_statistics(results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        计算结果的汇总统计
        
        Args:
            results: 结果数据
            
        Returns:
            Dict: 统计信息
        """
        if not results:
            return {}
        
        total_costs = [r.get("total_cost", 0) for r in results]
        areas = [r.get("area_sqft", 0) for r in results]
        capexs = [r.get("capex", 0) for r in results]
        opexs = [r.get("opex_annual", 0) for r in results]
        
        def safe_stats(values):
            valid_values = [v for v in values if v > 0]
            if not valid_values:
                return {"min": 0, "max": 0, "avg": 0, "median": 0}
            
            valid_values.sort()
            n = len(valid_values)
            median = valid_values[n//2] if n % 2 == 1 else (valid_values[n//2-1] + valid_values[n//2]) / 2
            
            return {
                "min": min(valid_values),
                "max": max(valid_values),
                "avg": sum(valid_values) / len(valid_values),
                "median": median
            }
        
        return {
            "total_sites": len(results),
            "total_cost_stats": safe_stats(total_costs),
            "area_stats": safe_stats(areas),
            "capex_stats": safe_stats(capexs),
            "opex_stats": safe_stats(opexs),
            "cost_range": {
                "lowest": min(total_costs) if total_costs else 0,
                "highest": max(total_costs) if total_costs else 0,
                "spread": (max(total_costs) - min(total_costs)) if total_costs else 0
            }
        } 
"""
站点选择算法包
实现工业站点选择的6步算法流程
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

__version__ = "1.0.0"

# 延迟导入以避免循环依赖问题
def get_site_selection_algorithm():
    from core_algorithm import SiteSelectionAlgorithm
    return SiteSelectionAlgorithm

def get_cost_calculator():
    from cost_calculator import CostCalculator
    return CostCalculator

def get_scoring_system():
    from scoring_system import ScoringSystem
    return ScoringSystem

def get_algorithm_utils():
    from utils import AlgorithmUtils
    return AlgorithmUtils

__all__ = [
    'get_site_selection_algorithm',
    'get_cost_calculator', 
    'get_scoring_system',
    'get_algorithm_utils',
    '__version__'
] 
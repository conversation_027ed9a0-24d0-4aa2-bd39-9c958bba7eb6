"""
评分系统
实现基于权重的地块评分算法
支持多指标综合评分和用户自定义权重
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class ScoringWeights:
    """评分权重配置"""
    land_price_weight: float = 0.25      # 地价权重 25%
    property_tax_weight: float = 0.15    # 地税权重 15%
    labor_cost_weight: float = 0.20      # 工资权重 20%
    electric_cost_weight: float = 0.20   # 电价权重 20%
    vacancy_weight: float = 0.10         # 空置权重 10%
    building_age_weight: float = 0.10    # 房龄权重 10%
    
    def __post_init__(self):
        """确保权重总和为1.0"""
        total = (self.land_price_weight + self.property_tax_weight + 
                self.labor_cost_weight + self.electric_cost_weight + 
                self.vacancy_weight + self.building_age_weight)
        
        if abs(total - 1.0) > 0.001:  # 允许小的浮点误差
            raise ValueError(f"权重总和必须为1.0，当前为 {total}")


@dataclass
class SiteScore:
    """地块评分结果"""
    parcel_id: str
    total_score: float
    rank: int
    
    # 各项指标得分
    land_price_score: float
    property_tax_score: float
    labor_cost_score: float
    electric_cost_score: float
    vacancy_score: float
    building_age_score: float
    
    # 权重信息
    weights_used: ScoringWeights
    
    # 原始数据
    raw_metrics: Dict[str, Any]


class ScoringSystem:
    """
    评分系统类
    提供多指标综合评分功能
    """
    
    def __init__(self, default_weights: Optional[ScoringWeights] = None):
        """
        初始化评分系统
        
        Args:
            default_weights: 默认权重配置，如果为None则使用标准配置
        """
        self.default_weights = default_weights or ScoringWeights()
    
    def score_sites(self, sites_data: List[Dict[str, Any]], 
                   weights: Optional[ScoringWeights] = None,
                   normalize_method: str = "min_max") -> List[SiteScore]:
        """
        对站点列表进行评分
        
        Args:
            sites_data: 站点数据列表
            weights: 评分权重，如果为None则使用默认权重
            normalize_method: 归一化方法 ("min_max", "z_score", "rank")
            
        Returns:
            List[SiteScore]: 评分结果列表，按得分排序
        """
        if not sites_data:
            return []
        
        scoring_weights = weights or self.default_weights
        
        # 提取各项指标数据
        metrics = self._extract_metrics(sites_data)
        
        # 归一化各项指标 (越小越好的指标需要反转)
        normalized_metrics = self._normalize_metrics(metrics, normalize_method)
        
        # 计算综合得分
        site_scores = []
        for i, site in enumerate(sites_data):
            parcel_id = site.get("parcel", {}).get("ll_uuid") or site.get("parcel_id", f"site_{i}")
            
            # 计算各项得分
            land_price_score = normalized_metrics["land_price"][i]
            property_tax_score = normalized_metrics["property_tax"][i]
            labor_cost_score = normalized_metrics["labor_cost"][i]
            electric_cost_score = normalized_metrics["electric_cost"][i]
            vacancy_score = normalized_metrics["vacancy"][i]
            building_age_score = normalized_metrics["building_age"][i]
            
            # 计算加权总分
            total_score = (
                land_price_score * scoring_weights.land_price_weight +
                property_tax_score * scoring_weights.property_tax_weight +
                labor_cost_score * scoring_weights.labor_cost_weight +
                electric_cost_score * scoring_weights.electric_cost_weight +
                vacancy_score * scoring_weights.vacancy_weight +
                building_age_score * scoring_weights.building_age_weight
            )
            
            site_score = SiteScore(
                parcel_id=parcel_id,
                total_score=round(total_score, 3),
                rank=0,  # 稍后设置
                land_price_score=round(land_price_score, 3),
                property_tax_score=round(property_tax_score, 3),
                labor_cost_score=round(labor_cost_score, 3),
                electric_cost_score=round(electric_cost_score, 3),
                vacancy_score=round(vacancy_score, 3),
                building_age_score=round(building_age_score, 3),
                weights_used=scoring_weights,
                raw_metrics=metrics["raw_data"][i]
            )
            
            site_scores.append(site_score)
        
        # 按得分排序 (得分越高越好)
        site_scores.sort(key=lambda x: x.total_score, reverse=True)
        
        # 设置排名
        for i, score in enumerate(site_scores):
            score.rank = i + 1
        
        return site_scores
    
    def _extract_metrics(self, sites_data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """
        从站点数据中提取评分指标
        
        Args:
            sites_data: 站点数据列表
            
        Returns:
            Dict: 各项指标的数值列表
        """
        land_prices = []
        property_taxes = []
        labor_costs = []
        electric_costs = []
        vacancy_scores = []
        building_ages = []
        raw_data = []
        
        for site in sites_data:
            parcel = site.get("parcel", {})
            
            # 地价 (越低越好)
            land_price = site.get("land_price", 0) or parcel.get("saleprice", 0) or parcel.get("totalval", 0)
            land_prices.append(float(land_price) if land_price else 1000000)
            
            # 房产税 (越低越好)
            property_tax = site.get("tax_cost_annual", 0) or parcel.get("taxamt", 0)
            property_taxes.append(float(property_tax) if property_tax else 10000)
            
            # 人工成本 (越低越好)
            labor_cost = site.get("labor_cost_annual", 0)
            labor_costs.append(float(labor_cost) if labor_cost else 2000000)
            
            # 电费 (越低越好)
            electric_cost = site.get("electric_cost_annual", 0)
            electric_costs.append(float(electric_cost) if electric_cost else 200000)
            
            # 空置状态 (空置更好，得分更高)
            vacancy_status = parcel.get("usps_vacancy", "N")
            vacancy_score = 1.0 if vacancy_status == "Y" else 0.0
            vacancy_scores.append(vacancy_score)
            
            # 建筑年份 (越新越好)
            year_built = parcel.get("yearbuilt", 0)
            if year_built and year_built > 1900:
                building_age = 2024 - year_built  # 计算建筑年龄
            else:
                building_age = 50  # 默认50年 (无建筑或未知年份)
            building_ages.append(float(building_age))
            
            # 保存原始数据
            raw_data.append({
                "land_price": land_price,
                "property_tax": property_tax,
                "labor_cost": labor_cost,
                "electric_cost": electric_cost,
                "vacancy_status": vacancy_status,
                "year_built": year_built,
                "building_age": building_age
            })
        
        return {
            "land_price": land_prices,
            "property_tax": property_taxes,
            "labor_cost": labor_costs,
            "electric_cost": electric_costs,
            "vacancy": vacancy_scores,
            "building_age": building_ages,
            "raw_data": raw_data
        }
    
    def _normalize_metrics(self, metrics: Dict[str, List[float]], 
                          method: str = "min_max") -> Dict[str, List[float]]:
        """
        归一化指标数据
        
        Args:
            metrics: 原始指标数据
            method: 归一化方法
            
        Returns:
            Dict: 归一化后的指标数据 (0-1范围，1表示最好)
        """
        normalized = {}
        
        for metric_name, values in metrics.items():
            if metric_name == "raw_data":
                continue
                
            if not values or all(v == 0 for v in values):
                # 如果所有值都是0，设置为中等得分
                normalized[metric_name] = [0.5] * len(values)
                continue
            
            if method == "min_max":
                normalized[metric_name] = self._min_max_normalize(values, metric_name)
            elif method == "z_score":
                normalized[metric_name] = self._z_score_normalize(values, metric_name)
            elif method == "rank":
                normalized[metric_name] = self._rank_normalize(values, metric_name)
            else:
                raise ValueError(f"未知的归一化方法: {method}")
        
        return normalized
    
    def _min_max_normalize(self, values: List[float], metric_name: str) -> List[float]:
        """
        最小-最大归一化
        对于成本类指标(越小越好)，需要反转：score = (max - value) / (max - min)
        对于收益类指标(越大越好)：score = (value - min) / (max - min)
        """
        min_val = min(values)
        max_val = max(values)
        
        if min_val == max_val:
            return [0.5] * len(values)  # 所有值相同时返回中等得分
        
        # 成本类指标 (越小越好)
        cost_metrics = ["land_price", "property_tax", "labor_cost", "electric_cost", "building_age"]
        
        if metric_name in cost_metrics:
            # 反转：最小值得1分，最大值得0分
            return [(max_val - val) / (max_val - min_val) for val in values]
        else:
            # 收益类指标：最大值得1分，最小值得0分
            return [(val - min_val) / (max_val - min_val) for val in values]
    
    def _z_score_normalize(self, values: List[float], metric_name: str) -> List[float]:
        """
        Z-score归一化 (标准化)
        """
        mean_val = sum(values) / len(values)
        variance = sum((x - mean_val) ** 2 for x in values) / len(values)
        std_dev = variance ** 0.5
        
        if std_dev == 0:
            return [0.5] * len(values)
        
        z_scores = [(val - mean_val) / std_dev for val in values]
        
        # 将z-scores转换到0-1范围
        min_z = min(z_scores)
        max_z = max(z_scores)
        
        if min_z == max_z:
            return [0.5] * len(values)
        
        # 成本类指标需要反转
        cost_metrics = ["land_price", "property_tax", "labor_cost", "electric_cost", "building_age"]
        
        if metric_name in cost_metrics:
            return [(max_z - z) / (max_z - min_z) for z in z_scores]
        else:
            return [(z - min_z) / (max_z - min_z) for z in z_scores]
    
    def _rank_normalize(self, values: List[float], metric_name: str) -> List[float]:
        """
        排名归一化
        """
        # 创建值-索引对并排序
        indexed_values = [(val, i) for i, val in enumerate(values)]
        
        # 成本类指标按升序排序 (越小排名越高)
        cost_metrics = ["land_price", "property_tax", "labor_cost", "electric_cost", "building_age"]
        
        if metric_name in cost_metrics:
            indexed_values.sort(key=lambda x: x[0])  # 升序
        else:
            indexed_values.sort(key=lambda x: x[0], reverse=True)  # 降序
        
        # 分配排名得分
        n = len(values)
        scores = [0.0] * n
        
        for rank, (_, original_index) in enumerate(indexed_values):
            # 排名转换为0-1得分，第1名得1分，最后一名得0分
            score = (n - rank - 1) / (n - 1) if n > 1 else 0.5
            scores[original_index] = score
        
        return scores
    
    def calculate_weight_sensitivity(self, sites_data: List[Dict[str, Any]], 
                                   base_weights: Optional[ScoringWeights] = None) -> Dict[str, Any]:
        """
        计算权重敏感性分析
        
        Args:
            sites_data: 站点数据
            base_weights: 基准权重
            
        Returns:
            Dict: 敏感性分析结果
        """
        base_weights = base_weights or self.default_weights
        base_scores = self.score_sites(sites_data, base_weights)
        
        sensitivity_results = {
            "base_ranking": [(score.parcel_id, score.rank) for score in base_scores],
            "weight_variations": {}
        }
        
        # 测试每个权重的变化
        weight_fields = [
            "land_price_weight", "property_tax_weight", "labor_cost_weight",
            "electric_cost_weight", "vacancy_weight", "building_age_weight"
        ]
        
        for field in weight_fields:
            variations = []
            
            # 测试权重增加20%和减少20%的情况
            for multiplier in [0.8, 1.2]:
                test_weights = ScoringWeights()
                
                # 调整目标权重
                original_value = getattr(base_weights, field)
                new_value = original_value * multiplier
                
                # 调整其他权重以保持总和为1
                remaining_weight = 1.0 - new_value
                other_weights_sum = 1.0 - original_value
                
                if other_weights_sum > 0:
                    scale_factor = remaining_weight / other_weights_sum
                    
                    for other_field in weight_fields:
                        if other_field == field:
                            setattr(test_weights, field, new_value)
                        else:
                            other_original = getattr(base_weights, other_field)
                            setattr(test_weights, other_field, other_original * scale_factor)
                    
                    # 验证权重总和
                    try:
                        test_scores = self.score_sites(sites_data, test_weights)
                        ranking_changes = self._calculate_ranking_changes(base_scores, test_scores)
                        
                        variations.append({
                            "multiplier": multiplier,
                            "new_weights": test_weights,
                            "ranking_changes": ranking_changes,
                            "top_3_changed": ranking_changes["top_3_changes"]
                        })
                    except ValueError:
                        # 权重验证失败，跳过
                        continue
            
            sensitivity_results["weight_variations"][field] = variations
        
        return sensitivity_results
    
    def _calculate_ranking_changes(self, base_scores: List[SiteScore], 
                                 test_scores: List[SiteScore]) -> Dict[str, Any]:
        """
        计算排名变化
        """
        base_ranking = {score.parcel_id: score.rank for score in base_scores}
        test_ranking = {score.parcel_id: score.rank for score in test_scores}
        
        changes = []
        for parcel_id in base_ranking:
            base_rank = base_ranking[parcel_id]
            test_rank = test_ranking.get(parcel_id, base_rank)
            change = test_rank - base_rank
            
            if change != 0:
                changes.append({
                    "parcel_id": parcel_id,
                    "base_rank": base_rank,
                    "test_rank": test_rank,
                    "change": change
                })
        
        # 检查前3名是否发生变化
        base_top_3 = [score.parcel_id for score in base_scores[:3]]
        test_top_3 = [score.parcel_id for score in test_scores[:3]]
        top_3_changed = base_top_3 != test_top_3
        
        return {
            "total_changes": len(changes),
            "ranking_changes": changes,
            "top_3_changes": top_3_changed,
            "max_change": max([abs(c["change"]) for c in changes]) if changes else 0
        } 
{"name": "industrial-geo-backend", "version": "1.0.0", "description": "Backend API server for Industrial Geography Development Platform", "main": "server.cjs", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node server.cjs", "dev": "nodemon server.cjs", "test": "echo \"Error: no test specified\" && exit 1", "setup-db": "node setup-aws-database.js", "check-db": "node -e \"require('./config/database').testConnection().then(r => console.log('连接状态:', r ? '成功' : '失败'))\""}, "keywords": ["industrial", "geography", "api", "mysql"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^4.21.1", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "node-fetch": "^2.7.0", "debug": "^4.3.7"}, "devDependencies": {"nodemon": "^3.1.10"}}
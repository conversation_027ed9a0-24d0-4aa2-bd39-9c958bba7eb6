const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');
require('dotenv').config();

const { testConnection, checkTablesExist } = require('./config/database');
const authRoutes = require('./routes/auth');
const projectRoutes = require('./routes/projects');
const siteSelectionRoutes = require('./routes/site-selection');
const qcewRoutes = require('./routes/qcew'); // 保留旧版本QCEW路由
const cpiRoutes = require('./routes/cpi'); // CPI成本上涨计算器路由
const usLaborRoutes = require('./us_labor'); // 新的US Labor API

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors({
  origin: [
    'http://localhost:5173', 
    'http://localhost:5174', 
    'http://localhost:3000',
    'https://first-beta.d1uxuleibeipug.amplifyapp.com'
  ],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/site-selection', siteSelectionRoutes);
app.use('/api/qcew', qcewRoutes); // 旧版本QCEW API (待弃用)
app.use('/api/cpi', cpiRoutes); // CPI成本上涨计算器API
app.use('/api/us-labor', usLaborRoutes); // 新的US Labor API

// AI分析端点 (保留原有功能)
app.post('/api/analyze', async (req, res) => {
  try {
    const { inputText } = req.body;

    if (!inputText || !inputText.trim()) {
      return res.status(400).json({ error: 'Input text is required' });
    }

    // 构建提示词
    const prompt = `Please analyze the following industrial park information and extract key information in JSON format. Extract cities, countries, highways, industries, utilities, and other relevant keywords:

Input text: "${inputText}"

Please respond with a JSON object in this format:
{
  "cities": ["city1", "city2"],
  "countries": ["country1", "country2"],
  "highways": ["highway1", "highway2"],
  "industries": ["industry1", "industry2"],
  "utilities": ["utility1", "utility2"],
  "other": ["keyword1", "keyword2"]
}

Only include items that are explicitly mentioned or clearly implied in the text.`;

    // 调用OpenRouter API
    const response = await fetch(process.env.OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'http://localhost:5174',
        'X-Title': 'AI Site Analyzer',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'tngtech/deepseek-r1t-chimera:free',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    // 尝试解析JSON响应
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const extractedData = JSON.parse(jsonMatch[0]);
        res.json({ success: true, data: extractedData });
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      // 如果JSON解析失败，创建一个基本的结果
      res.json({
        success: true,
        data: {
          cities: [],
          countries: [],
          highways: [],
          industries: [],
          utilities: [],
          other: [aiResponse.substring(0, 100) + '...']
        }
      });
    }

  } catch (error) {
    console.error('AI Analysis error:', error);
    res.status(500).json({
      error: 'Failed to analyze text',
      details: error.message
    });
  }
});

// 健康检查端点
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// 数据库连接测试端点
app.get('/api/db-status', async (req, res) => {
  try {
    const isConnected = await testConnection();
    let tablesInfo = null;
    
    if (isConnected) {
      tablesInfo = await checkTablesExist();
    }
    
    res.json({
      success: true,
      database: {
        status: isConnected ? 'connected' : 'disconnected',
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        tables_ready: tablesInfo
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'AWS RDS 数据库连接测试失败',
      error: error.message
    });
  }
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  });
});

// 全局错误处理中间件
app.use((error, req, res, next) => {
  console.error('全局错误处理:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    ...(process.env.NODE_ENV === 'development' && { error: error.message })
  });
});

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接
    console.log('🔄 测试 AWS RDS 数据库连接...');
    const isConnected = await testConnection();
    
    if (!isConnected) {
      console.error('❌ AWS RDS 数据库连接失败，服务器将继续启动但功能可能受限');
      console.error('🔧 请检查 .env 文件中的数据库配置');
    } else {
      // 检查必要的数据表是否存在
      console.log('🔄 检查数据库表结构...');
      const tablesExist = await checkTablesExist();
      
      if (!tablesExist) {
        console.warn('⚠️  数据库表结构不完整');
        console.log('💡 提示: 运行 "node setup-aws-database.js" 来初始化数据库表');
      }
    }

    app.listen(PORT, () => {
      console.log(`🚀 Industrial Geography Backend Server 已启动`);
      console.log(`📍 服务器地址: http://localhost:${PORT}`);
      console.log(`📊 运行环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🗄️  数据库: AWS RDS MySQL (${process.env.DB_HOST})`);
      console.log('');
      console.log('📡 API 端点:');
      console.log(`   🔗 健康检查: http://localhost:${PORT}/api/health`);
      console.log(`   🗄️  数据库状态: http://localhost:${PORT}/api/db-status`);
      console.log(`   👤 用户认证: http://localhost:${PORT}/api/auth`);
      console.log(`   📁 项目管理: http://localhost:${PORT}/api/projects`);
      console.log(`   🏭 选址分析: http://localhost:${PORT}/api/site-selection`);
      console.log(`   🤖 AI 分析: http://localhost:${PORT}/api/analyze`);
      console.log('');
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

startServer();

module.exports = app;

"""
Regrid API数据模型定义
基于Regrid API文档定义的数据结构
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
import json


@dataclass
class GeometryData:
    """地理几何数据"""
    type: str  # "Polygon", "MultiPolygon"
    coordinates: List[Any]  # GeoJSON coordinates
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "type": self.type,
            "coordinates": self.coordinates
        }


@dataclass
class ParcelData:
    """
    地块数据模型 - 基于Regrid API返回的字段
    """
    # 唯一标识
    ll_uuid: str  # 唯一标识符
    geoid: str  # 地理标识符
    parcelnumb: str  # 地块编号
    
    # 地理空间数据
    geometry: Optional[GeometryData] = None  # GeoJSON格式边界数据
    ll_gisacre: Optional[float] = None  # 面积(英亩)
    ll_gissqft: Optional[float] = None  # 面积(平方英尺)
    
    # 房产信息
    owner: Optional[str] = None  # 业主姓名
    address: Optional[str] = None  # 房产地址
    usps_vacancy: Optional[str] = None  # 空置状态
    saledate: Optional[str] = None  # 销售日期
    saleprice: Optional[float] = None  # 销售价格
    
    # 估值信息
    landval: Optional[float] = None  # 土地估值
    improvval: Optional[float] = None  # 建筑估值
    totalval: Optional[float] = None  # 总估值
    taxamt: Optional[float] = None  # 税额
    
    # 建筑用途
    yearbuilt: Optional[int] = None  # 建筑年份
    ll_bldg_count: Optional[int] = None  # 建筑数量
    zoning: Optional[str] = None  # 区域规划代码
    
    # 行政区划
    county: Optional[str] = None  # 所在县
    state2: Optional[str] = None  # 所在州(2字符缩写)
    qoz: Optional[bool] = None  # 合格机会区状态
    
    # 土地利用分类 (LBCS codes)
    lbcs_activity: Optional[str] = None
    lbcs_function: Optional[str] = None
    lbcs_structure: Optional[str] = None
    lbcs_site: Optional[str] = None
    lbcs_ownership: Optional[str] = None
    
    # 人口普查信息
    census_tract: Optional[str] = None
    census_block: Optional[str] = None
    census_blockgroup: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {}
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, GeometryData):
                    result[key] = {
                        "type": value.type,
                        "coordinates": value.coordinates
                    }
                else:
                    result[key] = value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ParcelData':
        """从字典创建实例"""
        geometry_data = data.get('geometry')
        if geometry_data:
            geometry = GeometryData(
                type=geometry_data.get('type', ''),
                coordinates=geometry_data.get('coordinates', [])
            )
        else:
            geometry = None
            
        return cls(
            ll_uuid=data.get('ll_uuid', ''),
            geoid=data.get('geoid', ''),
            parcelnumb=data.get('parcelnumb', ''),
            geometry=geometry,
            ll_gisacre=data.get('ll_gisacre'),
            ll_gissqft=data.get('ll_gissqft'),
            owner=data.get('owner'),
            address=data.get('address'),
            usps_vacancy=data.get('usps_vacancy'),
            saledate=data.get('saledate'),
            saleprice=data.get('saleprice'),
            landval=data.get('landval'),
            improvval=data.get('improvval'),
            totalval=data.get('totalval'),
            taxamt=data.get('taxamt'),
            yearbuilt=data.get('yearbuilt'),
            ll_bldg_count=data.get('ll_bldg_count'),
            zoning=data.get('zoning'),
            county=data.get('county'),
            state2=data.get('state2'),
            qoz=data.get('qoz'),
            lbcs_activity=data.get('lbcs_activity'),
            lbcs_function=data.get('lbcs_function'),
            lbcs_structure=data.get('lbcs_structure'),
            lbcs_site=data.get('lbcs_site'),
            lbcs_ownership=data.get('lbcs_ownership'),
            census_tract=data.get('census_tract'),
            census_block=data.get('census_block'),
            census_blockgroup=data.get('census_blockgroup')
        )


@dataclass
class PropertyInfo:
    """房产基本信息汇总"""
    parcel_id: str
    area_sqft: float
    area_acres: float
    address: str
    owner: str
    sale_price: Optional[float] = None
    assessed_value: Optional[float] = None
    tax_amount: Optional[float] = None
    year_built: Optional[int] = None
    zoning: Optional[str] = None
    vacancy_status: Optional[str] = None


@dataclass
class ValuationInfo:
    """估值信息汇总"""
    land_value: Optional[float] = None
    improvement_value: Optional[float] = None
    total_assessed_value: Optional[float] = None
    market_value: Optional[float] = None
    tax_assessment_ratio: Optional[float] = None


@dataclass
class SearchFilter:
    """搜索过滤条件"""
    # 地理条件
    state: Optional[str] = None
    county: Optional[str] = None
    city: Optional[str] = None
    
    # 面积条件
    min_area_sqft: Optional[float] = None
    max_area_sqft: Optional[float] = None
    min_area_acres: Optional[float] = None
    max_area_acres: Optional[float] = None
    
    # 价格条件
    min_sale_price: Optional[float] = None
    max_sale_price: Optional[float] = None
    min_assessed_value: Optional[float] = None
    max_assessed_value: Optional[float] = None
    
    # 用途和分区
    zoning: Optional[List[str]] = None
    land_use_codes: Optional[List[str]] = None
    
    # 建筑条件
    min_year_built: Optional[int] = None
    max_year_built: Optional[int] = None
    vacancy_only: Optional[bool] = None
    
    # 其他条件
    qoz_only: Optional[bool] = None  # 只查找机会区
    max_distance_to_point: Optional[Dict[str, Any]] = None  # 距离条件
    
    def to_api_params(self) -> Dict[str, Any]:
        """转换为API查询参数"""
        params = {}
        
        # 添加非空参数
        for key, value in self.__dict__.items():
            if value is not None:
                if isinstance(value, list) and len(value) > 0:
                    params[key] = value
                elif not isinstance(value, list):
                    params[key] = value
                    
        return params 
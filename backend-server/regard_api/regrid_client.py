"""
Regrid API客户端
用于调用Regrid API获取地理空间数据和房产信息
目前使用模拟数据，未来只需要添加API key即可转换为真实数据调用
"""

import json
import sys
import time
import requests
from typing import List, Dict, Any, Optional
from data_models import ParcelData, SearchFilter
from mock_data import MockDataGenerator


class RegridClient:
    """
    Regrid API客户端
    
    用法:
    client = RegridClient(api_key="your_api_key")  # 未来使用真实API key
    parcels = client.search_parcels(search_filter)
    """
    
    def __init__(self, api_key: Optional[str] = None, use_mock_data: bool = True):
        """
        初始化Regrid客户端
        
        Args:
            api_key: Regrid API密钥 (目前为可选，因为使用模拟数据)
            use_mock_data: 是否使用模拟数据 (默认True，未来改为False)
        """
        self.api_key = api_key
        self.use_mock_data = use_mock_data
        self.base_url = "https://app.regrid.com/api/v1"
        self.session = requests.Session()
        
        # 设置请求头
        if api_key:
            self.session.headers.update({
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            })
        
        # 初始化模拟数据生成器
        self.mock_generator = MockDataGenerator()
        
        # API调用统计
        self.api_calls_count = 0
        self.last_call_time = None
        
    def search_parcels(self, search_filter: SearchFilter, limit: int = 100) -> List[ParcelData]:
        """
        搜索地块数据
        
        Args:
            search_filter: 搜索条件
            limit: 返回结果数量限制
            
        Returns:
            List[ParcelData]: 地块数据列表
        """
        self.api_calls_count += 1
        self.last_call_time = time.time()
        
        if self.use_mock_data or not self.api_key:
            # 使用模拟数据
            print("📍 使用模拟数据生成Regrid API响应", file=sys.stderr)
            return self._search_parcels_mock(search_filter, limit)
        else:
            # 调用真实API (未来实现)
            return self._search_parcels_real(search_filter, limit)
    
    def _search_parcels_mock(self, search_filter: SearchFilter, limit: int) -> List[ParcelData]:
        """
        使用模拟数据搜索地块
        """
        # 模拟API延迟
        time.sleep(0.5)
        
        # 基于搜索条件生成模拟数据
        mock_parcels = self.mock_generator.generate_parcels(
            state=search_filter.state,
            county=search_filter.county, 
            min_area_sqft=search_filter.min_area_sqft,
            max_area_sqft=search_filter.max_area_sqft,
            zoning_filter=search_filter.zoning,
            count=min(limit, 50)  # 限制模拟数据数量
        )
        
        print(f"✅ 模拟生成了 {len(mock_parcels)} 个地块数据", file=sys.stderr)
        return mock_parcels
    
    def _search_parcels_real(self, search_filter: SearchFilter, limit: int) -> List[ParcelData]:
        """
        调用真实Regrid API搜索地块
        未来实现，目前返回模拟数据
        """
        # TODO: 实现真实API调用
        # 构建查询参数
        params = search_filter.to_api_params()
        params['limit'] = limit
        
        try:
            # 示例API调用逻辑（未来启用）
            # response = self.session.get(f"{self.base_url}/parcels", params=params)
            # response.raise_for_status()
            # data = response.json()
            
            # 解析响应数据
            # parcels = []
            # for item in data.get('features', []):
            #     parcel = ParcelData.from_dict(item['properties'])
            #     if item.get('geometry'):
            #         parcel.geometry = GeometryData(
            #             type=item['geometry']['type'],
            #             coordinates=item['geometry']['coordinates']
            #         )
            #     parcels.append(parcel)
            
            # return parcels
            
            # 暂时返回模拟数据
            print("⚠️ 真实API调用尚未实现，返回模拟数据", file=sys.stderr)
            return self._search_parcels_mock(search_filter, limit)
            
        except requests.RequestException as e:
            print(f"❌ Regrid API调用失败: {e}", file=sys.stderr)
            # API失败时返回模拟数据作为回退
            return self._search_parcels_mock(search_filter, limit)
    
    def get_parcel_by_id(self, parcel_id: str) -> Optional[ParcelData]:
        """
        根据ID获取单个地块详细信息
        
        Args:
            parcel_id: 地块ID (ll_uuid)
            
        Returns:
            ParcelData: 地块数据，如果未找到返回None
        """
        self.api_calls_count += 1
        self.last_call_time = time.time()
        
        if self.use_mock_data or not self.api_key:
            # 使用模拟数据
            return self.mock_generator.generate_single_parcel(parcel_id)
        else:
            # 调用真实API (未来实现)
            return self._get_parcel_real(parcel_id)
    
    def _get_parcel_real(self, parcel_id: str) -> Optional[ParcelData]:
        """
        调用真实API获取单个地块
        """
        try:
            # TODO: 实现真实API调用
            # response = self.session.get(f"{self.base_url}/parcels/{parcel_id}")
            # response.raise_for_status()
            # data = response.json()
            # return ParcelData.from_dict(data)
            
            # 暂时返回模拟数据
            return self.mock_generator.generate_single_parcel(parcel_id)
            
        except requests.RequestException as e:
            print(f"❌ 获取地块详情失败: {e}", file=sys.stderr)
            return None
    
    def search_by_coordinates(self, lat: float, lng: float, radius_miles: float = 5) -> List[ParcelData]:
        """
        根据坐标和半径搜索地块
        
        Args:
            lat: 纬度
            lng: 经度  
            radius_miles: 搜索半径(英里)
            
        Returns:
            List[ParcelData]: 地块数据列表
        """
        self.api_calls_count += 1
        self.last_call_time = time.time()
        
        if self.use_mock_data or not self.api_key:
            # 使用模拟数据
            return self.mock_generator.generate_parcels_by_location(lat, lng, radius_miles)
        else:
            # 调用真实API (未来实现)
            return self._search_by_coordinates_real(lat, lng, radius_miles)
    
    def _search_by_coordinates_real(self, lat: float, lng: float, radius_miles: float) -> List[ParcelData]:
        """
        调用真实API根据坐标搜索
        """
        try:
            # TODO: 实现真实API调用
            # 使用PostGIS ST_DWithin函数查询
            # params = {
            #     'geometry': f"POINT({lng} {lat})",
            #     'distance': radius_miles * 1609.34,  # 转换为米
            #     'limit': 100
            # }
            # response = self.session.get(f"{self.base_url}/parcels/search", params=params)
            
            # 暂时返回模拟数据
            return self.mock_generator.generate_parcels_by_location(lat, lng, radius_miles)
            
        except requests.RequestException as e:
            print(f"❌ 地理搜索失败: {e}", file=sys.stderr)
            return []
    
    def get_api_usage_stats(self) -> Dict[str, Any]:
        """
        获取API使用统计
        """
        return {
            "total_calls": self.api_calls_count,
            "last_call_time": self.last_call_time,
            "using_mock_data": self.use_mock_data,
            "has_api_key": bool(self.api_key)
        }
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试API连接
        
        Returns:
            Dict: 连接测试结果
        """
        if self.use_mock_data:
            return {
                "status": "success",
                "message": "使用模拟数据模式",
                "mock_data": True,
                "api_key_provided": bool(self.api_key)
            }
        
        try:
            # TODO: 实现真实API连接测试
            # response = self.session.get(f"{self.base_url}/status")
            # response.raise_for_status()
            
            return {
                "status": "success", 
                "message": "API连接正常",
                "mock_data": False,
                "api_key_provided": bool(self.api_key)
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"API连接失败: {str(e)}",
                "mock_data": self.use_mock_data,
                "api_key_provided": bool(self.api_key)
            }


# 全局实例，方便其他模块使用
regrid_client = RegridClient(use_mock_data=True) 
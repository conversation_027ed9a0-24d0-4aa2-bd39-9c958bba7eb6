"""
模拟数据生成器
基于Regrid API数据结构生成真实的测试数据
包含德克萨斯州达拉斯地区的模拟地块数据
"""

import random
import uuid
from typing import List, Optional
from datetime import datetime, timedelta
from data_models import ParcelData, GeometryData


class MockDataGenerator:
    """
    模拟数据生成器
    生成符合Regrid API格式的测试数据
    """
    
    def __init__(self):
        """初始化模拟数据生成器"""
        # 德克萨斯州主要县区
        self.texas_counties = [
            "Dallas County", "Tarrant County", "Collin County", 
            "Denton County", "Rockwall County", "Ellis County"
        ]
        
        # 工业分区代码
        self.industrial_zones = [
            "I-1", "I-2", "I-3", "IM", "IL", "IH", 
            "M-1", "M-2", "MU", "PD-IP", "I-R"
        ]
        
        # 业主名称样本
        self.owner_names = [
            "Dallas Industrial Holdings LLC",
            "Lone Star Properties Inc",
            "Texas Manufacturing Corp",
            "Southwest Industrial Partners",
            "DFW Logistics Holdings", 
            "Metro Dallas Properties",
            "Industrial Park Ventures LLC",
            "Texas Land Development Co",
            "American Industrial REIT",
            "Regional Manufacturing Holdings"
        ]
        
        # 街道名称样本
        self.street_names = [
            "Industrial Blvd", "Manufacturing Dr", "Logistics Way",
            "Commerce St", "Factory Rd", "Business Parkway",
            "Technology Dr", "Distribution Ave", "Corporate Dr"
        ]
        
        # 城市区域样本 (达拉斯都市区)
        self.cities = [
            "Dallas", "Plano", "Irving", "Garland", "Mesquite",
            "Carrollton", "Richardson", "Lewisville", "Allen", "Frisco"
        ]
        
        # 达拉斯地区地理边界 (大致坐标范围)
        self.dallas_bounds = {
            "min_lat": 32.617, "max_lat": 33.017,
            "min_lng": -97.021, "max_lng": -96.462
        }
    
    def generate_parcels(self, 
                        state: Optional[str] = "TX",
                        county: Optional[str] = None, 
                        min_area_sqft: Optional[float] = None,
                        max_area_sqft: Optional[float] = None,
                        zoning_filter: Optional[List[str]] = None,
                        count: int = 20) -> List[ParcelData]:
        """
        生成多个地块数据
        
        Args:
            state: 州代码 (默认TX)
            county: 县名
            min_area_sqft: 最小面积(平方英尺)
            max_area_sqft: 最大面积(平方英尺) 
            zoning_filter: 分区过滤条件
            count: 生成数量
            
        Returns:
            List[ParcelData]: 地块数据列表
        """
        parcels = []
        
        for i in range(count):
            parcel = self._generate_single_parcel_data(
                state=state,
                county=county,
                min_area_sqft=min_area_sqft,
                max_area_sqft=max_area_sqft,
                zoning_filter=zoning_filter,
                index=i
            )
            parcels.append(parcel)
        
        return parcels
    
    def generate_single_parcel(self, parcel_id: Optional[str] = None) -> ParcelData:
        """
        生成单个地块数据
        
        Args:
            parcel_id: 指定的地块ID，如果为None则随机生成
            
        Returns:
            ParcelData: 地块数据
        """
        return self._generate_single_parcel_data(parcel_id=parcel_id)
    
    def generate_parcels_by_location(self, lat: float, lng: float, 
                                   radius_miles: float = 5, 
                                   count: int = 15) -> List[ParcelData]:
        """
        根据坐标位置生成附近的地块数据
        
        Args:
            lat: 纬度
            lng: 经度
            radius_miles: 搜索半径(英里)
            count: 生成数量
            
        Returns:
            List[ParcelData]: 地块数据列表
        """
        parcels = []
        
        # 将半径转换为度数 (大致换算)
        radius_deg = radius_miles / 69.0  # 1度约等于69英里
        
        for i in range(count):
            # 在指定半径内随机生成坐标
            angle = random.uniform(0, 2 * 3.14159)
            distance = random.uniform(0, radius_deg)
            
            parcel_lat = lat + distance * random.uniform(-1, 1)
            parcel_lng = lng + distance * random.uniform(-1, 1)
            
            parcel = self._generate_single_parcel_data(
                index=i,
                center_lat=parcel_lat,
                center_lng=parcel_lng
            )
            parcels.append(parcel)
            
        return parcels
    
    def _generate_single_parcel_data(self, 
                                   state: Optional[str] = "TX",
                                   county: Optional[str] = None,
                                   min_area_sqft: Optional[float] = None,
                                   max_area_sqft: Optional[float] = None,
                                   zoning_filter: Optional[List[str]] = None,
                                   index: int = 0,
                                   parcel_id: Optional[str] = None,
                                   center_lat: Optional[float] = None,
                                   center_lng: Optional[float] = None) -> ParcelData:
        """
        生成单个地块数据的内部方法
        """
        # 生成唯一标识
        if parcel_id:
            ll_uuid = parcel_id
        else:
            ll_uuid = f"TX-DAL-{str(uuid.uuid4())[:8]}-{index:03d}"
        
        geoid = f"48{random.randint(100, 999)}{random.randint(100000, 999999)}"
        parcelnumb = f"R{random.randint(100000, 999999)}"
        
        # 面积生成
        if min_area_sqft and max_area_sqft:
            area_sqft = random.uniform(min_area_sqft, max_area_sqft)
        else:
            area_sqft = random.uniform(50000, 500000)  # 5万到50万平方英尺
        
        area_acres = area_sqft / 43560  # 转换为英亩
        
        # 分区选择
        if zoning_filter and len(zoning_filter) > 0:
            zoning = random.choice(zoning_filter)
        else:
            zoning = random.choice(self.industrial_zones)
        
        # 县区选择
        if county:
            selected_county = county
        else:
            selected_county = random.choice(self.texas_counties)
        
        # 生成地理坐标
        if center_lat and center_lng:
            lat = center_lat
            lng = center_lng
        else:
            lat = random.uniform(self.dallas_bounds["min_lat"], self.dallas_bounds["max_lat"])
            lng = random.uniform(self.dallas_bounds["min_lng"], self.dallas_bounds["max_lng"])
        
        # 生成地块几何 (简单的矩形)
        geometry = self._generate_polygon_geometry(lat, lng, area_sqft)
        
        # 价格生成 (基于面积和位置)
        base_price_per_sqft = random.uniform(8, 25)  # $8-25 per sqft
        land_price = area_sqft * base_price_per_sqft
        
        # 估值信息
        landval = land_price * random.uniform(0.8, 1.2)
        improvval = random.uniform(0, land_price * 0.3) if random.random() > 0.3 else 0
        totalval = landval + improvval
        
        # 税额 (约为总估值的1-3%)
        tax_rate = random.uniform(0.01, 0.03)
        taxamt = totalval * tax_rate
        
        # 销售价格 (可能为空)
        saleprice = land_price * random.uniform(0.9, 1.3) if random.random() > 0.4 else None
        
        # 销售日期
        if saleprice:
            days_ago = random.randint(30, 730)  # 1个月到2年前
            saledate = (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
        else:
            saledate = None
        
        # 建筑信息
        if random.random() > 0.6:  # 60%概率有建筑
            yearbuilt = random.randint(1980, 2020)
            ll_bldg_count = random.randint(1, 3)
        else:
            yearbuilt = None
            ll_bldg_count = 0
        
        # 地址生成
        street_num = random.randint(1000, 9999)
        street_name = random.choice(self.street_names)
        city = random.choice(self.cities)
        address = f"{street_num} {street_name}, {city}, TX"
        
        # 业主
        owner = random.choice(self.owner_names)
        
        # 空置状态
        usps_vacancy = "Y" if random.random() > 0.7 else "N"
        
        # LBCS代码 (土地利用分类)
        lbcs_activity = "2000"  # Manufacturing
        lbcs_function = "2100"  # Light Manufacturing
        lbcs_structure = random.choice(["1000", "2000", "5000"])
        lbcs_site = "1000"
        lbcs_ownership = random.choice(["1000", "2000"])
        
        # 人口普查数据
        census_tract = f"48{random.randint(100, 999)}.{random.randint(10, 99)}"
        census_block = f"{random.randint(1000, 9999)}"
        census_blockgroup = f"{random.randint(1, 9)}"
        
        # 机会区状态
        qoz = random.random() > 0.8  # 20%概率是机会区
        
        return ParcelData(
            ll_uuid=ll_uuid,
            geoid=geoid,
            parcelnumb=parcelnumb,
            geometry=geometry,
            ll_gisacre=round(area_acres, 2),
            ll_gissqft=round(area_sqft, 0),
            owner=owner,
            address=address,
            usps_vacancy=usps_vacancy,
            saledate=saledate,
            saleprice=saleprice,
            landval=round(landval, 2),
            improvval=round(improvval, 2),
            totalval=round(totalval, 2),
            taxamt=round(taxamt, 2),
            yearbuilt=yearbuilt,
            ll_bldg_count=ll_bldg_count,
            zoning=zoning,
            county=selected_county,
            state2=state,
            qoz=qoz,
            lbcs_activity=lbcs_activity,
            lbcs_function=lbcs_function,
            lbcs_structure=lbcs_structure,
            lbcs_site=lbcs_site,
            lbcs_ownership=lbcs_ownership,
            census_tract=census_tract,
            census_block=census_block,
            census_blockgroup=census_blockgroup
        )
    
    def _generate_polygon_geometry(self, center_lat: float, center_lng: float, 
                                 area_sqft: float) -> GeometryData:
        """
        生成地块的多边形几何数据
        
        Args:
            center_lat: 中心纬度
            center_lng: 中心经度
            area_sqft: 面积(平方英尺)
            
        Returns:
            GeometryData: 几何数据
        """
        # 计算大致的边长 (假设正方形)
        side_length_ft = (area_sqft ** 0.5)
        
        # 转换为度数 (粗略换算: 1度约69英里, 1英里约5280英尺)
        side_length_deg = side_length_ft / (69 * 5280)
        
        # 生成矩形的四个角点
        half_side = side_length_deg / 2
        
        coordinates = [[
            [center_lng - half_side, center_lat - half_side],  # 左下
            [center_lng + half_side, center_lat - half_side],  # 右下
            [center_lng + half_side, center_lat + half_side],  # 右上
            [center_lng - half_side, center_lat + half_side],  # 左上
            [center_lng - half_side, center_lat - half_side]   # 闭合到起点
        ]]
        
        return GeometryData(
            type="Polygon",
            coordinates=coordinates
        )
    
    def generate_cost_parameters(self) -> dict:
        """
        生成成本计算参数 (模拟德克萨斯州达拉斯地区的实际数据)
        
        Returns:
            dict: 成本参数字典
        """
        return {
            # 电价 (德克萨斯州平均电价约12-15美分/kWh)
            "electric_rate_cents_kwh": random.uniform(11.5, 16.8),
            
            # 水价 (达拉斯地区约$3-5/1000加仑)
            "water_rate_cents_gallon": random.uniform(0.3, 0.5),
            
            # 房产税厘税率 (德克萨斯州约2.0-2.5%)
            "millage_rate": random.uniform(20.0, 25.0),  # 千分之20-25
            
            # 平均时薪 (德克萨斯州制造业约$15-25/小时)
            "avg_hourly_wage": random.uniform(15.0, 25.0),
            
            # 默认用电量 (kWh/年)
            "default_annual_kwh": 1800000,
            
            # 默认用水量 (加仑/年) 
            "default_annual_gallons": 3000000,
            
            # 交易杂费系数
            "closing_fee_rate": 0.02,  # 2%
            
            # 运营期年数
            "operating_years": 5
        } 
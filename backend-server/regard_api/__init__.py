"""
Regrid API包
提供与Regrid地理数据API的集成功能
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入模块 - 使用直接导入，避免在这里触发循环依赖
__version__ = "1.0.0"

# 延迟导入以避免循环依赖问题
def get_regrid_client():
    from regard_api.regrid_client import RegridClient
    return RegridClient

def get_mock_data_generator():
    from regard_api.mock_data import MockDataGenerator
    return MockDataGenerator

# 只在直接使用时导入具体类
__all__ = [
    'get_regrid_client',
    'get_mock_data_generator',
    '__version__'
] 
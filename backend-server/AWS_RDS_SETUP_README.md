# Industrial Geography Backend - AWS RDS 集成指南

## 概述

本后端服务已成功重构并连接到 AWS RDS MySQL 数据库，实现了完整的用户认证和项目管理API系统。

## 🗄️ 数据库配置

### AWS RDS 连接信息
- **主机地址**: platform-core-db.cdcwmywi0frz.us-east-2.rds.amazonaws.com
- **端口**: 3306
- **数据库名**: platform_core_db
- **用户名**: admin
- **密码**: industrialgeodev

### 环境变量配置
所有敏感信息已安全存储在 `.env` 文件中：

```env
# AWS RDS MySQL 数据库配置
DB_HOST=platform-core-db.cdcwmywi0frz.us-east-2.rds.amazonaws.com
DB_USER=admin
DB_PASSWORD=industrialgeodev
DB_PORT=3306
DB_NAME=platform_core_db

# JWT 认证配置
JWT_SECRET=Kj9$mP2@vX8#qR5*wN7&bC3!eH6^yL4%tZ1+fA9-nS0*dG8@rQ2#vM5&xB7!
JWT_EXPIRES_IN=7d
```

## 🚀 快速启动

### 1. 安装依赖
```bash
cd backend-server
npm install
```

### 2. 初始化数据库
```bash
# 方法1: 使用npm脚本
npm run setup-db

# 方法2: 直接运行脚本
node setup-aws-database.js
```

### 3. 验证系统
```bash
# 运行完整的系统测试
node test-system.js

# 或只检查数据库连接
npm run check-db
```

### 4. 启动服务器
```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

## 📡 API 端点

### 用户认证 API

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "username": "username",
  "preferred_language": "zh"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "loginIdentifier": "<EMAIL>",
  "password": "password123"
}
```

#### 获取当前用户信息
```http
GET /api/auth/me
Authorization: Bearer <JWT_TOKEN>
```

### 项目管理 API

#### 创建项目（受保护的接口）
```http
POST /api/projects
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "project_name": "我的工业园区项目",
  "description": "项目描述",
  "natural_language_input": "自然语言描述需求"
}
```

#### 获取用户项目列表
```http
GET /api/projects
Authorization: Bearer <JWT_TOKEN>
```

#### 获取单个项目
```http
GET /api/projects/{project_id}
Authorization: Bearer <JWT_TOKEN>
```

#### 更新项目
```http
PUT /api/projects/{project_id}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "project_name": "更新后的项目名称",
  "description": "更新后的描述"
}
```

#### 删除项目
```http
DELETE /api/projects/{project_id}
Authorization: Bearer <JWT_TOKEN>
```

### 系统状态 API

#### 健康检查
```http
GET /api/health
```

#### 数据库状态
```http
GET /api/db-status
```

## 🔐 安全特性

### JWT 认证机制
- 使用 JWT (JSON Web Token) 进行用户认证
- Token 有效期：7天
- 自动提取用户信息并验证权限

### 密码安全
- 使用 bcryptjs 进行密码哈希加密
- Salt rounds: 8（平衡安全性和性能）

### SQL 注入防护
- 所有数据库查询使用参数化查询（prepared statements）
- 输入验证使用 express-validator

### 数据库连接安全
- 连接池管理，避免连接泄露
- 自动重连机制
- 连接超时保护

## 🗃️ 数据库结构

### users 表
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  username VARCHAR(100) UNIQUE NOT NULL,
  preferred_language ENUM('en', 'zh') DEFAULT 'en',
  active_subscription_status ENUM('free', 'basic', 'premium') DEFAULT 'free',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### projects 表
```sql
CREATE TABLE projects (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  project_name VARCHAR(255) NOT NULL,
  description TEXT,
  natural_language_input TEXT,
  structured_parameters JSON,
  status ENUM('active', 'archived', 'completed') DEFAULT 'active',
  isFavorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## 🛠️ 管理脚本

### 可用的 npm 脚本
```bash
# 启动开发服务器
npm run dev

# 启动生产服务器
npm start

# 初始化数据库表
npm run setup-db

# 检查数据库连接
npm run check-db
```

### 直接运行的脚本
```bash
# 完整系统测试
node test-system.js

# 数据库初始化
node setup-aws-database.js
```

## 🔍 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查环境变量
cat .env

# 测试网络连接
ping platform-core-db.cdcwmywi0frz.us-east-2.rds.amazonaws.com

# 检查数据库状态
npm run check-db
```

#### 2. 表不存在错误
```bash
# 重新初始化数据库
npm run setup-db
```

#### 3. JWT 认证失败
- 确认 JWT_SECRET 在 .env 文件中正确设置
- 检查 Token 格式：`Authorization: Bearer <token>`
- 验证 Token 是否过期

### 日志查看
服务器启动时会显示详细的连接信息：
```
🚀 Industrial Geography Backend Server 已启动
📍 服务器地址: http://localhost:3001
📊 运行环境: development
🗄️  数据库: AWS RDS MySQL (platform-core-db.cdcwmywi0frz.us-east-2.rds.amazonaws.com)

📡 API 端点:
   🔗 健康检查: http://localhost:3001/api/health
   🗄️  数据库状态: http://localhost:3001/api/db-status
   👤 用户认证: http://localhost:3001/api/auth
   📁 项目管理: http://localhost:3001/api/projects
```

## 📝 开发说明

### 权限逻辑
1. **公开接口**: 用户注册、登录、健康检查
2. **受保护接口**: 项目管理相关API需要JWT认证
3. **自动权限检查**: 中间件自动验证用户身份，确保用户只能访问自己的数据

### 数据关联
- 每个项目都通过 `user_id` 字段与创建它的用户关联
- 删除用户时会自动删除其所有项目（CASCADE）
- 支持项目收藏功能和状态管理

### 扩展性
- 模块化设计，易于添加新的API端点
- 数据库连接池支持高并发
- JWT 认证支持无状态扩展

## 🎉 完成状态

✅ **已完成的功能：**
- AWS RDS MySQL 数据库连接
- 用户注册和登录API
- JWT 认证中间件
- 项目管理 CRUD API
- 安全的密码哈希存储
- SQL 注入防护
- 自动化测试脚本
- 完整的错误处理

🚀 **系统已准备就绪，可以开始使用！** 
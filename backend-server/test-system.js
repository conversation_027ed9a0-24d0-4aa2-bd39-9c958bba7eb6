const { testConnection, executeQuery, executeQuerySingle } = require('./config/database');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

// 测试配置
const testUser = {
  email: '<EMAIL>',
  username: 'testuser',
  password: 'test123456'
};

const testProject = {
  project_name: '测试工业园区项目',
  description: '这是一个用于测试的工业园区选址项目',
  natural_language_input: '我需要在中国东部地区寻找适合制造业的工业园区'
};

// 系统测试函数
const runSystemTests = async () => {
  console.log('🧪 开始系统功能测试...\n');
  
  try {
    // 1. 数据库连接测试
    console.log('1️⃣ 测试数据库连接...');
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('数据库连接失败');
    }
    console.log('✅ 数据库连接正常\n');
    
    // 2. 清理测试数据（如果存在）
    console.log('2️⃣ 清理之前的测试数据...');
    
    // 先查找要删除的用户ID
    const existingUser = await executeQuerySingle('SELECT id FROM users WHERE email = ? OR username = ?', [testUser.email, testUser.username]);
    
    if (existingUser) {
      // 删除用户的项目
      await executeQuery('DELETE FROM projects WHERE user_id = ?', [existingUser.id]);
      // 删除用户
      await executeQuery('DELETE FROM users WHERE id = ?', [existingUser.id]);
      console.log(`🗑️  删除了现有的测试用户 (ID: ${existingUser.id})`);
    }
    
    console.log('✅ 测试数据清理完成\n');
    
    // 3. 测试用户注册
    console.log('3️⃣ 测试用户注册功能...');
    const hashedPassword = await bcrypt.hash(testUser.password, 8);
    const registerResult = await executeQuery(
      `INSERT INTO users (email, password_hash, username, preferred_language, created_at, last_login_at, active_subscription_status)
       VALUES (?, ?, ?, 'zh', NOW(), NOW(), 'free')`,
      [testUser.email, hashedPassword, testUser.username]
    );
    
    if (!registerResult.insertId) {
      throw new Error('用户注册失败');
    }
    
    const userId = registerResult.insertId;
    console.log(`✅ 用户注册成功，用户ID: ${userId}\n`);
    
    // 4. 测试用户登录
    console.log('4️⃣ 测试用户登录功能...');
    const user = await executeQuerySingle(
      'SELECT id, email, username, password_hash FROM users WHERE email = ?',
      [testUser.email]
    );
    
    if (!user) {
      throw new Error('找不到注册的用户');
    }
    
    const isPasswordValid = await bcrypt.compare(testUser.password, user.password_hash);
    if (!isPasswordValid) {
      throw new Error('密码验证失败');
    }
    
    const token = jwt.sign(
      { userId: user.id },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    console.log('✅ 用户登录成功');
    console.log(`🔑 JWT Token: ${token.substring(0, 50)}...\n`);
    
    // 5. 测试JWT令牌验证
    console.log('5️⃣ 测试JWT令牌验证...');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    if (decoded.userId !== user.id) {
      throw new Error('JWT令牌验证失败');
    }
    console.log('✅ JWT令牌验证成功\n');
    
    // 6. 测试项目创建
    console.log('6️⃣ 测试项目创建功能...');
    const projectResult = await executeQuery(
      `INSERT INTO projects (user_id, project_name, description, natural_language_input, structured_parameters, status)
       VALUES (?, ?, ?, ?, '{}', 'active')`,
      [userId, testProject.project_name, testProject.description, testProject.natural_language_input]
    );
    
    if (!projectResult.insertId) {
      throw new Error('项目创建失败');
    }
    
    const projectId = projectResult.insertId;
    console.log(`✅ 项目创建成功，项目ID: ${projectId}\n`);
    
    // 7. 测试项目查询
    console.log('7️⃣ 测试项目查询功能...');
    const projects = await executeQuery(
      'SELECT * FROM projects WHERE user_id = ?',
      [userId]
    );
    
    if (projects.length === 0) {
      throw new Error('项目查询失败');
    }
    
    console.log(`✅ 项目查询成功，找到 ${projects.length} 个项目\n`);
    
    // 8. 测试数据关联性
    console.log('8️⃣ 测试数据关联性...');
    const projectWithUser = await executeQuerySingle(
      `SELECT p.*, u.username, u.email 
       FROM projects p 
       JOIN users u ON p.user_id = u.id 
       WHERE p.id = ?`,
      [projectId]
    );
    
    if (!projectWithUser || projectWithUser.email !== testUser.email) {
      throw new Error('数据关联性测试失败');
    }
    
    console.log('✅ 数据关联性测试成功\n');
    
    // 9. 清理测试数据
    console.log('9️⃣ 清理测试数据...');
    await executeQuery('DELETE FROM projects WHERE user_id = ?', [userId]);
    await executeQuery('DELETE FROM users WHERE id = ?', [userId]);
    console.log('✅ 测试数据清理完成\n');
    
    console.log('🎉 所有系统测试通过！');
    console.log('📊 测试摘要:');
    console.log('   ✅ 数据库连接正常');
    console.log('   ✅ 用户注册功能正常');
    console.log('   ✅ 用户登录功能正常');
    console.log('   ✅ JWT认证功能正常');
    console.log('   ✅ 项目管理功能正常');
    console.log('   ✅ 数据关联性正常');
    console.log('\n🚀 系统已准备就绪，可以开始使用！');
    
  } catch (error) {
    console.error('❌ 系统测试失败:', error.message);
    console.error('🔧 错误详情:', error);
    throw error;
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  runSystemTests()
    .then(() => {
      console.log('\n✅ 系统测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 系统测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  runSystemTests
}; 
/**
 * 🏭 QCEW CSV数据服务 - 已禁用
 * 由于CSV文件过大已被删除，此服务现在返回适当的错误信息
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

/**
 * 📊 QCEW数据服务 - 已禁用
 * 原CSV文件因过大已被删除，所有方法现在返回适当的错误信息
 */
class QCEWDataService {
    constructor() {
        this.csvFiles = []; // 清空文件列表
        this.data = new Map();
        this.isLoaded = false;
        this.isDisabled = true; // 标记服务已禁用
        this.loadStartTime = null;
        this.loadedYears = new Set();
        this.totalRecords = 0;
    }

    /**
     * 🔄 加载所有年份的QCEW数据 - 已禁用
     */
    async loadData() {
        console.log('⚠️  QCEW数据服务已禁用 - CSV文件因过大已被删除');
        return { 
            success: false, 
            error: 'QCEW数据服务不可用',
            message: '由于数据文件过大，QCEW服务已被禁用。请使用其他数据源。',
            disabled: true
        };
    }

    /**
     * 🔍 根据地区代码获取QCEW数据（已禁用）
     */
    getDataByAreaCode(areaCode, options = {}) {
        throw new Error('QCEW数据服务不可用 - CSV文件已被删除');
    }

    /**
     * 📅 获取地区可用年份（已禁用）
     */
    getAvailableYearsForArea(areaCode, options = {}) {
        return [];
    }

    /**
     * 📊 获取统计信息（已禁用）
     */
    getStats() {
        return {
            disabled: true,
            totalRecords: 0,
            totalAreas: 0,
            loadedYears: [],
            message: 'QCEW数据服务已禁用'
        };
    }

    /**
     * 📋 获取可用地区代码（已禁用）
     */
    getAvailableAreaCodes(limit = 200) {
        return [];
    }

    /**
     * 📈 按年份获取地区统计（已禁用）
     */
    getAreaStatsByYear() {
        return {};
    }

    /**
     * 🎯 获取最佳可用年份（已禁用）
     */
    getBestAvailableYear(areaCode, preferredYear = null) {
        return null;
    }

    // 保留原有的私有方法但标记为已禁用
    async loadSingleCSV(csvPath, year) {
        throw new Error('QCEW数据服务已禁用');
    }

    parseQCEWRecord(row) {
        return null;
    }

    parseNumeric(value) {
        return null;
    }
}

// 创建全局实例
const qcewDataService = new QCEWDataService();

module.exports = qcewDataService; 
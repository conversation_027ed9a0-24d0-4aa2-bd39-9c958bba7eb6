const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

/**
 * 🏙️ BLS地区代码查询服务
 * 基于area_titles.csv文件的地区名称到代码转换
 */
class AreaCodeService {
  constructor() {
    this.areaData = new Map(); // area_code -> area_title 
    this.isLoaded = false;
    this.loadPromise = null;
  }

  /**
   * 加载area_titles.csv数据
   */
  async loadAreaData() {
    if (this.isLoaded) return;
    if (this.loadPromise) return this.loadPromise;

    this.loadPromise = new Promise((resolve, reject) => {
      const csvPath = path.join(__dirname, '../../../public/data/area_titles.csv');
      console.log('📍 正在加载地区代码数据:', csvPath);

      if (!fs.existsSync(csvPath)) {
        reject(new Error(`Area titles CSV file not found: ${csvPath}`));
        return;
      }

      const results = [];
      fs.createReadStream(csvPath)
        .pipe(csv())
        .on('data', (data) => {
          // CSV格式: "area_fips","area_title"
          const areaCode = data.area_fips?.replace(/['"]/g, '').trim();
          const areaTitle = data.area_title?.replace(/['"]/g, '').trim();
          
          if (areaCode && areaTitle) {
            this.areaData.set(areaCode, areaTitle);
          }
        })
        .on('end', () => {
          this.isLoaded = true;
          console.log(`✅ 地区代码数据加载完成，共 ${this.areaData.size} 个地区`);
          resolve();
        })
        .on('error', (error) => {
          console.error('❌ 加载地区代码数据失败:', error);
          reject(error);
        });
    });

    return this.loadPromise;
  }

  /**
   * 根据城市名称查找地区代码
   * @param {string} cityName - 城市名称（如 "Los Angeles", "New York"）
   * @param {string} state - 州名（可选，如 "California", "CA"）
   * @returns {Array} 匹配的地区列表
   */
  async findAreaCodes(cityName, state = null) {
    await this.loadAreaData();

    const searchTerm = cityName.toLowerCase().trim();
    const stateSearchTerm = state ? state.toLowerCase().trim() : null;
    const matches = [];

    // 遍历所有地区，查找匹配项
    for (const [areaCode, areaTitle] of this.areaData.entries()) {
      const titleLower = areaTitle.toLowerCase();
      
      // 基本匹配：城市名称包含在地区标题中
      let isMatch = titleLower.includes(searchTerm);
      
      // 如果指定了州，也需要匹配州
      if (isMatch && stateSearchTerm) {
        isMatch = titleLower.includes(stateSearchTerm) || 
                 titleLower.includes(this.normalizeState(stateSearchTerm));
      }

      if (isMatch) {
        // 计算匹配度得分
        const score = this.calculateMatchScore(searchTerm, titleLower, areaCode);
        
        matches.push({
          areaCode,
          areaTitle,
          score,
          // 判断是否为县级代码（5位数字）
          isCounty: /^\d{5}$/.test(areaCode),
          // 判断是否为州级代码（2位数字 + 000）
          isState: /^\d{2}000$/.test(areaCode),
          // 判断是否为MSA代码（C开头）
          isMSA: areaCode.startsWith('C'),
          // 判断是否为CSA代码（CS开头）
          isCSA: areaCode.startsWith('CS')
        });
      }
    }

    // 按匹配度排序，优先县级代码
    matches.sort((a, b) => {
      // 首先按类型排序：县级 > MSA > 州级 > CSA
      const typeScore = (item) => {
        if (item.isCounty) return 4;
        if (item.isMSA) return 3;
        if (item.isState) return 2;
        if (item.isCSA) return 1;
        return 0;
      };

      const typeDiff = typeScore(b) - typeScore(a);
      if (typeDiff !== 0) return typeDiff;
      
      // 然后按匹配度排序
      return b.score - a.score;
    });

    return matches.slice(0, 10); // 最多返回10个结果
  }

  /**
   * 计算匹配度得分
   */
  calculateMatchScore(searchTerm, titleLower, areaCode) {
    let score = 0;
    
    // 精确匹配得分最高
    if (titleLower.includes(searchTerm + ' county') || 
        titleLower.includes(searchTerm + ' city')) {
      score += 100;
    }
    
    // 开头匹配得分较高
    if (titleLower.startsWith(searchTerm)) {
      score += 50;
    }
    
    // 包含匹配基础分
    if (titleLower.includes(searchTerm)) {
      score += 10;
    }
    
    // 县级代码优先
    if (/^\d{5}$/.test(areaCode)) {
      score += 20;
    }
    
    // MSA代码次优先
    if (areaCode.startsWith('C') && !areaCode.startsWith('CS')) {
      score += 15;
    }

    return score;
  }

  /**
   * 标准化州名
   */
  normalizeState(state) {
    const stateMap = {
      'california': 'ca',
      'new york': 'ny',
      'texas': 'tx',
      'florida': 'fl',
      'illinois': 'il',
      'pennsylvania': 'pa',
      'ohio': 'oh',
      'georgia': 'ga',
      'north carolina': 'nc',
      'michigan': 'mi'
    };
    
    return stateMap[state.toLowerCase()] || state;
  }

  /**
   * 获取特定地区代码的详细信息
   */
  async getAreaInfo(areaCode) {
    await this.loadAreaData();
    const areaTitle = this.areaData.get(areaCode);
    
    if (!areaTitle) {
      return null;
    }

    return {
      areaCode,
      areaTitle,
      isCounty: /^\d{5}$/.test(areaCode),
      isState: /^\d{2}000$/.test(areaCode),
      isMSA: areaCode.startsWith('C') && !areaCode.startsWith('CS'),
      isCSA: areaCode.startsWith('CS')
    };
  }

  /**
   * 获取推荐的主要城市列表
   */
  getRecommendedCities() {
    return [
      { name: 'Los Angeles', state: 'California', areaCode: '06037' },
      { name: 'New York', state: 'New York', areaCode: '36061' },
      { name: 'Chicago', state: 'Illinois', areaCode: '17031' },
      { name: 'Houston', state: 'Texas', areaCode: '48201' },
      { name: 'Phoenix', state: 'Arizona', areaCode: '04013' },
      { name: 'Philadelphia', state: 'Pennsylvania', areaCode: '42101' },
      { name: 'San Antonio', state: 'Texas', areaCode: '48029' },
      { name: 'San Diego', state: 'California', areaCode: '06073' },
      { name: 'Dallas', state: 'Texas', areaCode: '48113' },
      { name: 'San Jose', state: 'California', areaCode: '06085' }
    ];
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    return {
      loaded: this.isLoaded,
      totalAreas: this.areaData.size,
      memoryUsage: process.memoryUsage()
    };
  }
}

// 创建单例实例
const areaCodeService = new AreaCodeService();

module.exports = areaCodeService; 
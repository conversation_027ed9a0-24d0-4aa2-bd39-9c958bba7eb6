/*
 * 🗂️ LAUS Data Service
 * Local Area Unemployment Statistics (LAUS) 数据服务 - 基于BLS Public Data API v2
 *
 * 功能：
 * 1. 根据县级FIPS代码构建LAUS系列ID
 * 2. 调用BLS API获取劳动力总量 (Labor Force) 与 已就业人数 (Employment)
 * 3. 计算可招聘余量 AVAILABLE_LABOR = LAUS_LABOR_FORCE − LAUS_EMPLOYED
 */

const fetch = require('node-fetch');
require('dotenv').config();

// 常量定义
const LAUS_CONSTANTS = {
  API_URL: 'https://api.bls.gov/publicAPI/v2/timeseries/data',
  // 数据类型代码 (Datatype Code)
  DATATYPE: {
    LAUS_LABOR_FORCE: '006', // Labor Force
    LAUS_EMPLOYED: '005',   // Employment
    LAUS_UNEMPLOYED: '003', // Unemployment (备用)
    LAUS_UNEMP_RATE: '004'  // Unemployment Rate (备用)
  },
  // 默认查询年份范围
  DEFAULT_YEAR_RANGE: {
    start: new Date().getFullYear() - 1, // 前一年
    end: new Date().getFullYear()        // 当年
  },
  // API请求配置
  REQUEST_CONFIG: {
    timeout: 30000, // 30秒超时
    retries: 3      // 重试3次
  }
};

/**
 * 构建LAUS系列ID
 * 规则: LAUCN + countyFips(5位) + 0000000 + datatypeCode(3位)
 * 参考: https://download.bls.gov/pub/time.series/la/la.txt
 * 实际格式例子: LAUCN060370000000006 (洛杉矶县劳动力总量)
 * @param {string} countyFips 5位FIPS县级代码
 * @param {string} datatypeCode 3位数据类型代码
 * @returns {string} 完整系列ID
 */
function buildSeriesId(countyFips, datatypeCode) {
  // 填充countyFips到5位
  const fips = countyFips.toString().padStart(5, '0');
  // LAUS格式: LAUCN + FIPS(5) + 0000000 + datatype(3)
  return `LAUCN${fips}0000000${datatypeCode}`;
}

/**
 * 带重试机制的fetch请求
 * @param {string} url 请求URL
 * @param {object} options 请求选项
 * @param {number} retries 剩余重试次数
 * @returns {Promise} 响应
 */
async function fetchWithRetry(url, options, retries = LAUS_CONSTANTS.REQUEST_CONFIG.retries) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), LAUS_CONSTANTS.REQUEST_CONFIG.timeout);
    
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    if (retries > 0 && (error.name === 'AbortError' || error.code === 'ETIMEDOUT')) {
      console.log(`BLS API请求失败，还有 ${retries} 次重试机会...`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒后重试
      return fetchWithRetry(url, options, retries - 1);
    }
    throw error;
  }
}

/**
 * 发送BLS公共API请求
 * @param {Array<string>} seriesIds 系列ID数组
 * @param {string|number} startYear 开始年份
 * @param {string|number} endYear 结束年份
 */
async function fetchBlsTimeseries(seriesIds, startYear, endYear) {
  const body = {
    seriesid: seriesIds,
    startyear: startYear.toString(),
    endyear: endYear.toString()
  };

  // 如果环境变量提供了BLS_API_KEY，则附加
  if (process.env.BLS_API_KEY) {
    body.registrationKey = process.env.BLS_API_KEY;
  }

  const response = await fetchWithRetry(LAUS_CONSTANTS.API_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'IndustrialGeoDev/1.0'
    },
    body: JSON.stringify(body)
  });

  if (!response.ok) {
    throw new Error(`BLS API HTTP错误: ${response.status} ${response.statusText}`);
  }

  const json = await response.json();
  
  if (json.status !== 'REQUEST_SUCCEEDED') {
    const errorMessages = json.message?.join('; ') || 'Unknown error';
    throw new Error(`BLS API返回错误: ${errorMessages}`);
  }

  return json.Results?.series || [];
}

/**
 * 解析系列数据，返回指定年份最新月份的数值
 * @param {object} seriesItem BLS返回的单个series对象
 * @param {string|number} year 目标年份
 * @returns {number|null}
 */
function extractLatestValueForYear(seriesItem, year) {
  if (!seriesItem || !Array.isArray(seriesItem.data)) {
    console.log(`❌ 无效的系列数据结构: ${seriesItem?.seriesID}`);
    return null;
  }
  
  const yearStr = year.toString();
  console.log(`🔍 查找年份 ${yearStr} 的数据，总数据点: ${seriesItem.data.length}`);

  // 过滤目标年份数据并按月份降序排序 (M12 -> M01)
  const yearData = seriesItem.data.filter(d => d.year === yearStr);
  console.log(`📅 找到 ${yearStr} 年的数据点: ${yearData.length}`);
  
  if (yearData.length === 0) {
    console.log(`❌ 没有找到 ${yearStr} 年的数据`);
    // 列出所有可用年份用于调试
    const availableYears = [...new Set(seriesItem.data.map(d => d.year))];
    console.log(`📊 可用年份: ${availableYears.join(', ')}`);
    return null;
  }

  // 数据已按API默认月份降序，但保险起见排序
  yearData.sort((a, b) => b.period.localeCompare(a.period));
  const latest = yearData[0];
  console.log(`✅ 使用最新数据: ${latest.year}-${latest.period} = ${latest.value}`);
  
  const numValue = Number(latest.value);
  if (isNaN(numValue)) {
    console.log(`❌ 无法解析数值: "${latest.value}"`);
    return null;
  }
  
  return numValue;
}

/**
 * 获取指定县级劳动力与就业数据，并计算可招聘余量
 * @param {string} countyFips 5位县级FIPS
 * @param {object} [options]
 * @param {number} [options.year] 指定年份，默认当年
 * @returns {Promise<object>} 计算结果
 */
async function getAvailableLaborByCounty(countyFips, options = {}) {
  const { year = 2023 } = options; // 默认使用2023年数据

  try {
    const laborForceId = buildSeriesId(countyFips, LAUS_CONSTANTS.DATATYPE.LAUS_LABOR_FORCE);
    const employedId = buildSeriesId(countyFips, LAUS_CONSTANTS.DATATYPE.LAUS_EMPLOYED);

    console.log(`🔍 查询县级FIPS ${countyFips} 的LAUS数据 (${year}年)`);
    console.log(`📊 系列ID: ${laborForceId}, ${employedId}`);
    
    const series = await fetchBlsTimeseries([laborForceId, employedId], year, year);
    console.log(`📈 BLS返回 ${series.length} 个系列`);

    // 映射系列ID到数值
    const valuesMap = {};
    for (const s of series) {
      console.log(`🔄 处理系列 ${s.seriesID}，数据点数量: ${s.data?.length || 0}`);
      const val = extractLatestValueForYear(s, year);
      if (val != null) {
        valuesMap[s.seriesID] = val;
        console.log(`✅ 系列 ${s.seriesID} 最新值: ${val.toLocaleString()}`);
      } else {
        console.log(`❌ 系列 ${s.seriesID} 无可用数据`);
      }
    }

    const laborForce = valuesMap[laborForceId] ?? null;
    const employed = valuesMap[employedId] ?? null;

    console.log(`📊 最终结果: 劳动力=${laborForce?.toLocaleString()}, 就业=${employed?.toLocaleString()}`);

    if (laborForce == null || employed == null) {
      return {
        success: false,
        message: `县级FIPS ${countyFips} 在 ${year}年 没有可用的LAUS数据。可能原因：1) 该地区代码不存在 2) 该年份数据尚未发布 3) 系列ID构建错误`,
        data: {
          county_fips: countyFips,
          year,
          labor_force_series_id: laborForceId,
          employed_series_id: employedId,
          labor_force_value: laborForce,
          employed_value: employed,
          available_data_count: Object.keys(valuesMap).length,
          series_returned: series.length,
          raw_series_ids: series.map(s => s.seriesID)
        }
      };
    }

    const availableLabor = laborForce - employed;

    return {
      success: true,
      calculation: {
        formula: 'AVAILABLE_LABOR = LAUS_LABOR_FORCE − LAUS_EMPLOYED',
        inputs: {
          county_fips: countyFips,
          year,
          LAUS_LABOR_FORCE: laborForce,
          LAUS_EMPLOYED: employed
        },
        result: {
          LAUS_UNEMPLOYED: availableLabor,
          AVAILABLE_LABOR: availableLabor
        }
      },
      metadata: {
        labor_force_series_id: laborForceId,
        employed_series_id: employedId,
        query_time: new Date().toISOString(),
        data_source: 'bls_api_v2'
      }
    };

  } catch (error) {
    console.error(`❌ LAUS API调用失败:`, error);
    return {
      success: false,
      message: `BLS LAUS API服务当前不可用: ${error.message}`,
      error_details: {
        county_fips: countyFips,
        year,
        error_type: error.name,
        error_message: error.message,
        timestamp: new Date().toISOString()
      }
    };
  }
}

module.exports = {
  buildSeriesId,
  fetchBlsTimeseries,
  getAvailableLaborByCounty,
  constants: LAUS_CONSTANTS
}; 
/**
 * 🏭 QCEW薪酬计算API路由 (基于CSV数据)
 * 使用本地CSV数据替代BLS API调用
 */

const express = require('express');
const router = express.Router();

// 导入服务
const qcewDataService = require('../services/qcewDataService');
const areaCodeService = require('../services/areaCodeService');

/**
 * QCEW常量定义
 */
const QCEW_CONSTANTS = {
    WEEKS_PER_YEAR: 52,
    
    // 默认筛选参数
    DEFAULT_FILTERS: {
        industry: '10',        // 所有行业
        ownership: '0',        // 所有所有制
        aggregationLevel: '50', // 县级聚合
        year: 2024,
        quarter: null          // 自动选择最新季度
    }
};

/**
 * 🔍 初始化数据服务
 */
async function initializeServices() {
    try {
        // 初始化地区代码服务
        console.log('🔄 初始化地区代码服务...');
        await areaCodeService.loadAreaData();
        
        // 初始化QCEW数据服务
        // 如果设置了环境变量 LOAD_QCEW_DATA=true，则加载QCEW大型CSV数据。
        // 默认跳过，以避免启动时占用过多内存导致崩溃。
        if (process.env.LOAD_QCEW_DATA === 'true') {
            console.log('🔄 初始化多年份QCEW数据服务...');
            await qcewDataService.loadData();
        } else {
            console.log('⏭️  跳过QCEW数据加载 (设置环境变量 LOAD_QCEW_DATA=true 可启用)');
        }
        
        console.log('✅ 所有数据服务初始化完成');
        return { success: true };
    } catch (error) {
        console.error('❌ 数据服务初始化失败:', error);
        throw error;
    }
}

/**
 * 检查QCEW服务是否可用的中间件
 */
function checkQCEWServiceAvailability(req, res, next) {
    const stats = qcewDataService.getStats();
    if (stats.disabled) {
        return res.status(503).json({
            success: false,
            message: 'QCEW数据服务不可用',
            error: '由于数据文件过大，QCEW服务已被禁用。请使用其他数据源。',
            service_status: 'disabled',
            reason: 'CSV文件已被删除以减小项目大小',
            alternatives: [
                '使用劳工统计局在线API',
                '使用其他经济数据源',
                '联系管理员获取替代方案'
            ],
            timestamp: new Date().toISOString()
        });
    }
    next();
}

/**
 * 📊 健康检查API
 * GET /api/us-labor/qcew/health
 */
router.get('/health', async (req, res) => {
    try {
        const stats = qcewDataService.getStats();
        
        if (stats.disabled) {
            return res.json({
                status: 'disabled',
                message: 'QCEW服务已被禁用',
                reason: 'CSV数据文件已被删除以减小项目大小',
                timestamp: new Date().toISOString(),
                qcew_data_service: stats,
                alternatives: [
                    '使用劳工统计局在线API',
                    '使用其他经济数据源'
                ]
            });
        }
        
        const yearStats = qcewDataService.getAreaStatsByYear();
        
        res.json({
            status: 'healthy',
            message: 'QCEW服务运行正常',
            timestamp: new Date().toISOString(),
            qcew_data_service: {
                ...stats,
                year_statistics: yearStats
            }
        });
    } catch (error) {
        console.error('❌ QCEW健康检查失败:', error);
        res.status(500).json({
            status: 'error',
            message: '服务异常',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * 🔍 城市搜索API - 使用中间件检查服务可用性
 * GET /api/us-labor/qcew/search-cities?city=Los Angeles&state=California
 */
router.get('/search-cities', checkQCEWServiceAvailability, async (req, res) => {
    try {
        const { query } = req.query;
        
        if (!query || query.trim().length < 2) {
            return res.status(400).json({
                success: false,
                message: '搜索查询至少需要2个字符',
                timestamp: new Date().toISOString()
            });
        }

        const results = await areaCodeService.findAreaCodes(query.trim());
        
        // 为每个结果添加年份可用性信息
        const enhancedResults = results.map(area => {
            const availableYears = qcewDataService.getAvailableYearsForArea(area.areaCode);
            const bestYear = qcewDataService.getBestAvailableYear(area.areaCode);
            
            return {
                area_fips: area.areaCode,
                area_title: area.areaTitle,
                score: area.score,
                is_county: area.isCounty,
                is_state: area.isState,
                is_msa: area.isMSA,
                is_csa: area.isCSA,
                qcew_data: {
                    has_data: availableYears.length > 0,
                    available_years: availableYears,
                    latest_year: bestYear,
                    data_coverage: availableYears.length > 0 ? 'available' : 'unavailable'
                }
            };
        });
        
        res.json({
            success: true,
            results: enhancedResults,
            total: enhancedResults.length,
            query,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 搜索地区失败:', error);
        res.status(500).json({
            success: false,
            message: '搜索地区时发生错误',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * 📊 获取城市QCEW数据 - 使用中间件检查服务可用性
 * POST /api/us-labor/qcew/data
 * Body: { city: "Los Angeles", state: "California", year: 2024, quarter: 4 }
 */
router.get('/data', checkQCEWServiceAvailability, async (req, res) => {
    try {
        const { area_code, year, quarter } = req.query;
        
        if (!area_code) {
            return res.status(400).json({
                success: false,
                message: '必须提供area_code参数',
                timestamp: new Date().toISOString()
            });
        }

        // 解析年份参数
        const requestedYear = year ? parseInt(year) : null;
        const requestedQuarter = quarter ? parseInt(quarter) : null;

        // 如果用户没有指定年份，获取最佳可用年份
        const bestYear = requestedYear || qcewDataService.getBestAvailableYear(area_code);
        const availableYears = qcewDataService.getAvailableYearsForArea(area_code);

        // 查询数据
        const data = qcewDataService.getDataByAreaCode(area_code, {
            year: bestYear,
            quarter: requestedQuarter
        });

        if (!data) {
            return res.json({
                success: false,
                message: `地区代码 ${area_code} 没有找到QCEW数据`,
                area_code,
                requested_year: requestedYear,
                available_years: availableYears,
                suggested_year: bestYear,
                timestamp: new Date().toISOString()
            });
        }

        // 获取地区名称信息
        const areaInfo = await areaCodeService.getAreaInfo(area_code);
        
        res.json({
            success: true,
            data: {
                ...data,
                area_info: areaInfo,
                query_metadata: {
                    requested_year: requestedYear,
                    actual_year: data.year,
                    requested_quarter: requestedQuarter,
                    actual_quarter: data.quarter,
                    available_years: availableYears,
                    is_latest_data: data.year === Math.max(...availableYears)
                }
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 获取QCEW数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取QCEW数据时发生错误',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * 🧮 薪酬计算API - 使用中间件检查服务可用性
 * POST /api/us-labor/qcew/calculate-payroll
 * Body: { city, state, planned_fte, year, quarter }
 */
router.post('/calculate-payroll', checkQCEWServiceAvailability, async (req, res) => {
    try {
        const { city, state, planned_fte, year = 2024, quarter = null } = req.body;
        
        if (!city || !planned_fte) {
            return res.status(400).json({
                success: false,
                error: '请提供城市名称和计划员工数量',
                code: 'MISSING_REQUIRED_PARAMS'
            });
        }

        const fte = parseFloat(planned_fte);
        if (isNaN(fte) || fte <= 0) {
            return res.status(400).json({
                success: false,
                error: '员工数量必须为正数',
                code: 'INVALID_FTE'
            });
        }

        console.log(`🧮 计算薪酬: ${city}, ${state || '未指定州'}, ${fte}人 (${year}年Q${quarter || '最新'})`);
        
        // 获取城市QCEW数据
        const matches = await areaCodeService.findAreaCodes(city, state);
        
        if (matches.length === 0) {
            return res.status(404).json({
                success: false,
                error: `未找到匹配 "${city}" 的地区`,
                recommendations: areaCodeService.getRecommendedCities()
            });
        }

        const bestMatch = matches[0];
        const areaCode = bestMatch.areaCode;
        
        // 获取QCEW数据
        const qcewData = qcewDataService.getDataByAreaCode(areaCode, {
            year: parseInt(year),
            quarter: quarter ? parseInt(quarter) : null,
            ...QCEW_CONSTANTS.DEFAULT_FILTERS
        });

        if (!qcewData || !qcewData.avg_weekly_wage) {
            return res.status(404).json({
                success: false,
                error: `${bestMatch.areaTitle} 在 ${year}年${quarter ? `Q${quarter}` : ''} 没有可用的薪酬数据`,
                area: {
                    code: areaCode,
                    title: bestMatch.areaTitle
                }
            });
        }

        // 计算年薪
        const weeklyWage = qcewData.avg_weekly_wage;
        const annualPayroll = weeklyWage * QCEW_CONSTANTS.WEEKS_PER_YEAR * fte;
        const monthlyPayroll = annualPayroll / 12;
        const perEmployeeAnnual = annualPayroll / fte;

        console.log(`✅ 薪酬计算完成: $${weeklyWage.toFixed(2)}/周 × 52周 × ${fte}人 = $${annualPayroll.toLocaleString()}`);

        res.json({
            success: true,
            calculation: {
                formula: "Annual_Payroll = QCEW_AVG_WEEKLY_WAGE × WEEKS_PER_YEAR × PLANNED_FTE",
                inputs: {
                    city,
                    state,
                    planned_fte: fte,
                    year: parseInt(year),
                    quarter: qcewData.quarter,
                    area_code: areaCode,
                    area_title: bestMatch.areaTitle
                },
                components: {
                    avg_weekly_wage: weeklyWage,
                    weeks_per_year: QCEW_CONSTANTS.WEEKS_PER_YEAR,
                    planned_fte: fte
                },
                result: {
                    annual_payroll: annualPayroll,
                    formatted_payroll: `$${annualPayroll.toLocaleString('en-US', { minimumFractionDigits: 2 })}`,
                    monthly_payroll: monthlyPayroll,
                    per_employee_annual: perEmployeeAnnual
                },
                qcew_data: qcewData,
                metadata: {
                    data_source: 'qcew_csv',
                    calculation_time: new Date().toISOString()
                }
            }
        });

    } catch (error) {
        console.error('❌ 薪酬计算失败:', error);
        res.status(500).json({
            success: false,
            error: '薪酬计算失败',
            details: error.message
        });
    }
});

/**
 * 📈 获取可用地区代码
 * GET /api/us-labor/qcew/available-areas?limit=50
 */
router.get('/available-areas', (req, res) => {
    try {
        const { limit = 50 } = req.query;
        const areas = qcewDataService.getAvailableAreaCodes(parseInt(limit));
        
        res.json({
            success: true,
            areas,
            total: areas.length,
            note: '显示有QCEW数据的地区代码'
        });
    } catch (error) {
        console.error('❌ 获取地区代码失败:', error);
        res.status(500).json({
            success: false,
            error: '获取地区代码失败',
            details: error.message
        });
    }
});

/**
 * 🧠 智能建议API
 * GET /api/us-labor/qcew/intelligent-suggestions
 * Query: { input: "Los Angeles", reason: "no_data" }
 */
router.get('/intelligent-suggestions', async (req, res) => {
    try {
        const { input, reason = 'general', context = '' } = req.query;
        
        if (!input || input.trim().length < 2) {
            return res.status(400).json({
                success: false,
                error: '请提供搜索关键词',
                code: 'MISSING_INPUT'
            });
        }

        console.log(`🧠 生成智能建议: "${input}", 原因: ${reason}`);
        
        const suggestions = await generateIntelligentSuggestions(input.trim(), reason, context);
        
        res.json({
            success: true,
            input: input.trim(),
            reason,
            suggestions,
            total: suggestions.length,
            generated_at: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 智能建议生成失败:', error);
        res.status(500).json({
            success: false,
            error: '智能建议生成失败',
            details: error.message
        });
    }
});

/**
 * 🔧 生成智能建议的核心逻辑
 */
async function generateIntelligentSuggestions(input, reason, context) {
    const suggestions = [];
    
    // 1. 获取有数据的地区列表
    const availableAreas = qcewDataService.getAvailableAreaCodes();
    const availableAreaCodes = availableAreas.map(area => area.area_fips);
    
    // 2. 查找所有匹配项
    const allMatches = await areaCodeService.findAreaCodes(input, null);
    
    // 3. 过滤有数据的匹配项
    const dataMatches = allMatches.filter(match => 
        availableAreaCodes.includes(match.areaCode)
    );
    
    // 4. 根据原因生成不同类型的建议
    switch (reason) {
        case 'no_data':
            await addNoDataSuggestions(suggestions, input, allMatches, dataMatches, availableAreas);
            break;
        case 'no_match':
            await addNoMatchSuggestions(suggestions, input, availableAreas);
            break;
        case 'spelling':
            await addSpellingSuggestions(suggestions, input, dataMatches);
            break;
        default:
            await addGeneralSuggestions(suggestions, input, dataMatches, availableAreas);
    }
    
    // 5. 去重并排序
    const uniqueSuggestions = removeDuplicateSuggestions(suggestions);
    return uniqueSuggestions.slice(0, 8); // 最多8个建议
}

/**
 * 🚫 无数据情况的建议
 */
async function addNoDataSuggestions(suggestions, input, allMatches, dataMatches, availableAreas) {
    // 1. 如果有匹配但无数据，说明情况
    if (allMatches.length > 0 && dataMatches.length === 0) {
        suggestions.push({
            type: 'explanation',
            title: `${allMatches[0].areaTitle}`,
            subtitle: '该地区暂无2024年QCEW数据',
            action_text: '了解详情',
            confidence: 1.0,
            metadata: { 
                area_code: allMatches[0].areaCode,
                found_but_no_data: true 
            }
        });
    }
    
    // 2. 推荐相似经济环境的地区
    const economicAlternatives = findEconomicAlternatives(input, availableAreas);
    economicAlternatives.forEach(alt => suggestions.push(alt));
    
    // 3. 推荐同州的其他地区
    const stateAlternatives = await findStateAlternatives(input, availableAreas);
    stateAlternatives.forEach(alt => suggestions.push(alt));
    
    // 4. 推荐热门地区
    const popularAlternatives = getPopularAlternatives(availableAreas);
    popularAlternatives.slice(0, 2).forEach(alt => suggestions.push(alt));
}

/**
 * 🔍 无匹配情况的建议
 */
async function addNoMatchSuggestions(suggestions, input, availableAreas) {
    // 1. 拼写纠正建议
    const spellingSuggestions = generateSpellingCorrections(input);
    spellingSuggestions.forEach(suggestion => suggestions.push(suggestion));
    
    // 2. 模糊匹配
    const fuzzyMatches = await performFuzzySearch(input, availableAreas);
    fuzzyMatches.forEach(match => suggestions.push(match));
    
    // 3. 推荐热门地区
    const popularAreas = getPopularAlternatives(availableAreas);
    popularAreas.slice(0, 3).forEach(area => suggestions.push(area));
}

/**
 * ✏️ 拼写建议
 */
async function addSpellingSuggestions(suggestions, input, dataMatches) {
    const corrections = generateSpellingCorrections(input);
    corrections.forEach(correction => suggestions.push(correction));
    
    if (dataMatches.length > 0) {
        dataMatches.slice(0, 3).forEach(match => {
            suggestions.push({
                type: 'available',
                title: match.areaTitle,
                subtitle: '有完整2024年数据',
                action_text: '选择此地区',
                confidence: match.score / 100,
                metadata: { 
                    area_code: match.areaCode,
                    has_data: true 
                }
            });
        });
    }
}

/**
 * 📊 通用建议
 */
async function addGeneralSuggestions(suggestions, input, dataMatches, availableAreas) {
    // 1. 有数据的匹配项
    if (dataMatches.length > 0) {
        dataMatches.slice(0, 3).forEach(match => {
            suggestions.push({
                type: 'available',
                title: match.areaTitle,
                subtitle: '有完整2024年数据',
                action_text: '选择此地区',
                confidence: match.score / 100,
                metadata: { 
                    area_code: match.areaCode,
                    has_data: true 
                }
            });
        });
    }
    
    // 2. 相关地区建议
    const relatedAreas = await findRelatedAreas(input, availableAreas);
    relatedAreas.forEach(area => suggestions.push(area));
}

/**
 * 🏢 查找经济相似的替代地区
 */
function findEconomicAlternatives(input, availableAreas) {
    const alternatives = [];
    
    // 根据输入的地区类型推荐相似规模的地区
    const economicMapping = {
        'los angeles': { type: 'mega_city', alternatives: ['Alabama', 'California Statewide'] },
        'new york': { type: 'mega_city', alternatives: ['Alabama', 'Texas Statewide'] },
        'chicago': { type: 'major_city', alternatives: ['Alabama', 'Michigan Statewide'] },
        'houston': { type: 'major_city', alternatives: ['Alabama', 'Texas Statewide'] },
        'california': { type: 'large_state', alternatives: ['Alabama', 'Texas'] },
        'texas': { type: 'large_state', alternatives: ['Alabama', 'California'] }
    };
    
    const lowerInput = input.toLowerCase();
    const mapping = economicMapping[lowerInput];
    
    if (mapping) {
        mapping.alternatives.forEach(altName => {
            // 检查这个地区是否有数据
            const hasData = availableAreas.some(area => 
                area.area_fips === '01000' || // Alabama
                area.area_fips.startsWith('01') // Alabama counties
            );
            
            if (hasData && altName === 'Alabama') {
                alternatives.push({
                    type: 'economic_alternative',
                    title: 'Alabama Statewide',
                    subtitle: `${mapping.type}的经济替代选择`,
                    action_text: '查看数据',
                    confidence: 0.7,
                    metadata: { 
                        area_code: '01000',
                        economic_similarity: mapping.type,
                        has_data: true 
                    }
                });
            }
        });
    }
    
    return alternatives;
}

/**
 * 🗺️ 查找同州替代地区
 */
async function findStateAlternatives(input, availableAreas) {
    const alternatives = [];
    
    // 尝试提取州信息
    const stateKeywords = {
        'california': '06',
        'texas': '48', 
        'new york': '36',
        'florida': '12',
        'alabama': '01'
    };
    
    const lowerInput = input.toLowerCase();
    let stateCode = null;
    
    // 检查输入中是否包含州名
    Object.keys(stateKeywords).forEach(state => {
        if (lowerInput.includes(state)) {
            stateCode = stateKeywords[state];
        }
    });
    
    if (stateCode) {
        // 查找该州内有数据的地区
        const stateAreas = availableAreas.filter(area => 
            area.area_fips.startsWith(stateCode) && area.area_fips !== `${stateCode}000`
        ).slice(0, 2);
        
        stateAreas.forEach(area => {
            alternatives.push({
                type: 'state_alternative',
                title: `${area.area_fips} 县`,
                subtitle: '同州内的其他地区',
                action_text: '查看数据',
                confidence: 0.6,
                metadata: { 
                    area_code: area.area_fips,
                    same_state: true,
                    has_data: true 
                }
            });
        });
    }
    
    return alternatives;
}

/**
 * ⭐ 获取热门推荐地区
 */
function getPopularAlternatives(availableAreas) {
    const popularAreas = [
        { area_fips: '01000', name: 'Alabama Statewide', reason: '完整州级数据，经济指标全面' },
        { area_fips: '01073', name: 'Jefferson County, Alabama', reason: '主要都市区，包含伯明翰' },
        { area_fips: '01097', name: 'Mobile County, Alabama', reason: '港口城市，制造业发达' }
    ];
    
    return popularAreas
        .filter(area => availableAreas.some(available => available.area_fips === area.area_fips))
        .map(area => ({
            type: 'popular',
            title: area.name,
            subtitle: area.reason,
            action_text: '查看数据',
            confidence: 0.8,
            metadata: { 
                area_code: area.area_fips,
                popular: true,
                has_data: true 
            }
        }));
}

/**
 * ✏️ 生成拼写纠正建议
 */
function generateSpellingCorrections(input) {
    const corrections = [];
    
    const commonMistakes = {
        'los angelas': 'Los Angeles',
        'new yourk': 'New York', 
        'chigago': 'Chicago',
        'houstin': 'Houston',
        'san fransisco': 'San Francisco',
        'philadelpia': 'Philadelphia',
        'huston': 'Houston',
        'califonia': 'California',
        'texus': 'Texas',
        'newyork': 'New York',
        'losangeles': 'Los Angeles'
    };
    
    const lowerInput = input.toLowerCase().replace(/[^a-z\s]/g, '');
    
    Object.keys(commonMistakes).forEach(mistake => {
        if (mistake === lowerInput || mistake.includes(lowerInput) || lowerInput.includes(mistake)) {
            corrections.push({
                type: 'spelling',
                title: commonMistakes[mistake],
                subtitle: '建议的正确拼写',
                action_text: '使用此拼写',
                confidence: 0.9,
                metadata: { 
                    original: input,
                    correction: commonMistakes[mistake],
                    spelling_fix: true 
                }
            });
        }
    });
    
    return corrections;
}

/**
 * 🔍 模糊搜索
 */
async function performFuzzySearch(input, availableAreas) {
    const matches = [];
    const lowerInput = input.toLowerCase();
    
    // 简单的模糊匹配：检查包含关系
    for (const area of availableAreas.slice(0, 20)) { // 限制搜索范围
        try {
            const areaInfo = await areaCodeService.getAreaInfo(area.area_fips);
            if (areaInfo && areaInfo.areaTitle) {
                const lowerTitle = areaInfo.areaTitle.toLowerCase();
                
                // 检查是否有部分匹配
                const words = lowerInput.split(' ');
                let matchCount = 0;
                
                words.forEach(word => {
                    if (word.length > 2 && lowerTitle.includes(word)) {
                        matchCount++;
                    }
                });
                
                if (matchCount > 0) {
                    matches.push({
                        type: 'fuzzy_match',
                        title: areaInfo.areaTitle,
                        subtitle: `模糊匹配 (${matchCount}/${words.length} 关键词)`,
                        action_text: '选择此地区',
                        confidence: matchCount / words.length * 0.6,
                        metadata: { 
                            area_code: area.area_fips,
                            match_score: matchCount,
                            has_data: true 
                        }
                    });
                }
            }
        } catch (err) {
            console.log(`⚠️  跳过地区 ${area.area_fips}: ${err.message}`);
        }
    }
    
    return matches.sort((a, b) => b.confidence - a.confidence).slice(0, 3);
}

/**
 * 🔗 查找相关地区
 */
async function findRelatedAreas(input, availableAreas) {
    const related = [];
    
    // 如果输入看起来像城市名，推荐包含该词的县或MSA
    const cityKeywords = ['city', 'town', 'county', 'metro'];
    const lowerInput = input.toLowerCase();
    
    if (!cityKeywords.some(keyword => lowerInput.includes(keyword))) {
        // 推荐相关的县级地区
        try {
            const countyAreas = availableAreas
                .filter(area => area.area_fips.length === 5) // 县级代码
                .slice(0, 3);
                
            for (const area of countyAreas) {
                const areaInfo = await areaCodeService.getAreaInfo(area.area_fips);
                if (areaInfo) {
                    related.push({
                        type: 'related',
                        title: areaInfo.areaTitle,
                        subtitle: '相关县级地区',
                        action_text: '查看数据',
                        confidence: 0.5,
                        metadata: { 
                            area_code: area.area_fips,
                            related: true,
                            has_data: true 
                        }
                    });
                }
            }
        } catch (err) {
            console.log('⚠️  查找相关地区时出错:', err.message);
        }
    }
    
    return related;
}

/**
 * 🔄 去除重复建议
 */
function removeDuplicateSuggestions(suggestions) {
    const seen = new Set();
    return suggestions.filter(suggestion => {
        const key = `${suggestion.type}-${suggestion.title}`;
        if (seen.has(key)) {
            return false;
        }
        seen.add(key);
        return true;
    });
}

// 新增：获取地区年份可用性端点
router.get('/area-years', async (req, res) => {
    try {
        const { area_code } = req.query;
        
        if (!area_code) {
            return res.status(400).json({
                success: false,
                message: '必须提供area_code参数',
                timestamp: new Date().toISOString()
            });
        }

        const availableYears = qcewDataService.getAvailableYearsForArea(area_code);
        const bestYear = qcewDataService.getBestAvailableYear(area_code);
        const areaInfo = await areaCodeService.getAreaInfo(area_code);

        res.json({
            success: true,
            data: {
                area_code,
                area_info: areaInfo,
                available_years: availableYears,
                latest_year: bestYear,
                has_data: availableYears.length > 0,
                year_count: availableYears.length
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 获取地区年份数据失败:', error);
        res.status(500).json({
            success: false,
            message: '获取地区年份数据时发生错误',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 新增：获取所有年份统计端点
router.get('/year-statistics', async (req, res) => {
    try {
        const yearStats = qcewDataService.getAreaStatsByYear();
        const globalStats = qcewDataService.getStats();

        res.json({
            success: true,
            data: {
                year_statistics: yearStats,
                global_statistics: {
                    total_areas: globalStats.totalAreas,
                    total_records: globalStats.totalRecords,
                    loaded_years: globalStats.loadedYears,
                    load_time: globalStats.loadTime
                },
                summary: {
                    available_years: Object.keys(yearStats).map(y => parseInt(y)).sort(),
                    total_unique_areas: globalStats.totalAreas,
                    most_recent_year: Math.max(...globalStats.loadedYears)
                }
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 获取年份统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取年份统计时发生错误',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 启动时初始化服务
initializeServices().catch(error => {
    console.error('💥 服务初始化失败:', error);
    process.exit(1);
});

module.exports = router; 
/**
 * 📈 LAUS (Local Area Unemployment Statistics) API 路由
 * 基于BLS Public Data API v2，提供县级失业统计与可招聘余量计算
 */

const express = require('express');
const router = express.Router();

// 服务
const lausDataService = require('../services/lausDataService');
const areaCodeService = require('../services/areaCodeService');

/**
 * 📊 健康检查
 * GET /api/us-labor/laus/health
 */
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    message: 'LAUS service running',
    timestamp: new Date().toISOString()
  });
});

/**
 * 🧮 计算可招聘余量 (AVAILABLE_LABOR)
 * POST /api/us-labor/laus/calculate-available-labor
 * Body: {
 *   "city": "Los Angeles",   // optional
 *   "state": "California",   // optional
 *   "area_code": "06037",    // county FIPS (5 digits) 优先
 *   "year": 2024              // optional
 * }
 */
router.post('/calculate-available-labor', async (req, res) => {
  try {
    const { area_code, city, state, year } = req.body || {};

    // Step 1: 解析县级FIPS
    let countyFips = null;

    if (area_code && /^\d{5}$/.test(area_code.toString())) {
      countyFips = area_code.toString();
    } else if (city) {
      const matches = await areaCodeService.findAreaCodes(city, state);
      const countyMatch = matches.find(m => m.isCounty);
      if (!countyMatch) {
        return res.status(400).json({
          success: false,
          message: `未找到符合 ${city}${state ? ', ' + state : ''} 的县级地区代码`,
          timestamp: new Date().toISOString()
        });
      }
      countyFips = countyMatch.areaCode;
    } else {
      return res.status(400).json({
        success: false,
        message: '请提供 area_code 或 city 参数',
        timestamp: new Date().toISOString()
      });
    }

    // Step 2: 调用数据服务
    const result = await lausDataService.getAvailableLaborByCounty(countyFips, { year });

    res.json({
      success: result.success,
      ...result,
      query: {
        area_code: countyFips,
        city,
        state,
        year
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ LAUS 计算失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 🧪 调试BLS API响应
 * GET /api/us-labor/laus/debug-bls?fips=06037&year=2023
 */
router.get('/debug-bls', async (req, res) => {
  try {
    const { fips = '06037', year = '2023' } = req.query;
    
    const laborForceId = lausDataService.buildSeriesId(fips, '006');
    const employedId = lausDataService.buildSeriesId(fips, '005');
    
    console.log(`🔧 调试BLS API - FIPS: ${fips}, Year: ${year}`);
    console.log(`📊 系列ID: ${laborForceId}, ${employedId}`);
    
    const series = await lausDataService.fetchBlsTimeseries([laborForceId, employedId], year, year);
    
    res.json({
      debug: true,
      request: {
        fips,
        year,
        series_ids: [laborForceId, employedId]
      },
      response: {
        series_count: series.length,
        series: series.map(s => ({
          seriesID: s.seriesID,
          data_count: s.data?.length || 0,
          first_data_point: s.data?.[0] || null,
          last_data_point: s.data?.[s.data?.length - 1] || null
        }))
      },
      raw_response: series
    });
  } catch (error) {
    res.status(500).json({
      debug: true,
      error: error.message,
      stack: error.stack
    });
  }
});

module.exports = router; 
/**
 * 🏭 US Labor API 主入口
 * 统一的美国劳工数据API接口
 */

const express = require('express');
const router = express.Router();

// 导入子路由
const qcewRouter = require('./routes/qcew');
const lausRouter = require('./routes/laus');

/**
 * 📊 API概览
 * GET /api/us-labor
 */
router.get('/', (req, res) => {
    res.json({
        success: true,
        api: 'US Labor Data API',
        version: '1.1.0',
        description: '美国劳工数据统一API接口',
        endpoints: {
            qcew: {
                path: '/api/us-labor/qcew',
                description: 'QCEW (Quarterly Census of Employment and Wages) 季度就业和工资调查',
                endpoints: [
                    'GET /health',
                    'GET /search-cities',
                    'GET /data',
                    'GET /calculate-payroll'
                ]
            },
            laus: {
                path: '/api/us-labor/laus',
                description: 'LAUS (Local Area Unemployment Statistics) 地方失业统计',
                endpoints: [
                    'GET /health',
                    'POST /calculate-available-labor'
                ]
            }
        },
        data_sources: [
            'QCEW CSV数据 (2024 Q1-Q4)',
            'BLS LAUS Public Data API'
        ],
        documentation: '/api/us-labor/laus/health',
        timestamp: new Date().toISOString()
    });
});

// 挂载子路由
router.use('/qcew', qcewRouter);
router.use('/laus', lausRouter);

module.exports = router; 
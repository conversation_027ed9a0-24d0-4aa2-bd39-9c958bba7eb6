const { executeQuery, executeQuerySingle, testConnection } = require('./config/database');
const bcrypt = require('bcryptjs');

async function testDatabase() {
  try {
    console.log('🔄 测试数据库连接...');
    const connected = await testConnection();
    
    if (!connected) {
      console.error('❌ 数据库连接失败');
      return;
    }

    console.log('✅ 数据库连接成功');

    // 检查用户表是否存在
    console.log('🔄 检查用户表...');
    try {
      const users = await executeQuery('SELECT id, email, username FROM users LIMIT 5');
      console.log('📊 用户表数据:', users);
      
      if (users.length === 0) {
        console.log('⚠️  用户表为空，创建测试用户...');
        
        // 创建测试用户
        const hashedPassword = await bcrypt.hash('123456', 8);
        await executeQuery(
          `INSERT INTO users (email, password_hash, username, preferred_language, created_at, last_login_at, active_subscription_status)
           VALUES (?, ?, ?, ?, NOW(), NOW(), 'free')`,
          ['<EMAIL>', hashedPassword, 'testuser', 'zh']
        );
        
        console.log('✅ 测试用户创建成功');
        console.log('📧 邮箱: <EMAIL>');
        console.log('🔑 密码: 123456');
      }
    } catch (error) {
      console.error('❌ 用户表操作失败:', error.message);
      
      // 检查是否是表不存在的错误
      if (error.message.includes("doesn't exist")) {
        console.log('🔄 用户表不存在，尝试创建...');
        
        // 创建用户表
        await executeQuery(`
          CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            username VARCHAR(100) UNIQUE NOT NULL,
            preferred_language ENUM('en', 'zh') DEFAULT 'en',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login_at TIMESTAMP NULL,
            active_subscription_status ENUM('free', 'premium', 'enterprise') DEFAULT 'free',
            INDEX idx_email (email),
            INDEX idx_username (username)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        
        console.log('✅ 用户表创建成功');
        
        // 创建测试用户
        const hashedPassword = await bcrypt.hash('123456', 8);
        await executeQuery(
          `INSERT INTO users (email, password_hash, username, preferred_language, created_at, last_login_at, active_subscription_status)
           VALUES (?, ?, ?, ?, NOW(), NOW(), 'free')`,
          ['<EMAIL>', hashedPassword, 'testuser', 'zh']
        );
        
        console.log('✅ 测试用户创建成功');
        console.log('📧 邮箱: <EMAIL>');
        console.log('🔑 密码: 123456');
      }
    }

  } catch (error) {
    console.error('❌ 数据库测试失败:', error);
  }
}

testDatabase(); 
const { pool, testConnection } = require('./config/database');

async function setupDatabase() {
  console.log('🔄 Starting database setup...');

  try {
    // Test connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ Cannot connect to database');
      process.exit(1);
    }

    // Check if users table exists
    try {
      const [result] = await pool.execute('SELECT COUNT(*) as count FROM users LIMIT 1');
      console.log('✅ Users table already exists with', result[0].count, 'records');
      process.exit(0);
    } catch (error) {
      if (error.code === 'ER_NO_SUCH_TABLE') {
        console.log('❌ Users table does not exist. Creating it...');

        // Create users table
        const createUsersTable = `
          CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            username VARCHAR(255) NOT NULL UNIQUE,
            preferred_language VARCHAR(10) DEFAULT 'en',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            active_subscription_status VARCHAR(50) DEFAULT 'free',
            INDEX idx_email (email),
            INDEX idx_username (username)
          )
        `;

        await pool.execute(createUsersTable);
        console.log('✅ Users table created successfully');

        // Insert demo users with bcrypt hashed passwords
        // Password for demo and test users is "password123"
        // Password for yuyuxuan99 is "yangyuxuan3005"
        const bcrypt = require('bcryptjs');
        const yuyuxuanPassword = await bcrypt.hash('yangyuxuan3005', 8);

        const insertDemoUsers = `
          INSERT INTO users (email, password_hash, username, preferred_language) VALUES
          ('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 'demo_user', 'en'),
          ('<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', 'test_user', 'zh')
        `;

        await pool.execute(insertDemoUsers);

        // Insert the specific user for the login form
        await pool.execute(
          'INSERT INTO users (email, password_hash, username, preferred_language) VALUES (?, ?, ?, ?)',
          ['<EMAIL>', yuyuxuanPassword, 'yuyuxuan99', 'zh']
        );

        console.log('✅ Demo users inserted');
        console.log('📝 Demo credentials:');
        console.log('   Email: <EMAIL> | Username: demo_user | Password: password123');
        console.log('   Email: <EMAIL> | Username: test_user | Password: password123');
        console.log('   Email: <EMAIL> | Username: yuyuxuan99 | Password: yangyuxuan3005');
      } else {
        throw error;
      }
    }

    // Verify final state
    const [tables] = await pool.execute('SHOW TABLES');
    console.log('📋 Available tables:', tables.map(t => Object.values(t)[0]));

    const [userCount] = await pool.execute('SELECT COUNT(*) as count FROM users');
    console.log('👤 Total users in database:', userCount[0].count);

    // Show user details
    const [users] = await pool.execute('SELECT id, email, username, preferred_language, created_at FROM users');
    console.log('👥 Users:');
    users.forEach(user => {
      console.log(`   ID: ${user.id} | Email: ${user.email} | Username: ${user.username} | Language: ${user.preferred_language}`);
    });

    console.log('✅ Database setup completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('💥 Database setup failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the setup
setupDatabase();

const { executeQuery, executeQuerySingle } = require('./config/database');
const bcrypt = require('bcryptjs');

async function createTestUser() {
  try {
    console.log('🔄 创建测试用户...');
    
    // 删除现有的测试用户（如果存在）
    await executeQuery('DELETE FROM users WHERE email = ?', ['<EMAIL>']);
    
    // 创建新的测试用户
    const hashedPassword = await bcrypt.hash('123456', 8);
    const result = await executeQuery(
      `INSERT INTO users (email, password_hash, username, preferred_language, created_at, last_login_at, active_subscription_status)
       VALUES (?, ?, ?, ?, NOW(), NOW(), 'free')`,
      ['<EMAIL>', hashedPassword, 'testuser123', 'zh']
    );
    
    console.log('✅ 测试用户创建成功');
    console.log('📧 邮箱: <EMAIL>');
    console.log('👤 用户名: testuser123');
    console.log('🔑 密码: 123456');
    console.log('🆔 用户ID:', result.insertId);
    
    // 验证用户创建
    const user = await executeQuerySingle(
      'SELECT id, email, username FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    console.log('✅ 验证用户数据:', user);
    
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
  }
}

createTestUser(); 
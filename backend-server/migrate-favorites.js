const { pool, testConnection } = require('./config/database');

async function migrateFavorites() {
  console.log('🔄 Starting favorites feature migration...');

  try {
    // Test connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ Cannot connect to database');
      process.exit(1);
    }

    // Check if isFavorite column already exists
    const [columns] = await pool.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME = 'projects' 
      AND COLUMN_NAME = 'isFavorite'
    `, [process.env.DB_NAME || 'platform_core_db']);

    if (columns.length > 0) {
      console.log('✅ isFavorite column already exists');
      
      // Show current table structure
      const [tableStructure] = await pool.execute('DESCRIBE projects');
      console.log('\n📋 Current projects table structure:');
      tableStructure.forEach(column => {
        console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${column.Default ? `DEFAULT ${column.Default}` : ''}`);
      });
      
      process.exit(0);
    }

    console.log('📝 Adding isFavorite column to projects table...');

    // Add isFavorite column
    await pool.execute(`
      ALTER TABLE projects 
      ADD COLUMN isFavorite TINYINT(1) DEFAULT 0 
      COMMENT 'Favorite status: 0 = not favorited, 1 = favorited' 
      AFTER status
    `);

    console.log('✅ isFavorite column added successfully');

    // Add index for better performance
    console.log('📝 Adding index for isFavorite column...');
    await pool.execute(`
      ALTER TABLE projects 
      ADD INDEX idx_isFavorite (isFavorite)
    `);

    console.log('✅ Index added successfully');

    // Show updated table structure
    const [updatedStructure] = await pool.execute('DESCRIBE projects');
    console.log('\n📋 Updated projects table structure:');
    updatedStructure.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${column.Default !== null ? `DEFAULT ${column.Default}` : ''}`);
    });

    // Show current project data to verify migration
    const [projects] = await pool.execute(`
      SELECT id, user_id, project_name, status, isFavorite, created_at 
      FROM projects 
      LIMIT 5
    `);

    console.log('\n📊 Sample project data after migration:');
    if (projects.length > 0) {
      projects.forEach(project => {
        console.log(`  ID: ${project.id}, Name: ${project.project_name}, Favorite: ${project.isFavorite}`);
      });
    } else {
      console.log('  No projects found in database');
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('✨ Favorites feature is now ready to use');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run migration
migrateFavorites();

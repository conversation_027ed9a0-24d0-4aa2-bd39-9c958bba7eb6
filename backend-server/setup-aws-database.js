const { pool, testConnection } = require('./config/database');

// 创建用户表的SQL
const createUsersTable = `
  CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    preferred_language ENUM('en', 'zh') DEFAULT 'en',
    active_subscription_status ENUM('free', 'basic', 'premium') DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_created_at (created_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
`;

// 创建项目表的SQL
const createProjectsTable = `
  CREATE TABLE IF NOT EXISTS projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    description TEXT,
    natural_language_input TEXT,
    structured_parameters JSON,
    status ENUM('active', 'archived', 'completed') DEFAULT 'active',
    isFavorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_favorite (isFavorite)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
`;

// 创建用户会话表（可选，用于更高级的会话管理）
const createUserSessionsTable = `
  CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id INT NOT NULL,
    session_data JSON,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
`;

// 执行数据库初始化
const initializeDatabase = async () => {
  let connection;
  
  try {
    console.log('🚀 开始初始化 AWS RDS 数据库...');
    
    // 测试连接
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('无法连接到数据库');
    }
    
    connection = await pool.getConnection();
    
    console.log('📋 创建 users 表...');
    await connection.execute(createUsersTable);
    console.log('✅ users 表创建成功');
    
    console.log('📋 创建 projects 表...');
    await connection.execute(createProjectsTable);
    console.log('✅ projects 表创建成功');
    
    console.log('📋 创建 user_sessions 表...');
    await connection.execute(createUserSessionsTable);
    console.log('✅ user_sessions 表创建成功');
    
    // 验证表创建
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);
    
    console.log('📊 数据库表列表:', tableNames.join(', '));
    
    // 检查表结构
    for (const tableName of ['users', 'projects', 'user_sessions']) {
      if (tableNames.includes(tableName)) {
        const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
        console.log(`🔍 ${tableName} 表结构:`, columns.map(col => `${col.Field}(${col.Type})`).join(', '));
      }
    }
    
    console.log('🎉 AWS RDS 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    console.error('🔧 错误详情:', error);
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log('✅ 数据库初始化脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 数据库初始化脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = {
  initializeDatabase,
  createUsersTable,
  createProjectsTable,
  createUserSessionsTable
}; 
/**
 * 🧪 QCEW API 可行性测试脚本
 * 测试劳工统计局API的连接性和数据获取功能
 */

const fetch = require('node-fetch');

// API配置 - 根据BLS官方文档更新
const BLS_API_CONFIG = {
    // BLS QCEW API的正确URL格式
    BASE_URL: 'https://api.bls.gov/publicAPI/v2/qcew',
    // 实际上QCEW数据通过不同的端点获取
    ALT_URL: 'https://data.bls.gov/api/v1/qcew',
    API_KEY: '48488361e34a4f2f96a5d482779d81a0',
    TIMEOUT: 30000
};

/**
 * 测试BLS QCEW API的可行性
 */
async function testQCEWAPI() {
    console.log('🧪 开始测试QCEW API可行性...\n');
    
    // 测试场景
    const testCases = [
        {
            name: '全国2023年年度数据',
            params: {
                year: '2023',
                quarter: 'A',
                area: 'US000'
            }
        },
        {
            name: '德克萨斯州2023年年度数据',
            params: {
                year: '2023',
                quarter: 'A', 
                area: '48000'
            }
        },
        {
            name: '加利福尼亚州2022年年度数据',
            params: {
                year: '2022',
                quarter: 'A',
                area: '06000'
            }
        }
    ];

    let successCount = 0;
    let failCount = 0;

    for (const testCase of testCases) {
        console.log(`📊 测试: ${testCase.name}`);
        console.log(`   参数: ${JSON.stringify(testCase.params)}`);
        
        try {
            const result = await callQCEWAPI(testCase.params);
            
            if (result.success) {
                console.log(`   ✅ 成功 - 获取到数据:`);
                console.log(`      平均周薪: $${result.data.avg_weekly_wage || 'N/A'}`);
                console.log(`      平均就业人数: ${result.data.avg_employment?.toLocaleString() || 'N/A'}`);
                console.log(`      企业数: ${result.data.qtrly_estabs?.toLocaleString() || 'N/A'}`);
                
                // 测试薪酬计算
                if (result.data.avg_weekly_wage) {
                    const payrollTest = calculatePayroll(result.data.avg_weekly_wage, 100);
                    console.log(`      测试薪酬计算 (100名员工): ${payrollTest.formatted}`);
                }
                
                successCount++;
            } else {
                console.log(`   ❌ 失败: ${result.error}`);
                failCount++;
            }
            
        } catch (error) {
            console.log(`   ❌ 异常: ${error.message}`);
            failCount++;
        }
        
        console.log(''); // 空行分隔
    }

    // 测试总结
    console.log('🏁 测试总结:');
    console.log(`   成功: ${successCount}/${testCases.length}`);
    console.log(`   失败: ${failCount}/${testCases.length}`);
    console.log(`   成功率: ${((successCount / testCases.length) * 100).toFixed(1)}%`);

    if (successCount > 0) {
        console.log('\n✅ QCEW API 测试通过！可以正常使用。');
        
        // 显示API使用说明
        console.log('\n📖 API使用说明:');
        console.log('   - 基础URL: https://api.bls.gov/publicAPI/v2/qcew/{year}/{quarter}/{area}');
        console.log('   - 年份格式: "2023" (字符串)');
        console.log('   - 季度格式: "A"(年度), "1"(Q1), "2"(Q2), "3"(Q3), "4"(Q4)');
        console.log('   - 地区代码: "US000"(全国), "48000"(德州), "06000"(加州)等');
        console.log('   - 数据返回: avg_weekly_wage, avg_employment, total_wages等');
    } else {
        console.log('\n❌ QCEW API 测试失败！请检查网络连接和API配置。');
    }
}

/**
 * 调用QCEW API
 */
async function callQCEWAPI(params) {
    const { year, quarter, area } = params;
    
    try {
        const apiUrl = `${BLS_API_CONFIG.BASE_URL}/${year}/${quarter}/${area}`;
        console.log(`      请求URL: ${apiUrl}`);
        
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'User-Agent': 'IndustrialGeoDev-Test/1.0',
                'Accept': 'application/json'
            },
            timeout: BLS_API_CONFIG.TIMEOUT
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const rawData = await response.json();
        
        // 处理API响应
        let processedData = {};
        
        if (rawData.data && Array.isArray(rawData.data) && rawData.data.length > 0) {
            const record = rawData.data[0]; // 取第一条记录
            processedData = {
                avg_weekly_wage: parseFloat(record.avg_weekly_wage) || null,
                avg_employment: parseInt(record.avg_employment) || null,
                total_wages: parseFloat(record.total_wages) || null,
                qtrly_estabs: parseInt(record.qtrly_estabs) || null,
                area_code: record.area_fips,
                year: record.year,
                quarter: record.qtr
            };
        } else if (rawData.data) {
            // 单条记录情况
            processedData = {
                avg_weekly_wage: parseFloat(rawData.data.avg_weekly_wage) || null,
                avg_employment: parseInt(rawData.data.avg_employment) || null,
                total_wages: parseFloat(rawData.data.total_wages) || null,
                qtrly_estabs: parseInt(rawData.data.qtrly_estabs) || null,
                area_code: rawData.data.area_fips,
                year: rawData.data.year,
                quarter: rawData.data.qtr
            };
        } else {
            throw new Error('API返回数据格式异常');
        }

        return {
            success: true,
            data: processedData,
            raw_response: rawData
        };

    } catch (error) {
        return {
            success: false,
            error: error.message,
            params
        };
    }
}

/**
 * 测试薪酬计算
 */
function calculatePayroll(avgWeeklyWage, plannedFTE) {
    const WEEKS_PER_YEAR = 52;
    const annualPayroll = avgWeeklyWage * WEEKS_PER_YEAR * plannedFTE;
    
    return {
        annual_payroll: annualPayroll,
        formatted: new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(annualPayroll),
        formula: `$${avgWeeklyWage} × ${WEEKS_PER_YEAR} × ${plannedFTE} = $${annualPayroll.toLocaleString()}`
    };
}

/**
 * 测试网络连接
 */
async function testNetworkConnection() {
    console.log('🌐 测试网络连接...');
    
    try {
        const response = await fetch('https://api.bls.gov', {
            method: 'HEAD',
            timeout: 10000
        });
        
        if (response.ok) {
            console.log('✅ 网络连接正常');
            return true;
        } else {
            console.log(`❌ 网络连接异常: ${response.status}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ 网络连接失败: ${error.message}`);
        return false;
    }
}

/**
 * 主测试函数
 */
async function main() {
    console.log('🏭 QCEW API 可行性测试');
    console.log('=====================================\n');
    
    // 1. 测试网络连接
    const networkOk = await testNetworkConnection();
    if (!networkOk) {
        console.log('\n❌ 网络连接失败，无法继续测试');
        process.exit(1);
    }
    
    console.log(''); // 空行
    
    // 2. 测试QCEW API
    await testQCEWAPI();
    
    console.log('\n🎯 测试建议:');
    console.log('   1. 如果测试成功，可以开始集成到项目中');
    console.log('   2. 如果测试失败，请检查网络连接和API密钥');
    console.log('   3. 建议在生产环境中添加缓存机制来减少API调用');
    console.log('   4. 注意API可能有频率限制，建议添加重试机制');
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        console.error('测试脚本运行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    testQCEWAPI,
    callQCEWAPI,
    calculatePayroll
}; 
const { pool } = require('./config/database');
const bcrypt = require('bcryptjs');

async function addUser() {
  try {
    console.log('🔄 <NAME_EMAIL>...');
    
    // Check if user already exists
    const [existingUser] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (existingUser.length > 0) {
      console.log('ℹ️  User already exists');
      return;
    }
    
    // Hash the password 'yangyuxuan3005' 
    const hashedPassword = await bcrypt.hash('yangyuxuan3005', 8);
    
    // Insert the user with the email from the login form
    await pool.execute(
      'INSERT INTO users (email, password_hash, username, preferred_language) VALUES (?, ?, ?, ?)',
      ['<EMAIL>', hashedPassword, 'yuyuxuan99', 'zh']
    );
    
    console.log('✅ User <EMAIL> added successfully');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: yangyuxuan3005');
    console.log('👤 Username: yuyuxuan99');
    console.log('🌐 Language: zh (Chinese)');
    
    // Verify the user was added
    const [user] = await pool.execute(
      'SELECT id, email, username, preferred_language FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    
    if (user.length > 0) {
      console.log('✅ User verified:', user[0]);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

addUser();

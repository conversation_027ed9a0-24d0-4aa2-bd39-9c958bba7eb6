const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { executeQuery, executeQuerySingle } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 生成JWT令牌
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// 用户注册
router.post('/register', [
  body('email').isEmail().normalizeEmail().withMessage('请输入有效的邮箱地址'),
  body('password').isLength({ min: 6 }).withMessage('密码至少需要6个字符'),
  body('username').isLength({ min: 2, max: 50 }).withMessage('用户名长度应在2-50个字符之间'),
  body('preferred_language').optional().isIn(['en', 'zh']).withMessage('语言设置无效')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { email, password, username, preferred_language = 'en' } = req.body;

    // 检查邮箱是否已存在
    const existingUser = await executeQuerySingle(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      });
    }

    // 检查用户名是否已存在
    const existingUsername = await executeQuerySingle(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (existingUsername) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      });
    }

    // 加密密码
    const saltRounds = 8; // 降低复杂度以提高速度
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 插入新用户
    const result = await executeQuery(
      `INSERT INTO users (email, password_hash, username, preferred_language, created_at, last_login_at, active_subscription_status)
       VALUES (?, ?, ?, ?, NOW(), NOW(), 'free')`,
      [email, hashedPassword, username, preferred_language]
    );

    const userId = result.insertId;

    // 生成JWT令牌
    const token = generateToken(userId);

    // 获取新创建的用户信息（不包含密码）
    const newUser = await executeQuerySingle(
      'SELECT id, email, username, preferred_language, created_at FROM users WHERE id = ?',
      [userId]
    );

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: newUser,
        token
      }
    });

  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 用户登录 - 支持邮箱或用户名登录
router.post('/login', [
  body('loginIdentifier').notEmpty().withMessage('请输入邮箱或用户名'),
  body('password').notEmpty().withMessage('密码不能为空')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { loginIdentifier, password } = req.body;

    // 判断输入是邮箱还是用户名
    const isEmail = loginIdentifier.includes('@');
    const searchField = isEmail ? 'email' : 'username';

    // 查找用户 - 支持邮箱或用户名
    const user = await executeQuerySingle(
      `SELECT id, email, username, password_hash, preferred_language, created_at, last_login_at, active_subscription_status FROM users WHERE ${searchField} = ?`,
      [loginIdentifier]
    );

    if (!user) {
      return res.status(401).json({
        success: false,
        message: isEmail ? '邮箱或密码错误' : '用户名或密码错误'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: isEmail ? '邮箱或密码错误' : '用户名或密码错误'
      });
    }

    // 更新最后登录时间
    await executeQuery(
      'UPDATE users SET last_login_at = NOW() WHERE id = ?',
      [user.id]
    );

    // 生成JWT令牌
    const token = generateToken(user.id);

    // 返回用户信息（不包含密码）
    const { password_hash, ...userWithoutPassword } = user;

    console.log('✅ Login successful for:', user.email);
    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userWithoutPassword,
        token
      }
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        user: req.user
      }
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 更新用户信息
router.put('/profile', authenticateToken, [
  body('username').optional().isLength({ min: 2, max: 50 }).withMessage('用户名长度应在2-50个字符之间'),
  body('preferred_language').optional().isIn(['en', 'zh']).withMessage('语言设置无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { username, preferred_language } = req.body;
    const userId = req.user.id;

    // 构建更新查询
    const updates = [];
    const values = [];

    if (username) {
      // 检查用户名是否已被其他用户使用
      const existingUser = await executeQuerySingle(
        'SELECT id FROM users WHERE username = ? AND id != ?',
        [username, userId]
      );

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该用户名已被使用'
        });
      }

      updates.push('username = ?');
      values.push(username);
    }

    if (preferred_language) {
      updates.push('preferred_language = ?');
      values.push(preferred_language);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
    }

    values.push(userId);

    await executeQuery(
      `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    // 获取更新后的用户信息
    const updatedUser = await executeQuerySingle(
      'SELECT id, email, username, preferred_language, created_at FROM users WHERE id = ?',
      [userId]
    );

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: {
        user: updatedUser
      }
    });

  } catch (error) {
    console.error('更新用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// Development endpoint to test specific login
router.post('/test-login', async (req, res) => {
  try {
    console.log('🧪 Testing <NAME_EMAIL>...');

    const { pool } = require('../config/database');
    const bcrypt = require('bcryptjs');

    // Get connection
    const connection = await pool.getConnection();
    console.log('✅ Got connection');

    // Find user
    const [users] = await connection.execute(
      'SELECT id, email, username, password_hash, preferred_language FROM users WHERE email = ?',
      ['<EMAIL>']
    );
    console.log('✅ Query executed. Found users:', users.length);

    if (users.length === 0) {
      connection.release();
      return res.json({
        success: false,
        message: 'User not found'
      });
    }

    const user = users[0];
    console.log('👤 User found:', user.email);

    // Test password
    const isValid = await bcrypt.compare('yangyuxuan3005', user.password_hash);
    console.log('🔑 Password test result:', isValid);

    connection.release();

    res.json({
      success: true,
      message: 'Login test completed',
      data: {
        userFound: true,
        passwordValid: isValid,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          preferred_language: user.preferred_language
        }
      }
    });

  } catch (error) {
    console.error('❌ Test login error:', error);
    res.status(500).json({
      success: false,
      message: 'Test failed',
      error: error.message
    });
  }
});

// Development endpoint to test database connection
router.get('/test-db', async (req, res) => {
  try {
    console.log('🔍 Testing database connection...');

    // Test basic connection
    const { pool } = require('../config/database');
    const connection = await pool.getConnection();
    console.log('✅ Got database connection');

    // Test simple query
    const [result] = await connection.execute('SELECT 1 as test');
    console.log('✅ Simple query works:', result);

    // Test users table query
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    console.log('✅ Users table query works:', users);

    connection.release();
    console.log('✅ Connection released');

    res.json({
      success: true,
      message: 'Database connection test passed',
      data: {
        userCount: users[0].count
      }
    });

  } catch (error) {
    console.error('❌ Database test error:', error);
    res.status(500).json({
      success: false,
      message: 'Database test failed',
      error: error.message
    });
  }
});

// Development endpoint to list users
router.get('/list-users', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: 'This endpoint is only available in development mode'
      });
    }

    const users = await executeQuery(
      'SELECT id, email, username, preferred_language, created_at FROM users ORDER BY id'
    );

    res.json({
      success: true,
      data: users
    });

  } catch (error) {
    console.error('List users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to list users'
    });
  }
});

// Development endpoint to get detailed user info including password hash
router.get('/debug-users', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: 'This endpoint is only available in development mode'
      });
    }

    const { pool } = require('../config/database');
    const connection = await pool.getConnection();

    // Show current database and connection info
    const [dbInfo] = await connection.execute('SELECT DATABASE() as current_db');
    const [tableInfo] = await connection.execute('SHOW TABLES');

    // Get all users with password hashes for debugging
    const [users] = await connection.execute(
      'SELECT id, email, username, password_hash, preferred_language, created_at FROM users ORDER BY id'
    );

    connection.release();

    res.json({
      success: true,
      message: 'Debug user data retrieved',
      database_info: {
        current_database: dbInfo[0].current_db,
        tables: tableInfo.map(t => Object.values(t)[0]),
        connection_config: {
          host: process.env.DB_HOST,
          port: process.env.DB_PORT,
          user: process.env.DB_USER,
          database: process.env.DB_NAME
        }
      },
      data: users.map(user => ({
        id: user.id,
        email: user.email,
        username: user.username,
        password_hash_preview: user.password_hash ? user.password_hash.substring(0, 20) + '...' : 'null',
        preferred_language: user.preferred_language,
        created_at: user.created_at
      }))
    });

  } catch (error) {
    console.error('Debug users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get debug user data',
      error: error.message
    });
  }
});

// Database integrity check endpoint
router.get('/check-database-integrity', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: 'This endpoint is only available in development mode'
      });
    }

    const { pool } = require('../config/database');
    const connection = await pool.getConnection();

    let result = {
      success: true,
      message: 'Database integrity check completed',
      checks: []
    };

    try {
      // Check 1: Verify all tables exist
      const [tables] = await connection.execute('SHOW TABLES');
      const tableNames = tables.map(t => Object.values(t)[0]);
      result.checks.push({
        check: 'Table existence',
        status: 'passed',
        details: `Found tables: ${tableNames.join(', ')}`
      });

      // Check 2: Look for any remaining foreign key references to 'user' table
      const [orphanedFKs] = await connection.execute(`
        SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_NAME = 'user' AND TABLE_SCHEMA = DATABASE()
      `);

      if (orphanedFKs.length > 0) {
        result.checks.push({
          check: 'Orphaned foreign keys to user table',
          status: 'failed',
          details: orphanedFKs,
          action_needed: 'Fix foreign key references'
        });
      } else {
        result.checks.push({
          check: 'Orphaned foreign keys to user table',
          status: 'passed',
          details: 'No orphaned foreign key references found'
        });
      }

      // Check 3: Verify foreign keys pointing to users table
      const [validFKs] = await connection.execute(`
        SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_NAME = 'users' AND TABLE_SCHEMA = DATABASE()
      `);

      result.checks.push({
        check: 'Valid foreign keys to users table',
        status: 'info',
        details: validFKs.length > 0 ? validFKs : 'No foreign keys to users table found'
      });

      // Check 4: Test critical table queries
      const criticalTables = ['users', 'projects', 'subscriptions'];
      for (const table of criticalTables) {
        if (tableNames.includes(table)) {
          try {
            const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
            result.checks.push({
              check: `${table} table query test`,
              status: 'passed',
              details: `Table accessible, ${count[0].count} records`
            });
          } catch (error) {
            result.checks.push({
              check: `${table} table query test`,
              status: 'failed',
              details: error.message
            });
          }
        }
      }

      // Check 5: Test user-related operations
      try {
        const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
        const [projectCount] = await connection.execute('SELECT COUNT(*) as count FROM projects');

        result.checks.push({
          check: 'User-project relationship integrity',
          status: 'passed',
          details: `${userCount[0].count} users, ${projectCount[0].count} projects`
        });
      } catch (error) {
        result.checks.push({
          check: 'User-project relationship integrity',
          status: 'failed',
          details: error.message
        });
      }

      connection.release();

    } catch (error) {
      connection.release();
      throw error;
    }

    res.json(result);

  } catch (error) {
    console.error('Database integrity check error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete database integrity check',
      error: error.message
    });
  }
});

// Fix foreign key constraints endpoint
router.post('/fix-foreign-keys', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: 'This endpoint is only available in development mode'
      });
    }

    const { pool } = require('../config/database');
    const connection = await pool.getConnection();

    let result = {
      success: true,
      message: 'Foreign key constraints fixed',
      actions: []
    };

    try {
      // Check if projects table has user_id column and add foreign key if missing
      const [projectsColumns] = await connection.execute('DESCRIBE projects');
      const hasUserIdColumn = projectsColumns.some(col => col.Field === 'user_id');

      if (hasUserIdColumn) {
        // Check if foreign key already exists
        const [existingFK] = await connection.execute(`
          SELECT CONSTRAINT_NAME
          FROM information_schema.KEY_COLUMN_USAGE
          WHERE TABLE_NAME = 'projects'
          AND COLUMN_NAME = 'user_id'
          AND REFERENCED_TABLE_NAME = 'users'
          AND TABLE_SCHEMA = DATABASE()
        `);

        if (existingFK.length === 0) {
          // Add foreign key constraint
          await connection.execute(`
            ALTER TABLE projects
            ADD CONSTRAINT fk_projects_user_id
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
          `);
          result.actions.push('Added foreign key constraint: projects.user_id -> users.id');
        } else {
          result.actions.push('Foreign key constraint already exists: projects.user_id -> users.id');
        }
      } else {
        result.actions.push('Warning: projects table does not have user_id column');
      }

      // Check subscriptions table if it exists
      const [tables] = await connection.execute('SHOW TABLES');
      const tableNames = tables.map(t => Object.values(t)[0]);

      if (tableNames.includes('subscriptions')) {
        const [subscriptionsColumns] = await connection.execute('DESCRIBE subscriptions');
        const hasUserIdColumn = subscriptionsColumns.some(col => col.Field === 'user_id');

        if (hasUserIdColumn) {
          // Check if foreign key already exists
          const [existingFK] = await connection.execute(`
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_NAME = 'subscriptions'
            AND COLUMN_NAME = 'user_id'
            AND REFERENCED_TABLE_NAME = 'users'
            AND TABLE_SCHEMA = DATABASE()
          `);

          if (existingFK.length === 0) {
            // Add foreign key constraint
            await connection.execute(`
              ALTER TABLE subscriptions
              ADD CONSTRAINT fk_subscriptions_user_id
              FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            `);
            result.actions.push('Added foreign key constraint: subscriptions.user_id -> users.id');
          } else {
            result.actions.push('Foreign key constraint already exists: subscriptions.user_id -> users.id');
          }
        } else {
          result.actions.push('Warning: subscriptions table does not have user_id column');
        }
      }

      connection.release();

    } catch (error) {
      connection.release();
      throw error;
    }

    res.json(result);

  } catch (error) {
    console.error('Fix foreign keys error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fix foreign key constraints',
      error: error.message
    });
  }
});

// Check and fix data integrity issues
router.post('/fix-data-integrity', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: 'This endpoint is only available in development mode'
      });
    }

    const { pool } = require('../config/database');
    const connection = await pool.getConnection();

    let result = {
      success: true,
      message: 'Data integrity issues fixed',
      actions: []
    };

    try {
      // Check for orphaned projects (projects with user_id that don't exist in users table)
      const [orphanedProjects] = await connection.execute(`
        SELECT p.id, p.user_id, p.project_name
        FROM projects p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE u.id IS NULL
      `);

      if (orphanedProjects.length > 0) {
        result.actions.push(`Found ${orphanedProjects.length} orphaned projects`);

        // Option 1: Delete orphaned projects
        // Option 2: Assign to a default user
        // Let's assign to user ID 1 (demo user) if it exists
        const [defaultUser] = await connection.execute('SELECT id FROM users WHERE id = 1');

        if (defaultUser.length > 0) {
          await connection.execute('UPDATE projects SET user_id = 1 WHERE user_id NOT IN (SELECT id FROM users)');
          result.actions.push(`Assigned ${orphanedProjects.length} orphaned projects to default user (ID: 1)`);
        } else {
          // If no default user, delete orphaned projects
          await connection.execute('DELETE FROM projects WHERE user_id NOT IN (SELECT id FROM users)');
          result.actions.push(`Deleted ${orphanedProjects.length} orphaned projects`);
        }
      } else {
        result.actions.push('No orphaned projects found');
      }

      // Check for orphaned subscriptions
      const [orphanedSubscriptions] = await connection.execute(`
        SELECT s.id, s.user_id
        FROM subscriptions s
        LEFT JOIN users u ON s.user_id = u.id
        WHERE u.id IS NULL
      `);

      if (orphanedSubscriptions.length > 0) {
        result.actions.push(`Found ${orphanedSubscriptions.length} orphaned subscriptions`);
        // Delete orphaned subscriptions
        await connection.execute('DELETE FROM subscriptions WHERE user_id NOT IN (SELECT id FROM users)');
        result.actions.push(`Deleted ${orphanedSubscriptions.length} orphaned subscriptions`);
      } else {
        result.actions.push('No orphaned subscriptions found');
      }

      connection.release();

    } catch (error) {
      connection.release();
      throw error;
    }

    res.json(result);

  } catch (error) {
    console.error('Fix data integrity error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fix data integrity issues',
      error: error.message
    });
  }
});

// Development endpoint to add missing user
router.post('/add-dev-user', async (req, res) => {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return res.status(403).json({
        success: false,
        message: 'This endpoint is only available in development mode'
      });
    }

    const email = '<EMAIL>';
    const password = 'yangyuxuan3005';
    const username = 'yuyuxuan99';
    const preferred_language = 'zh';

    // Check if user already exists
    const existingUser = await executeQuerySingle(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser) {
      return res.json({
        success: true,
        message: 'User already exists',
        data: { email, username }
      });
    }

    // Hash password
    const saltRounds = 8;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Insert user
    await executeQuery(
      'INSERT INTO users (email, password_hash, username, preferred_language) VALUES (?, ?, ?, ?)',
      [email, hashedPassword, username, preferred_language]
    );

    res.json({
      success: true,
      message: 'Development user added successfully',
      data: {
        email,
        username,
        password: password,
        preferred_language
      }
    });

  } catch (error) {
    console.error('Add dev user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add development user'
    });
  }
});

module.exports = router;

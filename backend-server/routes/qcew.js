/**
 * 🏭 QCEW (Quarterly Census of Employment and Wages) API Routes
 * 季度就业与工资普查API路由 - 基于城市名称查询和BLS API调用
 * 
 * 提供以下功能：
 * - 城市名称到地区代码转换 
 * - QCEW数据获取 (从BLS API获取数据)
 * - 薪酬计算 (Annual Payroll Calculation)
 * - API可行性测试
 */

const express = require('express');
const fetch = require('node-fetch');
const areaCodeService = require('../us_labor/services/areaCodeService');
const router = express.Router();

// 劳工统计局API配置 - 使用正确的时间序列API端点
const BLS_API_CONFIG = {
    BASE_URL: 'https://api.bls.gov/publicAPI/v2/timeseries/data', // BLS官方API端点
    API_KEY: '48488361e34a4f2f96a5d482779d81a0', // 用户提供的API密钥
    TIMEOUT: 30000, // 30秒超时
    MAX_RETRIES: 3, // 最大重试次数
    // 仅使用真实BLS API数据
    USE_REAL_API_ONLY: true // 只使用真实API，失败时返回错误
};

/**
 * QCEW常量定义 - 与前端保持一致
 */
const QCEW_CONSTANTS = {
    // 系列ID构建参数
    SERIES_COMPONENTS: {
        SURVEY: 'EW',        // QCEW调查
        SEASONALITY: 'U',    // 未季节性调整
        INDUSTRY: '10',      // 所有行业
        OWNERSHIP: '5',      // 私营部门 (改为5，与用户示例一致)
        DATA_TYPES: {
            EMPLOYMENT: '1',        // 平均就业人数
            AVG_WEEKLY_WAGE: '6',   // 平均周薪 (主要使用)
            TOTAL_WAGES: '7',       // 总工资
            ESTABLISHMENTS: '8'     // 季度企业数
        }
    },
    
    // 自定义变量名到API字段的映射
    FIELD_MAPPING: {
        QCEW_AVG_WEEKLY_WAGE: 'avg_weekly_wage',
        QCEW_EMPLOYMENT: 'avg_employment',
        QCEW_TOTAL_WAGES: 'total_wages',
        QCEW_ESTABLISHMENTS: 'qtrly_estabs'
    },
    
    // 计算常量
    WEEKS_PER_YEAR: 52,
    
    // 默认参数
    DEFAULT_PARAMS: {
        area: 'US000',      // 全国
        industry: '10',     // 所有行业
        ownership: '5',     // 私营部门
        year: '2023',       // 默认年份 (2024数据可能未发布)
        dataType: '6'       // 平均周薪
    }
};

// 模拟数据已删除 - 仅使用真实BLS API数据

/**
 * 🔍 城市搜索API - 将城市名称转换为地区代码
 * GET /api/qcew/search-areas?city=Los Angeles&state=California
 */
router.get('/search-areas', async (req, res) => {
    try {
        const { city, state } = req.query;
        
        if (!city) {
            return res.status(400).json({
                success: false,
                error: '请提供城市名称',
                code: 'MISSING_CITY_NAME'
            });
        }

        console.log(`🔍 搜索地区代码: ${city}, ${state || '未指定州'}`);
        
        // 使用地区代码服务查找匹配项
        const matches = await areaCodeService.findAreaCodes(city, state);
        
        if (matches.length === 0) {
            return res.json({
                success: true,
                matches: [],
                message: `未找到匹配 "${city}" 的地区`,
                recommendations: areaCodeService.getRecommendedCities()
            });
        }

        console.log(`✅ 找到 ${matches.length} 个匹配的地区`);
        
        res.json({
            success: true,
            query: { city, state },
            matches: matches.map(match => ({
                areaCode: match.areaCode,
                areaTitle: match.areaTitle,
                score: match.score,
                type: match.isCounty ? 'County' : 
                      match.isMSA ? 'MSA' : 
                      match.isState ? 'State' : 
                      match.isCSA ? 'CSA' : 'Other',
                recommended: match.isCounty || match.isMSA // 推荐县级或MSA级别
            })),
            total: matches.length
        });

    } catch (error) {
        console.error('❌ 地区搜索失败:', error);
        res.status(500).json({
            success: false,
            error: '地区搜索服务错误',
            details: error.message
        });
    }
});

/**
 * 构建正确的QCEW系列ID
 * 格式示例: EWU100603756 = EW + U + 10 + 06037 + 5 + 6
 * @param {string} areaCode - 地区代码 (如: 06037)
 * @param {string} dataType - 数据类型代码 (如: 6为平均周薪)
 * @param {string} industry - 行业代码 (默认: 10为所有行业)
 * @param {string} ownership - 所有制代码 (默认: 5为私营部门)
 * @returns {string} - 系列ID
 */
function buildQCEWSeriesId(areaCode, dataType = '6', industry = '10', ownership = '5') {
    const { SURVEY, SEASONALITY } = QCEW_CONSTANTS.SERIES_COMPONENTS;
    
    // 确保area code格式正确
    const formattedAreaCode = areaCode.padStart(5, '0');
    
    // 构建系列ID: EW + U + 行业(2位) + 地区(5位) + 所有制(1位) + 数据类型(1位)
    const seriesId = `${SURVEY}${SEASONALITY}${industry}${formattedAreaCode}${ownership}${dataType}`;
    
    console.log(`🏗️ 构建系列ID: ${seriesId} (地区: ${areaCode}, 数据类型: ${dataType})`);
    
    return seriesId;
}

/**
 * 🏛️ 调用真实BLS时间序列API
 * @param {string} areaCode - 地区代码
 * @param {string} year - 年份
 * @param {string} dataType - 数据类型 (默认6为平均周薪)
 * @returns {Promise} - 真实BLS API响应
 */
async function callBlsQcewApi(areaCode, year = '2023', dataType = '6') {
    const seriesId = buildQCEWSeriesId(areaCode, dataType);
    let lastError = null;
    
    console.log(`📡 调用BLS官方API: ${seriesId} (${year}年)`);
    
    // 重试调用真实BLS API
    for (let attempt = 1; attempt <= BLS_API_CONFIG.MAX_RETRIES; attempt++) {
        try {
            const requestBody = {
                seriesid: [seriesId],
                startyear: year,
                endyear: year,
                registrationkey: BLS_API_CONFIG.API_KEY
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), BLS_API_CONFIG.TIMEOUT);

            const response = await fetch(BLS_API_CONFIG.BASE_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'IndustrialGeoDev/1.0'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`BLS服务器响应错误: HTTP ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.status !== 'REQUEST_SUCCEEDED') {
                const errorMsg = data.message?.[0] || 'BLS API未知错误';
                throw new Error(`BLS API错误: ${errorMsg}`);
            }

            // 检查是否有有效数据
            const series = data.Results?.series?.[0];
            const hasData = series?.data && series.data.length > 0;
            
            if (!hasData) {
                const blsMessage = data.message?.[0] || '数据不可用';
                const reason = `${blsMessage} (系列ID: ${seriesId}, 年份: ${year})`;
                throw new Error(`BLS数据不可用: ${reason}。请稍后重试或联系BLS数据支持。`);
            }

            console.log(`✅ BLS API调用成功 (第${attempt}次尝试): 获取到${series.data.length}条数据`);
            
            // 标记为真实BLS数据
            data.data_source = 'bls_api';
            data.api_response_time = new Date().toISOString();
            
            return data;

        } catch (error) {
            lastError = error;
            const errorType = error.name === 'AbortError' ? '超时' : '失败';
            
            console.log(`❌ BLS API调用${errorType} (第${attempt}/${BLS_API_CONFIG.MAX_RETRIES}次): ${error.message}`);
            
            // 如果不是最后一次尝试，递增延迟后重试
            if (attempt < BLS_API_CONFIG.MAX_RETRIES) {
                const delay = 2000 * attempt; // 2秒, 4秒, 6秒...
                console.log(`⏳ ${delay/1000}秒后重试...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    // 所有重试都失败了，抛出最终错误
    const finalError = `BLS API调用失败 (已重试${BLS_API_CONFIG.MAX_RETRIES}次): ${lastError?.message}`;
    console.error(`💥 ${finalError}`);
    throw new Error(finalError);
}

// 模拟响应函数已删除 - 仅使用真实BLS API数据

/**
 * 处理BLS API响应数据
 * @param {object} rawData - BLS API原始响应
 * @param {string} areaCode - 地区代码
 * @returns {object} - 处理后的数据
 */
function processBLSResponse(rawData, areaCode) {
    try {
        if (!rawData.Results?.series || rawData.Results.series.length === 0) {
            throw new Error('BLS API返回数据为空');
        }

        const series = rawData.Results.series[0];
        const latestData = series.data?.[0];

        if (!latestData) {
            throw new Error('系列数据为空');
        }

        const avgWeeklyWage = parseFloat(latestData.value);
        
        if (isNaN(avgWeeklyWage)) {
            throw new Error('无效的周薪数据');
        }

        return {
            area_code: areaCode,
            series_id: series.seriesID,
            year: latestData.year,
            period: latestData.period,
            avg_weekly_wage: avgWeeklyWage,
            data_source: 'bls_api', // 始终是真实BLS数据
            api_response_time: rawData.api_response_time,
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error('❌ 处理BLS响应失败:', error);
        throw new Error(`数据处理错误: ${error.message}`);
    }
}

/**
 * 📊 基于城市名称获取QCEW数据
 * POST /api/qcew/data-by-city
 * Body: { city: "Los Angeles", state: "California", year: "2024" }
 */
router.post('/data-by-city', async (req, res) => {
    try {
        const { city, state, year = '2024' } = req.body;
        
        if (!city) {
            return res.status(400).json({
                success: false,
                error: '请提供城市名称',
                code: 'MISSING_CITY_NAME'
            });
        }

        console.log(`📊 获取城市QCEW数据: ${city}, ${state || '未指定州'} (${year})`);
        
        // 第一步：查找地区代码
        const matches = await areaCodeService.findAreaCodes(city, state);
        
        if (matches.length === 0) {
            return res.status(404).json({
                success: false,
                error: `未找到匹配 "${city}" 的地区`,
                recommendations: areaCodeService.getRecommendedCities()
            });
        }

        // 选择最佳匹配（优先县级代码）
        const bestMatch = matches[0];
        const areaCode = bestMatch.areaCode;
        
        console.log(`✅ 选择地区: ${bestMatch.areaTitle} (${areaCode})`);

        // 第二步：获取QCEW数据
        const rawData = await callBlsQcewApi(areaCode, year);
        const processedData = processBLSResponse(rawData, areaCode);

        // 第三步：返回结果
        res.json({
            success: true,
            query: { city, state, year },
            area: {
                code: areaCode,
                title: bestMatch.areaTitle,
                type: bestMatch.isCounty ? 'County' : 
                      bestMatch.isMSA ? 'MSA' : 
                      bestMatch.isState ? 'State' : 'Other'
            },
            data: {
                avg_weekly_wage: processedData.avg_weekly_wage,
                series_id: processedData.series_id,
                year: processedData.year,
                period: processedData.period,
                data_source: processedData.data_source,
                timestamp: processedData.timestamp
            },
            alternatives: matches.slice(1, 5).map(match => ({
                areaCode: match.areaCode,
                areaTitle: match.areaTitle,
                score: match.score
            }))
        });

    } catch (error) {
        console.error('❌ 获取城市QCEW数据失败:', error);
        res.status(500).json({
            success: false,
            error: '数据获取失败',
            details: error.message
        });
    }
});

/**
 * 🧮 基于城市的薪酬计算API
 * POST /api/qcew/calculate-payroll-by-city
 * Body: { city, state, planned_fte, year }
 */
router.post('/calculate-payroll-by-city', async (req, res) => {
    try {
        const { city, state, planned_fte, year = '2024' } = req.body;
        
        if (!city || !planned_fte) {
            return res.status(400).json({
                success: false,
                error: '请提供城市名称和计划员工数量',
                code: 'MISSING_REQUIRED_PARAMS'
            });
        }

        const fte = parseFloat(planned_fte);
        if (isNaN(fte) || fte <= 0) {
            return res.status(400).json({
                success: false,
                error: '员工数量必须为正数',
                code: 'INVALID_FTE'
            });
        }

        console.log(`🧮 计算城市薪酬: ${city}, ${state || '未指定州'}, ${fte}人 (${year})`);
        
        // 获取城市QCEW数据
        const matches = await areaCodeService.findAreaCodes(city, state);
        
        if (matches.length === 0) {
            return res.status(404).json({
                success: false,
                error: `未找到匹配 "${city}" 的地区`,
                recommendations: areaCodeService.getRecommendedCities()
            });
        }

        const bestMatch = matches[0];
        const areaCode = bestMatch.areaCode;
        
        // 获取周薪数据
        const rawData = await callBlsQcewApi(areaCode, year);
        const processedData = processBLSResponse(rawData, areaCode);
        
        // 执行薪酬计算
        const avgWeeklyWage = processedData.avg_weekly_wage;
        const weeksPerYear = QCEW_CONSTANTS.WEEKS_PER_YEAR;
        const annualPayroll = avgWeeklyWage * weeksPerYear * fte;

        console.log(`✅ 薪酬计算完成: $${avgWeeklyWage}/周 × ${weeksPerYear}周 × ${fte}人 = $${annualPayroll.toLocaleString()}`);

        res.json({
            success: true,
            calculation: {
                formula: 'Annual_Payroll = QCEW_AVG_WEEKLY_WAGE × WEEKS_PER_YEAR × PLANNED_FTE',
                inputs: {
                    city,
                    state,
                    planned_fte: fte,
                    year,
                    area_code: areaCode,
                    area_title: bestMatch.areaTitle
                },
                components: {
                    avg_weekly_wage: avgWeeklyWage,
                    weeks_per_year: weeksPerYear,
                    planned_fte: fte
                },
                result: {
                    annual_payroll: annualPayroll,
                    formatted_payroll: `$${annualPayroll.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
                    monthly_payroll: annualPayroll / 12,
                    per_employee_annual: avgWeeklyWage * weeksPerYear
                },
                metadata: {
                    series_id: processedData.series_id,
                    data_source: processedData.data_source,
                    calculation_time: new Date().toISOString()
                }
            }
        });

    } catch (error) {
        console.error('❌ 薪酬计算失败:', error);
        res.status(500).json({
            success: false,
            error: '薪酬计算失败',
            details: error.message
        });
    }
});

/**
 * 🏥 服务健康检查
 * GET /api/qcew/health
 */
router.get('/health', async (req, res) => {
    try {
        // 测试地区代码服务
        const testCities = ['Los Angeles', 'New York'];
        const testResults = [];
        
        for (const city of testCities) {
            try {
                const matches = await areaCodeService.findAreaCodes(city);
                testResults.push({
                    city,
                    status: 'ok',
                    matches: matches.length
                });
            } catch (error) {
                testResults.push({
                    city,
                    status: 'error',
                    error: error.message
                });
            }
        }

        res.json({
            success: true,
            service: 'QCEW薪酬计算服务',
            status: '正常运行',
            version: '2.0.0',
            features: [
                '城市名称搜索',
                '地区代码转换', 
                '真实BLS API调用',
                '薪酬计算',
                '错误重试机制'
            ],
            area_code_service: {
                status: testResults.every(r => r.status === 'ok') ? 'healthy' : 'degraded',
                test_results: testResults
            },
            api_config: {
                bls_endpoint: BLS_API_CONFIG.BASE_URL,
                data_source: 'real_bls_api_only',
                timeout: BLS_API_CONFIG.TIMEOUT,
                max_retries: BLS_API_CONFIG.MAX_RETRIES
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('❌ 健康检查失败:', error);
        res.status(500).json({
            success: false,
            service: 'QCEW薪酬计算服务',
            status: '服务异常',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// 测试端点已删除 - 保持代码简洁

// 兼容性路由 - 保持向后兼容
router.get('/data', (req, res) => {
    res.json({
        message: '此端点已弃用，请使用 POST /api/qcew/data-by-city',
        new_endpoints: {
            city_search: 'GET /api/qcew/search-areas?city=Los Angeles',
            city_data: 'POST /api/qcew/data-by-city',
            payroll_calc: 'POST /api/qcew/calculate-payroll-by-city'
        }
    });
});

module.exports = router; 
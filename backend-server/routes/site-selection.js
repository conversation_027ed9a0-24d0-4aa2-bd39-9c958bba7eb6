/**
 * 🏭 Industrial Site Selection API Routes
 * 站点选择功能的Node.js路由
 * 
 * 提供以下功能：
 * - 站点勘探分析 (Site Prospecting)
 * - 财务承保分析 (Financial Underwriting)
 * - 压力测试 (Stress Testing)
 */

const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const router = express.Router();

// Python脚本路径 - 更新为新的site_selection_algorithm文件夹
const PYTHON_SCRIPT_PATH = path.join(__dirname, '../site_selection_algorithm/api_wrapper.py');

/**
 * 执行Python脚本的通用函数
 * @param {string} command - Python命令 (prospecting, financial, stress)
 * @param {object} data - 请求数据
 * @returns {Promise} - Python脚本执行结果
 */
function executePythonScript(command, data) {
    return new Promise((resolve, reject) => {
        const python = spawn('python3', [PYTHON_SCRIPT_PATH, command], {
            cwd: path.dirname(PYTHON_SCRIPT_PATH)
        });

        let stdout = '';
        let stderr = '';

        // 发送JSON数据到Python脚本
        python.stdin.write(JSON.stringify(data));
        python.stdin.end();

        // 收集输出
        python.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        python.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        python.on('close', (code) => {
            if (code !== 0) {
                console.error(`Python脚本错误 (退出码 ${code}):`, stderr);
                reject(new Error(`Python脚本执行失败: ${stderr}`));
                return;
            }

            try {
                const result = JSON.parse(stdout);
                resolve(result);
            } catch (error) {
                console.error('JSON解析错误:', error);
                console.error('Python输出:', stdout);
                reject(new Error(`JSON解析失败: ${error.message}`));
            }
        });

        python.on('error', (error) => {
            console.error('Python进程错误:', error);
            reject(error);
        });
    });
}

/**
 * POST /api/site-selection/prospecting
 * 站点勘探分析
 */
router.post('/prospecting', async (req, res) => {
    try {
        console.log('收到站点勘探请求:', req.body);

        // 验证请求数据
        const { search_area, parcel_filters, user_constraints, max_candidates } = req.body;

        if (!search_area || !search_area.type || !search_area.value) {
            return res.status(400).json({
                success: false,
                error: '搜索区域参数不完整',
                message: '请提供有效的搜索区域信息'
            });
        }

        // 准备Python脚本输入数据
        const requestData = {
            search_area,
            parcel_filters: parcel_filters || {},
            user_constraints: user_constraints || {},
            max_candidates: max_candidates || 10
        };

        // 执行Python脚本
        console.log('🔍 Starting site selection prospecting analysis...');
        console.log('Input data:', JSON.stringify(requestData, null, 2));

        const result = await executePythonScript('prospecting', requestData);

        console.log('✅ Prospecting analysis completed');
        console.log('Result:', JSON.stringify(result, null, 2));

        res.json(result);

    } catch (error) {
        console.error('站点勘探错误:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: '站点勘探分析失败'
        });
    }
});

/**
 * POST /api/site-selection/financial
 * 财务承保分析
 */
router.post('/financial', async (req, res) => {
    try {
        console.log('收到财务分析请求:', req.body);

        // 验证请求数据
        const { parcel, financial_params } = req.body;

        if (!parcel || !parcel.parcel_id) {
            return res.status(400).json({
                success: false,
                error: '地块数据不完整',
                message: '请提供有效的地块信息'
            });
        }

        // 准备Python脚本输入数据
        const requestData = {
            parcel,
            financial_params: financial_params || {}
        };

        // 执行Python脚本
        const result = await executePythonScript('financial', requestData);

        res.json(result);

    } catch (error) {
        console.error('财务分析错误:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: '财务分析失败'
        });
    }
});

/**
 * POST /api/site-selection/stress
 * 压力测试分析
 */
router.post('/stress', async (req, res) => {
    try {
        console.log('收到压力测试请求:', req.body);

        // 验证请求数据
        const { base_inputs, stress_params } = req.body;

        if (!base_inputs) {
            return res.status(400).json({
                success: false,
                error: '基础输入数据不完整',
                message: '请提供有效的基础财务数据'
            });
        }

        // 准备Python脚本输入数据
        const requestData = {
            base_inputs,
            stress_params: stress_params || {}
        };

        // 执行Python脚本
        const result = await executePythonScript('stress', requestData);

        res.json(result);

    } catch (error) {
        console.error('压力测试错误:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: '压力测试失败'
        });
    }
});

/**
 * GET /api/site-selection/test
 * 测试端点，验证Python环境
 */
router.get('/test', async (req, res) => {
    try {
        // 测试Python环境
        const testData = {
            search_area: {
                type: "city",
                value: "Dallas, TX"
            },
            parcel_filters: {
                min_area_sqft: 50000,
                max_area_sqft: 100000,
                zoning: "industrial"
            },
            user_constraints: {
                workers: 50,
                annual_kwh: 250000,
                port_hub: "Houston, TX"
            },
            max_candidates: 3
        };

        const result = await executePythonScript('prospecting', testData);

        res.json({
            success: true,
            message: 'Python环境测试成功',
            test_result: result
        });

    } catch (error) {
        console.error('Python环境测试失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            message: 'Python环境测试失败，请检查Python安装和依赖'
        });
    }
});

/**
 * GET /api/site-selection/health
 * 健康检查端点
 */
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: '站点选择API服务正常',
        timestamp: new Date().toISOString(),
        endpoints: [
            'POST /api/site-selection/prospecting',
            'POST /api/site-selection/financial', 
            'POST /api/site-selection/stress',
            'GET /api/site-selection/test',
            'GET /api/site-selection/health'
        ]
    });
});

module.exports = router;

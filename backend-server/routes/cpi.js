/**
 * 🏭 CPI (Consumer Price Index) Cost Escalation Calculator API Routes
 * 消费价格指数成本上涨计算器API路由 - 基于BLS CPI数据计算通胀调整
 * 
 * 提供以下功能：
 * - CPI数据获取 (从BLS API获取数据)
 * - 成本上涨计算 (基于通胀调整的成本换算)
 * - 多种CPI系列支持 (全国、地区等)
 */

const express = require('express');
const fetch = require('node-fetch');
const router = express.Router();

// 劳工统计局API配置 - 与QCEW路由保持一致
const BLS_API_CONFIG = {
    BASE_URL: 'https://api.bls.gov/publicAPI/v2/timeseries/data',
    API_KEY: '48488361e34a4f2f96a5d482779d81a0',
    TIMEOUT: 30000,
    MAX_RETRIES: 3,
    USE_REAL_API_ONLY: true
};

/**
 * CPI常用系列ID定义
 */
const CPI_SERIES = {
    // 全国平均CPI
    'US_ALL_ITEMS': {
        id: 'CUUR0000SA0',
        title: 'U.S. City Average - All Items',
        description: '美国城市平均 - 所有商品'
    },
    // 主要城市CPI系列
    'NORTHEAST_URBAN': {
        id: 'CUUR0100SA0',
        title: 'Northeast Urban - All Items',
        description: '东北部城市 - 所有商品'
    },
    'MIDWEST_URBAN': {
        id: 'CUUR0200SA0',
        title: 'Midwest Urban - All Items', 
        description: '中西部城市 - 所有商品'
    },
    'SOUTH_URBAN': {
        id: 'CUUR0300SA0',
        title: 'South Urban - All Items',
        description: '南部城市 - 所有商品'
    },
    'WEST_URBAN': {
        id: 'CUUR0400SA0',
        title: 'West Urban - All Items',
        description: '西部城市 - 所有商品'
    },
    // 主要大都市区CPI
    'LOS_ANGELES': {
        id: 'CUURA101SA0',
        title: 'Los Angeles-Long Beach-Anaheim, CA',
        description: '洛杉矶-长滩-阿纳海姆地区'
    },
    'NEW_YORK': {
        id: 'CUURA102SA0',
        title: 'New York-Newark-Jersey City, NY-NJ-PA',
        description: '纽约-纽瓦克-泽西城地区'
    },
    'CHICAGO': {
        id: 'CUURA207SA0',
        title: 'Chicago-Naperville-Elgin, IL-IN-WI',
        description: '芝加哥-内珀维尔-埃尔金地区'
    }
};

/**
 * 🔍 获取可用的CPI系列列表
 * GET /api/cpi/series
 */
router.get('/series', (req, res) => {
    try {
        const seriesList = Object.entries(CPI_SERIES).map(([key, series]) => ({
            key,
            id: series.id,
            title: series.title,
            description: series.description
        }));

        res.json({
            success: true,
            series: seriesList,
            total: seriesList.length
        });
    } catch (error) {
        console.error('❌ 获取CPI系列列表失败:', error);
        res.status(500).json({
            success: false,
            error: '获取CPI系列列表失败',
            details: error.message
        });
    }
});

/**
 * 🏛️ 调用真实BLS CPI API
 * @param {string} seriesId - CPI系列ID
 * @param {string} startYear - 开始年份
 * @param {string} endYear - 结束年份
 * @returns {Promise} - BLS API响应
 */
async function callBlsCpiApi(seriesId, startYear, endYear) {
    let lastError = null;
    
    console.log(`📡 调用BLS CPI API: ${seriesId} (${startYear}-${endYear})`);
    
    for (let attempt = 1; attempt <= BLS_API_CONFIG.MAX_RETRIES; attempt++) {
        try {
            const requestBody = {
                seriesid: [seriesId],
                startyear: startYear,
                endyear: endYear,
                registrationkey: BLS_API_CONFIG.API_KEY
            };

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), BLS_API_CONFIG.TIMEOUT);

            const response = await fetch(BLS_API_CONFIG.BASE_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'IndustrialGeoDev/1.0'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`BLS服务器响应错误: HTTP ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            
            if (data.status !== 'REQUEST_SUCCEEDED') {
                const errorMsg = data.message?.[0] || 'BLS API未知错误';
                throw new Error(`BLS API错误: ${errorMsg}`);
            }

            const series = data.Results?.series?.[0];
            if (!series?.data || series.data.length === 0) {
                throw new Error('BLS API返回空数据');
            }

            console.log(`✅ BLS CPI API调用成功: 获得 ${series.data.length} 条数据记录`);
            return data;

        } catch (error) {
            lastError = error;
            console.error(`❌ BLS CPI API调用失败 (尝试 ${attempt}/${BLS_API_CONFIG.MAX_RETRIES}):`, error.message);
            
            if (attempt < BLS_API_CONFIG.MAX_RETRIES) {
                const delay = Math.pow(2, attempt) * 1000;
                console.log(`⏳ ${delay}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    throw lastError;
}

/**
 * 解析日期格式 (支持 YYYY-MM 和 YYYY-MXX 格式)
 * @param {string} dateStr - 日期字符串 (如 "2022-01" 或 "2022-M01")
 * @returns {object} - {year, month}
 */
function parseDateString(dateStr) {
    if (!dateStr) throw new Error('日期字符串不能为空');
    
    // 支持多种格式: "2022-01", "2022-M01", "2022-1"等
    const match = dateStr.match(/^(\d{4})-?M?(\d{1,2})$/);
    if (!match) {
        throw new Error(`无效的日期格式: ${dateStr}, 支持格式: YYYY-MM 或 YYYY-MXX`);
    }
    
    const year = match[1];
    const month = match[2].padStart(2, '0');
    
    if (parseInt(month) < 1 || parseInt(month) > 12) {
        throw new Error(`无效的月份: ${month}, 月份应在1-12之间`);
    }
    
    return { year, month };
}

/**
 * 从BLS数据中查找特定日期的CPI值
 * @param {array} data - BLS API返回的数据数组
 * @param {string} targetYear - 目标年份
 * @param {string} targetMonth - 目标月份 (如 "01")
 * @returns {number|null} - CPI值或null
 */
function findCpiValue(data, targetYear, targetMonth) {
    for (const item of data) {
        if (item.year === targetYear && item.period === `M${targetMonth}`) {
            const value = parseFloat(item.value);
            if (!isNaN(value)) {
                return value;
            }
        }
    }
    return null;
}

/**
 * 💰 CPI成本上涨计算API
 * POST /api/cpi/calculate-escalation
 * 
 * Request Body:
 * {
 *   "base_cost": 1000,
 *   "base_date": "2022-01", 
 *   "current_date": "2025-05",
 *   "cpi_series_id": "CUUR0000SA0"
 * }
 */
router.post('/calculate-escalation', async (req, res) => {
    try {
        const { base_cost, base_date, current_date, cpi_series_id } = req.body;
        
        // 参数验证
        if (!base_cost || !base_date || !current_date || !cpi_series_id) {
            return res.status(400).json({
                success: false,
                error: '缺少必需参数',
                details: '需要提供: base_cost, base_date, current_date, cpi_series_id'
            });
        }

        if (typeof base_cost !== 'number' || base_cost <= 0) {
            return res.status(400).json({
                success: false,
                error: '基础成本必须是正数'
            });
        }

        console.log(`🧮 开始CPI成本上涨计算: $${base_cost} (${base_date} → ${current_date})`);

        // 解析日期
        let baseParsed, currentParsed;
        try {
            baseParsed = parseDateString(base_date);
            currentParsed = parseDateString(current_date);
        } catch (error) {
            return res.status(400).json({
                success: false,
                error: '日期格式错误',
                details: error.message
            });
        }

        // 确定需要查询的年份范围
        const startYear = baseParsed.year;
        const endYear = currentParsed.year;

        // 调用BLS CPI API
        const blsResponse = await callBlsCpiApi(cpi_series_id, startYear, endYear);
        const cpiData = blsResponse.Results.series[0].data;

        // 查找基础日期和目标日期的CPI值
        const baseCpi = findCpiValue(cpiData, baseParsed.year, baseParsed.month);
        const currentCpi = findCpiValue(cpiData, currentParsed.year, currentParsed.month);

        if (baseCpi === null) {
            return res.status(404).json({
                success: false,
                error: '未找到基础日期的CPI数据',
                details: `无法找到 ${base_date} 的CPI数据`
            });
        }

        if (currentCpi === null) {
            return res.status(404).json({
                success: false,
                error: '未找到目标日期的CPI数据', 
                details: `无法找到 ${current_date} 的CPI数据`
            });
        }

        // 计算上涨后的成本
        const escalationRatio = currentCpi / baseCpi;
        const escalatedCost = base_cost * escalationRatio;
        const inflationRate = ((escalationRatio - 1) * 100);

        console.log(`✅ CPI计算完成: $${base_cost} → $${escalatedCost.toFixed(2)} (通胀率: ${inflationRate.toFixed(2)}%)`);

        // 返回详细计算结果
        res.json({
            success: true,
            original_cost: base_cost,
            escalated_cost: Math.round(escalatedCost * 100) / 100, // 四舍五入到分
            base_period: base_date,
            base_cpi: baseCpi,
            current_period: current_date,
            current_cpi: currentCpi,
            series_id: cpi_series_id,
            inflation_rate: Math.round(inflationRate * 100) / 100, // 百分比，保留两位小数
            escalation_ratio: Math.round(escalationRatio * 10000) / 10000, // 保留四位小数
            calculation_summary: {
                formula: 'escalated_cost = base_cost × (current_cpi / base_cpi)',
                base_cost_formatted: `$${base_cost.toLocaleString()}`,
                escalated_cost_formatted: `$${escalatedCost.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`,
                period_span: `${base_date} 到 ${current_date}`,
                series_title: Object.values(CPI_SERIES).find(s => s.id === cpi_series_id)?.title || cpi_series_id
            }
        });

    } catch (error) {
        console.error('❌ CPI成本上涨计算失败:', error);
        res.status(500).json({
            success: false,
            error: '计算失败',
            details: error.message
        });
    }
});

/**
 * 🧪 CPI API测试端点
 * GET /api/cpi/test
 */
router.get('/test', async (req, res) => {
    try {
        console.log('🧪 测试CPI API连通性...');
        
        // 测试调用：获取2023年美国平均CPI数据
        const testData = await callBlsCpiApi('CUUR0000SA0', '2023', '2023');
        
        res.json({
            success: true,
            message: 'CPI API连通性测试成功',
            test_series: 'CUUR0000SA0 (U.S. City Average)',
            data_points: testData.Results.series[0].data.length,
            sample_data: testData.Results.series[0].data.slice(0, 3) // 返回前3条数据作为示例
        });
        
    } catch (error) {
        console.error('❌ CPI API测试失败:', error);
        res.status(500).json({
            success: false,
            error: 'CPI API测试失败',
            details: error.message
        });
    }
});

module.exports = router; 
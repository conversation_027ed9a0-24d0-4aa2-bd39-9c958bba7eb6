# 数据库连接和登录功能设置指南
# Database Connection and Login Feature Setup Guide

## 概述 / Overview

本指南将帮助您设置MySQL数据库并连接到工业地理开发平台，实现完整的用户登录和项目管理功能。

This guide will help you set up MySQL database and connect it to the Industrial Geography Development Platform for complete user authentication and project management functionality.

## 前提条件 / Prerequisites

1. **MySQL Server** - 确保MySQL服务器已安装并运行
2. **Node.js** - 版本 16+ 
3. **npm** - Node包管理器

## 数据库设置步骤 / Database Setup Steps

### 1. 启动MySQL服务 / Start MySQL Service

```bash
# macOS (使用Homebrew)
brew services start mysql

# 或者使用系统服务
sudo systemctl start mysql

# Windows
# 通过服务管理器启动MySQL服务
```

### 2. 连接到MySQL / Connect to MySQL

```bash
# 使用root用户连接（如果没有密码）
mysql -u root

# 如果有密码
mysql -u root -p
```

### 3. 执行数据库设置脚本 / Execute Database Setup Script

```bash
# 在MySQL命令行中执行
mysql -u root -p < database-setup.sql

# 或者在MySQL命令行内执行
source database-setup.sql;
```

### 4. 验证数据库设置 / Verify Database Setup

```sql
USE platform_core_db;
SHOW TABLES;
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM projects;
```

## 后端服务器配置 / Backend Server Configuration

### 1. 配置环境变量 / Configure Environment Variables

编辑 `backend-server/.env` 文件：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password_here
DB_NAME=platform_core_db

# JWT配置
JWT_SECRET=industrial_geo_dev_jwt_secret_2024
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3001
NODE_ENV=development
```

### 2. 启动后端服务器 / Start Backend Server

```bash
cd backend-server
node server.cjs
```

成功启动后，您应该看到：
```
✅ 数据库连接成功
🚀 Industrial Geography Backend Server running on port 3001
```

## 前端应用启动 / Frontend Application Startup

```bash
# 在项目根目录
npm run dev
```

访问：http://localhost:5173

## 测试登录功能 / Test Login Functionality

### 示例用户账户 / Sample User Accounts

1. **演示账户 / Demo Account**
   - 邮箱: <EMAIL>
   - 密码: password123
   - 用户名: demo_user

2. **测试账户 / Test Account**
   - 邮箱: <EMAIL>
   - 密码: password123
   - 用户名: test_user

### 功能测试 / Feature Testing

1. **用户注册 / User Registration**
   - 访问 http://localhost:5173/login
   - 点击"注册"选项卡
   - 填写邮箱、用户名、密码
   - 提交注册

2. **用户登录 / User Login**
   - 使用注册的账户或示例账户登录
   - 成功后跳转到主页面

3. **项目管理 / Project Management**
   - 访问 http://localhost:5173/ai-analyzer
   - 创建新项目
   - 查看项目列表
   - 编辑和删除项目

## API端点测试 / API Endpoint Testing

```bash
# 健康检查
curl http://localhost:3001/api/health

# 数据库状态检查
curl http://localhost:3001/api/db-status

# 用户注册
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","username":"newuser"}'

# 用户登录
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 故障排除 / Troubleshooting

### 数据库连接问题 / Database Connection Issues

1. **检查MySQL服务状态**
   ```bash
   # macOS
   brew services list | grep mysql
   
   # Linux
   systemctl status mysql
   ```

2. **检查MySQL用户权限**
   ```sql
   SELECT user, host FROM mysql.user WHERE user = 'root';
   SHOW GRANTS FOR 'root'@'localhost';
   ```

3. **重置MySQL密码（如需要）**
   ```bash
   mysql -u root -p
   ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
   FLUSH PRIVILEGES;
   ```

### 端口冲突 / Port Conflicts

如果端口3001被占用：
```bash
# 查找占用端口的进程
lsof -i :3001

# 修改backend-server/.env中的PORT配置
PORT=3002
```

## 架构说明 / Architecture Overview

```
前端 (React) ←→ 后端API (Express.js) ←→ 数据库 (MySQL)
Frontend     ←→ Backend API        ←→ Database

- 前端：http://localhost:5173
- 后端：http://localhost:3001
- 数据库：localhost:3306
```

## 下一步 / Next Steps

1. 配置生产环境数据库
2. 添加更多用户管理功能
3. 实现项目协作功能
4. 添加数据备份和恢复机制

#!/bin/bash

# 工业地理开发平台启动脚本
# Industrial Geography Development Platform Startup Script

echo "🚀 启动工业地理开发平台 / Starting Industrial Geography Development Platform"
echo "=================================================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    echo "❌ Node.js not found, please install Node.js first"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    echo "❌ npm not found, please install npm first"
    exit 1
fi

echo "✅ npm 版本: $(npm --version)"

# 检查MySQL是否运行
if command -v mysql &> /dev/null; then
    if mysql -u root -e "SELECT 1;" &> /dev/null; then
        echo "✅ MySQL 连接成功"
        echo "✅ MySQL connection successful"
    else
        echo "⚠️  MySQL 连接失败，请检查数据库配置"
        echo "⚠️  MySQL connection failed, please check database configuration"
        echo "   参考 DATABASE_SETUP_README.md 进行数据库设置"
        echo "   Refer to DATABASE_SETUP_README.md for database setup"
    fi
else
    echo "⚠️  MySQL 未找到，请安装并配置 MySQL"
    echo "⚠️  MySQL not found, please install and configure MySQL"
fi

echo ""
echo "🔧 检查依赖 / Checking dependencies..."

# 检查前端依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖 / Installing frontend dependencies..."
    npm install
fi

# 检查后端依赖
if [ ! -d "backend-server/node_modules" ]; then
    echo "📦 安装后端依赖 / Installing backend dependencies..."
    cd backend-server
    npm install
    cd ..
fi

echo ""
echo "🌐 启动服务器 / Starting servers..."

# 启动后端服务器
echo "🔧 启动后端服务器 / Starting backend server..."
cd backend-server
node server.cjs &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:3001/api/health > /dev/null; then
    echo "✅ 后端服务器启动成功 / Backend server started successfully"
    echo "   API地址: http://localhost:3001"
else
    echo "❌ 后端服务器启动失败 / Backend server failed to start"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务器
echo "🎨 启动前端服务器 / Starting frontend server..."
npm run dev &
FRONTEND_PID=$!

# 等待前端启动
sleep 5

echo ""
echo "🎉 启动完成！/ Startup complete!"
echo "=================================================================="
echo "📱 前端应用: http://localhost:5173"
echo "🔧 后端API:  http://localhost:3001"
echo "📊 健康检查: http://localhost:3001/api/health"
echo "🗄️  数据库状态: http://localhost:3001/api/db-status"
echo ""
echo "🔑 登录页面: http://localhost:5173/login"
echo "📊 AI分析器: http://localhost:5173/ai-analyzer"
echo ""
echo "按 Ctrl+C 停止所有服务器 / Press Ctrl+C to stop all servers"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务器 / Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
wait

# Industrial Geo Dev

Industrial Parks Management System

## 项目整体目录结构

```text
IndustrialGeoDev/
├── backend-server/               # 后端服务（Node.js + Python）
│   ├── config/                   # 数据库与全局配置
│   ├── middleware/               # Express 中间件
│   ├── routes/                   # API 路由
│   ├── regard_api/               # Python FastAPI 适配层
│   ├── site_selection_algorithm/ # 站点选址核心算法（Python）
│   ├── us_labor/                 # 美国劳工数据 API
│   └── server.cjs                # Express 服务器入口
├── public/                       # 前端静态资源与数据
├── src/                          # React 前端源代码
│   ├── components/               # 可复用 UI 组件
│   ├── pages/                    # 页面级组件
│   ├── services/                 # 与后端交互的 API 封装
│   ├── utils/                    # 通用工具函数
│   └── styles/                   # 全局与组件级样式
├── database-setup.sql            # 数据库初始化脚本
├── start-*.sh / *.bat            # 启动与辅助脚本
├── *.md                          # 设计与实现文档
└── README.md
```

### 1. backend-server 目录

```text
backend-server/
├── add-user.js
├── config/
│   └── database.js
├── middleware/
│   └── auth.js
├── routes/
│   ├── auth.js
│   ├── cpi.js
│   ├── projects.js
│   ├── qcew.js
│   └── site-selection.js
├── regard_api/
│   ├── __init__.py
│   ├── data_models.py
│   ├── mock_data.py
│   └── regrid_client.py
├── site_selection_algorithm/
│   ├── __init__.py
│   ├── api_wrapper.py
│   ├── core_algorithm.py
│   ├── cost_calculator.py
│   ├── scoring_system.py
│   └── utils.py
├── us_labor/
│   ├── index.js
│   ├── routes/
│   │   ├── laus.js
│   │   └── qcew.js
│   └── services/
│       ├── areaCodeService.js
│       ├── lausDataService.js
│       └── qcewDataService.js
└── server.cjs
```

### 2. 前端结构 (src)

```
src
├── App.jsx                     # 应用主入口和路由配置
├── assets                      # 静态资源 (如图片)
├── components                  # UI 组件
│   ├── analysis                # 共享的分析类组件 (图表、数据面板)
│   │   ├── TimeSeriesChartPanel.jsx # 时间序列图表面板
│   │   ├── TimeSeriesPanel.jsx      # 时间序列通用面板
│   │   ├── labor               # 劳动力相关分析组件
│   │   │   ├── LaborDataCharts.jsx         # 劳动力数据图表 (用于详情页)
│   │   │   ├── LaborEconomicsAnalysis.jsx  # 劳动力经济分析 (用于详情页)
│   │   │   └── LaborTrendDataPanel.jsx     # 劳动力趋势数据面板 (用于详情页地图)
│   │   └── migration           # 迁移相关分析组件
│   │       ├── EducationLevelAnalysis.jsx      # 教育水平分析 (用于详情页)
│   │       ├── EmploymentMigrationAnalysis.jsx # 就业迁移分析 (用于详情页)
│   │       ├── ExpectedStayAnalysis.jsx        # 预期停留时间分析 (用于详情页)
│   │       ├── FutureMigrationReasonAnalysis.jsx # 未来迁移原因分析 (用于详情页)
│   │       ├── HouseholdRegistrationAnalysis.jsx # 户籍登记分析 (用于详情页)
│   │       ├── MaritalStatusAnalysis.jsx       # 婚姻状况分析 (用于详情页)
│   │       ├── MigrationFrequencyAnalysis.jsx  # 迁移频率分析 (用于详情页)
│   │       ├── MigrationPopup.jsx              # 迁移数据弹出窗口
│   │       ├── MigrationTypeAnalysis.jsx       # 迁移类型分析 (用于详情页)
│   │       ├── MoneyGoodsReceiptAnalysis.jsx   # 接收金钱和物品分析 (用于详情页)
│   │       ├── MoneyGoodsRecipientAnalysis.jsx # 金钱和物品接收者分析 (用于详情页)
│   │       ├── MoneyGoodsSenderAnalysis.jsx    # 金钱和物品发送者分析 (用于详情页)
│   │       ├── MoneyGoodsSendingAnalysis.jsx   # 发送金钱和物品分析 (用于详情页)
│   │       ├── MoneyPurposeAnalysis.jsx        # 接收金钱用途分析 (用于详情页)
│   │       ├── MoneySendingPurposeAnalysis.jsx # 发送金钱用途分析 (用于详情页)
│   │       └── PopulationMigrationAnalysis.jsx # 人口迁移分析 (用于详情页)
│   ├── common                  # 通用组件
│   ├── controls                # 共享的地图工具或控件
│   │   ├── MapTimeSlider.jsx   # 地图时间滑块
│   │   ├── RulerTool.jsx       # 地图测量工具
│   │   └── WeatherModule.jsx   # 天气信息模块
│   ├── detail-map              # 仅用于详情地图界面的组件
│   │   ├── ParkCharts.jsx      # 园区特定图表 (用于详情页侧边栏)
│   │   └── ParkOverview.jsx    # 园区概览面板 (用于详情页侧边栏)
│   ├── layers                  # 共享的地图图层组件
│   │   ├── EnvironmentMonitoringLayer.jsx # 环境监测图层
│   │   ├── LaborTrendOverlay.jsx          # 劳动力趋势覆盖层
│   │   ├── MigrationTypeLayer.jsx         # 迁移类型图层
│   │   ├── ThailandProvincesLayer.jsx     # 泰国省份图层
│   │   └── USStatesLayer.jsx              # 美国州图层
│   ├── main-map                # 仅用于主地图界面的组件
│   │   ├── CustomMarkerHandler.jsx        # 自定义标记处理组件
│   │   ├── EnhancedMapControls.jsx        # 增强的地图控制按钮
│   │   ├── EnhancedMapZoomControl.jsx     # 增强的地图缩放控制
│   │   ├── LayerExplanation.jsx           # 图层说明组件
│   │   ├── MapLayersControl.jsx           # 地图图层控制组件
│   │   ├── NationwideMigrationSummary.jsx # 全国迁移摘要
│   │   └── filters                        # 主地图筛选器相关
│   │       ├── MigrationControls.jsx      # 迁移数据控制面板
│   │       ├── MigrationReasonFilter.jsx  # 迁移原因筛选器
│   │       └── MigrationTypeFilter.jsx    # 迁移类型筛选器
│   ├── map                     # 共享的、核心的地图元素或基础功能
│   │   ├── leaflet-gridlayer.js           # Leaflet网格图层扩展
│   │   ├── leaflet-providers.js           # Leaflet提供者配置
│   │   ├── MapStyleSwitcher.jsx           # 地图样式切换器
│   │   ├── MapZoomControl.jsx             # 地图缩放控件
│   │   └── MarkerTooltip.jsx              # 通用标记提示
│   └── search                  # 共享的搜索功能组件
│       └── FacilitySearch.jsx  # 设施搜索组件
├── css                         # 额外的CSS样式文件
├── docs                        # 项目文档
├── pages                       # 页面级组件
│   ├── MainPage.jsx            # 主地图页面
│   └── ParkDetailPage.jsx      # 园区详情页面
├── styles                      # CSS 样式文件
│   ├── App.css                 # 应用全局样式
│   ├── ChartPanel.css          # 图表面板样式
│   ├── DetailPageSideBar.css   # 详情页侧边栏样式
│   ├── EducationLevelAnalysis.css # 教育水平分析组件样式
│   ├── EmploymentMigrationAnalysis.css # 就业迁移分析样式
│   ├── ExpectedStayAnalysis.css # 预期停留时间分析样式
│   ├── FacilitySearch.css      # 设施搜索样式
│   ├── FutureMigrationReasonAnalysis.css # 未来迁移原因分析样式
│   ├── Heatmap.css             # 热力图样式
│   ├── HouseholdRegistrationAnalysis.css # 户籍登记分析样式
│   ├── MapButtons.css          # 地图按钮样式
│   ├── MapComponent.css        # 地图组件样式
│   ├── MapFilterControls.css   # 地图过滤控件样式
│   ├── MaritalStatusAnalysis.css # 婚姻状况分析组件样式
│   ├── MigrationFrequencyAnalysis.css # 迁移频率分析样式
│   ├── MigrationTypeAnalysis.css # 迁移类型分析样式
│   ├── MoneyGoodsReceiptAnalysis.css # 接收金钱和物品分析样式
│   ├── MoneyGoodsRecipientAnalysis.css # 金钱和物品接收者分析样式
│   ├── MoneyGoodsSenderAnalysis.css # 金钱和物品发送者分析样式
│   ├── MoneyGoodsSendingAnalysis.css # 发送金钱和物品分析样式
│   ├── MoneyPurposeAnalysis.css # 接收金钱用途分析样式
│   ├── MoneySendingPurposeAnalysis.css # 发送金钱用途分析样式
│   ├── WeatherTool.css         # 天气工具样式
│   └── ...                     # 其他样式文件
├── utils                       # 通用工具函数
│   ├── mapUtils.js             # 地图工具函数
│   ├── markerUtils.js          # 标记工具函数
│   ├── translationHelper.js    # 翻译辅助函数
│   └── translations.js         # 翻译文本配置
└── main.jsx                    # 应用渲染入口
```

## 搜索功能

我们优化了搜索功能，支持更智能的园区搜索体验。

### 功能特点

- 支持模糊搜索，按优先级匹配：精确匹配 > 开头匹配 > 包含匹配
- 支持中英文搜索
- 搜索结果按匹配度排序
- 当没有匹配结果时显示友好提示信息
- 点击搜索结果可直接跳转到对应园区
- 精确输入园区名称也能找到对应园区

### 搜索界面设计

- 搜索按钮点击后，以按钮为起点优雅展开搜索面板
- 搜索面板包含搜索输入框和结果显示区域
- 支持键盘Enter键触发搜索
- 搜索结果显示园区名称和位置信息
- 适配深色模式，提供一致的视觉体验
- 搜索过程中显示加载状态
- 搜索结果为空时显示明确的提示信息

### 搜索实现细节

- 使用多级匹配算法实现模糊搜索
- 搜索结果根据匹配程度排序展示
- 支持美国和泰国两个国家的园区搜索
- 搜索面板使用Framer Motion实现流畅动画效果
- 搜索结果项添加悬停效果，提升用户体验

## 关于天气工具

我们新增了天气工具功能，可在园区详情页面右上角查看当前天气和天气预报。

### 功能特点

- 显示当前园区位置的实时天气
- 提供未来48小时逐小时天气预报
- 提供未来8天的每日天气预报
- 支持查看周边城市天气情况

### API 使用限制

该功能使用 OpenWeather API，有以下限制：

- 每日限制1000次调用
- 数据每10分钟更新一次
- 请谨慎使用，避免超出API调用限制

### 环境变量设置

- 在开发环境中，需要在项目根目录创建 `.env` 文件并添加以下内容：

```
REACT_APP_OPENWEATHER_API_KEY=your_api_key_here
```

- 在生产环境中，请确保在部署服务器上正确设置此环境变量

## 数据表说明

本系统使用以下数据表进行人口迁移分析和可视化。数据表按优先级分组，以便于开发和实施。

### 高优先级数据表

| 表号 | 表名 | 前端功能 | 用户价值 | 实现组件 |
|------|------|----------|----------|----------|
| 表1 | 按迁移状态、年龄组、性别和地区分类的人口 | 交互式筛选器、柱状图、热力图 | 可视化按迁移状态、年龄、性别和地区的人口分布，帮助理解人口趋势 | PopulationMigrationAnalysis.jsx |
| 表5 | 15岁及以上就业人口（按迁移状态、当前职业、性别和地区） | 交互式图表、职业分布热力图 | 分析就业状况和职业，帮助企业和政府规划劳动力需求 | EmploymentMigrationAnalysis.jsx |
| 表6 | 15岁及以上就业人口（按迁移状态、行业、性别和地区） | 基于地区的柱状图、行业和迁移状态筛选器 | 评估不同行业对迁移与非迁移人口的雇佣情况 | EmploymentMigrationAnalysis.jsx |
| 表7 | 15岁及以上就业人口（按迁移状态、就业状态、性别和地区） | 带有就业状态、迁移状态等筛选器的柱状图 | 评估就业趋势与迁移状态的相关性 | EmploymentMigrationAnalysis.jsx |
| 表9 | 按迁移流动、性别和地区划分的迁移人数 | 流程图、交互式地图、柱状图 | 可视化迁移流动趋势（城乡流动等），用于政策调整 | MigrationTypeLayer.jsx |
| 表10 | 按迁移类型、性别和地区划分的迁移人数 | 交互式地图、柱状图 | 分析迁移类型（临时vs永久）和区域迁移流动 | NationwideMigrationSummary.jsx, MigrationTypeLayer.jsx |
| 表11 | 按迁移原因、性别和地区划分的迁移人数 | 原因筛选器、柱状图和饼图 | 了解人们迁移的原因，影响迁移管理政策 | MigrationReasonFilter.jsx |

### 中优先级数据表

| 表号 | 表名 | 前端功能 | 用户价值 | 实现组件 |
|------|------|----------|----------|----------|
| 表2 | 户籍登记状态 | 饼图、带地区和性别筛选器的柱状图 | 探索户籍类型比例，影响地方规划和服务分配 | HouseholdRegistrationAnalysis.jsx |
| 表3 | 6岁及以上人口（按迁移状态、教育程度、性别和地区） | 按教育水平和迁移状态的交互式图表 | 了解移民的教育水平，为教育和劳动力政策提供信息 | EducationLevelAnalysis.jsx |
| 表4 | 15岁及以上人口（按迁移状态、婚姻状况、性别和地区） | 带有婚姻状况等筛选器的图表 | 可视化迁移状态与婚姻状况的关系 | MaritalStatusAnalysis.jsx |
| 表8 | 按迁移类型、地区和行政区域划分的迁移人数 | 交互式地图、迁移类型筛选器 | 探索行政区域的迁移类型，协助区域政策制定 | MigrationTypeAnalysis.jsx |
| 表12 | 按过去12个月内迁移次数、性别和地区划分的迁移人数 | 柱状图、折线图、滑块筛选器 | 评估迁移频率，预测迁移趋势和区域资源需求 | MigrationFrequencyAnalysis.jsx |
| 表13 | 按预期停留时间、性别和地区划分的迁移人数 | 折线图、时间筛选器、交互式地图 | 预测移民预期停留时间，辅助基础设施规划 | ExpectedStayAnalysis.jsx |
| 表14 | 按预期未来迁移原因、性别和地区划分的迁移人数 | 柱状图、未来迁移原因筛选器 | 预测未来迁移驱动因素，提前规划 | FutureMigrationReasonAnalysis.jsx |

### 低优先级数据表

| 表号 | 表名 | 前端功能 | 用户价值 | 实现组件 |
|------|------|----------|----------|----------|
| 表15 | 按接收金钱和物品、性别和地区划分的迁移人数 | 柱状图、显示资金流的堆叠图 | 了解迁移的财务影响，关注汇款和援助分配 | MoneyGoodsReceiptAnalysis.jsx |
| 表16 | 按发送者、性别和地区划分的接收金钱或物品的迁移人数 | 堆叠柱状图、发送者类型筛选器 | 按发送者可视化汇款数据，识别移民财务的主要贡献者 | MoneyGoodsSenderAnalysis.jsx |
| 表17 | 按使用目的、性别和地区划分的接收金钱的迁移人数 | 饼图、柱状图、使用目的筛选器 | 显示汇款的使用方式，辅助区域财务规划 | MoneyPurposeAnalysis.jsx |
| 表18 | 按发送金钱和物品、性别和地区划分的迁移人数 | 柱状图、区域洞察的交互式地图 | 分析资金和物品的反向流动，辅助贸易和经济规划 | MoneyGoodsSendingAnalysis.jsx |
| 表19 | 按接收者、性别和地区划分的发送金钱和物品的迁移人数 | 显示接收地区的堆叠柱状图 | 探索移民如何及向何处发送资金和物品 | MoneyGoodsRecipientAnalysis.jsx |
| 表20 | 按发送金钱用途、性别和地区划分的发送金钱的迁移人数 | 饼图、柱状图 | 显示汇款的使用方式，协助经济建模和预测 | MoneySendingPurposeAnalysis.jsx |

## 地图图层

本系统提供多种地图图层，用于可视化不同类型的地理数据。

### 基础图层

- **底图切换**：支持多种底图样式，包括标准地图、卫星图像、暗色模式等
- **行政区划图层**：
  - 美国州级边界 (USStatesLayer.jsx)
  - 泰国省级边界 (ThailandProvincesLayer.jsx)

### 数据可视化图层

- **迁移类型图层** (MigrationTypeLayer.jsx)：显示不同类型的人口迁移流动
- **劳动力趋势覆盖层** (LaborTrendOverlay.jsx)：展示劳动力分布和变化趋势
- **环境监测图层** (EnvironmentMonitoringLayer.jsx)：显示环境相关数据
- **经济地理热点图层** (EconomicHotspotLayer.jsx)：基于区位商(LQ)或重力模型显示经济活动强度

### 交互功能

- **自定义标记工具**：允许用户在地图上添加自定义标记点
- **测量工具**：用于测量地图上的距离和面积
- **时间滑块控件**：用于查看不同时间点的数据变化

## UI 设计与优化

我们对系统界面进行了全面优化，提升用户体验和视觉效果。

### 设计原则

- **一致性**：采用卡片式设计，保持界面元素的对称布局和统一风格
- **视觉层次**：通过阴影、间距和颜色对比创建清晰的视觉层次
- **响应式设计**：避免界面溢出和水平滚动，确保在不同设备上的良好显示
- **加载状态**：为数据加载过程提供明确的加载指示器（动画或骨架屏）
- **工具提示**：提供自适应明暗模式的工具提示，点击按钮后自动关闭

### 色彩系统

- **暗色模式**：为暗色模式优化按钮和界面元素，降低色彩饱和度
- **通知指示器**：改进红点通知的显示方式，使其悬浮在按钮上方而不被裁剪
- **图标一致性**：避免侧边栏中的重复图标，保持视觉一致性

## 多语言支持

系统支持多语言显示，满足不同用户的需求。

### 语言切换

- **语言切换按钮**：位于左侧栏的首位，提供英文和中文切换选项
- **中文标题**：中文模式下显示"工域探索"作为系统标题
- **界面元素翻译**：完整的中文翻译覆盖所有侧边栏元素和主要界面组件

### 标记显示

- **默认英文显示**：标记默认显示英文名称（来自Region_EN列）
- **泰语支持**：提供切换到泰语名称的选项
- **详情界面**：详情界面默认显示英文，特定元素可显示中文

## 系统状态管理

### 导航与状态保持

- **详情界面默认视图**：详情界面默认显示地图视图，仅在首次进入时有优化的飞入动画
- **地图视图状态保持**：从其他标签页返回地图视图时，保持之前的缩放级别
- **主界面状态保持**：从详情界面返回主界面时，保持之前的地图位置、缩放级别和侧边栏状态

### 虚拟数据说明

为了展示系统的完整功能，我们在某些地方使用了虚拟数据：

- **泰国园区数据**：泰国园区的月租金（monthly_leasing_cost）、面积（square_footage）和状态（status）是随机生成的虚拟数据
- **可建面积数据**：所有园区的可建面积是基于总面积计算的虚拟数据（总面积的80%）
- **经济地理热点数据**：区位商(LQ)计算使用的行业数据来自Thai_labor_economics.json，重力模型使用的中心点数据是预设的经济中心坐标
- **其他虚拟数据**：在README中标记为虚拟数据的部分不应用于实际决策，仅用于演示系统功能

### 数据可视化与图表

- **简化图表UI**：图表仅在上方显示标题，移除悬停/点击效果和冗余图例
- **饼图数据完整性**：饼图包含所有数据类别，使用详细时间描述而非简化标签
- **就业状态分布图表**：左侧显示总体性别数据，右侧显示筛选后的男/女数据
- **分页显示**：迁移数据界面使用基于分页的显示方式，而非可滚动显示

## 经济地理热点功能

经济地理热点功能是一个强大的可视化工具，用于分析和展示区域经济活动的强度和分布。

### 功能概述

- **区位商(LQ)模型**：显示特定行业在不同地区的专业化程度
- **重力模型**：基于经济中心的影响力展示经济活动的辐射范围
- **交互式控制面板**：调整模型类型、行业、强度和服务半径
- **直观的图例**：帮助理解不同颜色代表的经济强度级别

### 数据来源与计算方法

#### 区位商(LQ)模型

区位商是衡量某地区特定行业集中度的指标，计算公式为：

```
LQ = (ei/e) / (Ei/E)
```

其中：
- ei = 地区i中行业e的就业人数
- e = 地区i的总就业人数
- Ei = 全国行业e的就业人数
- E = 全国总就业人数

数据来源：
- 使用`Thai_labor_economics.json`中的行业就业数据
- 支持制造业、政府部门、私营企业和个体经营四种行业类型
- 区位商值大于1表示该地区在此行业有专业化优势

#### 重力模型

重力模型基于经济地理学中的引力理论，计算公式为：

```
G = (M1 * M2) / (Distance^2)
```

其中：
- M1, M2 = 两地的经济质量（如GDP、人口等）
- Distance = 两地之间的距离

实现方式：
- 预设了7个经济中心点，包括曼谷、东部经济走廊等
- 每个中心点有不同的经济质量值（mass）
- 系统计算地图上每个网格点受到的经济引力
- 引力值越高的区域显示越强的热力效果

### 技术实现

- 使用Leaflet的圆形图层（L.circle）创建热力效果
- 根据计算结果动态调整圆形的半径、颜色和透明度
- 使用五级颜色方案表示不同强度级别
- 支持深色模式和浅色模式的颜色适配

### 使用场景

- **产业规划**：识别特定行业的集聚区，辅助产业园区选址
- **投资决策**：发现具有行业优势的地区，优化投资布局
- **政策制定**：了解区域经济结构，制定有针对性的发展政策
- **市场分析**：识别核心市场和边缘市场，优化市场策略

## 开发与部署

### 开发环境

- 本项目使用 React 和 Vite 构建
- 使用 Leaflet 作为地图库
- 使用 Chart.js 和 React-Chartjs-2 进行数据可视化
- 使用 Framer Motion 实现动画效果

### 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器（前端+后端）
npm run dev:all

# 仅启动前端
npm run dev

# 仅启动后端
npm run backend:dev
```

### 故障排除

#### 依赖包问题解决

如果遇到依赖包缺失错误（特别是在macOS上遇到`@rollup/rollup-darwin-x64`错误），请使用以下命令解决：

```bash
# 清理并重新安装依赖
npm run reinstall

# 或手动清理
npm run clean
npm cache clean --force
npm install
```

#### 环境检查

使用环境检查工具快速诊断开发环境：

```bash
# 检查前端环境状态
npm run check-env

# 检查后端系统健康状况
npm run check-backend
```

环境检查会检查：
- Node.js和npm版本
- 关键依赖包是否正确安装
- 端口占用情况
- 平台特定依赖

后端健康检查会检查：
- 服务器运行状态
- 数据库连接
- 用户认证系统
- API端点响应
- 数据库完整性
- 环境配置

#### 常用维护命令

```bash
# 清理依赖和缓存
npm run clean

# 完全重新安装依赖
npm run reinstall

# 检查依赖状态
npm ls --depth=0

# 检查过时的依赖
npm outdated
```

### 部署注意事项

- 确保正确设置所有环境变量
- 对于天气功能，需要有效的 OpenWeather API 密钥
- 确保服务器支持 React 路由的历史模式
- 建议使用 HTTPS 以确保 API 调用的安全性

### 项目文档

- [依赖问题解决指南](./DEPENDENCY_ISSUE_RESOLUTION.md) - 详细的依赖包问题解决和预防方案

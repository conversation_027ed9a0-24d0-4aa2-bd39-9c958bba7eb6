-- 数据库迁移脚本：添加收藏功能
-- Database Migration Script: Add Favorites Feature
-- 
-- 此脚本为现有的 projects 表添加 isFavorite 列
-- This script adds the isFavorite column to the existing projects table

-- 使用数据库
USE platform_core_db;

-- 检查列是否已存在，如果不存在则添加
-- Check if column exists, add if it doesn't exist
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'platform_core_db'
    AND TABLE_NAME = 'projects'
    AND COLUMN_NAME = 'isFavorite'
);

-- 添加 isFavorite 列（如果不存在）
-- Add isFavorite column (if it doesn't exist)
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE projects ADD COLUMN isFavorite TINYINT(1) DEFAULT 0 COMMENT "Favorite status: 0 = not favorited, 1 = favorited" AFTER status',
    'SELECT "Column isFavorite already exists" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果列被成功添加）
-- Add index (if column was successfully added)
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = 'platform_core_db'
    AND TABLE_NAME = 'projects'
    AND INDEX_NAME = 'idx_isFavorite'
);

SET @sql_index = IF(@index_exists = 0 AND @column_exists = 0,
    'ALTER TABLE projects ADD INDEX idx_isFavorite (isFavorite)',
    'SELECT "Index idx_isFavorite already exists or column was not added" AS message'
);

PREPARE stmt_index FROM @sql_index;
EXECUTE stmt_index;
DEALLOCATE PREPARE stmt_index;

-- 显示更新后的表结构
-- Show updated table structure
DESCRIBE projects;

-- 显示当前项目数据（验证迁移）
-- Show current project data (verify migration)
SELECT id, user_id, project_name, status, isFavorite, created_at 
FROM projects 
LIMIT 10;

-- 迁移完成消息
-- Migration completion message
SELECT 'Database migration completed successfully. isFavorite column added to projects table.' AS migration_status;

# AI Site Analyzer 测试用例

## 测试输入示例 (Test Input Examples)

### 示例 1: 中文输入
```
我们公司正在寻找一个位于上海市浦东新区的工业园区，
需要靠近G2京沪高速公路和浦东国际机场，
园区应该有完善的电力、供水和污水处理设施，
适合电子制造业和新能源汽车产业，
园区面积需要在300-500亩之间，
希望有良好的物流配送条件。
```

**预期提取结果:**
- Cities: ["上海市", "浦东新区"]
- Countries: ["中国"]
- Highways: ["G2京沪高速公路"]
- Industries: ["电子制造业", "新能源汽车产业"]
- Utilities: ["电力", "供水", "污水处理设施"]
- Other: ["浦东国际机场", "物流配送", "300-500亩"]

### 示例 2: 英文输入
```
We are looking for an industrial park located in California, USA,
preferably near Interstate 5 or Highway 101,
close to the Port of Los Angeles for shipping convenience.
The park should have reliable electricity, water supply, and waste management facilities.
We need space for automotive manufacturing and aerospace industries,
with good access to skilled workforce and research institutions.
```

**预期提取结果:**
- Cities: ["Los Angeles"]
- Countries: ["USA"]
- Highways: ["Interstate 5", "Highway 101"]
- Industries: ["automotive manufacturing", "aerospace industries"]
- Utilities: ["electricity", "water supply", "waste management facilities"]
- Other: ["Port of Los Angeles", "skilled workforce", "research institutions"]

### 示例 3: 混合信息
```
Looking for industrial site in Shenzhen, China, near Guangzhou-Shenzhen Highway,
with access to Shenzhen Port and Hong Kong International Airport.
Need facilities for electronics manufacturing, telecommunications equipment,
and renewable energy technology development.
Required infrastructure: high-speed internet, reliable power grid, 
water treatment plant, and logistics center.
```

**预期提取结果:**
- Cities: ["Shenzhen", "Guangzhou", "Hong Kong"]
- Countries: ["China"]
- Highways: ["Guangzhou-Shenzhen Highway"]
- Industries: ["electronics manufacturing", "telecommunications equipment", "renewable energy technology"]
- Utilities: ["high-speed internet", "power grid", "water treatment plant", "logistics center"]
- Other: ["Shenzhen Port", "Hong Kong International Airport"]

### 示例 4: 简单查询
```
Need factory space in Bangkok, Thailand.
Close to highways and airport.
For food processing industry.
```

**预期提取结果:**
- Cities: ["Bangkok"]
- Countries: ["Thailand"]
- Highways: ["highways"]
- Industries: ["food processing industry"]
- Utilities: []
- Other: ["airport", "factory space"]

## 测试步骤 (Test Steps)

### 1. 基本功能测试
1. 访问 http://localhost:5174
2. 点击 SERVICES → AI Site Analyzer
3. 创建新项目：
   - 项目名称: "测试项目1"
   - 项目描述: "用于测试AI分析功能"
4. 打开项目
5. 输入上述任一示例文本
6. 点击"AI分析"按钮
7. 验证分析结果是否合理

### 2. 多语言测试
1. 在项目页面切换语言（EN/中文）
2. 验证界面文本是否正确切换
3. 使用中文和英文输入分别测试
4. 验证AI能否正确处理不同语言的输入

### 3. 错误处理测试
1. 输入空文本，验证按钮是否禁用
2. 输入无意义文本，验证AI如何处理
3. 断网情况下测试，验证错误提示
4. 测试重试功能

### 4. 项目管理测试
1. 创建多个项目
2. 删除项目
3. 在不同项目间切换
4. 验证项目数据是否正确保存和加载

### 5. 响应式设计测试
1. 在不同屏幕尺寸下测试界面
2. 移动设备上的使用体验
3. 验证所有功能在小屏幕上是否可用

## 性能测试 (Performance Testing)

### API响应时间
- 正常情况下应在5-15秒内返回结果
- 超时时间设置为30秒

### 用户体验
- 加载动画应流畅显示
- 按钮状态应正确反映操作状态
- 错误信息应清晰易懂

## 已知限制 (Known Limitations)

1. **本地存储**: 项目数据仅保存在浏览器本地，清除缓存会丢失数据
2. **API限制**: 免费API可能有调用频率限制
3. **网络依赖**: 需要稳定的网络连接才能使用AI分析功能
4. **语言处理**: AI对某些专业术语或地名可能识别不准确

## 故障排除 (Troubleshooting)

### 常见问题及解决方案

1. **分析按钮无响应**
   - 检查输入框是否有内容
   - 查看浏览器控制台是否有错误信息
   - 尝试刷新页面

2. **分析结果为空**
   - 尝试使用更具体的描述
   - 检查网络连接
   - 重新提交分析请求

3. **页面无法加载**
   - 确认开发服务器正在运行
   - 检查URL是否正确
   - 清除浏览器缓存

4. **语言切换无效**
   - 刷新页面
   - 检查localStorage是否可用

## 测试报告模板

### 测试环境
- 浏览器: 
- 操作系统: 
- 网络状况: 
- 测试时间: 

### 测试结果
- [ ] 项目创建功能正常
- [ ] AI分析功能正常
- [ ] 结果显示正确
- [ ] 多语言切换正常
- [ ] 错误处理正确
- [ ] 响应式设计良好

### 发现的问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 

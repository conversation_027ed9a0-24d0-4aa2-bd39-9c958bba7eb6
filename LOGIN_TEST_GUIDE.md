# 登录注册页面测试指南

## 测试环境
- 开发服务器: `http://localhost:5174`
- 登录页面: `http://localhost:5174/login`
- 首页: `http://localhost:5174/`

## 功能测试清单

### 1. 页面访问测试
- [ ] 直接访问 `/login` 路径
- [ ] 从首页点击"登录/注册"按钮导航
- [ ] 页面加载完整，无控制台错误
- [ ] 3D背景和粒子效果正常显示

### 2. 界面元素测试
- [ ] 顶部导航栏显示正常
- [ ] "返回首页"按钮功能正常
- [ ] 语言切换按钮工作正常
- [ ] 左侧品牌区域显示完整
- [ ] 右侧表单区域布局正确

### 3. 语言切换测试
- [ ] 点击语言切换按钮
- [ ] 界面文字正确切换中英文
- [ ] 中文字体显示正常
- [ ] 语言偏好保存到本地存储

### 4. 登录表单测试
#### 4.1 基本功能
- [ ] 邮箱输入框正常
- [ ] 密码输入框正常
- [ ] "记住我"复选框
- [ ] "忘记密码"链接
- [ ] 登录按钮响应

#### 4.2 表单验证
- [ ] 空邮箱提交显示错误
- [ ] 无效邮箱格式显示错误
- [ ] 空密码提交显示错误
- [ ] 密码少于6位显示错误
- [ ] 输入正确信息后错误消失

### 5. 注册表单测试
#### 5.1 模式切换
- [ ] 点击"没有账户？立即注册"
- [ ] 表单切换到注册模式
- [ ] 显示用户名和确认密码字段
- [ ] 表单数据和错误状态清空

#### 5.2 注册验证
- [ ] 用户名为空显示错误
- [ ] 用户名少于3位显示错误
- [ ] 确认密码为空显示错误
- [ ] 两次密码不匹配显示错误
- [ ] 所有字段正确后可以提交

### 6. 表单提交测试
- [ ] 点击提交按钮显示加载动画
- [ ] 加载期间按钮禁用
- [ ] 1.5秒后跳转到主页面
- [ ] 表单数据在控制台正确显示

### 7. 社交登录测试
- [ ] Google登录按钮显示正常
- [ ] GitHub登录按钮显示正常
- [ ] 按钮悬停效果正常
- [ ] 点击暂无功能（预留接口）

### 8. 响应式测试
#### 8.1 桌面端 (1024px+)
- [ ] 左右分栏布局正常
- [ ] 所有元素显示完整
- [ ] 动画效果流畅

#### 8.2 平板端 (768px-1024px)
- [ ] 布局切换为单列
- [ ] 品牌区域居中显示
- [ ] 表单宽度适配

#### 8.3 移动端 (480px-768px)
- [ ] 顶部导航适配
- [ ] 表单内边距调整
- [ ] 字体大小适配
- [ ] 触摸操作正常

### 9. 动画效果测试
- [ ] 页面进入淡入动画
- [ ] 左侧品牌区域滑入
- [ ] 右侧表单区域滑入
- [ ] 按钮悬停效果
- [ ] 表单切换动画
- [ ] 3D背景粒子动画

### 10. 错误处理测试
- [ ] 网络错误模拟
- [ ] 表单验证错误显示
- [ ] 错误消息多语言支持
- [ ] 错误状态样式正确

## 测试步骤

### 完整流程测试
1. 访问首页 `http://localhost:5174/`
2. 点击顶部"登录/注册"按钮
3. 验证跳转到登录页面
4. 测试语言切换功能
5. 尝试提交空表单，验证错误提示
6. 填写无效邮箱，验证邮箱格式检查
7. 填写短密码，验证密码长度检查
8. 切换到注册模式
9. 测试注册表单验证
10. 填写正确信息并提交
11. 验证跳转到主页面

### 快速验证
```bash
# 启动开发服务器
npm run dev

# 在浏览器中测试以下URL
http://localhost:5174/login
http://localhost:5174/
```

## 已知问题和限制

### 当前限制
- 社交登录按钮仅为UI展示
- 表单提交为模拟操作
- 忘记密码功能未实现
- 无后端API集成

### 预期行为
- 所有表单验证在前端完成
- 提交成功后跳转到主页面
- 语言偏好保存在localStorage
- 表单数据在控制台输出

## 性能测试

### 加载性能
- [ ] 页面首次加载时间 < 2秒
- [ ] 3D背景渲染流畅
- [ ] 动画帧率稳定

### 内存使用
- [ ] 长时间停留无内存泄漏
- [ ] 3D场景资源正确释放
- [ ] 事件监听器正确清理

## 浏览器兼容性测试

### 推荐浏览器
- [ ] Chrome 90+ 
- [ ] Firefox 88+
- [ ] Safari 14+
- [ ] Edge 90+

### 功能支持
- [ ] CSS Grid布局
- [ ] CSS动画和过渡
- [ ] WebGL支持（3D背景）
- [ ] ES6+语法支持

## 调试信息

### 控制台输出
正常情况下应该看到：
```
Form submitted: {email: "...", password: "...", ...}
```

### 错误排查
如果遇到问题，检查：
1. 浏览器控制台是否有错误
2. 网络请求是否正常
3. 本地存储是否可用
4. WebGL是否支持

---

**注意**: 这是UI测试版本，所有后端功能都是模拟实现。在生产环境中需要集成真实的认证系统。

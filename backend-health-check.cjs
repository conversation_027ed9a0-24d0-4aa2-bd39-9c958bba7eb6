#!/usr/bin/env node

/**
 * 后端健康检查脚本
 * 用于定期检查后端系统的各项功能
 */

const fs = require('fs');
const path = require('path');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`
};

class BackendHealthChecker {
  constructor() {
    this.results = [];
    this.token = null;
  }

  async check(name, testFn) {
    try {
      console.log(`🔍 检查 ${name}...`);
      const result = await testFn();
      this.results.push({ name, status: 'passed', ...result });
      console.log(colors.green(`✅ ${name}: 通过`));
      if (result.details) {
        console.log(`   详情: ${result.details}`);
      }
    } catch (error) {
      this.results.push({ name, status: 'failed', error: error.message });
      console.log(colors.red(`❌ ${name}: 失败`));
      console.log(`   错误: ${error.message}`);
    }
  }

  async checkServerHealth() {
    const response = await fetch(`${BASE_URL}/api/health`);
    if (!response.ok) throw new Error(`Server responded with ${response.status}`);
    
    const data = await response.json();
    return {
      details: `状态: ${data.status}, 环境: ${data.environment}`
    };
  }

  async checkDatabaseConnection() {
    const response = await fetch(`${BASE_URL}/api/db-status`);
    if (!response.ok) throw new Error(`Database check failed with ${response.status}`);
    
    const data = await response.json();
    if (!data.success) throw new Error('Database connection failed');
    
    return {
      details: `数据库: ${data.database}`
    };
  }

  async checkAuthentication() {
    // 测试登录
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginIdentifier: '<EMAIL>',
        password: 'yangyuxuan3005'
      })
    });

    if (!response.ok) throw new Error(`Login failed with ${response.status}`);
    
    const data = await response.json();
    if (!data.success) throw new Error('Login unsuccessful');
    
    this.token = data.data.token;
    
    return {
      details: `用户: ${data.data.user.username}, 令牌已获取`
    };
  }

  async checkProtectedEndpoint() {
    if (!this.token) throw new Error('No token available');
    
    const response = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: { 'Authorization': `Bearer ${this.token}` }
    });

    if (!response.ok) throw new Error(`Protected endpoint failed with ${response.status}`);
    
    const data = await response.json();
    if (!data.success) throw new Error('Protected endpoint unsuccessful');
    
    return {
      details: `当前用户: ${data.data.user.email}`
    };
  }

  async checkDatabaseIntegrity() {
    const response = await fetch(`${BASE_URL}/api/auth/check-database-integrity`);
    if (!response.ok) throw new Error(`Integrity check failed with ${response.status}`);
    
    const data = await response.json();
    if (!data.success) throw new Error('Database integrity check failed');
    
    const passedChecks = data.checks.filter(check => check.status === 'passed').length;
    const totalChecks = data.checks.length;
    
    return {
      details: `通过 ${passedChecks}/${totalChecks} 项检查`
    };
  }

  async checkEnvironmentConfig() {
    const envPath = path.join(__dirname, 'backend-server', '.env');
    
    if (!fs.existsSync(envPath)) {
      throw new Error('Environment file not found');
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredVars = ['DB_HOST', 'DB_NAME', 'JWT_SECRET', 'PORT'];
    const missingVars = [];

    for (const varName of requiredVars) {
      if (!envContent.includes(varName)) {
        missingVars.push(varName);
      }
    }

    if (missingVars.length > 0) {
      throw new Error(`Missing environment variables: ${missingVars.join(', ')}`);
    }

    return {
      details: `所有必需的环境变量都已配置`
    };
  }

  async checkAPI端点() {
    const endpoints = [
      { path: '/api/health', method: 'GET' },
      { path: '/api/db-status', method: 'GET' },
      { path: '/api/projects', method: 'GET', requiresAuth: true }
    ];

    let passedCount = 0;
    let failedCount = 0;

    for (const endpoint of endpoints) {
      try {
        const headers = {};
        if (endpoint.requiresAuth && this.token) {
          headers['Authorization'] = `Bearer ${this.token}`;
        }

        const response = await fetch(`${BASE_URL}${endpoint.path}`, {
          method: endpoint.method,
          headers
        });

        if (response.ok) {
          passedCount++;
        } else {
          failedCount++;
        }
      } catch (error) {
        failedCount++;
      }
    }

    if (failedCount > 0) {
      throw new Error(`${failedCount} endpoints failed`);
    }

    return {
      details: `${passedCount} 个端点正常响应`
    };
  }

  async runAllChecks() {
    console.log(colors.bold('\n🏥 后端系统健康检查'));
    console.log('='.repeat(40));
    console.log(`检查时间: ${new Date().toLocaleString()}\n`);

    await this.check('服务器健康状态', () => this.checkServerHealth());
    await this.check('数据库连接', () => this.checkDatabaseConnection());
    await this.check('环境配置', () => this.checkEnvironmentConfig());
    await this.check('用户认证', () => this.checkAuthentication());
    await this.check('受保护端点', () => this.checkProtectedEndpoint());
    await this.check('API端点响应', () => this.checkAPI端点());
    await this.check('数据库完整性', () => this.checkDatabaseIntegrity());

    this.generateReport();
  }

  generateReport() {
    console.log('\n' + '='.repeat(40));
    console.log(colors.bold('📋 健康检查报告'));
    console.log('='.repeat(40));

    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const total = this.results.length;

    console.log(`总计检查项: ${total}`);
    console.log(colors.green(`通过: ${passed}`));
    console.log(failed > 0 ? colors.red(`失败: ${failed}`) : colors.green(`失败: ${failed}`));

    if (failed === 0) {
      console.log(colors.green('\n🎉 所有检查都通过了！后端系统运行正常。'));
    } else {
      console.log(colors.red('\n⚠️ 发现问题，请检查失败的项目。'));
      
      const failedChecks = this.results.filter(r => r.status === 'failed');
      console.log('\n失败的检查项:');
      failedChecks.forEach(check => {
        console.log(colors.red(`- ${check.name}: ${check.error}`));
      });
    }

    // 生成JSON报告
    const report = {
      timestamp: new Date().toISOString(),
      summary: { total, passed, failed },
      checks: this.results
    };

    const reportPath = path.join(__dirname, 'backend-health-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 主函数
async function main() {
  const checker = new BackendHealthChecker();
  
  try {
    await checker.runAllChecks();
  } catch (error) {
    console.log(colors.red('\n💥 健康检查过程中发生错误:'));
    console.log(colors.red(error.message));
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = BackendHealthChecker; 
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 环境检查工具');
console.log('================');

// 检查Node.js版本
console.log('\n📦 运行时环境:');
console.log(`Node.js版本: ${process.version}`);

try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`npm版本: ${npmVersion}`);
} catch (e) {
  console.log('❌ npm未找到');
}

console.log(`平台: ${process.platform}`);
console.log(`架构: ${process.arch}`);

// 检查是否存在多个node_modules目录
console.log('\n📂 目录检查:');
const nodeModulesPaths = [
  'node_modules',
  'node_modules 2',
  'node_modules.old',
  'node_modules.backup'
];

const existingPaths = nodeModulesPaths.filter(p => fs.existsSync(p));
if (existingPaths.length > 1) {
  console.log('⚠️  检测到多个node_modules目录:', existingPaths.join(', '));
  console.log('💡 建议: 运行 npm run clean && npm install');
} else if (existingPaths.length === 1) {
  console.log('✅ node_modules目录正常');
} else {
  console.log('❌ 未找到node_modules目录');
  console.log('💡 建议: 运行 npm install');
}

// 检查package-lock.json
if (fs.existsSync('package-lock.json')) {
  console.log('✅ package-lock.json存在');
} else {
  console.log('⚠️  package-lock.json不存在');
}

// 检查关键依赖
console.log('\n🔧 关键依赖检查:');
const keyDeps = [
  '@rollup/rollup-darwin-x64',
  'vite', 
  'react',
  'concurrently',
  'nodemon'
];

keyDeps.forEach(dep => {
  try {
    require.resolve(dep);
    console.log(`✅ ${dep}`);
  } catch (e) {
    console.log(`❌ ${dep} (未找到)`);
  }
});

// 检查平台特定的Rollup依赖
console.log('\n🎯 平台特定依赖检查:');
const platformDeps = {
  'darwin': '@rollup/rollup-darwin-x64',
  'linux': '@rollup/rollup-linux-x64-gnu',
  'win32': '@rollup/rollup-win32-x64-msvc'
};

const expectedDep = platformDeps[process.platform];
if (expectedDep) {
  try {
    require.resolve(expectedDep);
    console.log(`✅ ${expectedDep} (当前平台需要)`);
  } catch (e) {
    console.log(`❌ ${expectedDep} (当前平台需要但未找到)`);
    console.log('💡 建议: 运行 npm run reinstall');
  }
}

// 检查端口占用
console.log('\n🚪 端口检查:');
const ports = [3001, 5173];

ports.forEach(port => {
  try {
    const result = execSync(`lsof -i :${port}`, { encoding: 'utf8', stdio: 'pipe' });
    if (result.trim()) {
      console.log(`⚠️  端口 ${port} 已被占用`);
    } else {
      console.log(`✅ 端口 ${port} 可用`);
    }
  } catch (e) {
    console.log(`✅ 端口 ${port} 可用`);
  }
});

// 检查磁盘空间
console.log('\n💾 磁盘空间检查:');
try {
  const stats = fs.statSync('.');
  const freeSpace = fs.statSync('.').size;
  console.log('✅ 磁盘空间检查完成');
} catch (e) {
  console.log('⚠️  无法检查磁盘空间');
}

console.log('\n================');
console.log('环境检查完成！');

// 提供建议
console.log('\n💡 常用命令:');
console.log('- 清理重装: npm run reinstall');
console.log('- 启动开发: npm run dev:all');
console.log('- 检查依赖: npm ls --depth=0');
console.log('- 环境检查: npm run check-env'); 
# AI Site Analyzer - AI选址分析器

## 概述 (Overview)

AI Site Analyzer 是一个智能工业选址分析平台，允许用户通过自然语言输入描述工业园区信息，然后使用AI技术自动提取和分析关键信息。

AI Site Analyzer is an intelligent industrial site analysis platform that allows users to input industrial park information in natural language and automatically extract and analyze key information using AI technology.

## 功能特点 (Features)

### 🚀 核心功能
- **项目管理**: 创建和管理多个分析项目
- **AI分析**: 使用DeepSeek AI模型分析工业园区信息
- **信息提取**: 自动提取城市、国家、高速公路、行业、设施等关键信息
- **多语言支持**: 支持中英文界面切换
- **本地存储**: 项目数据本地存储，关闭浏览器后数据会丢失

### 🎨 设计特点
- **科技风格**: 现代化科技感界面设计
- **响应式布局**: 适配不同屏幕尺寸
- **动画效果**: 流畅的交互动画
- **暗色主题**: 护眼的暗色界面

## 使用方法 (Usage)

### 1. 访问AI分析器
1. 在主页点击顶部导航栏的 "SERVICES"
2. 在下拉菜单中选择 "AI Site Analyzer" / "AI选址分析器"

### 2. 创建项目
1. 点击 "Create New Project" / "创建新项目" 按钮
2. 输入项目名称和描述
3. 点击 "Create" / "创建" 完成项目创建

### 3. 分析工业园区信息
1. 点击项目卡片上的 📂 图标打开项目
2. 在左侧输入区域输入工业园区相关信息，例如：
   ```
   我们正在寻找一个位于上海附近的工业园区，
   需要靠近G2京沪高速公路，
   有完善的电力和水利设施，
   适合制造业和物流行业，
   园区面积约500亩。
   ```
3. 点击 "AI分析" / "Analyze with AI" 按钮
4. 等待AI分析完成，查看右侧的分析结果

### 4. 查看分析结果
AI会自动提取以下类型的信息：
- **城市 (Cities)**: 提及的城市名称
- **国家 (Countries)**: 提及的国家名称
- **高速公路 (Highways)**: 交通路线信息
- **行业 (Industries)**: 目标行业类型
- **设施 (Utilities)**: 基础设施需求
- **其他 (Other)**: 其他相关关键词

## 技术架构 (Technical Architecture)

### 前端技术栈
- **React 19**: 用户界面框架
- **React Router**: 路由管理
- **CSS3**: 样式和动画
- **Fetch API**: HTTP请求

### AI集成
- **DeepSeek API**: 通过OpenRouter平台调用
- **模型**: tngtech/deepseek-r1t-chimera:free (DeepSeek R1T Chimera版本)
- **安全策略**: 支持后端代理和前端直调两种模式

### 数据存储
- **本地存储**: 使用localStorage存储项目数据
- **数据结构**:
  ```javascript
  {
    id: "项目ID",
    name: "项目名称",
    description: "项目描述",
    createdAt: "创建时间",
    updatedAt: "更新时间",
    status: "状态"
  }
  ```

## API配置 (API Configuration)

### 后端API服务器（推荐）
为了安全起见，建议使用后端服务器代理API调用：

1. 安装后端依赖：
   ```bash
   # 复制server-package.json为package.json
   cp server-package.json package.json
   npm install
   ```

2. 启动后端服务器：
   ```bash
   node server.js
   # 或使用nodemon进行开发
   npm run dev
   ```

3. 后端服务器将在 http://localhost:3001 运行

### 前端直调（本地策略）
如果后端服务器不可用，系统会自动回退到前端直接调用API的模式。

## 文件结构 (File Structure)

```
src/
├── pages/
│   ├── AIAnalyzerPage.jsx     # AI分析器项目列表页面
│   └── AIProjectPage.jsx     # AI项目工作页面
├── styles/
│   ├── AIAnalyzerPage.css     # 项目列表页面样式
│   └── AIProjectPage.css     # 项目工作页面样式
└── App.jsx                    # 路由配置

server.js                      # 后端API服务器
server-package.json           # 后端依赖配置
```

## 路由配置 (Routing)

- `/ai-analyzer` - AI分析器项目列表页面
- `/ai-project/:id` - 具体项目的工作页面

## 安全考虑 (Security Considerations)

1. **API密钥保护**:
   - 生产环境应使用后端代理
   - 避免在前端代码中暴露API密钥

2. **数据隐私**:
   - 项目数据仅存储在本地浏览器
   - 不会上传到服务器

3. **输入验证**:
   - 限制输入文本长度
   - 验证API响应格式

## 未来规划 (Future Plans)

### 数据库集成
按照提供的数据库结构，未来将集成：
- **projects表**: 项目基本信息
- **recommended_sites表**: AI推荐的选址结果
- **reports表**: 生成的分析报告

### 功能扩展
- 地图可视化集成
- 更详细的分析报告
- 导出功能
- 用户账户系统
- 项目协作功能

## 故障排除 (Troubleshooting)

### 常见问题

1. **AI分析失败**
   - 检查网络连接
   - 确认API密钥有效
   - 尝试重新分析

2. **项目无法创建**
   - 检查浏览器localStorage是否可用
   - 清除浏览器缓存后重试

3. **页面无法访问**
   - 确认开发服务器正在运行
   - 检查路由配置是否正确

### 开发调试
- 打开浏览器开发者工具查看控制台错误
- 检查网络请求状态
- 验证localStorage中的数据

## 贡献指南 (Contributing)

1. Fork项目仓库
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证 (License)

本项目采用MIT许可证。

## 虚拟机部署注意事项 (VM Deployment Notes)

> **本节用于记录从本地开发迁移到虚拟机/生产环境时需要特别注意和调整的事项，便于未来回忆和部署。**

### 1. API密钥管理 (API Key Management)
- **本地开发**：API密钥存储在本地 `server.js`，仅本机可用。
- **虚拟机/生产环境**：
  - 不要将API密钥写死在代码仓库，建议通过环境变量（如 `.env` 文件）管理。
  - 部署时在虚拟机上设置环境变量，例如：
    ```bash
    export OPENROUTER_API_KEY=your_api_key_here
    ```
  - `server.js` 需读取环境变量获取API密钥。

### 2. 后端API服务器部署 (Backend API Server Deployment)
- **本地**：`node server.js` 只在本机监听 `localhost:3001`。
- **虚拟机/生产环境**：
  - 确保 `server.js` 监听公网IP或0.0.0.0，便于外部访问。
  - 检查防火墙和云服务安全组，开放3001端口或配置反向代理（如Nginx）。
  - 推荐使用 `pm2` 或 `systemd` 等进程管理工具保证服务稳定运行。

### 3. 前端API地址调整 (Frontend API Endpoint)
- **本地**：前端通过 `/api/ai-analyze` 或 `localhost:3001` 访问后端。
- **虚拟机/生产环境**：
  - 如前后端分离部署，需将前端API地址指向虚拟机后端实际地址（如 `https://yourdomain.com/api/ai-analyze`）。
  - 可通过 `.env` 或配置文件设置前端API基础路径。
  - 若使用Nginx反代，配置 `/api` 路径转发到后端服务。

### 4. 数据存储与持久化 (Data Storage & Persistence)
- **当前**：所有项目数据存储在浏览器 `localStorage`，仅本地有效，关闭浏览器或更换设备会丢失。
- **生产建议**：如需多用户/多端访问，需后续集成数据库（如MySQL/PostgreSQL），并实现后端API持久化存储。

### 5. 环境变量与配置 (Environment Variables & Config)
- **建议**：所有敏感信息（API密钥、数据库连接等）均通过环境变量管理，不要硬编码在代码中。
- **部署时**：检查 `.env` 文件权限，避免泄露。

### 6. 端口与安全 (Ports & Security)
- **端口映射**：确保虚拟机的3001端口（或自定义端口）对外开放，或通过Nginx等反向代理到80/443。
- **HTTPS**：生产环境建议开启HTTPS，保护数据传输安全。
- **CORS**：如前后端分离，需在后端设置允许的CORS来源。

### 7. 其他注意事项 (Other Notes)
- **日志与监控**：建议配置日志输出和监控，便于排查问题。
- **自动重启**：使用 `pm2`、`forever` 或 `systemd` 保证服务异常自动重启。
- **备份**：如集成数据库，需定期备份数据。
- **代码更新**：生产环境更新代码需重启后端服务。

---

> **迁移到虚拟机/生产环境时，务必检查以上所有点，确保API安全、服务可用、数据不丢失。**

---

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
</head>
<body>
    <h1>Test Login Setup</h1>
    <button onclick="setTestLogin()">Set Test Login</button>
    <button onclick="clearLogin()">Clear Login</button>
    <button onclick="checkLogin()">Check Login Status</button>
    <div id="status"></div>

    <script>
        function setTestLogin() {
            const token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjE0LCJpYXQiOjE3NDg1NjgxNzQsImV4cCI6MTc0OTE3Mjk3NH0.2LLyAh4Gm3htWW1uWmPkgXYBj6iaYyZew8PrMrBdsPo";
            const user = {
                id: 14,
                email: "<EMAIL>",
                username: "testuser3",
                preferred_language: "zh"
            };
            
            localStorage.setItem('authToken', token);
            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.setItem('preferredLanguage', 'zh');
            
            document.getElementById('status').innerHTML = '<p style="color: green;">Test login set successfully!</p>';
        }
        
        function clearLogin() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('preferredLanguage');
            
            document.getElementById('status').innerHTML = '<p style="color: red;">Login cleared!</p>';
        }
        
        function checkLogin() {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('currentUser');
            
            document.getElementById('status').innerHTML = `
                <p><strong>Token:</strong> ${token ? 'Present' : 'Not found'}</p>
                <p><strong>User:</strong> ${user ? JSON.parse(user).username : 'Not found'}</p>
            `;
        }
    </script>
</body>
</html>

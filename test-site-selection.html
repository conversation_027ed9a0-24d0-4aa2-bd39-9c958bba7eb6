<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>站点选择页面测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #0f172a;
            color: white;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            background: rgba(255,255,255,0.1); 
            border-radius: 8px; 
        }
        button { 
            padding: 10px 20px; 
            margin: 10px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
        }
        button:hover { background: #0056b3; }
        .result { 
            margin-top: 20px; 
            padding: 10px; 
            background: #1e293b; 
            border-radius: 5px; 
            white-space: pre-wrap; 
            max-height: 400px;
            overflow-y: auto;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #334155;
            border-radius: 8px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏭 站点选择页面测试</h1>
        
        <div class="test-section">
            <h3>📊 API连接测试</h3>
            <button onclick="testAPI()">测试后端API</button>
            <button onclick="testSiteSelection()">测试站点选择API</button>
            <div id="api-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🎯 页面直接访问测试</h3>
            <p>点击按钮在新窗口中打开页面：</p>
            <button onclick="openPage('/')">打开首页</button>
            <button onclick="openPage('/site-selection')">打开站点选择页面</button>
            <button onclick="openPage('/test')">打开测试页面</button>
        </div>
        
        <div class="test-section">
            <h3>🔍 页面预览</h3>
            <p>在iframe中预览站点选择页面：</p>
            <iframe id="preview-frame" src="http://localhost:5173/site-selection"></iframe>
        </div>
        
        <div class="test-section">
            <h3>🚨 控制台错误检查</h3>
            <button onclick="checkConsoleErrors()">检查控制台错误</button>
            <div id="console-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function showResult(elementId, message, isError = false) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.textContent = message;
            resultDiv.style.color = isError ? '#f87171' : '#10b981';
        }
        
        async function testAPI() {
            try {
                showResult('api-result', '正在测试后端API...');
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                showResult('api-result', `✅ 后端API正常！\n${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                showResult('api-result', `❌ 后端API连接失败: ${error.message}`, true);
            }
        }
        
        async function testSiteSelection() {
            try {
                showResult('api-result', '正在测试站点选择API...');
                const requestData = {
                    search_area: { type: "city", value: "Dallas, TX" },
                    parcel_filters: { min_area_sqft: 50000, max_area_sqft: 100000 },
                    user_constraints: { workers: 50 },
                    max_candidates: 3
                };
                
                const response = await fetch(`${API_BASE}/site-selection/prospecting`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                showResult('api-result', `✅ 站点选择API正常！找到 ${data.data?.parcels?.length || 0} 个站点\n\n响应摘要:\n${JSON.stringify(data.summary || {}, null, 2)}`);
            } catch (error) {
                showResult('api-result', `❌ 站点选择API失败: ${error.message}`, true);
            }
        }
        
        function openPage(path) {
            window.open(`http://localhost:5173${path}`, '_blank');
        }
        
        function checkConsoleErrors() {
            showResult('console-result', '检查控制台错误...\n请按F12打开开发者工具查看Console标签');
            
            // 尝试访问页面并检查错误
            const testFrame = document.createElement('iframe');
            testFrame.style.display = 'none';
            testFrame.src = 'http://localhost:5173/site-selection';
            
            testFrame.onload = function() {
                try {
                    // 尝试访问iframe内容（可能因同源策略失败）
                    const iframeDoc = testFrame.contentDocument || testFrame.contentWindow.document;
                    const rootElement = iframeDoc.getElementById('root');
                    
                    if (rootElement && rootElement.innerHTML.trim() === '') {
                        showResult('console-result', '⚠️  发现问题：root元素为空！这可能是白屏的原因。\n请检查浏览器控制台是否有JavaScript错误。', true);
                    } else if (rootElement) {
                        showResult('console-result', `✅ root元素包含内容 (${rootElement.innerHTML.length} 字符)`);
                    } else {
                        showResult('console-result', '❌ 找不到root元素', true);
                    }
                } catch (error) {
                    showResult('console-result', `⚠️  无法检查iframe内容 (同源策略限制): ${error.message}\n请手动在浏览器中访问 http://localhost:5173/site-selection 并查看控制台`, true);
                }
                
                setTimeout(() => {
                    document.body.removeChild(testFrame);
                }, 1000);
            };
            
            testFrame.onerror = function(error) {
                showResult('console-result', `❌ 页面加载失败: ${error}`, true);
                document.body.removeChild(testFrame);
            };
            
            document.body.appendChild(testFrame);
        }
        
        // 页面加载时自动测试API
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
